name: PROD Build and Publish Docker Image For Dashboard App and Deploy to <PERSON>er

on:
  push:
    branches:
      - prod

jobs:
  build-and-push:
    if: "!contains(github.event.head_commit.message, '[skip ci]')"
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install jq
        run: sudo apt-get install jq -y

      - name: Clone web repo
        uses: actions/checkout@v3
        with:
          repository: taostat/taostats-api-dashboard
          ref: prod
          token: ${{ secrets.GH_PAT }}
          path: web

      # - name: Bump version in apps/dashboard/package.json & commit
      #   run: |
      #     cd web
      #     ./apps/dashboard/bump_version.sh
      #     git config --local user.email "<EMAIL>"
      #     git config --local user.name "GitHub Action"
      #     git add apps/dashboard/package.json
      #     git commit -m "Bump version to $(jq -r '.version' apps/dashboard/package.json) [skip ci]"
      #     git push origin HEAD:prod
      #     cd .. && rm -rf web

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Log in to Quay.io
        uses: docker/login-action@v1
        with:
          registry: quay.io
          username: ${{ secrets.QUAY_USERNAME }}
          password: ${{ secrets.QUAY_PASSWORD }}

      - name: Build and Push Docker image for taostats-api-dashboard
        run: |
          IMAGE_NAME="taostats-api-dashboard"
          VERSION=prod-${{ github.sha }}
          REGISTRY_URL="quay.io/taostats"
          docker build -f apps/dashboard/Dockerfile -t $IMAGE_NAME:$VERSION \
            --build-arg APP_ENV=production \
            --build-arg NEXT_PUBLIC_BITTENSOR_SOCKET_URL=wss://openrpc.taostats.io:443 \
            --build-arg NEXT_PUBLIC_DASHBOARD_BASE_URL=https://dash.taostats.io/ \
            --build-arg NEXT_PUBLIC_WEBSITE_BASE_URL=https://taostats.io/ \
            .
          docker tag $IMAGE_NAME:$VERSION $REGISTRY_URL/$IMAGE_NAME:$VERSION
          docker push $REGISTRY_URL/$IMAGE_NAME:$VERSION

      - name: Clone ci-cd-repo
        uses: actions/checkout@v3
        with:
          repository: taostat/ci-cd-repo
          token: ${{ secrets.GH_PAT }}
          path: ci-cd-repo

      - name: Update image tag in ConfigMapGenerator
        run: |
          cd ci-cd-repo/taostats-api-dashboard/kustomize/base
          # installing kustomize
          curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh" | bash
          ./kustomize edit set image prod="quay.io/taostats/taostats-api-dashboard:prod-${{ github.sha }}"
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add kustomization.yaml
          git commit -m "Update image tag to ${{ github.sha }}"
          git push

      - name: Notify Discord
        uses: tsickert/discord-webhook@v5.3.0
        if: always()
        with:
          webhook-url: ${{ secrets.DISCORD_WEBHOOK_URL }}
          content: 'Workflow result for ${{ github.repository }}: ✧･ﾟ: *✧･ﾟ:* ${{ job.status }} *:･ﾟ✧*:･ﾟ✧.'
          embed-description: 'Check out the [actions tab](https://github.com/taostat/api/actions/runs/${{ github.run_id }}) for more details!'
          embed-color: ${{ job.status == 'success' && '3066993' || '15158332' }}
          embed-author-icon-url: https://github.githubassets.com/assets/GitHub-Mark-ea2971cee799.png

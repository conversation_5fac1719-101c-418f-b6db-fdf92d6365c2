{"private": true, "scripts": {"build": "NODE_OPTIONS='--max-old-space-size=4096' turbo run build", "dev": "NODE_OPTIONS='--max-old-space-size=4096' turbo run dev --concurrency=15", "lint": "NODE_OPTIONS='--max-old-space-size=4096' turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "get-dash-api-types": "npx swagger-typescript-api -p https://management-api.taostats.io/doc/json -o ./packages/types/dashboard-api-types/ -n dashboard-api-types-generated.ts", "get-website-api-types": "npx swagger-typescript-api -p https://api-beta-v2.taostats.io/api/openapi.json -o ./packages/types/website-api-types-generated/ -n website-api-types-generated.ts", "check-types": "turbo run type-check", "validate": "turbo run lint type-check"}, "devDependencies": {"prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "turbo": "^2.3.3"}, "name": "taostats-frontend-monorepo", "packageManager": "pnpm@9.1.1"}
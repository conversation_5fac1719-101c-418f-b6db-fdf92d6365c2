{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "globalEnv": ["APP_ENV", "NEXT_PUBLIC_DASHBOARD_BASE_URL", "DASHBOARD_API_URL", "NEXT_PUBLIC_WEBSITE_BASE_URL", "WEBSITE_API_URL", "WEBSITE_API_TOKEN", "NEXT_PUBLIC_STRIPE_PUBLIC_KEY", "STRIPE_SECRET_KEY", "NEXT_PUBLIC_BITTENSOR_SOCKET_URL"], "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**"]}, "lint": {}, "type-check": {}, "dev": {"cache": false, "persistent": true}, "clean": {"cache": false}}, "ui": "stream"}
const rules = {
  '@typescript-eslint/consistent-type-definitions': 'off',
  'import/no-default-export': 'off',
  'no-console': ['warn', { allow: ['error'] }],
  '@typescript-eslint/explicit-function-return-type': 'off',
  'react/function-component-definition': 'off',
  'import/order': [
    'warn',
    {
      'newlines-between': 'always',
      alphabetize: {
        order: 'asc',
      },
      pathGroups: [
        {
          pattern: 'react',
          group: 'external',
          position: 'before',
        },
        {
          pattern: 'react-dom',
          group: 'external',
          position: 'before',
        },
        {
          pattern: '@repo/**/**',
          group: 'internal',
          position: 'before',
        },
      ],
      pathGroupsExcludedImportTypes: ['react', 'react-dom'],
    },
  ],
  'no-restricted-imports': [
    'error',
    {
      patterns: [
        {
          group: ['../*'],
          message:
            'Please use absolute imports instead of parent directory imports.',
        },
      ],
    },
  ],
};

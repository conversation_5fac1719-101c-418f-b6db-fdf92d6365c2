import '@tanstack/react-table';
import type { RowData } from '@tanstack/react-table';

declare global {
  namespace UiComponents {
    declare module '*.png' {
      const value: string;
      export = value;
    }

    declare module '*.jpg' {
      const value: string;
      export = value;
    }

    declare module '*.svg' {
      const value: React.FunctionComponent<React.SVGProps<SVGSVGElement>>;
      export default value;
    }
  }
}

declare module '@tanstack/react-table' {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars -- Allow.
  interface ColumnMeta<TData extends RowData, TValue> {
    align?: 'start' | 'end' | 'center';
    separator?: 'dashed';
  }
}

// Need to export something for TypeScript to recognize this as a module
export {};

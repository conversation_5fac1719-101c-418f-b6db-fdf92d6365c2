import type { ReactNode } from 'react';
import { Alert } from './alert';

export const AlertError = ({
  title,
  description,
  className,
  alignItems = 'start',
}: {
  title?: string;
  description: ReactNode;
  className?: string;
  alignItems?: 'start' | 'center';
}) => {
  return (
    <Alert
      type='error'
      title={title}
      description={description}
      className={className}
      alignItems={alignItems}
    />
  );
};

import type { ReactNode } from 'react';
import type { IconType } from 'react-icons';
import {
  BiSolidXCircle,
  BiSolidCheckCircle,
  BiInfoCircle,
} from 'react-icons/bi';
import { cn } from '../../lib';
import { Spinner } from '../spinner';
import {
  TooltipContent,
  TooltipTrigger,
  Tooltip,
  TooltipProvider,
} from '../tooltip';

export const Alert = ({
  title,
  description,
  className,
  type = 'default',
  showSpinner = false,
  alignItems = 'start',
  iconTooltip,
  Icon,
}: {
  title?: string;
  description: ReactNode;
  className?: string;
  type?: 'error' | 'warning' | 'info' | 'default' | 'success';
  showSpinner?: boolean;
  alignItems?: 'start' | 'center';
  iconTooltip?: string;
  Icon?: IconType;
}) => {
  const styles = {
    success: {
      bg: 'bg-[#00DBBC4D]',
      iconClassName: 'text-white',
      title: 'text-white',
      description: 'text-white/60',
      Icon: BiSolidCheckCircle,
      borderColor: 'border-white/20',
    },
    error: {
      bg: 'bg-[#EB534780]',
      iconClassName: 'text-white',
      title: 'text-white',
      description: 'text-white/60',
      Icon: BiSolidXCircle,
      borderColor: 'border-white/20',
    },
    warning: {
      bg: 'bg-[#EBA4474D]',
      iconClassName: 'text-white',
      title: 'text-white',
      description: 'text-white/60',
      Icon: BiInfoCircle,
      borderColor: 'border-white/20',
    },
    info: {
      bg: 'bg-[#47C6EB4D]',
      iconClassName: 'text-white',
      title: 'text-white',
      description: 'text-white/60',
      Icon: BiInfoCircle,
      borderColor: 'border-white/20',
    },
    default: {
      bg: 'bg-card',
      iconClassName: 'text-white',
      title: 'text-white',
      description: 'text-white/60',
      Icon: BiInfoCircle,
      borderColor: 'border-white/20',
    },
  };

  const currentStyle = styles[type];
  const icon = Icon ? (
    <Icon
      aria-hidden='true'
      className={`size-5 ${currentStyle.iconClassName}`}
    />
  ) : (
    <currentStyle.Icon
      aria-hidden='true'
      className={`size-5 ${currentStyle.iconClassName}`}
    />
  );

  const iconOrIconWithTooltip = iconTooltip ? (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger>{icon}</TooltipTrigger>
        <TooltipContent>{iconTooltip}</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  ) : (
    icon
  );

  return (
    <div
      className={cn(
        `rounded-xl ${currentStyle.bg} ${currentStyle.borderColor} border p-4 [overflow-wrap:anywhere]`,
        className
      )}
    >
      <div
        className={cn(
          'flex flex-row gap-2',
          alignItems === 'center' ? 'items-center' : 'items-start'
        )}
      >
        <div className='shrink-0'>
          {showSpinner ? (
            <Spinner className='shrink-0' />
          ) : (
            iconOrIconWithTooltip
          )}
        </div>
        <div className='flex w-full flex-col gap-4'>
          {title ? <h3 className={currentStyle.title}>{title}</h3> : null}
          <div className={cn(currentStyle.description)}>
            {typeof description === 'string' ? (
              <p>{description}</p>
            ) : (
              <div className='flex'>{description}</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

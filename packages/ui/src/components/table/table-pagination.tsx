import type { Table } from '@tanstack/react-table';
import { BiChevronLeft, BiChevronRight } from 'react-icons/bi';
import { Text } from '../text';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationPrevious,
  PaginationNext,
} from './pagination-primitives';

export const TablePagination = <T,>({
  table,
  total,
}: {
  table: Table<T>;
  total: number;
}) => {
  // Don't show pagination if there's only one page
  if (table.getPageCount() <= 1) {
    return null;
    // return <PaginationStatus table={table} total={total} />;
  }

  return (
    <>
      <Pagination className='justify-end'>
        <PaginationContent>
          <PaginationItem>
            <PaginationLink
              onClick={() => {
                table.firstPage();
              }}
              isDisabled={!table.getCanPreviousPage()}
              aria-label='Go to first page'
              size='default'
              className='gap-1 pl-2.5'
            >
              <BiChevronLeft className='h-4 w-4' />
              <span>First</span>
            </PaginationLink>
          </PaginationItem>
          <PaginationItem>
            <PaginationPrevious
              isDisabled={!table.getCanPreviousPage()}
              size='default'
              className='gap-1 pl-2.5'
              onClick={() => {
                table.previousPage();
              }}
            />
          </PaginationItem>
          <PaginationItem>
            <PaginationNext
              isDisabled={!table.getCanNextPage()}
              size='default'
              className='gap-1 pl-2.5'
              onClick={() => {
                table.nextPage();
              }}
            />
          </PaginationItem>
          <PaginationItem>
            <PaginationLink
              onClick={() => {
                table.lastPage();
              }}
              isDisabled={!table.getCanNextPage()}
              aria-label='Go to last page'
              size='default'
              className='gap-1 pl-2.5'
            >
              <span>Last</span>
              <BiChevronRight className='h-4 w-4' />
            </PaginationLink>
          </PaginationItem>
          <PaginationItem>
            <Text
              level='labelMedium'
              as='span'
              className='flex items-center gap-1 text-white/60'
            >
              Go to page:
              <input
                type='number'
                min='1'
                max={table.getPageCount()}
                defaultValue={table.getState().pagination.pageIndex + 1}
                onChange={(e) => {
                  const page = e.target.value ? Number(e.target.value) - 1 : 0;
                  if (page >= 0 && page < table.getPageCount()) {
                    table.setPageIndex(page);
                  }
                }}
                className='w-16 rounded border p-1 text-black'
              />
            </Text>
          </PaginationItem>
        </PaginationContent>
      </Pagination>
      <PaginationStatus table={table} total={total} />
    </>
  );
};

const PaginationStatus = <T,>({
  table,
  total,
}: {
  table: Table<T>;
  total: number;
}) => {
  const currentPage = table.getState().pagination.pageIndex + 1;
  const totalPages = table.getPageCount();
  const pageString =
    currentPage === totalPages ? `` : `Page ${currentPage} of ${totalPages}.`;

  const rowString =
    table.getRowModel().rows.length === total
      ? `${total.toLocaleString()} Rows.`
      : `${table.getRowModel().rows.length.toLocaleString()} of ${total.toLocaleString()} Rows.`;

  return (
    <Text level='labelMedium' className='text-right text-white/60'>
      <span className='flex items-center justify-end gap-1'>
        <span>{pageString}</span>
        <span>{rowString}</span>
      </span>
    </Text>
  );
};

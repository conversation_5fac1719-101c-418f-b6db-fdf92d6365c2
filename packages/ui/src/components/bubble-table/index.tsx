'use client';
import React from 'react';
import {
  type ReactNode,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import { rankItem } from '@tanstack/match-sorter-utils';
import {
  type ColumnDef,
  type FilterFn,
  type Row as RowType,
  type SortingState,
  createColumnHelper,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import type { ClassNameValue } from 'tailwind-merge';
import type { TableData } from '@repo/types/website-api-types';
import { Selector, Text } from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { Header } from './header';
import { Row, ValidatorPerformanceRow } from './row';
import { SearchInput } from './search-input';

interface TableProps {
  heading?: string;
  description?: string;
  rowData: TableData[];
  columnSchemas: ColumnDef<any, any>[];
  searchable?: boolean;
  searchPlaceholder?: string;
  pagination?: boolean;
  selector?: boolean;
  isFetching?: boolean;
  pageSize?: number;
  latestHighlight?: boolean;
  initialSortBy?: SortingState;
  initialPageSize?: number;
  small?: boolean;
  focusIndex?: number;
  closeIcon?: boolean;
  csvFilename?: string;
  amountFilter?: React.ReactNode;
  isManual?: boolean;
  rowTotalData?: TableData[];
  onFilterChange?: (filter: string, sorting: SortingState) => void;
  onLimitChange?: (limit: number) => void;
  onRowClick?: (event: React.MouseEvent, values: RowType<TableData>) => void;
  variant?:
    | 'ValidatorPerformance'
    | 'Default'
    | 'WeightEvent'
    | 'RootMetagraph'
    | 'Subnets'
    | 'Validator'
    | 'Balances';
  colors?: string[][];
  subnetColor?: Record<number, number>[];
  stickyHeader?: boolean;
  stickyHeaderClass?: string;
  iconClassName?: ClassNameValue;
  inputClassName?: ClassNameValue;
  inputStyleClassName?: ClassNameValue;
  topPagination?: boolean;
  taoSwitch?: React.ReactNode;
  link?: boolean;
  csvExport?: boolean;
  inlineHeading?: boolean;
  customRow?: ReactNode;
  className?: string;
  selectorType?: string;
  searchInputRootClassName?: string;
}

/* 	Note that for sticky header to work correctly, no parent should have an overflow value set that is NOT the default "visible".
	See: https://developer.mozilla.org/en-US/docs/Web/CSS/position?v=example#sticky
*/
export const BubbleTable = ({
  heading,
  description,
  rowData,
  columnSchemas,
  searchable,
  searchPlaceholder = 'Filter Table...',
  pagination,
  initialSortBy = [],
  selector,
  pageSize = 50,
  isFetching = false,
  latestHighlight,
  small,
  initialPageSize = 10,
  focusIndex,
  closeIcon = true,
  csvFilename,
  amountFilter,
  isManual = true,
  rowTotalData,
  onFilterChange,
  onLimitChange,
  onRowClick,
  variant = 'Default',
  colors,
  subnetColor,
  stickyHeader,
  stickyHeaderClass = 'bg-darkgrey md:bg-neutral-900',
  iconClassName,
  inputClassName,
  inputStyleClassName,
  topPagination,
  taoSwitch,
  link,
  csvExport = true,
  inlineHeading,
  className,
  selectorType,
  searchInputRootClassName,
}: TableProps) => {
  const [data, setData] = useState(() => [...rowData]);
  const [sorting, setSorting] = useState<SortingState>(initialSortBy);
  const [globalFilter, setGlobalFilter] = useState('');
  const [colWidths, setColWidths] = useState<number[] | undefined>(undefined);
  const stickyHeaderWrapperRef = useRef<HTMLDivElement>(null);
  const tableWrapperRef = useRef<HTMLDivElement>(null);
  const scrollLock = useRef(false);

  const columnHelper = createColumnHelper<TableData>();

  const columns = columnSchemas.map((schema) =>
    columnHelper.accessor(schema.id!, {
      header: schema.header,
      footer: schema.footer,
      cell: schema.cell,
      sortingFn: schema.sortingFn ?? 'auto',
      enableSorting: schema.enableSorting,
      size: schema.size,
    })
  );

  const fuzzyFilter: FilterFn<any> = (row, columnId, value, addMeta) => {
    const itemRank = rankItem(row.getValue(columnId), value);
    addMeta({
      itemRank,
    });
    return itemRank.passed;
  };

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      globalFilter,
    },
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onSortingChange: setSorting,
    manualSorting: isManual,
    manualFiltering: isManual,
    globalFilterFn: fuzzyFilter,
    onGlobalFilterChange: setGlobalFilter,
    // debugTable: true,
    // debugHeaders: true,
    // debugColumns: true,
    sortDescFirst: true,
  });

  const changeFilter = useCallback(
    (globalFilters: string, sortingFilter: SortingState) => {
      if (onFilterChange) {
        onFilterChange(globalFilters, sortingFilter);
      }
    },
    [onFilterChange]
  );

  // const syncScroll = useCallback((source: "header" | "table") => {
  // 	if (scrollLock.current) return;

  // 	const headerEl = stickyHeaderWrapperRef.current;
  // 	const tableEl = tableWrapperRef.current;

  // 	if (!headerEl || !tableEl) return;

  // 	scrollLock.current = true;

  // 	requestAnimationFrame(() => {
  // 		if (tableEl.scrollLeft !== headerEl.scrollLeft) {
  // 			if (source === "table") {
  // 				headerEl.scrollLeft = tableEl.scrollLeft;
  // 			} else {
  // 				tableEl.scrollLeft = headerEl.scrollLeft;
  // 			}
  // 		}
  // 		scrollLock.current = false;
  // 	});
  // }, []);

  const syncHeaderWithTableScroll = () => {
    const headerEl = stickyHeaderWrapperRef.current;
    const tableEl = tableWrapperRef.current;

    if (!headerEl || !tableEl) return;

    const syncScroll = (e: Event) => {
      if (e.target === headerEl) {
        tableEl.scrollLeft = headerEl.scrollLeft;
      } else if (e.target === tableEl) {
        headerEl.scrollLeft = tableEl.scrollLeft;
      }
    };

    headerEl.addEventListener('scroll', syncScroll);
    tableEl.addEventListener('scroll', syncScroll);

    return () => {
      headerEl.removeEventListener('scroll', syncScroll);
      tableEl.removeEventListener('scroll', syncScroll);
    };
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (stickyHeader) {
      return syncHeaderWithTableScroll();
    }
  }, [stickyHeader]);

  // const syncScrollToTable = useCallback(() => {
  // 	syncScroll("table");
  // }, [syncScroll]);

  useEffect(() => {
    setData([...rowData]);
  }, [rowData]);

  useEffect(() => {
    changeFilter(globalFilter, sorting);
  }, [sorting, globalFilter, changeFilter]);

  // useEffect(() => {
  // 	if (stickyHeader) {
  // 		// TODO - also sync up the scroll on the header to the body for that one person that scrolls with the header and not the body xD
  // 		tableWrapperRef.current?.addEventListener("scroll", syncScrollToTable);

  // 		return () => {
  // 			tableWrapperRef.current?.removeEventListener(
  // 				"scroll",
  // 				syncScrollToTable
  // 			);
  // 		};
  // 	}
  // }, [syncScrollToTable, stickyHeader]);

  return (
    <div className='flex w-full flex-col items-start'>
      {heading ||
      description ||
      searchable ||
      amountFilter ||
      selector ||
      taoSwitch ? (
        <section
          id='taostats-table-header'
          className='flex w-full flex-col gap-4'
        >
          {heading && !inlineHeading ? (
            <Text level='mdTitle' className='font-medium'>
              {heading}
            </Text>
          ) : null}
          {description ? (
            <Text level='sm' className='text-neutral-700'>
              {description}
            </Text>
          ) : null}
          <div
            className={cn(
              'flex w-full items-end justify-between gap-4 pb-4 pt-4',
              variant !== 'Subnets'
                ? 'flex-wrap'
                : 'max-lg:flex-col max-lg:items-start'
            )}
          >
            {heading && inlineHeading ? (
              <Text level='mdTitle' className='font-medium'>
                {heading}
              </Text>
            ) : null}
            <div
              className={cn(
                'flex flex-wrap items-end gap-2',
                (variant === 'Subnets' || variant === 'Validator') && '!w-full',
                searchInputRootClassName
              )}
            >
              {amountFilter ? amountFilter : null}
              {searchable ? (
                <SearchInput
                  setGlobalFilter={setGlobalFilter}
                  closeIcon={closeIcon}
                  searchPlaceholder={searchPlaceholder}
                  iconClassName={iconClassName}
                  inputClassName={inputClassName}
                  inputStyleClassName={inputStyleClassName}
                />
              ) : null}
            </div>
            <div className='flex flex-row items-center gap-10'>
              {taoSwitch ? taoSwitch : null}
              {topPagination ? null : (
                <div className='flex items-center gap-4'>
                  {selector ? (
                    <div className='hidden md:block'>
                      <Selector
                        selectorType={selectorType}
                        defaultSize={initialPageSize}
                        onLimitChange={onLimitChange}
                      />
                    </div>
                  ) : null}
                  {csvExport ? (
                    <CSVModal
                      rowData={rowTotalData ?? rowData}
                      columnSchemas={columnSchemas}
                      filename={csvFilename}
                    />
                  ) : null}
                </div>
              )}
            </div>
          </div>
          {selector ? (
            <div className='self-start px-3 md:hidden'>
              <Selector
                defaultSize={initialPageSize}
                onLimitChange={onLimitChange}
              />
            </div>
          ) : null}
        </section>
      ) : null}

      {stickyHeader ? (
        <div
          ref={stickyHeaderWrapperRef}
          className={cn(
            'no-scrollbar z-1 sticky top-[110px] -mb-8 max-w-full overflow-x-auto md:top-[104px]',
            stickyHeaderClass
          )}
        >
          <table>
            <Header widths={colWidths} {...table} tableName={csvFilename} />
          </table>
        </div>
      ) : null}

      <div ref={tableWrapperRef} className='w-full overflow-x-auto'>
        <table
          id='taostats-table'
          className={cn(
            'min-w-full border-separate',
            variant === 'WeightEvent'
              ? 'border-spacing-y-0'
              : 'border-spacing-y-1'
          )}
        >
          <Header
            invisible={stickyHeader}
            setWidths={stickyHeader ? setColWidths : undefined}
            {...table}
            tableName={csvFilename}
          />

          <tbody className='space-y-1'>
            {table.getRowModel().rows.map((row, i) => {
              const props = {
                latestHighlight,
                row,
                i,
                pageSize,
                small,
                isFocus: focusIndex === i,
                tableName: csvFilename,
              };

              return variant === 'ValidatorPerformance' ? (
                <ValidatorPerformanceRow {...props} key={i} />
              ) : variant === 'WeightEvent' ? (
                <Row
                  {...props}
                  colors={colors ? colors[i] : []}
                  key={i}
                  link={link}
                  onClick={(event) => onRowClick?.(event, props.row)}
                  className={className}
                />
              ) : variant === 'RootMetagraph' ? (
                <Row
                  {...props}
                  subnetColors={subnetColor ? subnetColor[i] : []}
                  key={i}
                  onClick={(event) => onRowClick?.(event, props.row)}
                  link={link}
                  className={className}
                />
              ) : variant === 'Subnets' ? (
                <Row
                  {...props}
                  key={i}
                  link={row.original.netuid !== undefined && link}
                  onClick={
                    row.original.netuid !== undefined
                      ? (event) => onRowClick?.(event, props.row)
                      : undefined
                  }
                  className={className}
                />
              ) : variant === 'Balances' ? (
                <WalletTableRow {...props} key={`${row.original.rowkey}`} />
              ) : (
                <Row
                  {...props}
                  key={i}
                  link={link}
                  onClick={(event) => onRowClick?.(event, props.row)}
                  className={className}
                />
              );
            })}
            {isFetching
              ? Array.from({ length: 10 }).map((_, index) => (
                  <tr
                    className={cn(
                      'w-full animate-pulse overflow-hidden rounded-lg bg-neutral-800/50',
                      small ? 'h-10' : 'h-14'
                    )}
                    key={index}
                  >
                    {columnSchemas.map((_, i) => (
                      <td
                        key={i}
                        className='overflow-hidden first:rounded-l-lg last:rounded-r-lg'
                      />
                    ))}
                  </tr>
                ))
              : null}
          </tbody>
        </table>
      </div>
    </div>
  );
};

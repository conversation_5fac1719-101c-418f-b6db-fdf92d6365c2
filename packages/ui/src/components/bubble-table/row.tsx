import { useMemo } from 'react';
import { type Row as TableRow, flexRender } from '@tanstack/react-table';
import { motion } from 'framer-motion';
import type { TableData } from '@repo/types/website-api-types';
import { cn } from '@repo/ui/lib';

interface RowProps {
  row: TableRow<TableData>;
  i: number;
  pageSize?: number;
  latestHighlight?: boolean;
  small?: boolean;
  isFocus?: boolean;
  tableName?: string;
  colors?: string[];
  subnetColors?: Record<number, number>;
  onClick?: (event: React.MouseEvent<HTMLTableRowElement>) => void;
  link?: boolean;
  className?: string;
}

interface RowSkeletonProps {
  columnCount: number;
  i: number;
}

export const Row = ({
  row,
  i,
  pageSize = 50,
  latestHighlight,
  small,
  isFocus,
  tableName,
  colors,
  subnetColors,
  onClick,
  link,
  className,
}: RowProps) => {
  const handleClick = (event: React.MouseEvent<HTMLTableRowElement>) => {
    if (onClick) {
      onClick(event);
    }
  };

  return (
    <motion.tr
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5, delay: (i % pageSize) * 0.01 }}
      className={cn(
        'overflow-hidden rounded-lg text-xs',
        i === 0 && latestHighlight
          ? 'bg-ocean/10 hover:bg-ocean/20'
          : 'bg-neutral-800/20 hover:bg-neutral-800 [&:hover>td]:bg-neutral-800',
        tableName === 'child-hotkey' &&
          'bg-[#0D0D0D] hover:bg-[#0C0C0C] [&:hover>td]:bg-[#0C0C0C]',
        isFocus ? 'bg-neutral-700' : '',
        className
      )}
      onClick={handleClick}
    >
      {row.getVisibleCells().map((cell, index) => (
        <td
          className={cn(
            small && '!h-10',
            'h-14 cursor-default text-left',
            link && 'cursor-pointer',
            i === 0 && latestHighlight ? 'text-ocean' : 'text-white',
            'overflow-hidden first:rounded-l-lg first:pl-2 last:rounded-r-lg last:pr-2',
            tableName === 'root-metagraph' && index === 2
              ? 'z-1 sticky left-0 bg-[#1a1a1a]'
              : tableName === 'root-metagraph-history' && index === 0
                ? 'z-1 sticky left-0 bg-[#151515]'
                : ''
          )}
          key={index}
        >
          <div
            className={cn(
              small && '!h-10',
              'flex h-14 w-max items-center px-2'
            )}
            style={{
              backgroundColor:
                tableName === 'root-metagraph-history' && index !== 0
                  ? colors?.[index - 1] || ''
                  : '',
              color:
                tableName === 'root-metagraph' && index > 3
                  ? Math.abs(subnetColors?.[index - 4] ?? 0) > 2
                    ? '#eb8547'
                    : Math.abs(subnetColors?.[index - 4] ?? 0) > 1
                      ? '#ffdb29'
                      : Math.abs(subnetColors?.[index - 4] ?? 0) > 3
                        ? '#bf1111'
                        : '#FFFFFF'
                  : '',
            }}
          >
            {flexRender(cell.column.columnDef.cell, cell.getContext())}
          </div>
        </td>
      ))}
    </motion.tr>
  );
};

export const ValidatorPerformanceRow = ({
  row,
  i,
  pageSize = 50,
  small,
}: RowProps) => {
  const alert = useMemo(
    () =>
      Number(row.original.updated) > 1000 ||
      Number(row.original.last_updated) > 1000,
    [row.original.updated, row.original.last_updated]
  );
  const isChildkey = useMemo(
    () => Boolean(row.original.isChildkey),
    [row.original.isChildkey]
  );

  return (
    <motion.tr
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5, delay: (i % pageSize) * 0.01 }}
      className={cn(
        'overflow-hidden rounded-lg text-xs',
        'bg-neutral-800/20 hover:bg-neutral-800 [&:hover>td]:bg-neutral-800',
        alert
          ? 'bg-[#EB5347]/20 hover:bg-[#EB5347]/30 [&:hover>td]:bg-[#EB5347]/30'
          : isChildkey
            ? 'bg-yellow-100/10 hover:bg-yellow-100/20 [&:hover>td]:bg-yellow-100/20'
            : 'bg-neutral-900 hover:bg-neutral-800 [&:hover>td]:bg-neutral-800',
        isChildkey
          ? 'border-yellow-200/50'
          : alert
            ? 'border-rose-500'
            : 'border-neutral-800'
      )}
    >
      {row.getVisibleCells().map((cell, index) => (
        <td
          className={cn(
            small && '!h-10',
            'h-14 cursor-default px-2 text-left text-white',
            'overflow-hidden first:rounded-l-lg first:pl-5 last:rounded-r-lg last:pr-5'
          )}
          key={index}
        >
          {flexRender(cell.column.columnDef.cell, cell.getContext())}
        </td>
      ))}
    </motion.tr>
  );
};

export const RowSkeleton = ({ columnCount, i }: RowSkeletonProps) => {
  return (
    <tr
      className={cn(
        'animate-pulse border-b border-neutral-800 transition-colors hover:bg-neutral-900'
      )}
      key={i}
    >
      {Array.from({ length: columnCount }).map((_, index) => (
        <td
          className='cursor-default px-2 py-3 text-left font-medium text-neutral-100'
          key={`${index}`}
        >
          <div className='h-6 w-full rounded-lg bg-[#3b3b3b4c]' />
        </td>
      ))}
    </tr>
  );
};

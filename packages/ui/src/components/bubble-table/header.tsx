import { useEffect, useRef } from 'react';
import { type Table, flexRender } from '@tanstack/react-table';
import { CgChevronUp } from 'react-icons/cg';
import { cn } from '@repo/ui/lib';

export const Header = <TData,>({
  tableName,
  setWidths,
  widths,
  invisible,
  ...table
}: Table<TData> & {
  tableName?: string;
  setWidths?: (widths: number[]) => void;
  widths?: number[];
  invisible?: boolean;
}) => {
  const headerRowRef = useRef<HTMLTableRowElement>(null);
  const observer = useRef<ResizeObserver | null>(null);

  useEffect(() => {
    if (!observer.current && setWidths && headerRowRef.current) {
      observer.current = new ResizeObserver((entries) => {
        const rowChildren = Array.from(entries[0].target.children);
        setWidths(rowChildren.map((child) => child.clientWidth));
      });

      observer.current.observe(headerRowRef.current);

      return () => {
        observer.current?.disconnect();
        observer.current = null;
      };
    }
  }, [setWidths]);

  return (
    <thead className={cn(invisible && 'invisible')}>
      {table.getHeaderGroups().map((headerGroup, idx) => (
        <tr ref={headerRowRef} key={idx}>
          {headerGroup.headers.map((header, index) => (
            // biome-ignore lint/a11y/useKeyWithClickEvents: <explanation>
            <th
              onClick={() => {
                if (header.column.getCanSort()) {
                  header.column.toggleSorting(
                    header.column.getNextSortingOrder() !== 'asc'
                  );
                }
              }}
              className={cn(
                'cursor-default px-2 py-2 text-left text-xs font-[400] text-neutral-500 transition',
                header.column.getCanSort() ? 'hover:text-neutral-300' : '',
                tableName === 'root-metagraph' && index === 2
                  ? 'z-1 sticky left-0 bg-neutral-900'
                  : tableName === 'root-metagraph-history' && index === 0
                    ? 'z-1 sticky left-0 bg-[#121212]'
                    : ''
              )}
              style={
                widths?.[index]
                  ? {
                      minWidth: `${widths[index]}px`,
                    }
                  : {}
              }
              key={index}
            >
              <span
                className={cn(
                  'group flex select-none items-center gap-3 truncate',
                  header.column.getIsSorted() && 'text-turquoise'
                )}
              >
                {header.isPlaceholder
                  ? null
                  : flexRender(
                      header.column.columnDef.header,
                      header.getContext()
                    )}

                {header.column.getCanSort() ? (
                  <CgChevronUp
                    className={cn(
                      'transition-transform',
                      header.column.getIsSorted() === 'asc'
                        ? ''
                        : header.column.getIsSorted() === 'desc'
                          ? 'rotate-180'
                          : 'rotate-45 opacity-50 saturate-0 group-hover:opacity-80'
                    )}
                  />
                ) : (
                  <></>
                )}
              </span>
            </th>
          ))}
        </tr>
      ))}
    </thead>
  );
};

import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import type { IconType } from 'react-icons';
import { cn } from '../../lib';

const badgeVariants = cva(
  'inline-flex items-center rounded-full border px-2 py-0.5 text-xs font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 w-fit',
  {
    variants: {
      variant: {
        green1: 'border-transparent bg-[#BAEB471A] text-[#BAEB47]',
        green2: 'border-transparent bg-[#47EB6C] text-black',
        green3: 'border-transparent bg-[#47EB6C1A] text-[#47EB6C]',
        green4: 'border-transparent bg-[#00DBBC] text-black',
        green5: 'border-transparent bg-[#00DBBC1A] text-[#00DBBC]',
        orange1: 'border-transparent bg-[#FF8B25] text-black',
        yellow1: 'border-transparent bg-[#EBC247] text-black',
        yellow2: 'border-transparent bg-[#CA8A04] text-black',
        red1: 'border-transparent bg-[#EB53471A] text-[#EB5347]',
        red2: 'border-transparent bg-[#EB5347] text-black',
        default: 'border-transparent bg-primary text-primary-foreground',
        secondary: 'border-transparent bg-secondary text-secondary-foreground',
        destructive:
          'border-transparent bg-destructive text-destructive-foreground',
        outline: 'text-foreground',
      },
      hover: {
        true: '',
      },
    },
    compoundVariants: [
      {
        hover: true,
        variant: 'green1',
        className: 'hover:bg-[#BAEB471A]/80',
      },
      {
        hover: true,
        variant: 'green2',
        className: 'hover:bg-[#47EB6C]/80',
      },
      {
        hover: true,
        variant: 'green3',
        className: 'hover:bg-[#47EB6C1A]/80',
      },
      {
        hover: true,
        variant: 'red1',
        className: 'hover:bg-[#EB53471A]/80',
      },
      {
        hover: true,
        variant: 'red2',
        className: 'hover:bg-accent-2/80',
      },
      {
        hover: true,
        variant: 'default',
        className: 'hover:bg-primary/80',
      },
      {
        hover: true,
        variant: 'secondary',
        className: 'hover:bg-secondary/80',
      },
      {
        hover: true,
        variant: 'destructive',
        className: 'hover:bg-destructive/80',
      },
    ],
  }
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  Icon?: IconType;
  showHoverStyles?: boolean;
}

function Badge({
  className,
  variant,
  Icon,
  showHoverStyles = false,
  ...props
}: BadgeProps) {
  return (
    <div
      className={cn(
        'transition-colors duration-500',
        badgeVariants({ variant, hover: showHoverStyles }),
        className
      )}
      {...props}
    >
      {Icon ? <Icon className='mr-1 h-4 w-4' /> : null}
      {props.children}
    </div>
  );
}

export { Badge, badgeVariants };

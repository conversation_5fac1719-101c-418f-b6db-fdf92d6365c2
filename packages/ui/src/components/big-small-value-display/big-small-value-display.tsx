import type { ComponentProps } from 'react';
import { cn } from '../../lib';
import { Text } from '../text';

// There are some cases where we want the decimal part of a number to be shown in slightly smaller font
// This component will split the number into whole and decimal parts and then display the decimal part in a smaller font
export const BigSmallValueDisplay = ({
  textLevel,
  className,
  value,
  valueFormattingOptions,
  areDecimalsSmall = true,
  suffix,
}: {
  textLevel: ComponentProps<typeof Text>['level'];
  className?: string;
  value: number;
  valueFormattingOptions: Intl.NumberFormatOptions;
  areDecimalsSmall?: boolean;
  suffix?: string;
}) => {
  const getFormattedNumberAsString = value.toLocaleString(
    'en-US',
    valueFormattingOptions
  );

  const formatted =
    getFormattedNumberAsString === '-0.00'
      ? '0.00'
      : getFormattedNumberAsString;

  const formatNumberParts = () => {
    const [whole, decimal] = formatted.split('.');

    return (
      <>
        {whole}
        {decimal ? <span className='text-[0.8em]'>.{decimal}</span> : null}
        {suffix ? <span className='text-[0.8em]'>{suffix}</span> : null}
      </>
    );
  };

  return (
    <Text
      level={textLevel}
      className={cn('transition-colors duration-500', className)}
    >
      {areDecimalsSmall
        ? formatNumberParts()
        : `${formatted}${suffix ? suffix : ''}`}
    </Text>
  );
};

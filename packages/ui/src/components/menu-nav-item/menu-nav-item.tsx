import type { IconType } from 'react-icons';
import { cn } from '../../lib';
import { Link } from '../link';

type MenuNavItemProps = {
  index: number;
  name: string;
  href: string;
  icon: IconType;
  rightIcon?: IconType;
  current?: boolean;
  target?: string;
  onNavigate?: () => void;
};

export const MenuNavItem = ({ item }: { item: MenuNavItemProps }) => {
  return (
    <li>
      <Link
        className={cn(
          item.current
            ? 'bg-navitem-selected text-white'
            : 'text-gray-400 hover:bg-navitem-selected hover:text-white',
          'group flex gap-x-3 rounded-md p-2 text-sm/6 font-semibold'
        )}
        href={item.href}
        target={item.target}
        onClick={() => {
          item.onNavigate?.();
        }}
      >
        <span
          className={cn(
            "relative before:content-[''] before:absolute before:left-0 before:top-0 before:bottom-0 before:w-[1px]",
            item.current ? 'before:bg-[#00DBBC]' : ''
          )}
        />
        <item.icon aria-hidden='true' className='size-6 shrink-0' />
        {item.name}
        {item.rightIcon ? (
          <item.rightIcon
            aria-hidden='true'
            className='size-6 shrink-0 ml-auto mr-2'
          />
        ) : null}
      </Link>
    </li>
  );
};

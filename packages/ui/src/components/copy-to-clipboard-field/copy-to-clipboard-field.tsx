import type { IconType } from 'react-icons';
import { BiCopy } from 'react-icons/bi';
import { cn } from '../../lib';
import { notify } from '../../lib/notify';

export const CopyToClipboardField = ({
  value,
  LeftIcon,
  className,
}: {
  value: string;
  LeftIcon: IconType;
  className?: string;
}) => {
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      notify.icon('Copied to clipboard', '📋');
    } catch (err) {
      notify.error('There was as error copying text');
    }
  };
  return (
    <div
      className={cn(
        'bg-gray-highlight flex items-center gap-2 rounded-lg border border-white/30 px-6 py-4',
        className
      )}
    >
      <LeftIcon className='h-5 w-5 text-gray-400' />
      <code className='flex-1 truncate font-mono text-sm text-gray-300'>
        {value}
      </code>
      <button
        className='rounded p-1 transition-colors hover:bg-white/10'
        onClick={() => {
          void copyToClipboard(value);
        }}
        type='button'
      >
        <BiCopy className='text-accent-1 h-5 w-5' />
      </button>
    </div>
  );
};

import { createElement, forwardRef, memo } from 'react';
import type { ComponentPropsWithoutRef } from 'react';
import { cva } from 'class-variance-authority';
import type { VariantProps } from 'class-variance-authority';
import { cn } from '../../lib';

export const textVariants = cva('font-normal text-foreground font-everett', {
  variants: {
    level: {
      displayLarge: 'text-[64px] leading-78 tracking-tighter font-medium',
      displayMedium: 'text-[48px] leading-60 tracking-tighter font-normal',
      displaySmall: 'text-[40px] leading-48 tracking-tighter font-medium',
      headerLarge: 'text-[36px] leading-44 tracking-tighter font-medium',
      headerMedium: 'text-[24px] leading-29 tracking-tight font-medium',
      headerSmall: 'text-[20px] leading-24 tracking-tight font-medium',
      headerExtraSmall: 'text-[16px] leading-20 tracking-tight font-medium',
      labelLarge: 'text-[15px] leading-18 tracking-tight font-normal',
      labelMedium: 'text-[14px] leading-16 tracking-tight font-normal',
      labelSmall: 'text-[13px] leading-16 tracking-tight font-medium',
      labelExtraSmall: 'text-[12px] leading-16 tracking-tight font-normal',
      labelHeaderExtraSmall:
        'text-[12px] leading-16 tracking-[0.12em] font-normal',
      bodyLarge: 'text-[20px] leading-26 tracking-tight font-normal',
      bodyMedium: 'text-[16px] leading-24 tracking-tight font-normal',
      bodySmall: 'text-[11px] leading-13 tracking-tight font-normal',
      buttonLarge: 'text-[20px] leading-24 tracking-tight font-medium',
      buttonMedium: 'text-[16px] leading-18 tracking-tight font-medium',
      buttonSmall: 'text-[14px] leading-16 tracking-tight font-medium',
      xxlTitle: 'text-6xl tracking-[-3px]',
      xlTitle: 'text-3xl md:text-6xl tracking-[-0.07em]',
      lgTitle: 'text-3xl lg:text-4xl leading-9 tracking-[-0.06em]',
      baseTitle: 'text-3xl leading-9',
      mdTitle: 'text-xl md:text-2xl leading-8 tracking-[-0.06em]',
      smTitle: 'text-xl leading-8',
      xsTitle: 'text-lg leading-8',
      metadata: 'font-mono text-[16px] leading-21 tracking-[-0.03em]',
      sub: 'text-base leading-7 md:text-lg md:leading-8 text-muted-foreground tracking-wide',
      xlTall: 'text-lg leading-7 md:text-xl md:leading-8',
      xl: 'text-lg md:text-xl md:leading-7',
      lgTall: 'text-base leading-7 md:text-lg md:leading-8',
      lg: 'text-base md:text-lg md:leading-7',
      base: 'text-sm leading-5 md:text-base md:leading-6',
      baseTall: 'text-sm md:text-base md:leading-7',
      mdTall: 'text-sm leading-4 md:text-base md:leading-5',
      md: 'text-sm md:leading-4',
      smTall: 'text-smt',
      sm: 'text-sm',
      caption: 'text-caption',
      xs: 'text-xs',
    },
  },
  defaultVariants: {
    level: 'md',
  },
});

interface TextProps
  extends ComponentPropsWithoutRef<'p'>,
    VariantProps<typeof textVariants> {
  as?: keyof JSX.IntrinsicElements;
}

export const Text = memo(
  forwardRef<HTMLHeadingElement, TextProps>(
    ({ level = 'base', as = 'p', className, ...props }, _ref) => {
      return createElement(as, {
        ref: _ref,
        className: cn(textVariants({ level }), className),
        ...props,
      });
    }
  )
);

Text.displayName = 'Text';

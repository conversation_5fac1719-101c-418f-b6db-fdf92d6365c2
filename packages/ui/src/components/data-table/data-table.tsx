/* eslint-disable @typescript-eslint/no-explicit-any -- Allow for now */
/* eslint-disable @typescript-eslint/no-unsafe-member-access -- Allow for now */
'use client';

import type { Table as TanstackTable, Row } from '@tanstack/react-table';
import { flexRender } from '@tanstack/react-table';
import type { ClassValue } from 'clsx';
import { CgChevronUp } from 'react-icons/cg';
import { cn } from '../../lib';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../table';

interface DataTableProps<TData> {
  table: TanstackTable<TData>;
  hasBorder?: boolean;
  hasHeader?: boolean;
  tableClassName?: string;
  rowClassName?: (row: Row<TData>) => string;
  hasRoundedRows?: boolean;
  small?: boolean;
  tableCellClassName?: string;
  onRowClick?: (event: React.MouseEvent, values: Row<TData>) => void;
  rootClassName?: ClassValue;
}

export function DataTable<TData>({
  table,
  hasBorder = true,
  hasHeader = true,
  rowClassName,
  tableClassName,
  hasRoundedRows = false,
  small,
  tableCellClassName,
  onRowClick,
  rootClassName,
}: DataTableProps<TData>) {
  return (
    <div className={cn('w-full rounded-md', hasBorder && 'border')}>
      <Table
        className={cn(
          tableClassName,
          'border-separate border-spacing-y-1 space-y-1'
        )}
        rootClassName={rootClassName}
      >
        {hasHeader ? (
          <TableHeader hasBorder={hasBorder}>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} hasBorder={hasBorder} isHeader>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      className={cn(
                        header.column.getCanSort() && 'cursor-pointer',
                        (header.column.columnDef.meta as any)?.separator ===
                          'dashed' &&
                          'border-right-dashed border-r border-r-white/30',
                        small && '!h-8'
                      )}
                      onClick={() => {
                        if (header.column.getCanSort()) {
                          header.column.toggleSorting(
                            header.column.getIsSorted() !== 'desc'
                          );
                        }
                      }}
                      colSpan={header.colSpan}
                    >
                      <span
                        className={cn(
                          'group flex select-none items-center gap-1 truncate',
                          (header.column.columnDef.meta as any)?.align ===
                            'end' && 'justify-end',
                          (header.column.columnDef.meta as any)?.align ===
                            'center' && 'justify-center',
                          header.column.getIsSorted() && 'text-accent-1'
                        )}
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}

                        {header.column.getCanSort() ? (
                          <CgChevronUp
                            className={cn(
                              'transition-transform',
                              header.column.getIsSorted() === 'asc'
                                ? ''
                                : header.column.getIsSorted() === 'desc'
                                  ? 'rotate-180'
                                  : 'rotate-45 opacity-50 saturate-0 group-hover:opacity-80'
                            )}
                          />
                        ) : null}
                      </span>
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
        ) : null}
        <TableBody className=''>
          {table.getRowModel().rows.length ? (
            table.getRowModel().rows.map((row) => (
              <TableRow
                key={row.id}
                hasBorder={hasBorder}
                data-state={row.getIsSelected() && 'selected'}
                className={rowClassName?.(row)}
                onClick={(event) => onRowClick?.(event, row)}
              >
                {row.getVisibleCells().map((cell) => (
                  <TableCell
                    key={cell.id}
                    className={cn(
                      'p-1 sm:p-2',
                      hasRoundedRows && 'first:rounded-l-lg last:rounded-r-lg',
                      (cell.column.columnDef.meta as any)?.separator ===
                        'dashed' &&
                        'border-right-dashed border-r border-r-white/30',
                      tableCellClassName,
                      small && 'cursor-pointer'
                    )}
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell
                colSpan={table.getVisibleLeafColumns().length}
                className='h-24 text-center'
              >
                No results.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}

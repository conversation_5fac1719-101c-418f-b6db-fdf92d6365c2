import type { ReactNode } from 'react';
import {
  Dialog as DialogHeadless,
  <PERSON>alogBackdrop,
  DialogPanel,
} from '@headlessui/react';
import type { IconType } from 'react-icons';
import { BiX } from 'react-icons/bi';
import { cn } from '../../lib';
import { Text } from '../text';

export const Dialog = ({
  isOpen,
  closeFn,
  title,
  HeaderIcon,
  headerIconClassName,
  HeaderNode,
  children,
  className,
}: {
  isOpen: boolean;
  closeFn: () => void;
  title: string;
  HeaderIcon?: IconType;
  headerIconClassName?: string;
  HeaderNode?: ReactNode;
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <DialogHeadless
      className='relative z-[100]'
      onClose={() => {
        closeFn();
      }}
      open={isOpen}
    >
      <DialogBackdrop
        className='fixed inset-0 z-[101] bg-[#1D1D1DCC] backdrop-blur-xl transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in'
        transition
      />

      <div className='fixed inset-0 z-[102] w-screen overflow-y-auto'>
        <div className='flex min-h-full items-end justify-center p-4 text-center sm:items-center'>
          <DialogPanel
            className={cn(
              'bg-hero-primary lg:w-lg relative w-full transform overflow-hidden rounded-3xl border border-[#323232] px-4 pb-4 pt-5 text-left shadow-[0px_5px_40px_0px_#00000066] transition-all data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in sm:my-8 sm:w-full sm:p-8 data-[closed]:sm:translate-y-0 data-[closed]:sm:scale-95 lg:max-w-2xl',
              className
            )}
            transition
          >
            <button
              className='absolute right-4 top-4 rounded-md bg-transparent text-[#909090] hover:text-gray-400 focus:outline-none focus:ring-offset-2'
              onClick={() => {
                closeFn();
              }}
              type='button'
            >
              <span className='absolute -inset-2.5' />
              <span className='sr-only'>Close dialog</span>
              <BiX aria-hidden='true' className='size-6' />
            </button>

            <div className='flex flex-col gap-6'>
              {HeaderIcon ? (
                <div
                  className={cn(
                    'bg-cta1 mx-auto flex size-12 items-center justify-center rounded-full',
                    headerIconClassName
                  )}
                >
                  <HeaderIcon
                    aria-hidden='true'
                    className='size-6 text-black'
                  />
                </div>
              ) : null}

              {HeaderNode ? HeaderNode : null}

              <Text as='h3' className='text-center' level='headerMedium'>
                {title}
              </Text>

              {children}
            </div>
          </DialogPanel>
        </div>
      </div>
    </DialogHeadless>
  );
};

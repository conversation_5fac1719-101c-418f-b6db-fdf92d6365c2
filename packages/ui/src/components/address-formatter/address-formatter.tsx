import { useContext, useMemo } from 'react';
import type { ClassValue } from 'clsx';
import Image from 'next/image';
import { BiCheckShield } from 'react-icons/bi';
import {
  cn,
  encodeSS58Address,
  getIconUrl,
  appRoutes,
  createUrl,
  walletFormat,
} from '../../lib';
import { ExchangeAddressMapping } from '../../lib/exchange-address-mapping';
import { CopyButton } from '../copy-button';
import { Link } from '../link';
import { NavigationContext } from '../navigation/navigation-context';
import { Text } from '../text';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../tooltip';

export const AddressFormatter = ({
  info,
  uid,
  noCopy,
  noIcon,
  noChange,
  noColor = true,
  fullWidth,
  max = 9,
  noLink = false,
  isEncoded,
  className,
  rootClassName,
  textClassName,
  noVerifiedIcon,
  isHotkey,
  isValidator,
  isChildHotkey,
  subnetId,
  underline,
  dtao,
  account,
  fromNavMenu = false,
}: {
  info?: { getValue: () => unknown };
  uid?: string;
  noCopy?: boolean;
  noIcon?: boolean;
  noChange?: boolean;
  noColor?: boolean;
  fullWidth?: boolean;
  max?: number;
  noLink?: boolean;
  isEncoded?: boolean;
  className?: ClassValue;
  rootClassName?: ClassValue;
  textClassName?: ClassValue;
  noVerifiedIcon?: boolean;
  isHotkey?: boolean;
  isValidator?: boolean;
  isChildHotkey?: boolean;
  subnetId?: string;
  underline?: string;
  dtao?: boolean;
  account?: boolean;
  fromNavMenu?: boolean;
}) => {
  const navigationContext = useContext(NavigationContext);

  const address = isEncoded
    ? encodeSS58Address((info?.getValue() as string | undefined) ?? uid ?? '')
    : (info?.getValue() as string | undefined) ?? uid ?? '';

  const currentValidator = useMemo(
    () =>
      navigationContext?.validatorIdentityData?.find(
        (d) => d.validator_hotkey?.ss58 === address
      ) ?? null,
    [navigationContext, address]
  );

  const isExchange = Object.keys(ExchangeAddressMapping).includes(address);

  if (address === '') return;

  const getDisplayName = () => {
    if (!noChange) {
      if (isExchange) {
        return ExchangeAddressMapping[address].name;
      }
      if (currentValidator) {
        return currentValidator.name;
      }
    }
    return address ? (fullWidth ? address : walletFormat(address)) : '';
  };

  const getDetailLink = () => {
    if (account) {
      return createUrl(
        navigationContext?.websiteUrl ?? '',
        appRoutes.blockchain.accountDetail(address)
      );
    }
    if (dtao) {
      return createUrl(
        navigationContext?.websiteUrl ?? '',
        appRoutes.dtao.accountDetail(address)
      );
    }
    if (isChildHotkey && address) {
      return createUrl(
        navigationContext?.websiteUrl ?? '',
        appRoutes.blockchain.hotkeyDetail(
          subnetId ? `${address}#subnet${subnetId}` : address
        )
      );
    }
    if (currentValidator || isValidator) {
      return createUrl(
        navigationContext?.websiteUrl ?? '',
        appRoutes.validator.detail(address)
      );
    }
    if (isHotkey && address) {
      return createUrl(
        navigationContext?.websiteUrl ?? '',
        appRoutes.blockchain.hotkeyDetail(address)
      );
    }
    if (address) {
      return createUrl(
        navigationContext?.websiteUrl ?? '',
        appRoutes.blockchain.accountDetail(address)
      );
    }
    return '';
  };

  return (
    <div
      className={cn(
        'group flex items-center justify-between gap-2',
        !noChange && currentValidator
          ? 'max-w-max'
          : fullWidth
            ? 'max-w-120 min-w-40 flex-shrink-0'
            : 'flex-shrink-0',
        rootClassName
      )}
    >
      {noLink ? (
        <Text
          level='xs'
          className={cn(
            'group-hover:text-ocean flex flex-1 items-center gap-2 truncate break-all',
            noColor ? '' : currentValidator ? 'text-ocean' : 'text-fire',
            className
          )}
        >
          {!noIcon && (
            <Image
              width={20}
              height={20}
              src={getIconUrl(address)}
              className={cn(
                'h-5 w-5 flex-shrink-0 origin-top-left rounded-full'
              )}
              alt='User PP'
            />
          )}
          <span className={cn('flex-shrink-0 truncate', textClassName)}>
            {getDisplayName()}
          </span>
        </Text>
      ) : (
        <Link
          href={getDetailLink()}
          className={cn('flex-shrink-0 hover:underline', underline)}
        >
          <Text
            level='xs'
            className={cn(
              'decoration-ocean group-hover:text-ocean flex items-center gap-2 truncate break-all',
              noColor
                ? ''
                : currentValidator
                  ? 'text-ocean decoration-ocean hover:underline'
                  : 'text-fire decoration-fire hover:underline',
              className
            )}
          >
            {!noIcon && (
              <Image
                width={20}
                height={20}
                src={getIconUrl(address)}
                className={cn(
                  'h-5 w-5 flex-shrink-0 origin-top-left rounded-full'
                )}
                alt='User PP'
              />
            )}
            {!noIcon && <span className='w-1' />}
            <span className={cn('flex-shrink-0 truncate', textClassName)}>
              {getDisplayName()}
            </span>
          </Text>
        </Link>
      )}
      {!noVerifiedIcon && !noColor && (
        <>
          {currentValidator ? (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <BiCheckShield size={16} color='#00DBBC' />
                </TooltipTrigger>
                <TooltipContent className='border-neutral-800'>
                  Verified Validator
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ) : null}
        </>
      )}
      {address && !noCopy ? (
        <CopyButton
          size={14}
          value={address}
          className='text-white opacity-60'
        />
      ) : null}
    </div>
  );
};

import { useContext } from 'react';
import { <PERSON>u, <PERSON>u<PERSON>utton, MenuItem, MenuItems } from '@headlessui/react';
import { BiChevronDown } from 'react-icons/bi';
import { createUrl } from '../../../lib';
import { truncateStringWithMax } from '../../../lib/utils';
import { NavigationContext } from '../navigation-context';

type UserNavigationItem =
  | { name: string; href: string; onClick?: never }
  | { name: string; href?: never; onClick: () => void };

export default function ProfileDropdown() {

  const navigationContext = useContext(NavigationContext);

  const userNavigation: UserNavigationItem[] = [
    {
      name: 'Your profile',
      href: '/profile',
    },
    {
      name: 'Sign out',
      onClick: () => void navigationContext?.signOut()
    },
  ];

  return (
    <Menu as='div' className='relative'>
      <MenuButton className='flex items-center'>
        <span className='sr-only'>Open user menu</span>

        <span className='flex items-center'>
          <span
            aria-hidden='true'
            className='text-sm/6 text-white'
          >
            {truncateStringWithMax(navigationContext?.userProfile?.name ||
              navigationContext?.userProfile?.email ||
              (navigationContext?.userProfile?.type === 'fingerprint'
                ? 'Anonymous'
                : 'No Name Found'), 13)}
          </span>
          <BiChevronDown
            aria-hidden='true'
            className='ml-2 size-5 text-white'
          />
        </span>
      </MenuButton>
      <MenuItems
        className='bg-gray-highlight absolute right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md py-2 shadow-lg ring-1 ring-gray-900/5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:transform data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in'
        transition
      >
        {userNavigation.map((item) => (
          <MenuItem key={item.name}>
            <a
              className='data-[focus]:bg-app-bg/75 block px-3 py-1 text-sm/6 text-white data-[focus]:outline-none'
              href={item.href ? createUrl(navigationContext?.dashboardUrl ?? '', item.href) : '/#'}
              onClick={() => {
                if (item.onClick) {
                  item.onClick();
                }
              }}
            >
              {item.name}
            </a>
          </MenuItem>
        ))}
      </MenuItems>
    </Menu>
  )
}
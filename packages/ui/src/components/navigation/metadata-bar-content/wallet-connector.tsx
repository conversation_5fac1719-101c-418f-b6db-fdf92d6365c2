import { useContext } from "react";
import { BiWallet } from "react-icons/bi";
import { Button } from "../../button";
import { Text } from "../../text";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "../../tooltip";
import { NavigationContext } from "../navigation-context";

export const WalletConnector = () => {
  const navigationContext = useContext(NavigationContext);

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            className='flex flex-row items-center gap-1 h-6'
            onClick={() => {
              if (navigationContext?.walletConnected === true) {
                navigationContext.disconnectWallet();
              } else {
                navigationContext?.connectWallet() ?? (() => null);
              }
            }}
          >
            <div
              className={`mr-2 h-2 w-2 rounded-full ${navigationContext?.walletConnected === true ? 'bg-accent-1' : 'bg-accent-2'}`}
            />
            <BiWallet />
          </Button>
        </TooltipTrigger>
        <TooltipContent side='bottom'>
          {navigationContext?.walletConnected === true ? (
            <Text>Your wallet is connected! Click to disconnect.</Text>
          ) : (
            <Text>Click to connect your wallet.</Text>
          )}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
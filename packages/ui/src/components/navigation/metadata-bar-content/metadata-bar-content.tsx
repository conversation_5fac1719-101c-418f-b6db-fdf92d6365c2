import { useContext } from 'react';
import NumberFlow from '@number-flow/react';
import { BiCubeAlt } from 'react-icons/bi';
import { Text } from '../../text';
import { NavigationContext } from '../navigation-context';
import { LatestPriceContent } from './latest-price-content';
import ProfileDropdown from './profile-dropdown';
import { WalletConnector } from './wallet-connector';

export const MetadataBarContent = () => {
  const navigationContext = useContext(NavigationContext);

  return (
    <div className='flex w-full flex-1 items-center justify-between gap-2 sm:justify-start sm:gap-5 md:w-max md:gap-8'>
      <LatestPriceContent data={navigationContext?.priceData} />
      {navigationContext?.priceData ? (
        <>
          <div className='flex flex-col items-center gap-1 sm:flex-row sm:gap-3'>
            <p className='text-grayish text-sm'>Market Cap</p>

            <p className='flex flex-row items-center text-sm text-white'>
              $
              <NumberFlow
                className='flex flex-row items-center'
                format={{
                  notation: 'compact',
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }}
                value={Number(navigationContext.priceData.market_cap)}
              />
            </p>
          </div>
          <div className='hidden items-center gap-3 lg:flex'>
            <p className='text-grayish text-sm'>24hr Volume</p>

            <p className='flex flex-row items-center text-sm text-white'>
              $
              <NumberFlow
                className='flex flex-row items-center'
                format={{
                  notation: 'compact',
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }}
                value={Number(navigationContext.priceData.volume_24h)}
              />
            </p>
          </div>
        </>
      ) : null}

      {navigationContext?.showBlockData ? (
        <Text
          className='hidden items-center gap-2 text-white min-[470px]:flex'
          level='sm'
        >
          <BiCubeAlt className='duration-10000 text-grayish' size={20} />
          <NumberFlow
            format={{ useGrouping: false }}
            value={navigationContext.latestBlock}
          />
        </Text>
      ) : null}

      <div className='flex flex-row items-center justify-end gap-2 sm:flex-1 sm:gap-5 md:gap-8'>
        <div className='max-sm:hidden'>
          <WalletConnector />
        </div>

        <ProfileDropdown />
      </div>
    </div>
  );
};

import { useMemo } from 'react';
import NumberFlow from '@number-flow/react';
import Image from 'next/image';
import { CgChevronUp } from 'react-icons/cg';
import type { Price } from '@repo/types/website-api-types';
import landingStatusImg from '../../../assets/images/logo/landing-status.png';
import { cn } from '../../../lib';
import { Text } from '../../text';

export const LatestPriceContent = ({ data }: { data?: Price }) => {
  const isPositive = useMemo(
    () => Number(data?.percent_change_24h) > 0,
    [data?.percent_change_24h]
  );

  return (
    <div className='flex flex-col sm:flex-row items-center gap-1.5 sm:gap-3'>
      <div className='flex items-center gap-0.5 sm:gap-3'>
        <Image
          alt='Status Logo'
          src={landingStatusImg}
          width={24}
          height={24}
        />
        <Text className='flex flex-row items-center font-medium' level='sm'>
          $
          <NumberFlow
            format={{
              maximumFractionDigits: 2,
              minimumFractionDigits: 2,
            }}
            value={Number(data?.price)}
          />
        </Text>
      </div>

      <div className='flex items-center gap-0.5 sm:gap-1.5'>
        <CgChevronUp
          className={cn(
            isPositive ? 'bg-positive-trend' : 'bg-negative-trend rotate-180',
            'text-black'
          )}
          size={12}
        />
        <Text
          className={cn(
            isPositive ? 'text-positive-trend' : 'text-negative-trend',
            'flex flex-row items-center'
          )}
          level='xs'
        >
          <NumberFlow
            value={Number(data?.percent_change_24h)}
            format={{
              maximumFractionDigits: 2,
              minimumFractionDigits: 2,
            }}
          />
          <span>%</span>
        </Text>
      </div>
    </div>
  );
};

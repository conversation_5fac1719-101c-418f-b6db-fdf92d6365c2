'use client';

import { use<PERSON><PERSON>back, useContext, useMemo, type Dispatch } from 'react';
import type { ColumnDef, Row } from '@tanstack/react-table';
import {
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { useRouter } from 'next/navigation';
import { TbStarOff } from 'react-icons/tb';
import type { SevenPriceItem, SubnetPool } from '@repo/types/website-api-types';
import { DataTable } from '@repo/ui/components';
import { appRoutes, createUrl, getFilter, taoDivider } from '@repo/ui/lib';
import { NavigationContext } from '../navigation-context';
import { EmissionCell, EmissionHeader } from './table-cell/emission-cell';
import { IdCell } from './table-cell/id-cell';
import { LastCell } from './table-cell/last-seven-cell';
import { LiquidityCell, LiquidityHeader } from './table-cell/liquidity-cell';
import { Market<PERSON>apCell, MarketCapHeader } from './table-cell/marketcap';
import { MonthCell, OneMonthPriceHeader } from './table-cell/month-price-cell';
import { DayCell, OneDayPriceHeader } from './table-cell/one-day-price-cell';
import {
  OneHourPriceCell,
  OneHourPriceHeader,
} from './table-cell/one-hour-price-cell';
import { PriceCell, PriceHeader } from './table-cell/price-cell';
import { RootPropCell, RootPropHeader } from './table-cell/root-prop';
import { SubnetCell, SubnetHeader } from './table-cell/subnet-cell';
import { VolumeCell, VolumeHeader } from './table-cell/volume-cell';
import { WatchCell } from './table-cell/watch-cell';
import { OneWeekPriceHeader, WeekCell } from './table-cell/week-price-cell';

const searchFields = ['netuid', 'subnet_name'];

export interface TableItem {
  name: string;
  netuid: number;
  symbol: string;
  emission: number;
  price: number;
  market_cap: number;
  total_tao: string;
  total_alpha: string;
  alpha_in_pool: string;
  alpha_staked: string;
  liquidity: number;
  seven_day_prices: SevenPriceItem[];
  price_change_1_hour: number;
  price_change_1_day: number;
  price_change_1_week: number;
  price_change_1_month: number;
  subnet_name: string;
  subnet_description: string;
  recycled_24_hours: string;
  recycled_lifetime: string;
  recycled_since_registration: string;
  tao_volume_24_hr: number;
  startup_mode: boolean;
  root_prop: number;
}

export default function SubnetsListTable({
  initialData,
  subnetsWatchList,
  watchList,
  setSubnetsWatchList,
  search,
}: {
  initialData: SubnetPool[];
  subnetsWatchList: number[];
  setSubnetsWatchList: Dispatch<React.SetStateAction<number[]>>;
  search: string;
  watchList: boolean;
}) {
  const router = useRouter();
  const navigationContext = useContext(NavigationContext);

  const tableData: TableItem[] = useMemo(() => {
    return initialData
      .map((item) => ({
        name: item.name,
        netuid: item.netuid,
        symbol: item.symbol,
        emission: Number(item.emission),
        price: Number(item.price),
        market_cap: Number(item.market_cap) / taoDivider,
        total_tao: item.total_tao,
        total_alpha: item.total_alpha,
        alpha_in_pool: item.alpha_in_pool,
        alpha_staked: item.alpha_staked,
        liquidity: Number(item.liquidity) / taoDivider,
        seven_day_prices: item.seven_day_prices,
        price_change_1_hour: Number(item.price_change_1_hour),
        price_change_1_day: Number(item.price_change_1_day),
        price_change_1_week: Number(item.price_change_1_week),
        price_change_1_month: Number(item.price_change_1_month),
        subnet_name: item.subnet_name,
        subnet_description: item.subnet_description,
        recycled_24_hours: item.recycled_24_hours,
        recycled_lifetime: item.recycled_lifetime,
        recycled_since_registration: item.recycled_since_registration,
        tao_volume_24_hr: Number(item.tao_volume_24_hr) / taoDivider,
        startup_mode: item.startup_mode,
        root_prop: Number(item.root_prop),
      }))
      .map((it, index) => ({ ...it, id: index + 1 }))
      .filter((it) => (watchList ? subnetsWatchList.includes(it.netuid) : it))
      .filter((idx) => getFilter(idx, search, searchFields));
  }, [initialData, search, subnetsWatchList, watchList]);

  const columns: ColumnDef<TableItem>[] = [
    {
      accessorKey: 'watch',
      header: '',
      // eslint-disable-next-line react/no-unstable-nested-components -- This component is only used in this file and doesn't need to be extracted
      cell: (props) => (
        <WatchCell
          {...props}
          subnetsWatchList={subnetsWatchList}
          setSubnetsWatchList={setSubnetsWatchList}
        />
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'id',
      header: '#',
      cell: IdCell,
    },
    {
      accessorKey: 'netuid',
      header: SubnetHeader,
      cell: SubnetCell,
    },
    {
      accessorKey: 'emission',
      header: EmissionHeader,
      cell: EmissionCell,
    },
    {
      accessorKey: 'price',
      header: PriceHeader,
      cell: PriceCell,
    },
    {
      accessorKey: 'price_change_1_hour',
      header: OneHourPriceHeader,
      cell: OneHourPriceCell,
    },
    {
      accessorKey: 'price_change_1_day',
      header: OneDayPriceHeader,
      cell: DayCell,
    },
    {
      accessorKey: 'price_change_1_week',
      header: OneWeekPriceHeader,
      cell: WeekCell,
    },
    {
      accessorKey: 'price_change_1_month',
      header: OneMonthPriceHeader,
      cell: MonthCell,
    },
    {
      accessorKey: 'market_cap',
      header: MarketCapHeader,
      cell: MarketCapCell,
    },
    {
      accessorKey: 'tao_volume_one_day',
      header: VolumeHeader,
      cell: VolumeCell,
    },
    {
      accessorKey: 'liquidity',
      header: LiquidityHeader,
      cell: LiquidityCell,
    },
    {
      accessorKey: 'root_prop',
      header: RootPropHeader,
      cell: RootPropCell,
    },
    {
      accessorKey: 'last_7_days',
      header: 'Last 7 days',
      cell: LastCell,
      enableSorting: false,
    },
  ];

  const table = useReactTable({
    data: tableData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    manualPagination: true,
    debugTable: true,
    state: {
      sorting: [
        {
          id: 'market_cap',
          desc: true,
        },
      ],
    },
  });

  const handleRowClick = useCallback(
    (event: React.MouseEvent, { original }: Row<TableItem>) => {
      const url = createUrl(
        navigationContext?.websiteUrl ?? '',
        appRoutes.subnets.detail(original.netuid.toString())
      );
      if (event.metaKey || event.ctrlKey) {
        window.open(url, '_blank');
      } else {
        router.push(url);
      }
    },
    [navigationContext?.websiteUrl, router]
  );

  return (
    <div className='flex max-h-[600px] min-h-60 w-full overflow-hidden px-10 pb-10'>
      {tableData.length > 0 ? (
        <DataTable
          table={table}
          hasBorder={false}
          small
          tableCellClassName='px-2 py-0 sm:py-0'
          onRowClick={(event: React.MouseEvent, rowInfo: Row<TableItem>) => {
            handleRowClick(event, rowInfo);
          }}
          rootClassName='h-full'
        />
      ) : (
        <div className='flex w-full flex-col items-center justify-center gap-3'>
          <div className='flex h-[72px] w-[72px] items-center justify-center rounded-full bg-[#323232]'>
            <TbStarOff size={24} className='opacity-60' />
          </div>
          <div className='text-xs font-medium text-[#909090]'>
            There are no favorite subnets
          </div>
        </div>
      )}
    </div>
  );
}

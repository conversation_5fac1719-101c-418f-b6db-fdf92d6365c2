import type { IconType } from 'react-icons';
import {
  BiCoinStack,
  BiKey,
  BiLinkAlt,
  BiLinkExternal,
  BiMoney,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  BiU<PERSON>,
} from 'react-icons/bi';
import { CgFileDocument } from 'react-icons/cg';

export type DashboardListItem = {
  label: string;
  href: string;
  target?: string;
  Icon: IconType;
  RightIcon?: IconType;
};

export type DashboardList = {
  dashboardItems: DashboardListItem[];
  developerItems: DashboardListItem[];
};

export const dashboardLists: DashboardListItem[] = [
  {
    label: 'Portfolio',
    href: '/portfolio',
    Icon: BiCoinStack,
  },
  {
    label: 'Stake',
    href: '/stake',
    Icon: BiLinkAlt,
  },
  {
    label: 'Transfer',
    href: '/transfer',
    Icon: BiTransfer,
  },
];

export const developerMenuSection: DashboardListItem[] = [
  {
    label: 'API Keys',
    href: '/api-keys',
    Icon: BiKey,
  },
  {
    label: 'Docs',
    href: 'https://docs.taostats.io/docs/index',
    target: '_blank',
    Icon: CgFileDocument,
    RightIcon: BiLinkExternal,
  },
  {
    label: 'Billing',
    href: '/billing',
    Icon: BiMoney,
  },
];

export const adminMenuItems: DashboardListItem[] = [
  {
    label: 'Accounts',
    href: '/admin/accounts',
    Icon: BiUser,
  },
];

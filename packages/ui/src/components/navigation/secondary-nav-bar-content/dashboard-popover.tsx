import { useContext, useState } from "react";
import { BiChevronDown } from "react-icons/bi";
import { CgMenuGridO } from "react-icons/cg";
import { Link } from "../../link";
import { Popover, PopoverContent, PopoverTrigger } from "../../popover";
import { Text } from "../../text";
import { NavigationContext } from "../navigation-context";
import type { DashboardList } from "./dashboard-lists";

export default function DashboardPopover({
  completeDashNavigationLinks
}: {
  completeDashNavigationLinks: DashboardList
}) {
  const navigationContext = useContext(NavigationContext);

  const [openPopover, setOpenPopover] = useState<boolean>(false);

  return (
    <div 
      className="sm:-mt-1 hidden h-10 rounded-lg border border-[#B3B3B3] bg-white py-2 pl-3 md:flex md:flex-row md:items-center md:gap-2"
      onMouseEnter={() => { if(!openPopover) setOpenPopover(true); }}
      onMouseLeave={() => { if(openPopover) setOpenPopover(false); }}
    >
      <Link
        href={navigationContext?.dashboardUrl ?? "https://dash.taostats.io"}
        rel="noopener noreferrer"
        // target="_blank"
        className="flex flex-row items-center gap-2.5"
      >
        <Text
          level="sm"
          className="flex flex-row items-center gap-1 truncate font-medium text-black leading-[18px]"
        >
          <CgMenuGridO size={24} />
          Dashboard
        </Text>
        <div className="w-max rounded-[4px] bg-[#00DBBC] p-1 font-medium text-[11px] text-black leading-[12px]">
          New
        </div>
      </Link>
      <Popover open={openPopover} onOpenChange={setOpenPopover}>
        <PopoverTrigger>
          <BiChevronDown size={24} color="black" className="mr-3" />
        </PopoverTrigger>
        <PopoverContent
          align="end"
          className="mt-3 flex w-auto min-w-[215px] flex-col rounded-lg border border-[#FFFFFF1F] bg-[#141414] px-4 py-3"
        >
          {completeDashNavigationLinks.dashboardItems.map((item) => (
            <Link
              key={item.label}
              href={item.href}
              className="flex flex-row items-center gap-1 p-2 hover:bg-[#111111]"
            >
              <item.Icon size={24} className="opacity-60" />
              <Text level="sm" className="font-medium">
                {item.label}
              </Text>
            </Link>
          ))}
          <Text level="xs" className="uppercase py-2 opacity-60 text-gray-500">Developers</Text>
          {completeDashNavigationLinks.developerItems.map((item) => (
            <Link
              key={item.label}
              href={item.href}
              target={item.target}
              className="flex flex-row items-center gap-1 p-2 hover:bg-[#111111]"
            >
              <item.Icon size={24} className="opacity-60" />
              <Text level="sm" className="font-medium text-gray-400 flex-1">
                {item.label}
              </Text>
              { item.RightIcon ? <item.RightIcon size={24} className="opacity-60" /> : null}
            </Link>
          ))}
        </PopoverContent>
      </Popover>
    </div>
  );
}

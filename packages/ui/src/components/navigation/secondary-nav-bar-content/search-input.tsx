'use client';

import { type Dispatch, useCallback, useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { BiSearch } from 'react-icons/bi';
import type { ClassNameValue } from 'tailwind-merge';
import { cn } from '@repo/ui/lib';
import { Input2 } from './input2';

export const SearchInput = ({
  closeIcon,
  searchPlaceholder,
  setGlobalFilter,
  iconClassName,
  inputClassName,
  inputStyleClassName,
  rightContent,
}: {
  closeIcon?: boolean;
  searchPlaceholder?: string;
  setGlobalFilter: Dispatch<React.SetStateAction<string>>;
  iconClassName?: ClassNameValue;
  inputClassName?: ClassNameValue;
  inputStyleClassName?: ClassNameValue;
  rightContent?: React.ReactNode;
}) => {
  const searchParams = useSearchParams();

  const [isInit, setIsInit] = useState<boolean>(true);
  const [searchString, setSearchString] = useState<string>('');

  const handleKeyUp = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.keyCode === 13) {
        setGlobalFilter(searchString);
      }
    },
    [searchString, setGlobalFilter]
  );

  useEffect(() => {
    const interval = setInterval(() => {
      setGlobalFilter(searchString);
    }, 1000);

    return () => {
      clearInterval(interval);
    };
  }, [searchString, setGlobalFilter]);

  useEffect(() => {
    if (isInit) {
      setIsInit(false);

      const filter = searchParams.get('filter');

      if (filter !== null) {
        setSearchString(filter);
        setGlobalFilter(filter);
      }
    }
  }, [searchParams, isInit, setGlobalFilter]);

  return (
    <Input2
      size='small'
      value={searchString}
      setValue={setSearchString}
      icon={BiSearch}
      placeholder={searchPlaceholder}
      onKeyUp={handleKeyUp}
      clearIcon={closeIcon}
      className={cn('ml-1 h-9', inputStyleClassName)}
      inputClassName={inputClassName}
      iconClassName={iconClassName}
      rightContent={rightContent}
    />
  );
};

'use client';

import { use<PERSON><PERSON>back, useContext, useMemo, type Dispatch } from 'react';
import type { ColumnDef, Row } from '@tanstack/react-table';
import {
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { useRouter } from 'next/navigation';
import { TbStarOff } from 'react-icons/tb';
import type { DtaoValidatorLatest } from '@repo/types/website-api-types';
import { DataTable } from '@repo/ui/components';
import { appRoutes, createUrl, getFilter, taoDivider } from '@repo/ui/lib';
import { NavigationContext } from '../navigation-context';
import { ActiveHeader, ActiveCell } from './table-cell/active-cell';
import { AddressHeader, AddressCell } from './table-cell/address-cell';
import {
  AlphaStakeHeader,
  AlphaStakeCell,
} from './table-cell/alpha-stake-cell';
import { Chart<PERSON>ell } from './table-cell/chart-cell';
import { DominanceHeader, DominanceCell } from './table-cell/dominance-cell';
import { NomsHeader, NomsCell } from './table-cell/noms-cell';
import {
  NomsChangeHeader,
  NomsChangeCell,
} from './table-cell/noms-change-cell';
import { RankHeader, RankCell } from './table-cell/rank-cell';
import { RootStakeHeader, RootStakeCell } from './table-cell/root-stake-cell';
import {
  RootWeightHeader,
  RootWeightCell,
} from './table-cell/root-weight-cell';
import { TakeCell, TakeHeader } from './table-cell/take-cell';
import {
  TotalWeightHeader,
  TotalWeightCell,
} from './table-cell/total-weight-cell';
import { ValidatorWatchCell } from './table-cell/validator-watch-cell';
import {
  WeightChangeHeader,
  WeightChangeCell,
} from './table-cell/weight-change';

const searchFields = ['id', 'name'];

export interface TableValidatorItem {
  id: string;
  name: string;
  rank: number;
  dominance: number;
  root_rank: number;
  alpha_rank: number;
  global_nominators: number;
  global_nominators_24_hr_change: number;
  global_weighted_stake: number;
  weighted_root_stake: number;
  global_alpha_stake_as_tao: number;
  take: string;
  active_subnets: number;
  root_stake: number;
  global_weighted_stake_24_hr_change: number;
}

export default function ValidatorsListTable({
  initialData,
  validatorsWatchList,
  watchValidator,
  setValidatorsWatchList,
  search,
}: {
  initialData: DtaoValidatorLatest[];
  validatorsWatchList: string[];
  setValidatorsWatchList: Dispatch<React.SetStateAction<string[]>>;
  search: string;
  watchValidator: boolean;
}) {
  const router = useRouter();
  const navigationContext = useContext(NavigationContext);

  const tableData: TableValidatorItem[] = useMemo(() => {
    return initialData
      .map((item) => ({
        id: item.hotkey.ss58,
        name:
          navigationContext?.validatorIdentityData?.find(
            (validator) =>
              (validator.validator_hotkey?.ss58 ?? '') === item.hotkey.ss58
          )?.name ?? '',
        rank: item.rank,
        dominance: Number(item.dominance),
        root_rank: item.root_rank,
        alpha_rank: item.alpha_rank,
        global_nominators: item.global_nominators,
        global_nominators_24_hr_change: item.global_nominators_24_hr_change,
        global_weighted_stake: Number(item.global_weighted_stake),
        weighted_root_stake: Number(item.weighted_root_stake),
        global_alpha_stake_as_tao: Number(item.global_alpha_stake_as_tao),
        take: item.take,
        active_subnets: item.active_subnets,
        root_stake: Number(item.root_stake),
        global_weighted_stake_24_hr_change:
          Number(item.global_weighted_stake_24_hr_change) / taoDivider,
      }))
      .filter(
        (it) => !watchValidator || validatorsWatchList.find((s) => s === it.id)
      )
      .filter((idx) => getFilter(idx, search, searchFields));
  }, [
    initialData,
    navigationContext?.validatorIdentityData,
    search,
    validatorsWatchList,
    watchValidator,
  ]);

  const columns: ColumnDef<TableValidatorItem>[] = [
    {
      accessorKey: 'watch',
      header: '',
      // eslint-disable-next-line react/no-unstable-nested-components
      cell: (props) => (
        <ValidatorWatchCell
          {...props}
          validatorsWatchList={validatorsWatchList}
          setValidatorsWatchList={setValidatorsWatchList}
        />
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'rank',
      header: RankHeader,
      cell: RankCell,
    },
    {
      accessorKey: 'id',
      header: AddressHeader,
      cell: AddressCell,
      enableSorting: false,
    },
    {
      accessorKey: 'dominance',
      header: DominanceHeader,
      cell: DominanceCell,
    },
    {
      accessorKey: 'global_nominators',
      header: NomsHeader,
      cell: NomsCell,
    },
    {
      accessorKey: 'global_nominators_24_hr_change',
      header: NomsChangeHeader,
      cell: NomsChangeCell,
    },
    {
      accessorKey: 'active_subnets',
      header: ActiveHeader,
      cell: ActiveCell,
    },
    {
      accessorKey: 'global_weighted_stake',
      header: TotalWeightHeader,
      cell: TotalWeightCell,
    },
    {
      accessorKey: 'global_weighted_stake_24_hr_change',
      header: WeightChangeHeader,
      cell: WeightChangeCell,
    },
    {
      accessorKey: 'root_stake',
      header: RootStakeHeader,
      cell: RootStakeCell,
    },
    {
      accessorKey: 'weighted_root_stake',
      header: RootWeightHeader,
      cell: RootWeightCell,
    },
    {
      accessorKey: 'global_alpha_stake_as_tao',
      header: AlphaStakeHeader,
      cell: AlphaStakeCell,
    },
    {
      accessorKey: 'chart',
      header: '',
      cell: ChartCell,
      enableSorting: false,
    },
    {
      accessorKey: 'take',
      header: TakeHeader,
      cell: TakeCell,
    },
  ];

  const table = useReactTable({
    data: tableData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    manualPagination: true,
    debugTable: true,
  });

  const handleRowClick = useCallback(
    (event: React.MouseEvent, { original }: Row<TableValidatorItem>) => {
      const url = createUrl(
        navigationContext?.websiteUrl ?? '',
        appRoutes.validator.detail(original.id.toString())
      );
      if (event.metaKey || event.ctrlKey) {
        window.open(url, '_blank');
      } else {
        router.push(url);
      }
    },
    [navigationContext?.websiteUrl, router]
  );

  return (
    <div className='flex max-h-[600px] min-h-60 w-full overflow-hidden px-10 pb-10'>
      {tableData.length > 0 ? (
        <DataTable
          table={table}
          hasBorder={false}
          small
          tableCellClassName='px-2 py-0 sm:py-0 h-10'
          onRowClick={(
            event: React.MouseEvent,
            rowInfo: Row<TableValidatorItem>
          ) => {
            handleRowClick(event, rowInfo);
          }}
          rootClassName='h-full'
        />
      ) : (
        <div className='flex w-full flex-col items-center justify-center gap-3'>
          <div className='flex h-[72px] w-[72px] items-center justify-center rounded-full bg-[#323232]'>
            <TbStarOff size={24} className='opacity-60' />
          </div>
          <div className='text-xs font-medium text-[#909090]'>
            There are no favorite validators
          </div>
        </div>
      )}
    </div>
  );
}

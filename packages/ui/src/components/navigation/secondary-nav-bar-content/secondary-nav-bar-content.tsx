import { useContext, useMemo } from 'react';
import emojiRegex from 'emoji-regex';
import type { DtaoValidatorLatest } from '@repo/types/website-api-types';
import {
  createUrl,
  taoDivider,
  VALIDATOR_MINIMUM_STAKE,
  appRoutes,
} from '../../../lib';
import { Link } from '../../link';
import { TaostatsLogo } from '../../taostats';
import { NavigationContext } from '../navigation-context';
import {
  adminMenuItems,
  dashboardLists,
  developerMenuSection,
} from './dashboard-lists';
import NavLinksGroup from './nav-links-group';
import NavLinksMobile from './nav-links-mobile';
import {
  websiteNavigationLinks,
  NavigationSectionTitles,
  type GroupedNavigationItem,
} from './navigation-links';

export function SecondaryNavbarContent() {
  const navigationContext = useContext(NavigationContext);

  const dynamicSubnetLinks: GroupedNavigationItem[] = useMemo(() => {
    return (
      navigationContext?.subnetData?.map(
        ({ netuid, subnet_name: subnetName }) => {
          return {
            href: appRoutes.subnets.detail(`${netuid}`),
            label: `${netuid}: ${subnetName.length > 0 ? subnetName.replace(emojiRegex(), '') : 'Unknown'}`,
          };
        }
      ) ?? []
    );
  }, [navigationContext?.subnetData]);

  const dynamicValidatorLinks: GroupedNavigationItem[] = useMemo(() => {
    return (
      navigationContext?.validatorData
        ?.filter(
          (item) =>
            Number(item.global_weighted_stake) / taoDivider >=
            VALIDATOR_MINIMUM_STAKE
        )
        .map((validator) => {
          return navigationContext.validatorIdentityData?.find(
            (metadataItem) =>
              metadataItem.validator_hotkey?.ss58 === validator.hotkey.ss58
          )
            ? validator
            : undefined;
        })
        .filter((item) => item !== undefined)
        .map((item, index) => {
          const address = item?.hotkey.ss58 ?? '';
          const name =
            navigationContext.validatorIdentityData?.find(
              (validator) =>
                (validator.validator_hotkey?.ss58 ?? '') === address
            )?.name ?? '';

          return {
            href: appRoutes.validator.detail(address),
            label: `${index + 1}: ${name}`,
            groupTitle: 'Validators',
          };
        }) || []
    );
  }, [
    navigationContext?.validatorData,
    navigationContext?.validatorIdentityData,
  ]);

  const validators: DtaoValidatorLatest[] = useMemo(() => {
    return (
      navigationContext?.validatorData
        ?.filter(
          (item) =>
            Number(item.global_weighted_stake) / taoDivider >=
            VALIDATOR_MINIMUM_STAKE
        )
        .map((validator) => {
          return navigationContext.validatorIdentityData?.find(
            (metadataItem) =>
              metadataItem.validator_hotkey?.ss58 === validator.hotkey.ss58
          )
            ? validator
            : undefined;
        })
        .filter((item): item is DtaoValidatorLatest => item !== undefined) ?? []
    );
  }, [
    navigationContext?.validatorData,
    navigationContext?.validatorIdentityData,
  ]);

  const completeWebNavigationLinks = useMemo(() => {
    return websiteNavigationLinks.map((link) => {
      const children =
        link.label === NavigationSectionTitles.Subnets
          ? dynamicSubnetLinks.concat(link.children ?? [])
          : link.label === NavigationSectionTitles.Validators
            ? (link.children ?? []).concat(dynamicValidatorLinks)
            : link.children ?? [];

      return {
        ...link,
        href: createUrl(navigationContext?.websiteUrl ?? '', link.href),
        children: children.map((child) => ({
          ...child,
          href: createUrl(navigationContext?.websiteUrl ?? '', child.href),
        })),
      };
    });
  }, [
    dynamicSubnetLinks,
    dynamicValidatorLinks,
    navigationContext?.websiteUrl,
  ]);

  const completeDashNavigationLinks = useMemo(() => {
    return {
      dashboardItems: (navigationContext?.userProfile?.isAdmin
        ? dashboardLists.concat(adminMenuItems)
        : dashboardLists
      ).map((item) => {
        return {
          ...item,
          href: item.href.startsWith('/')
            ? createUrl(navigationContext?.dashboardUrl ?? '', item.href)
            : item.href,
        };
      }),
      developerItems: developerMenuSection.map((item) => {
        return {
          ...item,
          href: item.href.startsWith('/')
            ? createUrl(navigationContext?.dashboardUrl ?? '', item.href)
            : item.href,
        };
      }),
    };
  }, [
    navigationContext?.dashboardUrl,
    navigationContext?.userProfile?.isAdmin,
  ]);

  return (
    <div className='flex w-full flex-row items-center justify-between gap-1 bg-[#121212]/70 px-5 py-4 backdrop-blur-xl sm:gap-3 sm:px-8 md:py-3'>
      <div className='2xl:gap-17 flex w-full flex-1 items-center justify-between gap-4 lg:w-max lg:justify-start xl:gap-10'>
        <Link href={createUrl(navigationContext?.websiteUrl ?? '', '/')}>
          <TaostatsLogo width={101} height={40} />
        </Link>
      </div>
      <div className='flex flex-row items-center gap-1 sm:gap-4'>
        <NavLinksGroup
          completeWebNavigationLinks={completeWebNavigationLinks}
          completeDashNavigationLinks={completeDashNavigationLinks}
          validators={validators}
        />
      </div>
      <NavLinksMobile
        completeWebNavigationLinks={completeWebNavigationLinks}
        completeDashNavigationLinks={completeDashNavigationLinks}
      />
    </div>
  );
}

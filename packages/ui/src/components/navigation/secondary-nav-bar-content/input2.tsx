'use client';
import { type VariantProps, cva } from 'class-variance-authority';
import type { IconType } from 'react-icons';
import { HiXMark } from 'react-icons/hi2';
import type { ClassNameValue } from 'tailwind-merge';
import { cn } from '@repo/ui/lib';

interface InputProps {
  placeholder?: string;
  icon?: IconType;
  value?: string;
  setValue?: (value: string) => void;
  className?: ClassNameValue;
  iconClassName?: ClassNameValue;
  inputClassName?: ClassNameValue;
  type?: React.HTMLInputTypeAttribute;
  clearIcon?: boolean;
  maxLength?: number;
  iconSize?: number;
  onKeyUp?: React.KeyboardEventHandler<HTMLInputElement>;
  onFocus?: React.FocusEventHandler<HTMLInputElement>;
  onClick?: React.MouseEventHandler<HTMLInputElement>;
  onBlur?: React.FocusEventHandler<HTMLInputElement>;
}

const inputStyles = cva(
  'flex h-8 w-full rounded-md border items-center border-input bg-neutral-900 gap-2 px-3 py-1 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
  {
    variants: {
      intent: {
        primary: ['bg-neutral-900', 'text-white', 'hover:bg-neutral-800'],
        secondary: [
          'bg-white',
          'text-gray-800',
          'border-gray-400',
          'hover:bg-gray-100',
        ],
      },
      size: {
        small: ['w-70', 'py-1', 'px-4'],
        default: ['py-1', 'px-3'],
      },
    },
    defaultVariants: {
      intent: 'primary',
      size: 'default',
    },
  }
);

interface Props extends InputProps, VariantProps<typeof inputStyles> {
  rightContent?: React.ReactNode;
}

export const Input2 = ({
  intent,
  size,
  placeholder,
  icon: Icon,
  value,
  setValue,
  className,
  inputClassName,
  onKeyUp,
  onFocus,
  onClick,
  onBlur,
  type,
  clearIcon,
  maxLength,
  iconClassName,
  iconSize = 14,
  rightContent,
}: Props) => {
  return (
    <div className={cn(inputStyles({ intent, size }), className)}>
      {Icon ? (
        <Icon
          size={iconSize}
          className={cn('text-neutral-500', iconClassName)}
        />
      ) : null}
      <input
        onChange={(e) => setValue?.(e.target.value)}
        value={value}
        className={cn(
          'h-full w-full flex-1 bg-transparent text-xs placeholder:text-neutral-500 focus:border-none focus:outline-none focus:ring-0',
          inputClassName
        )}
        placeholder={placeholder}
        onKeyUp={onKeyUp}
        onFocus={onFocus}
        onClick={onClick}
        onBlur={onBlur}
        type={type}
        maxLength={maxLength}
      />
      {clearIcon && value && value.length > 0 ? (
        <HiXMark
          cursor='pointer'
          size={18}
          onClick={() => setValue?.('')}
          className='rounded-full bg-[#535353] p-0.5'
        />
      ) : null}
      {rightContent}
    </div>
  );
};

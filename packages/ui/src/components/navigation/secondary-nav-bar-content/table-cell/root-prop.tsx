import type { CellContext } from '@tanstack/react-table';
import type { TableItem } from '../subnets-list';

export const RootPropCell = ({ row }: CellContext<TableItem, unknown>) => {
  const rootPropValue = row.getValue('root_prop');

  const percentageValue = (Number(rootPropValue) * 100).toLocaleString(
    'en-US',
    {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }
  );

  return row.original.netuid === 0 ? (
    <div className='flex w-16 justify-end text-sm'>NA</div>
  ) : (
    <div className='flex w-16 justify-end text-sm'>{percentageValue}%</div>
  );
};

export const RootPropHeader = () => {
  return <span className='flex w-16 justify-end'>Root Prop</span>;
};

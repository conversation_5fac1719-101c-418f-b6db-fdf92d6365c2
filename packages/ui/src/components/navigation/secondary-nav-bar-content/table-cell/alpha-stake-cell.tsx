import type { CellContext } from '@tanstack/react-table';
import { taoDivider } from '../../../../lib';
import type { TableValidatorItem } from '../validators-list';
import { Bittensor } from './bittensor';

export const AlphaStakeCell = ({
  row,
}: CellContext<TableValidatorItem, unknown>) => {
  const value = row.original.global_alpha_stake_as_tao / taoDivider;

  return (
    <div className='flex w-24 justify-end'>
      <div className='flex items-center whitespace-nowrap text-sm'>
        <Bittensor className='mb-0.25 -mr-1' />
        {value.toLocaleString('en-US', {
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        })}
      </div>
    </div>
  );
};

export const AlphaStakeHeader = () => {
  return <span className='flex w-24 justify-end'>Alpha Stake</span>;
};

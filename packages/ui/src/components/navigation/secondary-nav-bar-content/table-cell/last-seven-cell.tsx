import type { CellContext } from '@tanstack/react-table';
import type { TableItem } from '../subnets-list';
import { SubnetsLastDaysChartDynamic } from '../subnets-last-days-chart.dynamic';

export const LastCell = ({ row }: CellContext<TableItem, unknown>) => {
  const value = row.original.seven_day_prices;

  return (
    <div className='w-32'>
      <SubnetsLastDaysChartDynamic
        data={value}
        state={Number(row.original.price_change_1_week) >= 0}
        className='!h-[40px]'
      />
    </div>
  );
};

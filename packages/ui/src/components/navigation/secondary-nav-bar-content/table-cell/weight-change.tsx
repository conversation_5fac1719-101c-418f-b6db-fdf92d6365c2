import type { CellContext } from '@tanstack/react-table';
import { format } from 'numerable';
import { MdCallReceived, MdOutlineNorthEast } from 'react-icons/md';
import { cn } from '../../../../lib';
import type { TableValidatorItem } from '../validators-list';
import { Bittensor } from './bittensor';

export const WeightChangeCell = ({
  row,
}: CellContext<TableValidatorItem, unknown>) => {
  const value = Number(row.original.global_weighted_stake_24_hr_change);

  return (
    <div className='flex w-[124px] justify-end'>
      <div
        className={cn(
          'flex w-fit flex-row items-center gap-0.5 rounded-full px-2 py-0.5 text-xs',
          value >= 0
            ? 'bg-[#00DBBC1A] text-[#00DBBC]'
            : 'bg-[#EB53471A] text-[#EB5347]'
        )}
      >
        {value >= 0 ? (
          <MdOutlineNorthEast size={16} />
        ) : (
          <MdCallReceived size={16} />
        )}
        <Bittensor
          className={cn(
            '-ml-1 -mr-1.5',
            value >= 0 ? 'text-[#00DBBC]' : 'text-[#EB5347]'
          )}
        />
        {format(value, '0.00a')}
      </div>
    </div>
  );
};

export const WeightChangeHeader = () => {
  return (
    <span className='flex w-[124px] justify-end'>Weight Change (24h)</span>
  );
};

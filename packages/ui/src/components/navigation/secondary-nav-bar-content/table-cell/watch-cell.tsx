import type { Dispatch } from 'react';
import type { CellContext } from '@tanstack/react-table';
import { BiSolidStar, BiStar } from 'react-icons/bi';
import {
  getCookie,
  setCookie,
  LOCAL_STORAGE_SUBNETS_WATCHLIST,
} from '../../../../lib';
import type { TableItem } from '../subnets-list';

export const WatchCell = ({
  row,
  subnetsWatchList,
  setSubnetsWatchList,
}: CellContext<TableItem, unknown> & {
  subnetsWatchList: number[];
  setSubnetsWatchList: Dispatch<React.SetStateAction<number[]>>;
}) => {
  return (
    <>
      {subnetsWatchList.includes(row.original.netuid) ? (
        <BiSolidStar
          size={16}
          color='#00DBBC'
          className='flex-shrink-0 cursor-pointer'
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            const watchListCookie =
              getCookie(LOCAL_STORAGE_SUBNETS_WATCHLIST) ?? '[]';
            const list: number[] = JSON.parse(watchListCookie) as number[];
            let newWatchList: number[];

            if (list.includes(row.original.netuid)) {
              newWatchList = list.filter(
                (netuid) => netuid !== row.original.netuid
              );
              setSubnetsWatchList(newWatchList);
            } else {
              newWatchList = list;
              setSubnetsWatchList(newWatchList);
            }

            setCookie({
              name: LOCAL_STORAGE_SUBNETS_WATCHLIST,
              value: JSON.stringify(newWatchList),
            });
          }}
        />
      ) : (
        <BiStar
          size={16}
          className='flex-shrink-0 cursor-pointer opacity-20'
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            const watchListCookie =
              getCookie(LOCAL_STORAGE_SUBNETS_WATCHLIST) ?? '[]';
            const temp: number[] = JSON.parse(watchListCookie) as number[];

            if (!temp.includes(row.original.netuid)) {
              temp.push(row.original.netuid);
              setSubnetsWatchList(temp);
            }

            setCookie({
              name: LOCAL_STORAGE_SUBNETS_WATCHLIST,
              value: JSON.stringify(temp),
            });
          }}
        />
      )}
    </>
  );
};

import type { CellContext } from '@tanstack/react-table';
import type { TableItem } from '../subnets-list';

export const EmissionCell = ({ row }: CellContext<TableItem, unknown>) => {
  const value = Number(row.getValue('emission'));
  const percentageValue = (value / 10000000).toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  });

  return (
    <div className='flex w-14 justify-end !text-white'>{percentageValue}%</div>
  );
};

export const EmissionHeader = () => {
  return <span className='flex w-14 justify-end'>Emission</span>;
};

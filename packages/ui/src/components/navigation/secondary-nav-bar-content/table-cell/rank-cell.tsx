import type { CellContext } from '@tanstack/react-table';
import type { TableValidatorItem } from '../validators-list';

export const RankCell = ({ row }: CellContext<TableValidatorItem, unknown>) => {
  const value = Number(row.getValue('rank'));

  return <div className='flex w-4 justify-end !text-white'>{value}</div>;
};

export const RankHeader = () => {
  return <span className='flex justify-end'>Rank</span>;
};

import type { CellContext } from '@tanstack/react-table';
import { format } from 'numerable';
import { MdCallReceived, MdOutlineNorthEast } from 'react-icons/md';
import type { TableItem } from '../subnets-list';
import { cn } from '@repo/ui/lib';

export const OneHourPriceCell = ({ row }: CellContext<TableItem, unknown>) => {
  const value = Number(row.original.price_change_1_hour);
  const percentageValue = format(value, '0.00');

  return (
    <div className='flex w-24 justify-end'>
      <div
        className={cn(
          'leading-3.75 flex w-fit flex-row items-center gap-0.5 truncate rounded-full px-2 py-0.5 text-xs',
          value >= 0
            ? 'bg-[#00DBBC1A] text-[#00DBBC]'
            : 'bg-[#EB53471A] text-[#EB5347]'
        )}
      >
        {value >= 0 ? (
          <MdOutlineNorthEast size={16} />
        ) : (
          <MdCallReceived size={16} />
        )}
        {percentageValue}%
      </div>
    </div>
  );
};

export const OneHourPriceHeader = () => {
  return <span className='flex w-[79px] justify-end'>1H</span>;
};

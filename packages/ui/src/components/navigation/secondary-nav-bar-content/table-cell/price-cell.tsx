import type { CellContext } from '@tanstack/react-table';
import type { TableItem } from '../subnets-list';
import { Bittensor } from './bittensor';

export const PriceCell = ({ row }: CellContext<TableItem, unknown>) => {
  const value = Number(row.getValue('price'));
  const percentageValue = value.toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 6,
  });

  return (
    <div className='flex w-28 justify-end'>
      <div className='flex items-center whitespace-nowrap text-sm'>
        <Bittensor className='mb-0.25 -mr-1' />
        {percentageValue}
      </div>
    </div>
  );
};

export const PriceHeader = () => {
  return <span className='flex w-28 justify-end'>Price</span>;
};

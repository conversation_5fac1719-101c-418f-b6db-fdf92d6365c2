import type { CellContext } from '@tanstack/react-table';
import { AddressFormatter } from '../../../address-formatter';
import type { TableValidatorItem } from '../validators-list';

export const AddressCell = ({
  row,
}: CellContext<TableValidatorItem, unknown>) => {
  const value = row.original.id;

  return (
    <div className='flex w-44'>
      <AddressFormatter
        uid={value}
        noColor={false}
        isEncoded
        isValidator
        className='text-sm'
        rootClassName='max-w-44 overflow-x-hidden'
        textClassName='flex-shrink-[1]'
      />
    </div>
  );
};

export const AddressHeader = () => {
  return <span className='flex w-44'>Name / Address</span>;
};

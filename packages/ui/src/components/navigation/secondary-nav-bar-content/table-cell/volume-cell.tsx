import type { CellContext } from '@tanstack/react-table';
import type { TableItem } from '../subnets-list';
import { Bittensor } from './bittensor';
import { format } from 'numerable';

export const VolumeCell = ({ row }: CellContext<TableItem, unknown>) => {
  const value = Number(row.original.tao_volume_24_hr);

  return (
    <div className='flex w-20 justify-end'>
      <div className='flex items-center whitespace-nowrap text-sm'>
        <Bittensor className='mb-0.25 -mr-1' />
        {format(value, '0.00a')}
      </div>
    </div>
  );
};

export const VolumeHeader = () => {
  return <span className='flex w-20 justify-end'>Volume (24h)</span>;
};

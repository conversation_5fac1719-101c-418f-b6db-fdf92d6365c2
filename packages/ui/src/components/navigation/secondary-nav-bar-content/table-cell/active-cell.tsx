import type { CellContext } from '@tanstack/react-table';
import type { TableValidatorItem } from '../validators-list';

export const ActiveCell = ({
  row,
}: CellContext<TableValidatorItem, unknown>) => {
  const value = Number(row.original.active_subnets);

  return <div className='flex w-14 justify-end !text-white'>{value}</div>;
};

export const ActiveHeader = () => {
  return <span className='flex justify-end'>Active</span>;
};

import type { CellContext } from '@tanstack/react-table';
import { format } from 'numerable';
import type { TableValidatorItem } from '../validators-list';

export const ChartCell = ({
  row,
}: CellContext<TableValidatorItem, unknown>) => {
  return (
    <div className='flex w-48 flex-col gap-1'>
      <div className='flex flex-row justify-between'>
        <div className='flex flex-row items-end gap-1'>
          <div className='text-xs font-normal opacity-60'>Root</div>
          <div className='flex flex-row items-end'>
            <p className='flex flex-row items-end text-sm font-normal leading-[18px]'>
              {format(
                (1 /
                  (Number(row.original.weighted_root_stake) +
                    Number(row.original.global_alpha_stake_as_tao))) *
                  Number(row.original.weighted_root_stake) *
                  100,
                '0.00a'
              )}
              <span className='ml-0.5 text-sm font-normal leading-[18px]'>
                %
              </span>
            </p>
          </div>
        </div>
        <div className='flex flex-row items-end gap-1'>
          <div className='text-xs font-normal opacity-60'>Alpha</div>
          <div className='flex flex-row items-end'>
            <p className='flex flex-row items-end text-sm font-normal leading-[18px]'>
              {format(
                (1 /
                  (Number(row.original.weighted_root_stake) +
                    Number(row.original.global_alpha_stake_as_tao))) *
                  Number(row.original.global_alpha_stake_as_tao) *
                  100,
                '0.00a'
              )}
              <span className='ml-0.5 text-sm font-normal leading-[18px]'>
                %
              </span>
            </p>
          </div>
        </div>
      </div>
      <div className='flex flex-row gap-0.5'>
        <div
          className='h-2 w-full rounded-full border border-[#262626] bg-[#00DBBC]'
          style={{
            width: `${
              (1 /
                (Number(row.original.weighted_root_stake) +
                  Number(row.original.global_alpha_stake_as_tao))) *
              Number(row.original.weighted_root_stake) *
              100
            }%`,
          }}
        />
        <div
          className='h-2 w-full rounded-full border border-[#262626] bg-[#EB5347]'
          style={{
            width: `${
              (1 /
                (Number(row.original.weighted_root_stake) +
                  Number(row.original.global_alpha_stake_as_tao))) *
              Number(row.original.global_alpha_stake_as_tao) *
              100
            }%`,
          }}
        />
      </div>
    </div>
  );
};

import type { CellContext } from '@tanstack/react-table';
import type { TableValidatorItem } from '../validators-list';

export const TakeCell = ({ row }: CellContext<TableValidatorItem, unknown>) => {
  const value = Number(row.getValue('take'));
  const percentageValue = (value * 100).toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  });

  return (
    <div className='flex w-11 justify-end !text-white'>{percentageValue}%</div>
  );
};

export const TakeHeader = () => {
  return <span className='flex w-11 justify-end'>Take</span>;
};

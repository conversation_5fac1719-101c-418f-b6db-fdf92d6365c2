import type { CellContext } from '@tanstack/react-table';
import { taoDivider } from '../../../../lib';
import type { TableValidatorItem } from '../validators-list';
import { Bittensor } from './bittensor';

export const TotalWeightCell = ({
  row,
}: CellContext<TableValidatorItem, unknown>) => {
  const value = row.original.global_weighted_stake / taoDivider;

  return (
    <div className='flex w-24 justify-end'>
      <div className='flex items-center whitespace-nowrap text-sm'>
        <Bittensor className='mb-0.25 -mr-1' />
        {value.toLocaleString('en-US', {
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        })}
      </div>
    </div>
  );
};

export const TotalWeightHeader = () => {
  return <span className='flex w-20 justify-end'>Total Weight</span>;
};

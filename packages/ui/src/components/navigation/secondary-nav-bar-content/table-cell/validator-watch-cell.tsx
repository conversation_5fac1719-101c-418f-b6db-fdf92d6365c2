import type { Dispatch } from 'react';
import type { CellContext } from '@tanstack/react-table';
import { BiSolidStar, BiStar } from 'react-icons/bi';
import {
  getCookie,
  setCookie,
  LOCAL_STORAGE_VALIDATORS_WATCHLIST,
} from '../../../../lib';
import type { TableValidatorItem } from '../validators-list';

export const ValidatorWatchCell = ({
  row,
  validatorsWatchList,
  setValidatorsWatchList,
}: CellContext<TableValidatorItem, unknown> & {
  validatorsWatchList: string[];
  setValidatorsWatchList: Dispatch<React.SetStateAction<string[]>>;
}) => {
  return (
    <>
      {validatorsWatchList.includes(row.original.id) ? (
        <BiSolidStar
          size={16}
          color='#00DBBC'
          className='flex-shrink-0 cursor-pointer'
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            const watchListCookie =
              getCookie(LOCAL_STORAGE_VALIDATORS_WATCHLIST) ?? '[]';
            const list: string[] = JSON.parse(watchListCookie) as string[];
            let newWatchList: string[];

            if (list.includes(row.original.id)) {
              newWatchList = list.filter(
                (netuid) => netuid !== row.original.id
              );
              setValidatorsWatchList(newWatchList);
            } else {
              newWatchList = list;
              setValidatorsWatchList(newWatchList);
            }

            setCookie({
              name: LOCAL_STORAGE_VALIDATORS_WATCHLIST,
              value: JSON.stringify(newWatchList),
            });
          }}
        />
      ) : (
        <BiStar
          size={16}
          className='flex-shrink-0 cursor-pointer opacity-20'
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            const watchListCookie =
              getCookie(LOCAL_STORAGE_VALIDATORS_WATCHLIST) ?? '[]';
            const temp: string[] = JSON.parse(watchListCookie) as string[];

            if (!temp.includes(row.original.id)) {
              temp.push(row.original.id);
              setValidatorsWatchList(temp);
            }

            setCookie({
              name: LOCAL_STORAGE_VALIDATORS_WATCHLIST,
              value: JSON.stringify(temp),
            });
          }}
        />
      )}
    </>
  );
};

import type { CellContext } from '@tanstack/react-table';
import type { TableValidatorItem } from '../validators-list';

export const NomsCell = ({ row }: CellContext<TableValidatorItem, unknown>) => {
  const value = Number(row.original.global_nominators);

  return <div className='flex w-10 justify-end !text-white'>{value}</div>;
};

export const NomsHeader = () => {
  return <span className='flex justify-end'>Noms</span>;
};

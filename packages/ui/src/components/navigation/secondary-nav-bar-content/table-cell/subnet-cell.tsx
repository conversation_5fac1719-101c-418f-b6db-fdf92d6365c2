import type { CellContext } from '@tanstack/react-table';
import emojiRegex from 'emoji-regex';
import type { TableItem } from '../subnets-list';
import { SubnetNameDisplay } from '@repo/ui/components';

export const SubnetCell = ({ row }: CellContext<TableItem, unknown>) => {
  const netuid = Number(row.getValue('netuid'));

  return (
    <SubnetNameDisplay
      subnetName={row.original.subnet_name.replace(emojiRegex(), '')}
      netuid={netuid}
      isClickable
      className='flex w-64'
    />
  );
};

export const SubnetHeader = () => {
  return <span className='flex'>Subnet</span>;
};

import type { CellContext } from '@tanstack/react-table';
import type { TableValidatorItem } from '../validators-list';

export const DominanceCell = ({
  row,
}: CellContext<TableValidatorItem, unknown>) => {
  const value = Number(row.getValue('dominance'));

  return <div className='flex w-[68px] justify-end !text-white'>{value}%</div>;
};

export const DominanceHeader = () => {
  return <span className='flex w-[68px] justify-end'>Dominance</span>;
};

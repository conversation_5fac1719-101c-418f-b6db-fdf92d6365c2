import type { CellContext } from '@tanstack/react-table';
import { Md<PERSON>allReceived, MdOutlineNorthEast } from 'react-icons/md';
import { cn } from '../../../../lib';
import type { TableValidatorItem } from '../validators-list';

export const NomsChangeCell = ({
  row,
}: CellContext<TableValidatorItem, unknown>) => {
  const value = Number(row.original.global_nominators_24_hr_change);

  return (
    <div className='flex w-16 justify-end'>
      <div
        className={cn(
          'flex flex-row items-center justify-center gap-0.5 rounded-full px-2 py-0.5',
          value >= 0 && 'bg-[#BAEB471A] text-[#00DBBC]',
          value < 0 && 'bg-[#EB53471A] text-[#EB5347]'
        )}
      >
        {value >= 0 ? (
          <MdOutlineNorthEast size={16} />
        ) : (
          <MdCallReceived size={16} />
        )}
        <p className='text-xs'>{value}</p>
      </div>
    </div>
  );
};

export const NomsChangeHeader = () => {
  return <span className='flex w-16 justify-end'>24h</span>;
};

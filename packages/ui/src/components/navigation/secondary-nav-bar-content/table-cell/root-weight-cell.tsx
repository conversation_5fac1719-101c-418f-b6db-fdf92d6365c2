import type { CellContext } from '@tanstack/react-table';
import { taoDivider } from '../../../../lib';
import type { TableValidatorItem } from '../validators-list';
import { Bittensor } from './bittensor';

export const RootWeightCell = ({
  row,
}: CellContext<TableValidatorItem, unknown>) => {
  const value = row.original.weighted_root_stake / taoDivider;

  return (
    <div className='flex w-[124px] justify-end'>
      <div className='flex items-center whitespace-nowrap text-sm'>
        <Bittensor className='mb-0.25 -mr-1' />
        {value.toLocaleString('en-US', {
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        })}
      </div>
    </div>
  );
};

export const RootWeightHeader = () => {
  return <span className='flex w-[124px] justify-end'>Root Weight (0.18)</span>;
};

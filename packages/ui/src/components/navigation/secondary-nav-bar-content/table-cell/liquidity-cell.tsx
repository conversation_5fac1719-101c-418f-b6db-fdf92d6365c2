import type { CellContext } from '@tanstack/react-table';
import type { TableItem } from '../subnets-list';
import { Bittensor } from './bittensor';
import { format } from 'numerable';

export const LiquidityCell = ({ row }: CellContext<TableItem, unknown>) => {
  const liquidity = row.original.liquidity;

  return (
    <div className='flex w-20 justify-end'>
      <div className='flex items-center whitespace-nowrap text-sm'>
        <Bittensor className='mb-0.25 -mr-1' />
        {format(liquidity, '0.00a')}
      </div>
    </div>
  );
};

export const LiquidityHeader = () => {
  return <span className='flex w-20 justify-end'>Liquidity</span>;
};

'use client';

import { useState } from 'react';
import type { DtaoValidatorLatest } from '@repo/types/website-api-types';
import { cn } from '../../../lib';
import type { DashboardList } from './dashboard-lists';
import DashboardPopover from './dashboard-popover';
import {
  NavigationSectionTitles,
  type NavigationLink,
} from './navigation-links';
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuList,
} from './navigation-menu';
import { SearchButton } from './search/search-button';
import { AnalyticsMenu } from './sub-menu/analytics-menu';
import { BlockchainMenu } from './sub-menu/blockchain-menu';
import { InvestorsMenu } from './sub-menu/investors-menu';
import { SubnetsMenu } from './sub-menu/subnets-menu';
import { ValidatorsMenu } from './sub-menu/validators-menu';

export default function NavLinksGroup({
  validators,
  completeWebNavigationLinks,
  completeDashNavigationLinks,
}: {
  validators: DtaoValidatorLatest[];
  completeWebNavigationLinks: NavigationLink[];
  completeDashNavigationLinks: DashboardList;
}) {
  const [label, setLabel] = useState<string>('');
  const [isSubnet, setIsSubnet] = useState<boolean>(false);
  const [isInvestors, setIsInvestors] = useState<boolean>(false);

  return (
    <NavigationMenu
      inline={cn(
        label === 'Subnets'
          ? '!w-full overflow-y-auto min-h-[calc(-170px+100vh)]'
          : label === 'Validators'
            ? '!w-full'
            : ''
      )}
      online={cn(
        label === 'Subnets' || label === 'Validators'
          ? 'w-[96vw] left-auto -right-2'
          : label === NavigationSectionTitles.Investors
            ? '!left-52'
            : isSubnet
              ? 'w-[96vw] left-auto -right-2'
              : isInvestors
                ? '!left-52'
                : ''
      )}
      onValueChange={(value) => {
        setLabel(value);
      }}
    >
      <NavigationMenuList>
        <SubnetsMenu
          label={label}
          setIsSubnet={setIsSubnet}
          setIsInvestors={setIsInvestors}
        />
        <BlockchainMenu
          label={label}
          completeWebNavigationLinks={completeWebNavigationLinks}
          setIsSubnet={setIsSubnet}
          setIsInvestors={setIsInvestors}
        />
        <ValidatorsMenu
          label={label}
          validators={validators}
          completeWebNavigationLinks={completeWebNavigationLinks}
          setIsSubnet={setIsSubnet}
          setIsInvestors={setIsInvestors}
        />
        <AnalyticsMenu
          label={label}
          completeWebNavigationLinks={completeWebNavigationLinks}
          setIsSubnet={setIsSubnet}
          setIsInvestors={setIsInvestors}
        />
        <InvestorsMenu
          label={label}
          completeWebNavigationLinks={completeWebNavigationLinks}
          setIsSubnet={setIsSubnet}
          setIsInvestors={setIsInvestors}
        />
        <NavigationMenuItem>
          <SearchButton />
        </NavigationMenuItem>
        <NavigationMenuItem className='hidden pl-4 min-[1260px]:block'>
          <DashboardPopover
            completeDashNavigationLinks={completeDashNavigationLinks}
          />
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  );
}

'use client';

import { useMemo } from 'react';
import { ParentSize } from '@visx/responsive';
import { AnimatedAreaSeries, buildChartTheme, XYChart } from '@visx/xychart';
import type { SevenPriceItem } from '@repo/types/website-api-types';
import { cn } from '@repo/ui/lib';

const accessors = {
  xAccessor: (d: any) => new Date(d?.x),
  yAccessor: (d: any) => d.y,
};

const Chart = ({
  data,
  height = 40,
  width = 128,
  state,
}: {
  data: SevenPriceItem[] | null;
  height?: number;
  width?: number;
  state?: boolean;
}) => {
  const chartData = useMemo(
    () =>
      data?.map((item) => ({
        x: item.timestamp,
        y: Number(item.price),
      })),
    [data]
  );

  const [min, max] = useMemo(() => {
    const ma = Math.max(...(chartData?.map((item) => item.y) ?? []));
    const mi = Math.min(...(chartData?.map((item) => item.y) ?? []));

    return [mi, ma];
  }, [chartData]);

  const customTheme = buildChartTheme({
    backgroundColor: '',
    colors: state ? ['#00DBBC', '#2C2E43'] : ['#EB5347', '#2C2E43'],
    gridColor: '#e8e8e8',
    gridColorDark: '#222831',
    tickLength: 1,
  });

  return (
    <XYChart
      height={height}
      width={width}
      xScale={{ type: 'time' }}
      yScale={{ type: 'linear', domain: [min, max], zero: false }}
      margin={{ top: 15, right: 12, bottom: 15, left: 12 }}
      theme={customTheme}
    >
      <AnimatedAreaSeries
        dataKey='Line 1'
        data={chartData?.slice().reverse() ?? []}
        {...accessors}
        fill='transparent'
      />
    </XYChart>
  );
};

export function SubnetsLastDaysChart({
  data,
  className,
  state,
}: {
  data: SevenPriceItem[];
  className?: string;
  state?: boolean;
}) {
  return (
    <ParentSize className={cn('relative flex-1', className)}>
      {({ width, height }) => (
        <Chart width={width} height={height} data={data} state={state} />
      )}
    </ParentSize>
  );
}

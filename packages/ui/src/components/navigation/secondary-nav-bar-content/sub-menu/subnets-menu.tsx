import type { Dispatch } from 'react';
import { useContext, useEffect, useState } from 'react';
import emojiRegex from 'emoji-regex';
import Link from 'next/link';
import { BiListUl, BiSolidStar, BiStar } from 'react-icons/bi';
import { CgMenuGridR } from 'react-icons/cg';
import { TbStarOff } from 'react-icons/tb';
import {
  appRoutes,
  cn,
  createUrl,
  getCookie,
  getFilter,
  setCookie,
  LOCAL_STORAGE_SUBNETS_WATCHLIST,
  LOCAL_STORAGE_SUBNETS_WATCH_VIEW,
  LOCAL_STORAGE_SUBNETS_MENU_SWITCH,
} from '../../../../lib';
import { Button } from '../../../button';
import { Text } from '../../../text';
import { NavigationContext } from '../../navigation-context';
import {
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuTrigger,
} from '../navigation-menu';
import { SearchInput } from '../search-input';
import SubnetsListTable from '../subnets-list';
import { Bittensor } from '../table-cell/bittensor';

const searchFields = ['netuid', 'subnet_name'];

export const SubnetsMenu = ({
  label,
  setIsInvestors,
  setIsSubnet,
}: {
  label: string;
  setIsInvestors: Dispatch<React.SetStateAction<boolean>>;
  setIsSubnet: Dispatch<React.SetStateAction<boolean>>;
}) => {
  const navigationContext = useContext(NavigationContext);
  const [subnetMenuSwitch, setSubnetMenuSwitch] = useState<boolean>(false);
  const [subnetsWatchList, setSubnetsWatchList] = useState<number[]>([]);
  const [watchList, setWatchList] = useState<boolean>(false);
  const [globalFilter, setGlobalFilter] = useState<string>('');

  useEffect(() => {
    const subnetsWatchListCookie =
      getCookie(LOCAL_STORAGE_SUBNETS_WATCHLIST) ?? '[]';
    setSubnetsWatchList(JSON.parse(subnetsWatchListCookie) as number[]);

    const watchViewCookie =
      getCookie(LOCAL_STORAGE_SUBNETS_WATCH_VIEW) ?? 'false';
    setWatchList(JSON.parse(watchViewCookie) as boolean);

    const menuSwitchCookie =
      getCookie(LOCAL_STORAGE_SUBNETS_MENU_SWITCH) ?? 'false';
    setSubnetMenuSwitch(JSON.parse(menuSwitchCookie) as boolean);
  }, []);

  return (
    <NavigationMenuItem
      key='subnets'
      value='Subnets'
      className='hidden min-[1260px]:flex'
      onClick={() => {
        setIsSubnet(true);
        setIsInvestors(false);
      }}
      onMouseOver={() => {
        setIsSubnet(true);
        setIsInvestors(false);
      }}
    >
      <NavigationMenuTrigger
        className={cn(
          'rounded-none bg-transparent pb-3 text-sm font-[400] hover:underline'
        )}
      >
        <Link
          href={createUrl(
            navigationContext?.websiteUrl ?? '',
            appRoutes.subnets.home
          )}
        >
          <Text
            className={label === 'Subnets' ? 'opacity-100' : 'opacity-60'}
            level='sm'
          >
            Subnets
          </Text>
        </Link>
      </NavigationMenuTrigger>
      <NavigationMenuContent
        className={cn(label === 'Subnets' ? '!w-full' : '')}
        style={{ maxHeight: 'calc(100vh - 115px)' }}
      >
        <div className='mx-10 mb-3 mt-10 flex flex-row items-center justify-between rounded-xl bg-[#242424] py-2 pl-3 pr-2'>
          <div className='flex flex-row items-end gap-4'>
            <div className='flex flex-row gap-2'>
              <div className='rotate-180 border-[2px] border-[#00DBBC]' />
              <Link
                href={createUrl(
                  navigationContext?.websiteUrl ?? '',
                  appRoutes.subnets.home
                )}
              >
                <Text level='lg' className='flex items-center gap-2 text-white'>
                  Subnets
                </Text>
              </Link>
            </div>
            <div className='-mb-0.25 flex flex-row items-center'>
              <Text level='xs' className='-mb-0.25 text-white opacity-60'>
                Subnets Value
              </Text>
              <Text level='base' className='flex flex-row items-center'>
                <Bittensor className='-mb-0.25 -mr-1 text-white' />
                {navigationContext?.subnetPoolData
                  .filter((item) => item.netuid !== 0)
                  .reduce((acc, cur) => {
                    return acc + (cur.startup_mode ? 0 : Number(cur.price));
                  }, 0)
                  .toFixed(2)}
              </Text>
            </div>
          </div>
          <div className='flex flex-row items-center gap-8'>
            <SearchInput
              setGlobalFilter={setGlobalFilter}
              closeIcon
              searchPlaceholder='Filter by Subnet or Netuid'
              iconClassName='h-5 w-5'
              inputClassName='font-normal text-[13px] leading-4 text-[#909090]'
              inputStyleClassName='!h-9 rounded-full px-3 py-2 w-[340px] border border-[#262626] bg-[#2E2E2E]'
              rightContent={
                <>
                  {watchList ? (
                    <BiSolidStar
                      size={16}
                      color='#00DBBC'
                      className='flex-shrink-0 cursor-pointer'
                      onClick={() => {
                        setCookie({
                          name: LOCAL_STORAGE_SUBNETS_WATCH_VIEW,
                          value: JSON.stringify(!watchList),
                        });
                        setWatchList(!watchList);
                      }}
                    />
                  ) : (
                    <BiStar
                      size={16}
                      className='flex-shrink-0 cursor-pointer opacity-20'
                      onClick={() => {
                        setCookie({
                          name: LOCAL_STORAGE_SUBNETS_WATCH_VIEW,
                          value: JSON.stringify(!watchList),
                        });
                        setWatchList(!watchList);
                      }}
                    />
                  )}
                </>
              }
            />
            <div className='flex flex-row gap-2 rounded-xl border border-[#323232] bg-[#1D1D1D] p-1'>
              <Button
                className={cn(
                  'h-fit cursor-pointer p-2',
                  subnetMenuSwitch
                    ? 'border border-[#00DBBC] bg-[#00DBBC]/10 hover:bg-[#00DBBC]/10'
                    : 'border-transparent bg-transparent hover:bg-transparent'
                )}
                onClick={() => {
                  setCookie({
                    name: LOCAL_STORAGE_SUBNETS_MENU_SWITCH,
                    value: JSON.stringify(true),
                  });
                  setSubnetMenuSwitch(true);
                }}
              >
                <BiListUl
                  size={16}
                  className={cn(
                    'text-white',
                    subnetMenuSwitch ? 'opacity-100' : 'opacity-40'
                  )}
                />
              </Button>
              <Button
                className={cn(
                  'h-fit cursor-pointer p-2',
                  !subnetMenuSwitch
                    ? 'border border-[#00DBBC] bg-[#00DBBC]/10 hover:bg-[#00DBBC]/10'
                    : 'border-transparent bg-transparent hover:bg-transparent'
                )}
                onClick={() => {
                  setCookie({
                    name: LOCAL_STORAGE_SUBNETS_MENU_SWITCH,
                    value: JSON.stringify(false),
                  });
                  setSubnetMenuSwitch(false);
                }}
              >
                <CgMenuGridR
                  size={16}
                  className={cn(
                    'text-white',
                    !subnetMenuSwitch ? 'opacity-100' : 'opacity-40'
                  )}
                />
              </Button>
            </div>
          </div>
        </div>
        {!subnetMenuSwitch ? (
          navigationContext?.subnetPoolData.filter(
            ({ netuid }) =>
              !watchList || subnetsWatchList.find((s) => s === netuid)
          ).length ? (
            <ul
              className='-right-2 left-auto grid w-[96vw] grid-flow-col gap-x-3 gap-y-1 p-5 lg:gap-x-5'
              style={{
                gridTemplateColumns: 'repeat(6, minmax(0, 1fr))',
                gridTemplateRows: `repeat(${Math.ceil(
                  navigationContext.subnetPoolData.length / 6
                )}, minmax(0, 1fr))`,
              }}
            >
              {navigationContext.subnetPoolData
                .sort((a, b) => a.netuid - b.netuid)
                .filter(
                  ({ netuid }) =>
                    !watchList || subnetsWatchList.find((s) => s === netuid)
                )
                .filter((subnet) =>
                  getFilter(subnet, globalFilter, searchFields)
                )
                .map(({ netuid }) => {
                  const subnetName =
                    navigationContext.subnetData
                      ?.find((it) => it.netuid === netuid)
                      ?.subnet_name.replace(emojiRegex(), '') ?? 'Unknown';
                  return (
                    <div
                      key={netuid}
                      className='flex flex-row items-center gap-1 pl-3'
                    >
                      {subnetsWatchList.find((s) => s === netuid) !==
                      undefined ? (
                        <BiSolidStar
                          size={16}
                          color='#00DBBC'
                          className='flex-shrink-0 cursor-pointer'
                          onClick={() => {
                            const watchListCookie =
                              getCookie(LOCAL_STORAGE_SUBNETS_WATCHLIST) ??
                              '[]';
                            const temp: number[] = JSON.parse(
                              watchListCookie
                            ) as number[];
                            let newWatchList: number[];

                            if (temp.find((s) => s === netuid) !== undefined) {
                              newWatchList = temp.filter((it) => it !== netuid);
                              setSubnetsWatchList(newWatchList);
                            } else {
                              newWatchList = temp;
                              setSubnetsWatchList(newWatchList);
                            }
                            setCookie({
                              name: LOCAL_STORAGE_SUBNETS_WATCHLIST,
                              value: JSON.stringify(newWatchList),
                            });
                          }}
                        />
                      ) : (
                        <BiStar
                          size={16}
                          className='flex-shrink-0 cursor-pointer opacity-20'
                          onClick={() => {
                            const watchListCookie =
                              getCookie(LOCAL_STORAGE_SUBNETS_WATCHLIST) ??
                              '[]';
                            const watch: number[] = JSON.parse(
                              watchListCookie
                            ) as number[];

                            if (watch.find((s) => s === netuid) === undefined) {
                              watch.push(netuid);
                              setSubnetsWatchList(watch);
                            }
                            setCookie({
                              name: LOCAL_STORAGE_SUBNETS_WATCHLIST,
                              value: JSON.stringify(watch),
                            });
                          }}
                        />
                      )}

                      <Link
                        key={netuid}
                        href={createUrl(
                          navigationContext.websiteUrl,
                          appRoutes.subnets.detail(netuid.toString())
                        )}
                        className='flex w-full items-center gap-1 rounded-md py-1 pl-1 pr-3 text-sm text-white hover:bg-[#202020] hover:text-white'
                      >
                        <p className='text-smt h-5 flex-shrink-0 pt-0.5 text-end font-mono opacity-60'>
                          {netuid}:
                        </p>{' '}
                        <p className='truncate'>
                          {subnetName.length > 0 ? subnetName : 'Unknown'}
                        </p>
                      </Link>
                    </div>
                  );
                })}
            </ul>
          ) : (
            <div className='flex h-60 w-full flex-col items-center justify-center gap-3'>
              <div className='flex h-[72px] w-[72px] items-center justify-center rounded-full bg-[#323232]'>
                <TbStarOff size={24} className='opacity-60' />
              </div>
              <div className='text-xs font-medium text-[#909090]'>
                There are no favorite subnets
              </div>
            </div>
          )
        ) : (
          <SubnetsListTable
            initialData={navigationContext?.subnetPoolData ?? []}
            subnetsWatchList={subnetsWatchList}
            setSubnetsWatchList={setSubnetsWatchList}
            search={globalFilter}
            watchList={watchList}
          />
        )}
      </NavigationMenuContent>
    </NavigationMenuItem>
  );
};

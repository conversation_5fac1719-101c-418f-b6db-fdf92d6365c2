import type { Dispatch } from 'react';
import Link from 'next/link';
import { cn } from '../../../../lib';
import { Button } from '../../../button';
import { Text } from '../../../text';
import type { NavigationLink } from '../navigation-links';
import {
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuTrigger,
} from '../navigation-menu';

export const AnalyticsMenu = ({
  label,
  completeWebNavigationLinks,
  setIsInvestors,
  setIsSubnet,
}: {
  label: string;
  completeWebNavigationLinks: NavigationLink[];
  setIsInvestors: Dispatch<React.SetStateAction<boolean>>;
  setIsSubnet: Dispatch<React.SetStateAction<boolean>>;
}) => {
  return (
    <>
      {completeWebNavigationLinks
        .filter((item) => item.label === 'Analytics')
        .map(
          ({ label: linkLabel, mainIcon: MainIcon, children, Icon, href }) => {
            return (
              <NavigationMenuItem
                key={linkLabel}
                value={linkLabel}
                className='hidden min-[1260px]:flex'
                onClick={() => {
                  setIsSubnet(false);
                  setIsInvestors(false);
                }}
                onMouseOver={() => {
                  setIsSubnet(false);
                  setIsInvestors(false);
                }}
              >
                <NavigationMenuTrigger
                  className={cn(
                    'rounded-none bg-transparent pb-3 text-sm font-[400] hover:underline'
                  )}
                >
                  <Link href={href}>
                    {MainIcon ? (
                      <MainIcon className='-mb-0.5' />
                    ) : (
                      <Text
                        className={
                          label === linkLabel ? 'opacity-100' : 'opacity-60'
                        }
                        level='sm'
                      >
                        {linkLabel}
                      </Text>
                    )}
                  </Link>
                </NavigationMenuTrigger>
                <NavigationMenuContent className={cn(label === '' && 'hidden')}>
                  <Link href={href} className='w-full'>
                    <Button
                      variant='empty'
                      className='group w-full justify-start border-b border-neutral-900 !py-8 px-8 hover:bg-neutral-800'
                    >
                      <Text
                        level='lg'
                        className='flex items-center gap-2 text-white'
                      >
                        <Icon className='text-ocean' size={16} />
                        {MainIcon ? <MainIcon /> : linkLabel}{' '}
                      </Text>
                    </Button>
                  </Link>

                  <ul
                    className={cn(
                      label !== 'Subnets' &&
                        'bg-background grid gap-x-5 gap-y-1 p-5',
                      label === 'Subnets'
                        ? 'w-full'
                        : label === 'Validators' || label === 'Analytics'
                          ? 'w-[800px]'
                          : label === 'Investors'
                            ? 'w-[500px]'
                            : 'w-[400px] md:w-[500px] lg:w-[600px]'
                    )}
                  >
                    <div className='grid grid-cols-3 gap-x-5'>
                      {Object.entries(
                        children?.reduce(
                          (acc: Record<string, typeof children>, curr) => {
                            const group = curr.groupTitle ?? '';
                            acc[group] ??= [];
                            acc[group].push(curr);
                            return acc;
                          },
                          {}
                        ) ?? {}
                      ).map(([groupTitle, items]) => (
                        <div key={groupTitle} className='flex flex-col gap-2'>
                          {groupTitle ? (
                            <Text
                              level='sm'
                              className='font-medium text-neutral-400'
                            >
                              {groupTitle}
                            </Text>
                          ) : null}
                          <div className='flex flex-col gap-1'>
                            {items.map(
                              ({
                                label: itemLabel,
                                groupTitle: itemGroupTitle,
                                href: itemHref,
                              }) => (
                                <Link
                                  key={`${itemGroupTitle}-${itemLabel}`}
                                  href={itemHref}
                                  className={cn(
                                    '-mx-3 truncate rounded-md px-3 py-1 text-sm text-white hover:bg-[#202020] hover:text-white'
                                  )}
                                >
                                  {itemLabel}
                                </Link>
                              )
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </ul>
                </NavigationMenuContent>
              </NavigationMenuItem>
            );
          }
        )}
    </>
  );
};

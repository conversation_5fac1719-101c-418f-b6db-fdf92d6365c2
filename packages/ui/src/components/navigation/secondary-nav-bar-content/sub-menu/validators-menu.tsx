import type { Dispatch } from 'react';
import { useContext, useEffect, useState } from 'react';
import Link from 'next/link';
import { BiListUl, BiSolidStar, BiStar } from 'react-icons/bi';
import { CgMenuGridR } from 'react-icons/cg';
import { TbStarOff } from 'react-icons/tb';
import type { DtaoValidatorLatest } from '@repo/types/website-api-types';
import {
  appRoutes,
  cn,
  createUrl,
  getCookie,
  getFilter,
  setCookie,
  LOCAL_STORAGE_VALIDATORS_WATCHLIST,
  LOCAL_STORAGE_VALIDATORS_WATCH_VIEW,
  LOCAL_STORAGE_VALIDATORS_MENU_SWITCH,
} from '../../../../lib';
import { Button } from '../../../button';
import { Text } from '../../../text';
import { NavigationContext } from '../../navigation-context';
import type { NavigationLink } from '../navigation-links';
import {
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuTrigger,
} from '../navigation-menu';
import { SearchInput } from '../search-input';
import ValidatorsListTable from '../validators-list';

const searchValidatorsFields = ['id', 'name'];

export const ValidatorsMenu = ({
  label,
  validators,
  completeWebNavigationLinks,
  setIsInvestors,
  setIsSubnet,
}: {
  validators: DtaoValidatorLatest[];
  label: string;
  completeWebNavigationLinks: NavigationLink[];
  setIsInvestors: Dispatch<React.SetStateAction<boolean>>;
  setIsSubnet: Dispatch<React.SetStateAction<boolean>>;
}) => {
  const navigationContext = useContext(NavigationContext);
  const [globalValidatorFilter, setGlobalValidatorFilter] = useState('');
  const [validatorsWatchList, setValidatorsWatchList] = useState<string[]>([]);
  const [watchValidator, setWatchValidator] = useState<boolean>(false);
  const [validatorMenuSwitch, setValidatorMenuSwitch] =
    useState<boolean>(false);

  useEffect(() => {
    const validatorsWatchListCookie =
      getCookie(LOCAL_STORAGE_VALIDATORS_WATCHLIST) ?? '[]';
    setValidatorsWatchList(JSON.parse(validatorsWatchListCookie) as string[]);

    const watchViewCookie =
      getCookie(LOCAL_STORAGE_VALIDATORS_WATCH_VIEW) ?? 'false';
    setWatchValidator(JSON.parse(watchViewCookie) as boolean);

    const menuSwitchCookie =
      getCookie(LOCAL_STORAGE_VALIDATORS_MENU_SWITCH) ?? 'false';
    setValidatorMenuSwitch(JSON.parse(menuSwitchCookie) as boolean);
  }, []);

  return (
    <>
      {completeWebNavigationLinks
        .filter((item) => item.label === 'Validators')
        .map(({ label: linkLabel, mainIcon: MainIcon, children, href }) => {
          return (
            <NavigationMenuItem
              key={linkLabel}
              value={linkLabel}
              className='hidden min-[1260px]:flex'
              onClick={() => {
                setIsSubnet(true);
                setIsInvestors(false);
              }}
              onMouseOver={() => {
                setIsSubnet(true);
                setIsInvestors(false);
              }}
            >
              <NavigationMenuTrigger
                className={cn(
                  'rounded-none bg-transparent pb-3 text-sm font-[400] hover:underline'
                )}
              >
                <Link href={href}>
                  {MainIcon ? (
                    <MainIcon className='-mb-0.5' />
                  ) : (
                    <Text
                      className={
                        label === linkLabel ? 'opacity-100' : 'opacity-60'
                      }
                      level='sm'
                    >
                      {linkLabel}
                    </Text>
                  )}
                </Link>
              </NavigationMenuTrigger>
              <NavigationMenuContent
                className={cn('!w-full', label === '' && 'hidden')}
              >
                <div className='mx-10 mb-3 mt-10 flex flex-row items-center justify-between rounded-xl bg-[#242424] py-2 pl-3 pr-2'>
                  <div className='flex flex-row gap-2'>
                    <div className='rotate-180 border-[2px] border-[#00DBBC]' />
                    <Link
                      href={createUrl(
                        navigationContext?.websiteUrl ?? '',
                        appRoutes.validator.home
                      )}
                    >
                      <Text
                        level='lg'
                        className='flex items-center gap-2 text-white'
                      >
                        {linkLabel}
                      </Text>
                    </Link>
                  </div>
                  <div className='flex flex-row items-center gap-8'>
                    <SearchInput
                      setGlobalFilter={setGlobalValidatorFilter}
                      closeIcon
                      searchPlaceholder='Filter by Validator'
                      iconClassName='h-5 w-5'
                      inputClassName='font-normal text-[13px] leading-4 text-[#909090]'
                      inputStyleClassName='!h-9 rounded-full px-3 py-2 w-[340px] border border-[#262626] bg-[#2E2E2E]'
                      rightContent={
                        <>
                          {watchValidator ? (
                            <BiSolidStar
                              size={16}
                              color='#00DBBC'
                              className='flex-shrink-0 cursor-pointer'
                              onClick={() => {
                                setCookie({
                                  name: LOCAL_STORAGE_VALIDATORS_WATCH_VIEW,
                                  value: JSON.stringify(!watchValidator),
                                });
                                setWatchValidator(!watchValidator);
                              }}
                            />
                          ) : (
                            <BiStar
                              size={16}
                              className='flex-shrink-0 cursor-pointer opacity-20'
                              onClick={() => {
                                setCookie({
                                  name: LOCAL_STORAGE_VALIDATORS_WATCH_VIEW,
                                  value: JSON.stringify(!watchValidator),
                                });
                                setWatchValidator(!watchValidator);
                              }}
                            />
                          )}
                        </>
                      }
                    />
                    <div className='flex flex-row gap-2 rounded-xl border border-[#323232] bg-[#1D1D1D] p-1'>
                      <Button
                        className={cn(
                          'h-fit cursor-pointer p-2',
                          validatorMenuSwitch
                            ? 'border border-[#00DBBC] bg-[#00DBBC]/10 hover:bg-[#00DBBC]/10'
                            : 'border-transparent bg-transparent hover:bg-transparent'
                        )}
                        onClick={() => {
                          setCookie({
                            name: LOCAL_STORAGE_VALIDATORS_MENU_SWITCH,
                            value: JSON.stringify(true),
                          });
                          setValidatorMenuSwitch(true);
                        }}
                      >
                        <BiListUl
                          size={16}
                          className={cn(
                            'text-white',
                            validatorMenuSwitch ? 'opacity-100' : 'opacity-40'
                          )}
                        />
                      </Button>
                      <Button
                        className={cn(
                          'h-fit cursor-pointer p-2',
                          !validatorMenuSwitch
                            ? 'border border-[#00DBBC] bg-[#00DBBC]/10 hover:bg-[#00DBBC]/10'
                            : 'border-transparent bg-transparent hover:bg-transparent'
                        )}
                        onClick={() => {
                          setCookie({
                            name: LOCAL_STORAGE_VALIDATORS_MENU_SWITCH,
                            value: JSON.stringify(false),
                          });
                          setValidatorMenuSwitch(false);
                        }}
                      >
                        <CgMenuGridR
                          size={16}
                          className={cn(
                            'text-white',
                            !validatorMenuSwitch ? 'opacity-100' : 'opacity-40'
                          )}
                        />
                      </Button>
                    </div>
                  </div>
                </div>

                {!validatorMenuSwitch ? (
                  validators.filter(
                    (it) =>
                      !watchValidator ||
                      validatorsWatchList.find((s) => s === it.hotkey.ss58)
                  ).length ? (
                    <ul
                      className='-right-2 left-auto grid w-[96vw] grid-flow-col gap-x-3 gap-y-1 p-5 lg:gap-x-5'
                      style={{
                        gridTemplateColumns: 'repeat(6, minmax(0, 1fr))',
                        gridTemplateRows: `repeat(${Math.ceil(
                          (children?.length ?? 0) / 6
                        )}, minmax(0, 1fr))`,
                      }}
                    >
                      {validators
                        .filter(
                          ({ hotkey }) =>
                            !watchValidator ||
                            validatorsWatchList.find((s) => s === hotkey.ss58)
                        )
                        .filter((validator) =>
                          getFilter(
                            validator,
                            globalValidatorFilter,
                            searchValidatorsFields
                          )
                        )
                        .map((item, index) => {
                          const name =
                            navigationContext?.validatorIdentityData?.find(
                              (validator) =>
                                (validator.validator_hotkey?.ss58 ?? '') ===
                                item.hotkey.ss58
                            )?.name ?? '';

                          return (
                            <div
                              key={`index-${item.hotkey.ss58}`}
                              className='flex flex-row items-center gap-1 pl-3'
                            >
                              {validatorsWatchList.find(
                                (s) => s === item.hotkey.ss58
                              ) !== undefined ? (
                                <BiSolidStar
                                  size={16}
                                  color='#00DBBC'
                                  className='flex-shrink-0 cursor-pointer'
                                  onClick={() => {
                                    const watchListCookie =
                                      getCookie(
                                        LOCAL_STORAGE_VALIDATORS_WATCHLIST
                                      ) ?? '[]';
                                    const temp: string[] = JSON.parse(
                                      watchListCookie
                                    ) as string[];
                                    let newWatchList: string[];

                                    if (
                                      temp.find(
                                        (s) => s === item.hotkey.ss58
                                      ) !== undefined
                                    ) {
                                      newWatchList = temp.filter(
                                        (it) => it !== item.hotkey.ss58
                                      );
                                      setValidatorsWatchList(newWatchList);
                                    } else {
                                      newWatchList = temp;
                                      setValidatorsWatchList(newWatchList);
                                    }
                                    setCookie({
                                      name: LOCAL_STORAGE_VALIDATORS_WATCHLIST,
                                      value: JSON.stringify(newWatchList),
                                    });
                                  }}
                                />
                              ) : (
                                <BiStar
                                  size={16}
                                  className='flex-shrink-0 cursor-pointer opacity-20'
                                  onClick={() => {
                                    const watchListCookie =
                                      getCookie(
                                        LOCAL_STORAGE_VALIDATORS_WATCHLIST
                                      ) ?? '[]';
                                    const watch: string[] = JSON.parse(
                                      watchListCookie
                                    ) as string[];

                                    if (
                                      watch.find(
                                        (s) => s === item.hotkey.ss58
                                      ) === undefined
                                    ) {
                                      watch.push(item.hotkey.ss58);
                                      setValidatorsWatchList(watch);
                                    }
                                    setCookie({
                                      name: LOCAL_STORAGE_VALIDATORS_WATCHLIST,
                                      value: JSON.stringify(watch),
                                    });
                                  }}
                                />
                              )}
                              <Link
                                key={`index-${item.hotkey.ss58}`}
                                href={createUrl(
                                  navigationContext?.websiteUrl ?? '',
                                  appRoutes.validator.detail(item.hotkey.ss58)
                                )}
                                className='flex w-full items-center gap-1 rounded-md py-1 pl-1 pr-3 text-sm text-white hover:bg-[#202020] hover:text-white'
                              >
                                <p className='text-smt h-5 flex-shrink-0 pt-0.5 text-end font-mono opacity-60'>
                                  {index + 1}:
                                </p>
                                <span className='truncate'>{name}</span>
                              </Link>
                            </div>
                          );
                        })}
                    </ul>
                  ) : (
                    <div className='flex h-60 w-full flex-col items-center justify-center gap-3'>
                      <div className='flex h-[72px] w-[72px] items-center justify-center rounded-full bg-[#323232]'>
                        <TbStarOff size={24} className='opacity-60' />
                      </div>
                      <div className='text-xs font-medium text-[#909090]'>
                        There are no favorite validators
                      </div>
                    </div>
                  )
                ) : (
                  <ValidatorsListTable
                    initialData={validators}
                    validatorsWatchList={validatorsWatchList}
                    setValidatorsWatchList={setValidatorsWatchList}
                    search={globalValidatorFilter}
                    watchValidator={watchValidator}
                  />
                )}
              </NavigationMenuContent>
            </NavigationMenuItem>
          );
        })}
    </>
  );
};

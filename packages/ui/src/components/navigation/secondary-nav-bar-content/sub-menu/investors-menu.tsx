import type { Dispatch } from 'react';
import Link from 'next/link';
import { cn } from '../../../../lib';
import { Button } from '../../../button';
import { Text } from '../../../text';
import type { NavigationLink } from '../navigation-links';
import {
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuTrigger,
} from '../navigation-menu';

export const InvestorsMenu = ({
  label,
  completeWebNavigationLinks,
  setIsInvestors,
  setIsSubnet,
}: {
  label: string;
  completeWebNavigationLinks: NavigationLink[];
  setIsInvestors: Dispatch<React.SetStateAction<boolean>>;
  setIsSubnet: Dispatch<React.SetStateAction<boolean>>;
}) => {
  return (
    <>
      {completeWebNavigationLinks
        .filter((item) => item.label === 'Investors')
        .map(
          ({ label: linkLabel, mainIcon: MainIcon, children, Icon, href }) => {
            return (
              <NavigationMenuItem
                key={linkLabel}
                value={linkLabel}
                className='hidden min-[1260px]:flex'
                onClick={() => {
                  setIsSubnet(false);
                  setIsInvestors(true);
                }}
                onMouseOver={() => {
                  setIsSubnet(false);
                  setIsInvestors(true);
                }}
              >
                <NavigationMenuTrigger className='rounded-none bg-transparent pb-3 text-sm font-[400] hover:underline'>
                  <Link href={href}>
                    {MainIcon ? (
                      <MainIcon className='-mb-0.5' />
                    ) : (
                      <Text
                        className={
                          label === linkLabel ? 'opacity-100' : 'opacity-60'
                        }
                        level='sm'
                      >
                        {linkLabel}
                      </Text>
                    )}
                  </Link>
                </NavigationMenuTrigger>
                <NavigationMenuContent className={cn(label === '' && 'hidden')}>
                  <Link href={href} className='w-full'>
                    <Button
                      variant='empty'
                      className='group w-full justify-start border-b border-neutral-900 !py-8 px-8 hover:bg-neutral-800'
                    >
                      <Text
                        level='lg'
                        className='flex items-center gap-2 text-white'
                      >
                        <Icon className='text-ocean' size={16} />
                        {MainIcon ? <MainIcon /> : linkLabel}{' '}
                      </Text>
                    </Button>
                  </Link>

                  <ul className='bg-background grid w-[500px] gap-x-5 gap-y-1 p-5'>
                    {children?.map(
                      ({
                        label: itemLabel,
                        groupTitle: itemGroupTitle,
                        href: itemHref,
                      }) => (
                        <Link
                          key={`${itemGroupTitle}-${itemLabel}`}
                          href={itemHref}
                          className='truncate rounded-md px-3 py-2 text-sm text-white hover:bg-[#202020] hover:text-white'
                        >
                          {itemLabel}
                        </Link>
                      )
                    )}
                  </ul>
                </NavigationMenuContent>
              </NavigationMenuItem>
            );
          }
        )}
    </>
  );
};

import { use<PERSON><PERSON>back, useContext, useEffect, useMemo, useState } from 'react';
import { useKeyDown } from '@react-hooks-library/core';
import { Search } from 'lucide-react';
import type { IconType } from 'react-icons';
import {
  BiCheckDouble,
  BiCoin,
  BiCubeAlt,
  BiHome,
  BiMoney,
  BiNetworkChart,
  BiSolidArrowToRight,
  BiWallet,
} from 'react-icons/bi';
import { CgArrowsExchange } from 'react-icons/cg';
import { FaRegCalendarAlt } from 'react-icons/fa';
import { GoIssueOpened } from 'react-icons/go';
import { appRoutes, createUrl } from '../../../../lib';
import {
  ExchangeAddressMapping,
  type ExchangeAddressMappingType,
} from '../../../../lib/exchange-address-mapping';
import { ModalWrapper } from '../../../modal-wrapper';
import { NavigationContext } from '../../navigation-context';
import { type SearchGroupProps, SearchPopover } from './search-popover';

const availablePages = [
  { href: appRoutes.base.landing, icon: BiHome },
  { href: appRoutes.subnets.home, icon: BiNetworkChart },
  { href: appRoutes.blockchain.blocks, icon: BiCubeAlt },
  { href: appRoutes.blockchain.transfer, icon: BiMoney },
  { href: appRoutes.blockchain.delegation, icon: BiSolidArrowToRight },
  { href: appRoutes.blockchain.accounts, icon: BiWallet },
  { href: appRoutes.blockchain.extrinsic, icon: FaRegCalendarAlt },
  { href: appRoutes.blockchain.event, icon: GoIssueOpened },
  { href: appRoutes.blockchain.tokenomics, icon: BiCoin },
  { href: appRoutes.validator.home, icon: BiCheckDouble },
  { href: appRoutes.analytics.exchanges, icon: CgArrowsExchange },
];

export type SearchItem = {
  title: string;
  path: string;
  href: string;
  filter?: string;
  type?: string;
  icon?: IconType;
};

export const SearchButton = () => {
  const navigationContext = useContext(NavigationContext);

  const [modalOpen, setModalOpen] = useState(false);
  const [userInput, setUserInput] = useState('');
  const latestBlockNumber = navigationContext?.latestBlock ?? ********;
  const [recentlySearched, setRecentlySearched] = useState<SearchItem[]>([]);

  const inputFields = ['input', 'textarea'];

  useKeyDown(['Shift', 'F'], (e) => {
    if (
      e.key === 'F' &&
      e.shiftKey &&
      !modalOpen &&
      !(
        e.target instanceof Element &&
        inputFields.includes(e.target.tagName.toLowerCase())
      )
    ) {
      setModalOpen(true);
      e.preventDefault();
    }
  });

  useKeyDown(['Escape'], (e) => {
    if (modalOpen) {
      setModalOpen(false);
      e.preventDefault();
    }
  });

  const isValidHash = useCallback((input: string): boolean => {
    const hashRegex = /^0x[a-fA-F0-9]{64}$/;
    return hashRegex.test(input);
  }, []);

  const isBase58 = useCallback((input: string): boolean => {
    const base58Regex = /^[1-9A-HJ-NP-Za-km-z]{25,}$/;
    const testResult = base58Regex.test(input);
    return testResult;
  }, []);

  const isSubnet = useCallback(
    (input: string): boolean => {
      const subnetRegex = /^(?:subnet )?(?:\d{1,2})$/i;
      const subnetNumber = subnetRegex.exec(input);

      if (subnetNumber) {
        const subnet = Number(subnetNumber[1]);
        if (subnet > 0 && subnet <= 70) {
          return true;
        }
      }

      if (
        navigationContext?.subnetData?.find((s) =>
          s.subnet_name.toLowerCase().includes(input.toLowerCase())
        )
      ) {
        return true;
      }

      return false;
    },
    [navigationContext?.subnetData]
  );

  const isBlockNumber = useCallback(
    (input: string): boolean => {
      const digitRegex = /^\d+$/;

      if (digitRegex.test(input)) {
        const number = Number(input);
        return number >= 0 && number <= latestBlockNumber;
      }

      return false;
    },
    [latestBlockNumber]
  );

  const isCustomFormat = useCallback((input: string): boolean => {
    const customFormatRegex = /^\d+-\d+$/;
    return customFormatRegex.test(input);
  }, []);

  const currentValidator = useMemo(
    () =>
      navigationContext?.validatorData?.find(
        (d) => d.hotkey.ss58 === userInput
      ) ?? null,
    [navigationContext?.validatorData, userInput]
  );

  const currentValidatorName = useMemo(
    () =>
      navigationContext?.validatorData?.find((d) =>
        d.name?.toLowerCase().includes(userInput.toLowerCase())
      ) ?? null,
    [navigationContext?.validatorData, userInput]
  );

  const isValidator = useCallback(
    (input: string): boolean => {
      const validatorRegex = /^[1-9A-HJ-NP-Za-km-z]{25,}$/;
      const isValidatorResult =
        (validatorRegex.test(input) && currentValidator) ||
        currentValidatorName;
      return Boolean(isValidatorResult);
    },
    [currentValidator, currentValidatorName]
  );

  const getExchange = useCallback((input: string) => {
    const isExchange = Object.values(ExchangeAddressMapping).filter(
      (exchange) => {
        return exchange.name.toLowerCase().includes(input.toLowerCase());
      }
    );

    return isExchange;
  }, []);

  const getSubnetInfo = useCallback(
    (input: string) => {
      const subnetRegex = /^(?:subnet )?(?:\d{1,2})$/i;
      const subnetNumber = subnetRegex.exec(input);

      if (subnetNumber) {
        const subnet = Number(subnetNumber[1]);

        return navigationContext?.subnetData?.find((s) => s.netuid === subnet);
      }

      return navigationContext?.subnetData?.find((s) =>
        s.subnet_name.toLowerCase().includes(input.toLowerCase())
      );
    },
    [navigationContext?.subnetData]
  );

  const getKeyByValue = useCallback(
    (
      obj: ExchangeAddressMappingType,
      value: string,
      index: number
    ): string | undefined => {
      const entries = Object.entries(obj).filter(([, val]) =>
        val.name.toLowerCase().includes(value.toLowerCase())
      );

      const allKeys = Object.keys(obj);

      return entries[index]?.[0] ?? allKeys[index];
    },
    []
  );

  const [searchGroups, setSearchGroups] = useState<SearchGroupProps[]>([
    {
      heading: 'Recent Searches',
      items: recentlySearched.slice(0, 3),
    },
    {
      heading: 'Pages',
      items: availablePages.map((page) => ({
        title: page.href,
        path: page.href,
        href: createUrl(navigationContext?.websiteUrl ?? '', page.href),
        type: 'page',
        icon: page.icon,
      })),
    },
    ...(isCustomFormat(userInput)
      ? [
          {
            heading: 'Extrinsic',
            items: [
              {
                title: userInput,
                path: `/extrinsic/${userInput}`,
                href: createUrl(
                  navigationContext?.websiteUrl ?? '',
                  `/extrinsic/${userInput}`
                ),
                type: 'extrinsic',
              },
            ],
          },
          {
            heading: 'Event',
            items: [
              {
                title: userInput,
                path: `/event/${userInput}`,
                href: createUrl(
                  navigationContext?.websiteUrl ?? '',
                  `/event/${userInput}`
                ),
                type: 'event',
              },
            ],
          },
        ]
      : []),
  ]);

  const handleSearchChange = useCallback(
    (value: string) => {
      const temp: SearchGroupProps[] = [
        {
          heading: 'Recent Searches',
          items: recentlySearched.slice(0, 3),
        },
        {
          heading: 'Pages',
          items: availablePages
            .filter((item) => item.href.includes(value))
            .map((page) => ({
              title: page.href,
              path: page.href,
              href: createUrl(navigationContext?.websiteUrl ?? '', page.href),
              type: 'page',
              icon: page.icon,
            })),
        },
      ];

      if (isBlockNumber(value)) {
        temp.push({
          heading: 'Block',
          items: [
            {
              title: value,
              path: `/block/${value}/extrinsics`,
              href: createUrl(
                navigationContext?.websiteUrl ?? '',
                `/block/${value}/extrinsics`
              ),
              type: 'block',
            },
          ],
        });
      }

      if (isBase58(value)) {
        temp.push({
          heading: 'Address',
          items: [
            {
              title: value,
              path: `/account/${value}`,
              href: createUrl(
                navigationContext?.websiteUrl ?? '',
                `/account/${value}`
              ),
              type: 'address',
            },
          ],
        });
      }

      if (isSubnet(value)) {
        temp.push({
          heading: 'Subnet',
          items: [
            {
              title: getSubnetInfo(value)?.subnet_name ?? '',
              path: `/subnets/${getSubnetInfo(value)?.netuid}`,
              href: createUrl(
                navigationContext?.websiteUrl ?? '',
                `/subnets/${getSubnetInfo(value)?.netuid}`
              ),
              filter: value,
              type: 'subnet',
            },
          ],
        });
      }

      if (getExchange(value).length > 0) {
        temp.push({
          heading: 'Exchange',
          items: getExchange(value).map((exchange, index) => ({
            title: getKeyByValue(ExchangeAddressMapping, value, index) ?? '',
            path: `/account/${getKeyByValue(
              ExchangeAddressMapping,
              exchange.name,
              index
            )}`,
            href: createUrl(
              navigationContext?.websiteUrl ?? '',
              `/account/${getKeyByValue(
                ExchangeAddressMapping,
                exchange.name,
                index
              )}`
            ),
            filter: exchange.name,
            type: 'address',
          })),
        });
      }

      if (isValidator(value)) {
        const validatorAddress =
          currentValidator?.hotkey.ss58 ?? currentValidatorName?.hotkey.ss58;
        temp.push({
          heading: 'Validator',
          items: [
            {
              title: validatorAddress ?? '',
              path: `/validators/${validatorAddress}`,
              href: createUrl(
                navigationContext?.websiteUrl ?? '',
                `/validators/${validatorAddress}`
              ),
              type: 'address',
            },
          ],
        });
      }

      if (isValidHash(value)) {
        temp.push({
          heading: 'Hash',
          items: [
            {
              title: value,
              path: `/hash/${value}`,
              href: createUrl(
                navigationContext?.websiteUrl ?? '',
                `/hash/${value}`
              ),
              type: 'address',
            },
          ],
        });
      }

      if (isCustomFormat(value)) {
        temp.push(
          ...[
            {
              heading: 'Extrinsic',
              items: [
                {
                  title: value,
                  path: `/extrinsic/${value}`,
                  href: createUrl(
                    navigationContext?.websiteUrl ?? '',
                    `/extrinsic/${value}`
                  ),
                  type: 'extrinsic',
                },
              ],
            },
            {
              heading: 'Event',
              items: [
                {
                  title: value,
                  path: `/event/${value}`,
                  href: createUrl(
                    navigationContext?.websiteUrl ?? '',
                    `/event/${value}`
                  ),
                  type: 'event',
                },
              ],
            },
          ]
        );
      }

      setSearchGroups(temp);
    },
    [
      recentlySearched,
      isBlockNumber,
      isBase58,
      isSubnet,
      getExchange,
      isValidator,
      isValidHash,
      isCustomFormat,
      getSubnetInfo,
      getKeyByValue,
      currentValidator?.hotkey.ss58,
      currentValidatorName?.hotkey.ss58,
      navigationContext?.websiteUrl,
    ]
  );

  useEffect(() => {
    handleSearchChange(userInput);
  }, [userInput, handleSearchChange]);

  return (
    <div className='flex-1 justify-end gap-1.5 px-4 lg:justify-start'>
      <Search
        size={16}
        className='cursor-pointer'
        onClick={() => {
          setModalOpen(true);
        }}
      />
      <ModalWrapper
        showModal={modalOpen}
        setShowModal={setModalOpen}
        contentClassName='p-0 border-none lg:w-1/2'
      >
        <SearchPopover
          userInput={userInput}
          setUserInput={setUserInput}
          searchGroups={searchGroups}
          setModalOpen={setModalOpen}
          setRecentlySearched={setRecentlySearched}
        />
      </ModalWrapper>
    </div>
  );
};

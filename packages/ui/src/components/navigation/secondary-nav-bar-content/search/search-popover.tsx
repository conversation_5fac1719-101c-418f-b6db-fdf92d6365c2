import { Fragment, type SetStateAction, memo } from 'react';
import { useRouter } from 'next/navigation';
import type { IconType } from 'react-icons';
import { cn } from '../../../../lib';
import { AddressFormatter } from '../../../address-formatter';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '../../../command';
import { Link } from '../../../link';
import type { SearchItem } from './search-button';

export interface SearchGroupProps {
  heading: string;
  items: {
    title: string;
    path: string;
    href: string;
    filter?: string;
    type?: string;
    icon?: IconType;
  }[];
  setModalOpen?: (open: boolean) => void;
  setRecentlySearched?: (value: SetStateAction<SearchItem[]>) => void;
}

export interface SearchPopoverProps {
  userInput: string;
  searchGroups: SearchGroupProps[];
  setUserInput: (value: SetStateAction<string>) => void;
  setModalOpen?: (open: boolean) => void;
  setRecentlySearched?: (value: SetStateAction<SearchItem[]>) => void;
}

export const SearchPopover = memo(
  ({
    userInput,
    setUserInput,
    searchGroups,
    setModalOpen,
    setRecentlySearched,
  }: SearchPopoverProps) => {
    return (
      <Command
        filter={() => {
          return 1;
        }}
      >
        <CommandInput
          value={userInput}
          onValueChange={(e) => {
            setUserInput(e);
          }}
          placeholder='Search for Addresses, Miners, Txs etc...'
        />
        <CommandList>
          <CommandEmpty>No results found.</CommandEmpty>
          {searchGroups.map((group, index) => (
            <Fragment key={group.heading}>
              {index > 0 && <CommandSeparator />}
              <SearchGroup
                setRecentlySearched={setRecentlySearched}
                heading={group.heading}
                items={group.items}
                setModalOpen={setModalOpen}
              />
            </Fragment>
          ))}
        </CommandList>
      </Command>
    );
  }
);

SearchPopover.displayName = 'SearchPopover';

export const SearchGroup: React.FC<SearchGroupProps> = ({
  heading,
  items,
  setModalOpen,
  setRecentlySearched,
}) => {
  const router = useRouter();

  return (
    <CommandGroup className='p-4' heading={heading}>
      {items.map((item) => (
        <CommandItem
          value={item.filter || item.title}
          className={cn('rounded-lg p-4')}
          onSelect={() => {
            const searchItem: SearchItem = {
              title: item.title,
              path: item.path,
              href: item.href,
              filter: item.filter,
              type: item.type,
              icon: item.icon,
            };
            setRecentlySearched?.((prevSearched: SearchItem[]) => {
              const itemExists = prevSearched.some(
                (prevItem) => prevItem.href === searchItem.href
              );

              if (!itemExists) {
                return [...prevSearched, searchItem];
              }

              return prevSearched;
            });

            router.push(item.href);
            if (setModalOpen) setModalOpen(false);
          }}
          key={`${item.title}-${item.path}`}
        >
          <Link
            className='flex h-full w-full items-center justify-between'
            href={item.href}
          >
            <div className='flex items-center gap-3'>
              {item.type === 'page' && item.icon ? (
                <div className='bg-ocean/10 text-ocean rounded-md p-1'>
                  <item.icon size={15} />
                </div>
              ) : null}
              {item.type === 'address' ? (
                <AddressFormatter uid={item.title} noCopy />
              ) : (
                item.title
              )}
            </div>
            <div className='max-w-[7rem] truncate text-xs opacity-20'>
              {item.path}
            </div>
          </Link>
        </CommandItem>
      ))}
    </CommandGroup>
  );
};

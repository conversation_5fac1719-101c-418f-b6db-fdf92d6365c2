import type React from 'react';
import {
  AreaChart,
  Blocks,
  Box,
  CircleDollarSign,
  type LucideIcon,
  Network,
} from 'lucide-react';
import type { Route } from 'next';
import { appRoutes } from '../../../lib';

export const NavigationSectionTitles = {
  Subnets: 'Subnets',
  Blockchain: 'Blockchain',
  Validators: 'Validators',
  Analytics: 'Analytics',
  Investors: 'Investors',
};

export enum BlockchainGroupTitles {
  Substrate = 'Substrate',
  Evm = 'EVM',
  Core = 'Core',
  Dtao = 'Dtao',
}

export enum AnalyticsTitles {
  Price = 'Price',
  Transactions = 'Transactions',
  Subnets = 'Subnets & Chain',
}

export type GroupedNavigationItem = {
  label: string;
  href: Route;
  groupTitle?: string;
};

export type NavigationLink = {
  label: string;
  mainIcon?: (props: React.SVGAttributes<SVGElement>) => React.JSX.Element;
  Icon: LucideIcon;
  href: Route;
  children: GroupedNavigationItem[] | null;
};

export const websiteNavigationLinks: NavigationLink[] = [
  {
    label: NavigationSectionTitles.Subnets,
    Icon: Blocks,
    href: appRoutes.subnets.home,
    children: [],
  },
  {
    label: NavigationSectionTitles.Blockchain,
    Icon: Box,
    href: appRoutes.blockchain.blocks,
    children: [
      {
        label: 'Blocks',
        href: appRoutes.blockchain.blocks,
        groupTitle: BlockchainGroupTitles.Substrate,
      },
      {
        label: 'Transfers',
        href: appRoutes.blockchain.transfer,
        groupTitle: BlockchainGroupTitles.Substrate,
      },
      {
        label: 'Delegation',
        href: appRoutes.blockchain.delegation,
        groupTitle: BlockchainGroupTitles.Substrate,
      },
      {
        label: 'Accounts',
        href: appRoutes.blockchain.accounts,
        groupTitle: BlockchainGroupTitles.Substrate,
      },
      {
        label: 'Extrinsics',
        href: appRoutes.blockchain.extrinsic,
        groupTitle: BlockchainGroupTitles.Substrate,
      },
      {
        label: 'Events',
        href: appRoutes.blockchain.event,
        groupTitle: BlockchainGroupTitles.Substrate,
      },
      {
        label: 'Blocks',
        href: appRoutes.evm.blocks,
        groupTitle: BlockchainGroupTitles.Evm,
      },
      {
        label: 'Transactions',
        href: appRoutes.evm.transaction,
        groupTitle: BlockchainGroupTitles.Evm,
      },
      {
        label: 'Contracts',
        href: appRoutes.evm.contracts,
        groupTitle: BlockchainGroupTitles.Evm,
      },
      {
        label: 'EVM Explorer (beta)',
        href: 'https://evm.taostats.io',
        groupTitle: BlockchainGroupTitles.Evm,
      },
      {
        label: 'Runtime',
        href: appRoutes.blockchain.runtime,
        groupTitle: BlockchainGroupTitles.Core,
      },
      {
        label: 'Sudo',
        href: appRoutes.blockchain.sudo,
        groupTitle: BlockchainGroupTitles.Core,
      },
    ],
  },
  {
    label: NavigationSectionTitles.Validators,
    href: appRoutes.validator.home,
    Icon: Network,
    children: [{ label: 'Yield', href: appRoutes.yield }],
  },
  {
    label: NavigationSectionTitles.Analytics,
    Icon: AreaChart,
    href: appRoutes.analytics.home,
    children: [
      {
        label: 'Staking vs Price History',
        href: appRoutes.analytics.chartDetails('stats'),
        groupTitle: AnalyticsTitles.Price,
      },
      {
        label: 'Daily Price Changes',
        href: appRoutes.analytics.chartDetails('price'),
        groupTitle: AnalyticsTitles.Price,
      },
      {
        label: 'Recent Exchange Transactions',
        href: appRoutes.analytics.chartDetails('exchanges'),
        groupTitle: AnalyticsTitles.Transactions,
      },
      {
        label: 'New Account Balances',
        href: appRoutes.analytics.chartDetails('accounts'),
        groupTitle: AnalyticsTitles.Transactions,
      },
      {
        label: 'Top 50 Bittensor Account Balances',
        href: appRoutes.analytics.chartDetails('balances'),
        groupTitle: AnalyticsTitles.Transactions,
      },
      {
        label: 'Recent Largest Transactions',
        href: appRoutes.analytics.chartDetails('transactions'),
        groupTitle: AnalyticsTitles.Transactions,
      },
      {
        label: 'Realtime Transaction Volume',
        href: appRoutes.analytics.chartDetails('latest'),
        groupTitle: AnalyticsTitles.Transactions,
      },
      {
        label: 'Chain Block Production',
        href: appRoutes.analytics.chartDetails('blocks'),
        groupTitle: AnalyticsTitles.Subnets,
      },
      {
        label: 'Miner Age vs. Incentive',
        href: appRoutes.analytics.chartDetails('incentive'),
        groupTitle: AnalyticsTitles.Subnets,
      },
      {
        label: '24h Subnet Emissions vs. Recycled',
        href: appRoutes.analytics.chartDetails('emissions'),
        groupTitle: AnalyticsTitles.Subnets,
      },
      {
        label: 'Subnet Growth',
        href: appRoutes.analytics.chartDetails('subnets'),
        groupTitle: AnalyticsTitles.Subnets,
      },
      {
        label: 'Extrinsics Outcomes',
        href: appRoutes.analytics.chartDetails('extrinsic'),
        groupTitle: AnalyticsTitles.Subnets,
      },
      {
        label: 'Validator Weights Deviation',
        href: appRoutes.analytics.deviation,
        groupTitle: AnalyticsTitles.Subnets,
      },
      {
        label: 'Daily Registration Recycle',
        href: appRoutes.analytics.chartDetails('recycle'),
        groupTitle: AnalyticsTitles.Subnets,
      },
      {
        label: 'Historical Root',
        href: appRoutes.analytics.historical,
        groupTitle: AnalyticsTitles.Subnets,
      },
    ],
  },
  {
    label: NavigationSectionTitles.Investors,
    Icon: CircleDollarSign,
    href: appRoutes.blockchain.tokenomics,
    children: [
      {
        label: 'Yield',
        href: appRoutes.yield,
      },
      {
        label: 'Tokenomics',
        href: appRoutes.blockchain.tokenomics,
      },
      {
        label: 'Where to Buy',
        href: appRoutes.buy,
      },
    ],
  },
];

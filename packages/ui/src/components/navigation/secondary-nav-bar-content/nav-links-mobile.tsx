"use client";

import { useContext, useEffect, useState } from "react";
import { usePathname } from "next/navigation";
import { cn } from "../../../lib/utils";
import {
	Accordion,
	AccordionContent,
	AccordionItem,
	AccordionTrigger,
} from "../../accordion";
import { Button } from "../../button";
import { Link } from "../../link";
import { Separator } from "../../separator";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Sheet<PERSON>eader, SheetTrigger } from "../../sheet";
import { TaostatsLogo } from "../../taostats";
import { Text } from "../../text";
import { TwoLinesIcon } from "../../two-lines-icon";
import { NavigationContext } from "../navigation-context";
import type { DashboardList} from "./dashboard-lists";
import type { NavigationLink } from "./navigation-links";
import { websiteNavigationLinks, NavigationSectionTitles } from "./navigation-links";

export default function NavLinksMobile({
	completeWebNavigationLinks,
	completeDashNavigationLinks,
}: {
	completeWebNavigationLinks: NavigationLink[];
	completeDashNavigationLinks: DashboardList;
}) {
	const navigationContext = useContext(NavigationContext);

	const [open, setOpen] = useState<boolean>(false);
	const pathname = usePathname();

	const [currentLabel, setCurrentLabel] = useState<string>("");
	const [currentLink, setCurrentLink] = useState<string>("");

	useEffect(() => {
		setCurrentLink(pathname);
		if (pathname === "/") {
			setCurrentLabel("");
			return;
		}

		for (const link of websiteNavigationLinks) {
			if (link.href === pathname) {
				setCurrentLabel(link.label);
				return;
			}

			for (const child of link.children ?? []) {
				if (child.href === pathname) {
					setCurrentLabel(link.label);
					return;
				}
			}
		}
	}, [pathname]);

	return (
		<Sheet open={open} onOpenChange={(e) => { setOpen(e); }}>
			<SheetTrigger asChild className="flex min-[1260px]:hidden">
				<Button
					variant="secondary"
					size="sm"
					className="sm:-mt-1 h-10 sm:ml-1"
				>
					<TwoLinesIcon />
				</Button>
			</SheetTrigger>
			<SheetContent className="h-full overflow-auto border-dark-gray bg-[#121212]">
				<SheetHeader>
					<Link href="/">
						<TaostatsLogo width={101} height={40} />
					</Link>
				</SheetHeader>
				<Accordion
					type="single"
					collapsible
					className="mx-auto flex w-full max-w-sm flex-col gap-2 py-8"
					value={currentLabel}
					onValueChange={(e) => { setCurrentLabel(e); }}
				>
					{completeWebNavigationLinks.map(({ label, mainIcon: MainIcon, href, children }) => (
						<AccordionItem
							key={label}
							value={label}
							className="border-dark-gray"
						>
							<AccordionTrigger className="p-0 hover:no-underline">
								<Button
									variant="link"
									asChild
									className="rounded-none text-base transition-colors hover:border-b hover:no-underline"
									onClick={() => { setOpen(false); }}
								>
									<Link href={href}>
										{MainIcon ? (
											<MainIcon className="-mb-0.5 -ml-1.5" />
										) : (
											<Text level="sm" className="text-white">{label}</Text>
										)}
									</Link>
								</Button>
							</AccordionTrigger>
							<AccordionContent>
								<div className="flex flex-col px-4">
									{Object.entries(
										children?.reduce(
											(acc: Record<string, typeof children>, curr) => {
												const group = curr.groupTitle ?? "";
												acc[group] ??= [];
												acc[group].push(curr);
												return acc;
											},
											{}
										) ?? {}
									).map(([groupTitle, items]) => (
										<div key={groupTitle} className="mb-4">
											{groupTitle && groupTitle !== "Validators" ? (
												<Text
													level="sm"
													className="mb-1 font-medium text-neutral-400"
												>
													{groupTitle}
												</Text>
											) : null}
											<div className="flex flex-col">
												{items.map(
													({ label: itemLabel, href: itemHref }) => (
														<div key={`${groupTitle}-${itemLabel}`}>
															<Button
																key={itemLabel}
																variant="link"
																size="sm"
																asChild
																className={cn(
																	"w-max rounded-none px-2 hover:no-underline",
																	currentLink === itemHref
																		? "border-[#00DBBC] border-b text-[#00DBBC]"
																		: "text-[#8a8a8a] transition-colors hover:border-b"
																)}
																onClick={() => { setOpen(false); }}
															>
																<Link href={itemHref}>
																	{label === NavigationSectionTitles.Subnets ||
																		(label === NavigationSectionTitles.Validators && groupTitle === "Validators") ? (
																		<>
																			<span className="w-6 text-end font-mono text-smt">{`${itemLabel.split(":")[0]
																				}:`}</span>
																			{itemLabel.split(":")[1]}
																		</>
																	) : (
																		itemLabel
																	)}
																</Link>
															</Button>
															{label === NavigationSectionTitles.Validators && groupTitle !== "Validators" && (
																<Separator className="my-1" />
															)}
														</div>
													)
												)}
											</div>
										</div>
									))}
								</div>
							</AccordionContent>
						</AccordionItem>
					))}
					<AccordionItem value="dashboard" className="border-dark-gray">
						<AccordionTrigger className="p-0 hover:no-underline">
							<Button
								variant="link"
								asChild
								className="rounded-none text-base transition-colors hover:border-b hover:no-underline"
								onClick={() => { setOpen(false); }}
							>
								<Link href={navigationContext?.dashboardUrl ?? "https://dash.taostats.io"}><Text level="sm" className="text-white">Dashboard</Text></Link>
							</Button>
						</AccordionTrigger>
						<AccordionContent>
							<div className="flex flex-col">
								{completeDashNavigationLinks.dashboardItems.map((item) => (
									<Button
										key={item.label}
										variant="link"
										size="sm"
										asChild
										className={cn(
											"w-max rounded-none px-2 hover:no-underline",
											currentLink === item.href
												? "border-[#00DBBC] border-b text-[#00DBBC]"
												: "text-[#8a8a8a] transition-colors hover:border-b"
										)}
										onClick={() => { setOpen(false); }}
									>
										<Link
											key={item.label}
											href={item.href}
											className="flex flex-row items-center gap-4 py-4 px-4"
										>
											<item.Icon size={24} className="opacity-60" />
											{item.label}
										</Link>
									</Button>
								))}
								<Text
									level="sm"
									className="mb-1 font-medium text-neutral-400 pt-2 px-4"
								>
									Developers
								</Text>
								{completeDashNavigationLinks.developerItems.map((item) => (
									<Link
										key={item.label}
										href={item.href}
										className="flex flex-row items-center gap-1 py-2 px-4 hover:bg-[#111111]"
									>
										<item.Icon size={24} className="opacity-60" />
										<Text level="sm" className="font-medium text-gray-400">
											{item.label}
										</Text>
									</Link>
								))}
							</div>
						</AccordionContent>
					</AccordionItem>
				</Accordion>
			</SheetContent>
		</Sheet>
	);
}

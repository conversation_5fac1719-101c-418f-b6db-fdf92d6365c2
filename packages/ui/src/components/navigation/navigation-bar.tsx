import type { DtaoValidatorLatest } from '@repo/types/website-api-types';
import { MetadataBarContent } from './metadata-bar-content/metadata-bar-content';
import type { NavbarProps } from './navigation-context';
import { NavigationContext } from './navigation-context';
import { SecondaryNavbarContent } from './secondary-nav-bar-content/secondary-nav-bar-content';

export function NavigationBar(props: NavbarProps) {
  const getName = (validator: DtaoValidatorLatest) => {
    const match = props.validatorMetadata?.find(
      (metadata) => metadata.address === validator.hotkey.ss58
    );

    return match?.name || validator.name;
  };

  const mappedProps: NavbarProps = {
    ...props,
    validatorData:
      props.validatorData?.map((validator) => ({
        ...validator,
        name: getName(validator),
      })) || null,
  };

  return (
    <NavigationContext.Provider value={mappedProps}>
      <div className='sticky top-0 z-50 flex w-full flex-col'>
        <div className='border-dark-gray text-gray z-10 flex flex-row items-center justify-between gap-3 border-b bg-black/60 px-2 py-2 backdrop-blur-md sm:px-5 lg:px-6'>
          <MetadataBarContent />
        </div>
        <SecondaryNavbarContent />
      </div>
    </NavigationContext.Provider>
  );
}

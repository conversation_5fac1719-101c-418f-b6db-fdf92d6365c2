'use client';

import { createContext } from 'react';
import type { TaoStatsDashboardProfile } from '@repo/types/dashboard-api-types';
import type {
  DtaoValidatorLatest,
  PriceAPI,
  SubnetIdentity,
  ValidatorMetadata,
  Identity,
  SubnetPool,
} from '@repo/types/website-api-types';

export type MetadataBarContentProps = {
  priceData: PriceAPI['data'][number] | undefined;
  showBlockData?: boolean | null;
  latestBlock?: number;
  signOut: () => Promise<void>;
};

export type SecondaryNavbarContentProps = {
  subnetData: SubnetIdentity[] | null;
  dashboardUrl: string;
  websiteUrl: string;
  validatorData: DtaoValidatorLatest[] | null;
  validatorMetadata: ValidatorMetadata[] | null;
  validatorIdentityData: Identity[] | null;
  subnetPoolData: SubnetPool[];
};

export type WalletConnectorProps = {
  connectWallet: () => void;
  disconnectWallet: () => void;
  walletConnected: boolean;
};

export type NavbarProps = SecondaryNavbarContentProps &
  MetadataBarContentProps &
  WalletConnectorProps & {
    userProfile: TaoStatsDashboardProfile | null;
  };

export const NavigationContext = createContext<NavbarProps | null>(null);

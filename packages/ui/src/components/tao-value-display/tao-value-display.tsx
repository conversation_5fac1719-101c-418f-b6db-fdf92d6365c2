import type { ComponentProps } from 'react';
import { cn } from '../../lib';
import { BigSmallValueDisplay } from '../big-small-value-display';
import { TaoIcon } from '../tao-icon';
import { Text } from '../text';

export interface TaoValueDisplayProps {
  valueTextLevel?: ComponentProps<typeof Text>['level'];
  valueClassName?: string;
  value: number;
  valueFormattingOptions?: Intl.NumberFormatOptions;
  iconClassName?: string;
  symbol?: string;
  colourClassName?: string;
  areDecimalsSmall?: boolean;
  className?: string;
}

// Pass the alpha symobol to the symbol prop if you want to show the symbol val
// Otherwise, the TaoIcon will be shown
export const TaoOrAlphaValueDisplay = ({
  valueTextLevel = 'displayMedium',
  valueClassName = '',
  value,
  valueFormattingOptions = {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  },
  iconClassName,
  symbol,
  colourClassName,
  areDecimalsSmall = false,
  className,
}: TaoValueDisplayProps) => {
  return (
    <div className={cn('flex items-baseline gap-1', className)}>
      <div>
        {symbol ? (
          <Text
            level={valueTextLevel}
            className={cn(
              'transition-colors duration-500',
              valueClassName,
              colourClassName
            )}
          >
            {symbol}
          </Text>
        ) : (
          <TaoIcon
            className={cn(
              'transition-colors duration-500',
              valueClassName,
              iconClassName,
              colourClassName
            )}
          />
        )}
      </div>
      <BigSmallValueDisplay
        textLevel={valueTextLevel}
        className={cn(valueClassName, colourClassName)}
        value={value}
        valueFormattingOptions={valueFormattingOptions}
        areDecimalsSmall={areDecimalsSmall}
      />
    </div>
  );
};

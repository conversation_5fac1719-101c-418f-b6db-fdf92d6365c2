import { useCallback } from 'react';
import type { Dispatch, PropsWithChildren, SetStateAction } from 'react';
import { useRouter } from 'next/navigation';
import { Drawer } from 'vaul';
import { useWindowSize } from '../../lib/hooks/use-window-size';
import { cn } from '../../lib/utils';
import { Button } from '../button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../dialog-shadcn';
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from '../sheet';

interface ModalWrapperProps {
  title?: string;
  titleClassName?: string;
  description?: string;
  descriptionClassName?: string;
  contentClassName?: string;
  useVaul?: boolean;
  useSheet?: boolean;
  showModal?: boolean;
  setShowModal?: Dispatch<SetStateAction<boolean>>;
  onClose?: () => void;
  preventDefaultClose?: boolean;
  sheetFooterCancelAction?: React.ReactNode;
}

export function ModalWrapper({
  title,
  titleClassName,
  description,
  descriptionClassName,
  contentClassName,
  children,
  useVaul,
  useSheet,
  showModal,
  setShowModal,
  onClose,
  preventDefaultClose,
  sheetFooterCancelAction,
}: PropsWithChildren<ModalWrapperProps>) {
  const router = useRouter();
  const { isMobile } = useWindowSize();
  const isOpen = setShowModal ? showModal : true;

  const handleOnOpenChange = useCallback(
    ({ dragged }: { dragged?: boolean } = {}) => {
      if (preventDefaultClose && !dragged) {
        return;
      }
      // fire onClose event if provided
      if (onClose) {
        onClose();
      }

      // if setShowModal is defined, use it to close modal
      if (setShowModal) {
        setShowModal(false);
        // else, this is intercepting route @modal
      } else {
        router.back();
      }
    },
    [onClose, preventDefaultClose, router, setShowModal]
  );

  if (isMobile || useVaul) {
    return (
      <Drawer.Root
        data-lenis-prevent
        shouldScaleBackground
        open={isOpen}
        onOpenChange={(open) => {
          if (!open) {
            handleOnOpenChange({ dragged: true });
          }
        }}
      >
        <Drawer.Overlay
          className='fixed inset-0 z-40 bg-black opacity-10 backdrop-blur'
          style={{ opacity: '70%' }}
        />
        <Drawer.Portal>
          <Drawer.Content
            className={cn(
              'bg-background fixed bottom-0 left-0 right-0 z-50 mt-24 h-full max-h-[90%] rounded-t-[10px] border-t px-2',
              contentClassName
            )}
          >
            <div className='sticky top-0 z-20 flex w-full items-center justify-center rounded-t-[10px] bg-inherit'>
              <div className='my-3 h-1 w-12 rounded-full bg-slate-300' />
            </div>
            {title ?? description ? (
              <DialogHeader>
                {title ? (
                  <DialogTitle className={cn(titleClassName)}>
                    {title}
                  </DialogTitle>
                ) : null}
                {description ? (
                  <DialogDescription className={cn(descriptionClassName)}>
                    {description}
                  </DialogDescription>
                ) : null}
              </DialogHeader>
            ) : null}
            {children}
          </Drawer.Content>
          <Drawer.Overlay />
        </Drawer.Portal>
      </Drawer.Root>
    );
  }

  if (useSheet) {
    return (
      <Sheet
        data-lenis-prevent
        open={isOpen}
        onOpenChange={(open) => {
          if (!open) {
            handleOnOpenChange();
          }
        }}
      >
        <SheetContent className={cn(contentClassName)}>
          {title ?? description ? (
            <SheetHeader>
              {title ? (
                <SheetTitle className={cn(titleClassName)}>{title}</SheetTitle>
              ) : null}
              {description ? (
                <SheetDescription className={cn(descriptionClassName)}>
                  {description}
                </SheetDescription>
              ) : null}
            </SheetHeader>
          ) : null}
          {children}
          {sheetFooterCancelAction ? (
            <SheetFooter>
              <SheetClose asChild>
                <Button type='submit'>Cancel</Button>
              </SheetClose>
            </SheetFooter>
          ) : null}
        </SheetContent>
      </Sheet>
    );
  }

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          handleOnOpenChange();
        }
      }}
    >
      <DialogContent className={cn(contentClassName)}>
        {title ?? description ? (
          <DialogHeader>
            {title ? (
              <DialogTitle className={cn(titleClassName)}>{title}</DialogTitle>
            ) : null}
            {description ? (
              <DialogDescription className={cn(descriptionClassName)}>
                {description}
              </DialogDescription>
            ) : null}
          </DialogHeader>
        ) : null}
        {children}
      </DialogContent>
    </Dialog>
  );
}

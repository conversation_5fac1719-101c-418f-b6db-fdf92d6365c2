import { memo } from 'react';
import { cn } from '../../lib';

const SpinnerUi = ({
  size = 'md',
  className,
}: {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}) => {
  const sizeClass =
    size === 'sm' ? 'h-4 w-4' : size === 'md' ? 'h-7 w-7' : 'h-10 w-10';
  return (
    <span
      className={cn(
        'block animate-spin rounded-full border-2 border-gray-300 border-t-[#00dbbc]',
        sizeClass,
        className
      )}
    />
  );
};

export const Spinner = memo(SpinnerUi);

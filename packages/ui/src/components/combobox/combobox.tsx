'use client';

import * as React from 'react';
import { memo } from 'react';
import { BiCheck, BiChevronDown } from 'react-icons/bi';
import { cn } from '../../lib';
import { Button } from '../button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '../command';
import { Popover, PopoverContent, PopoverTrigger } from '../popover';

interface ComboboxOption {
  value: string;
  label: string;
}

interface ComboboxProps {
  options: ComboboxOption[];
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  searchPlaceholder?: string;
  emptyMessage?: string;
  className?: string;
  canSearch?: boolean;
  disabled?: boolean;
  leftContent?: React.ReactNode;
}

export function Combobox({
  options,
  value: controlledValue,
  onChange,
  placeholder = 'Select option...',
  searchPlaceholder = 'Search...',
  emptyMessage = 'No option found.',
  className,
  canSearch = true,
  disabled = false,
  leftContent,
}: ComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [internalValue, setInternalValue] = React.useState('');

  const value = controlledValue ?? internalValue;
  const handleChange = onChange ?? setInternalValue;
  const valueToDisplay =
    (value
      ? options.find((option) => option.value === value)?.label
      : placeholder) ?? '';

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          aria-expanded={open}
          className={cn(
            'w-[300px] justify-between truncate',
            disabled && 'opacity-50',
            className
          )}
          disabled={disabled}
        >
          {leftContent ? (
            <div className='flex flex-row gap-2'>
              {leftContent}
              <ValueDisplay value={valueToDisplay} />
            </div>
          ) : (
            <ValueDisplay value={valueToDisplay} />
          )}

          <BiChevronDown className='ml-2 h-6 w-6 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className='z-[100] w-[300px] p-0'
        style={{
          width: 'var(--radix-popover-trigger-width)',
          pointerEvents: 'auto',
        }}
      >
        <Command
          filter={(_value, search, keywords = []) => {
            const extendValue = `${_value} ${keywords.join(' ')}`;
            if (extendValue.toLowerCase().includes(search.toLowerCase())) {
              return 1;
            }
            return 0;
          }}
        >
          {canSearch ? <CommandInput placeholder={searchPlaceholder} /> : null}
          <CommandList className='max-h-[200px] overflow-y-auto'>
            <CommandEmpty>{emptyMessage}</CommandEmpty>
            <CommandGroup>
              {options.map((option) => (
                <CommandItem
                  key={option.value}
                  value={option.value}
                  onSelect={(currentValue) => {
                    handleChange(currentValue === value ? '' : currentValue);
                    setOpen(false);
                  }}
                  keywords={[option.label]}
                >
                  {option.label}
                  <BiCheck
                    className={cn(
                      'ml-auto',
                      value === option.value ? 'opacity-100' : 'opacity-0'
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

const ValueDisplay = memo(function ValueDisplay({ value }: { value: string }) {
  return <span className='truncate text-sm'>{value}</span>;
});

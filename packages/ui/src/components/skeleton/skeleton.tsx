/* eslint-disable react/no-array-index-key -- allow for this file */

'use client';

import { cn } from '../../lib';

export default function Skeleton({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn(
        'w-full animate-pulse rounded-lg bg-gray-600/50',
        className
      )}
      {...props}
    />
  );
}

export function SkeletonChartWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className='flex flex-col gap-9 rounded-2xl border border-neutral-800 bg-neutral-900 p-4 shadow-lg md:p-12'>
      {children}
    </div>
  );
}

export function SkeletonTable({
  container,
  padding = true,
  length = 10,
  inline,
}: {
  container?: boolean;
  padding?: boolean;
  length?: number;
  inline?: boolean;
}) {
  if (container) {
    return (
      <div className={padding ? 'md:px-10' : undefined}>
        <div
          className={cn(
            'flex w-full flex-col overflow-x-auto rounded-2xl p-2 md:border md:border-neutral-900 md:bg-neutral-900 md:p-6 md:py-10',
            inline && 'border-none bg-transparent'
          )}
        >
          <SkeletonTableInner length={length} />
        </div>
      </div>
    );
  }
  return (
    <div className={padding ? 'md:px-10' : undefined}>
      <SkeletonTableInner length={length} />
    </div>
  );
}

function SkeletonTableInner({ length = 10 }: { length?: number }) {
  return (
    <div className={cn('flex flex-col gap-1')}>
      {Array.from({ length }).map((_, index) => (
        <Skeleton className='h-14 w-full' key={index} />
      ))}
    </div>
  );
}

export const SkeletonTableGroup = () => {
  return (
    <div className='grid grid-rows-2 gap-2 pb-0 md:px-10 lg:grid-cols-2 lg:grid-rows-1'>
      <div className='flex w-full flex-col gap-2 overflow-x-auto rounded-2xl p-2 md:border md:border-neutral-900 md:bg-neutral-900 md:p-6 md:py-10'>
        {Array.from({ length: 5 }).map((_, index) => (
          <Skeleton className='h-14 w-full' key={index} />
        ))}
      </div>
      <div className='flex w-full flex-col gap-2 overflow-x-auto rounded-2xl p-2 md:border md:border-neutral-900 md:bg-neutral-900 md:p-6 md:py-10'>
        {Array.from({ length: 5 }).map((_, index) => (
          <Skeleton className='h-14 w-full' key={index} />
        ))}
      </div>
    </div>
  );
};

export const SkeletonSubTab = ({ count = 5 }: { count?: number }) => {
  return (
    <nav className='flex gap-0 self-start overflow-x-auto px-3 pb-2 pt-8 sm:gap-2 md:gap-3 md:px-16 md:py-8'>
      {Array.from({ length: count }).map((_, index) => (
        <Skeleton className='h-8.5 w-32' key={index} />
      ))}
    </nav>
  );
};

export const ChartSkeleton = () => {
  return <Skeleton className='h-[400px] w-full' />;
};

export { Skeleton };

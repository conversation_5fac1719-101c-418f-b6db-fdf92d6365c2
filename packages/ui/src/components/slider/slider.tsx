'use client';

import * as React from 'react';
import * as SliderPrimitive from '@radix-ui/react-slider';
import { cn } from '../../lib';

const Slider = React.forwardRef<
  React.ElementRef<typeof SliderPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>
>(({ className, ...props }, ref) => (
  <SliderPrimitive.Root
    ref={ref}
    className={cn(
      'relative flex w-full touch-none select-none items-center',
      'data-[disabled]:opacity-50',
      className
    )}
    {...props}
  >
    <SliderPrimitive.Track className='bg-input-primary relative h-4 w-full grow overflow-hidden rounded-full'>
      <SliderPrimitive.Range
        className='bg-accent-1 absolute h-full'
        style={{
          width:
            (props.value?.[0] ?? 0) > 90
              ? `calc(${props.value?.[0]}% - 10px)`
              : undefined,
        }}
      />
    </SliderPrimitive.Track>
    <SliderPrimitive.Thumb className='border-primary ring-offset-app-bg focus-visible:ring-ring block h-7 w-7 rounded-full border-2 bg-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 data-[disabled]:pointer-events-none' />
  </SliderPrimitive.Root>
));
Slider.displayName = SliderPrimitive.Root.displayName;

export { Slider };

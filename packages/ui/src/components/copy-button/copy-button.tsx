'use client';

import { useCallback, useEffect, useState } from 'react';
import copy from 'clipboard-copy';
import { Bi<PERSON><PERSON>ck, BiCopy } from 'react-icons/bi';
import type { ClassNameValue } from 'tailwind-merge';
import { cn, notify } from '../../lib';
import { Button } from '../button';

export function CopyButton({
  value,
  size = 20,
  className,
}: {
  value: string;
  size?: number;
  className?: ClassNameValue;
}) {
  const [showTooltip, setShowTooltip] = useState<boolean>(false);

  const handleCopyClick = useCallback(async () => {
    await copy(value);
    notify.success('Copied!');
    setShowTooltip(true);
  }, [value]);

  useEffect(() => {
    if (showTooltip) {
      const timer = setInterval(() => {
        setShowTooltip(false);
      }, 2000);

      return () => {
        clearInterval(timer);
      };
    }
  }, [showTooltip]);

  if (showTooltip) {
    return (
      <BiCheck
        size={size}
        className='text-[#14dec2]'
        style={{ minWidth: `${size}px` }}
      />
    );
  }

  return (
    <Button
      variant='link'
      className={cn('h-max w-max p-0 opacity-70', className)}
      onClick={(e) => {
        e.stopPropagation();
        e.preventDefault();
        void handleCopyClick();
      }}
    >
      <BiCopy size={size} />
    </Button>
  );
}

import type { ReactNode } from 'react';
import { cn } from '../../lib';
import { Text } from '../text';

export const Card = ({
  children,
  title,
  subTitle,
  headerRight,
  className,
  contentContainerClassName,
}: {
  children: ReactNode;
  title?: string;
  subTitle?: string;
  headerRight?: ReactNode;
  className?: string;
  contentContainerClassName?: string;
}) => {
  return (
    <div className={cn('bg-card rounded-xl p-6', className)}>
      <div className={cn('flex flex-col gap-6', contentContainerClassName)}>
        {title || subTitle ? (
          <div className='flex flex-col gap-4'>
            {title ? (
              <div className='flex items-center justify-between gap-2'>
                <Text as='h3' className='text-white' level='headerMedium'>
                  {title}
                </Text>
                {headerRight}
              </div>
            ) : null}
            {subTitle ? (
              <Text as='p' className='text-gray-400' level='bodyLarge'>
                {subTitle}
              </Text>
            ) : null}
          </div>
        ) : null}
        {children}
      </div>
    </div>
  );
};

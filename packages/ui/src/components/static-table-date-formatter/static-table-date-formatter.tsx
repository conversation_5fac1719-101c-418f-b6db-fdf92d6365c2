'use client';

import { useEffect, useState } from 'react';
import type { ClassValue } from 'clsx';
import { utc, duration } from 'moment';
import { cn } from '@repo/ui/lib';
import { Text, Thin } from '../text';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../tooltip';

export const StaticTableDateFormatter = ({
  timestamp,
  noTooltip,
  className,
  timeClassName,
  suffix,
}: {
  timestamp: Date | string;
  noTooltip?: boolean;
  className?: ClassValue;
  timeClassName?: ClassValue;
  suffix?: string;
}) => {
  const [currentTime, setCurrentTime] = useState(utc());

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(utc());
    }, 1000);

    return () => {
      clearInterval(interval);
    };
  }, [currentTime]);

  if (timestamp === '') {
    return <Text className={cn('text-neutral-500', className)}>N/A</Text>;
  }

  const time = noTooltip
    ? undefined
    : `${utc(timestamp).format('MMM D, YYYY h:mm A')} (UTC)`;
  const timeDiff = duration(currentTime.diff(utc(timestamp)));

  if (timeDiff.asSeconds() < 60) {
    const seconds = Math.floor(timeDiff.asSeconds());
    return (
      <DateBox className={className} tooltip={time}>
        {seconds}{' '}
        <Thin className={timeClassName}>
          {seconds === 1 ? 'sec' : 'secs'} {suffix}
        </Thin>
      </DateBox>
    );
  }

  if (timeDiff.asMinutes() < 60) {
    const minutes = Math.floor(timeDiff.asMinutes());
    return (
      <DateBox className={className} tooltip={time}>
        {minutes}{' '}
        <Thin className={timeClassName}>
          {minutes === 1 ? 'min' : 'mins'} {suffix}
        </Thin>
      </DateBox>
    );
  }

  if (timeDiff.asHours() < 24) {
    const hours = Math.floor(timeDiff.asHours());
    return (
      <DateBox className={className} tooltip={time}>
        {hours}{' '}
        <Thin className={timeClassName}>
          {hours === 1 ? 'hour' : 'hours'} {suffix}
        </Thin>
      </DateBox>
    );
  }

  if (timeDiff.asDays() < 31) {
    const days = Math.floor(timeDiff.asDays());
    return (
      <DateBox className={className} tooltip={time}>
        {days}{' '}
        <Thin className={timeClassName}>
          {days === 1 ? 'day' : 'days'} {suffix}
        </Thin>
      </DateBox>
    );
  }

  if (timeDiff.asMonths() < 12) {
    const months = Math.floor(timeDiff.asMonths());
    return (
      <DateBox className={className} tooltip={time}>
        {months}{' '}
        <Thin className={timeClassName}>
          {months === 1 ? 'month' : 'months'} {suffix}
        </Thin>
      </DateBox>
    );
  }

  const years = Math.floor(timeDiff.asYears());
  return (
    <DateBox className={className} tooltip={time}>
      {years}{' '}
      <Thin className={timeClassName}>
        {years === 1 ? 'year' : 'years'} {suffix}
      </Thin>
    </DateBox>
  );
};

export function DateBox({
  children,
  tooltip,
  className,
}: {
  children: React.ReactNode;
  tooltip?: string;
  className?: ClassValue;
}) {
  if (tooltip) {
    return (
      <TooltipProvider>
        <Tooltip delayDuration={1}>
          <TooltipTrigger>
            <p
              className={cn('whitespace-nowrap text-xs opacity-50', className)}
            >
              {children}
            </p>
          </TooltipTrigger>
          <TooltipContent
            side='top'
            sideOffset={8}
            className='inline-flex max-w-64 flex-col items-start justify-start gap-3 rounded-xl border border-neutral-700 bg-[#171717] px-4 py-3 backdrop-blur-3xl'
          >
            <Text className='text-white' level='md'>
              {tooltip}
            </Text>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return (
    <div className='flex'>
      <p
        className={cn(
          'w-max min-w-20 whitespace-nowrap !text-xs opacity-50',
          className
        )}
      >
        {children}
      </p>
    </div>
  );
}

'use client';

import { memo } from 'react';
import { BsCurrencyDollar } from 'react-icons/bs';
import { cn } from '@repo/ui/lib';
import { AlphaIcon } from '../alpha-icon';
import { TaoIcon } from '../tao-icon';
import { Text } from '../text';

export type CurrencySelectionOptions = 'ALPHA' | 'TAO' | 'USD';

const CurrencyOptions: {
  label: CurrencySelectionOptions;
  icon: React.JSX.Element;
}[] = [
  {
    label: 'ALPHA',
    icon: <AlphaIcon className='-ml-0.5 text-xs text-black' />,
  },
  {
    label: 'TAO',
    icon: <TaoIcon className='-ml-0.5 text-xs text-black' />,
  },
  {
    label: 'USD',
    icon: <BsCurrencyDollar />,
  },
];

type CurrencySelectorProps = {
  selection: CurrencySelectionOptions;
  onSelectionChange: (selection: CurrencySelectionOptions) => void;
  options: CurrencySelectionOptions[];
};

export const CurrencySelector = memo(function CurrencySelector(
  props: CurrencySelectorProps
) {
  return (
    <div className='flex h-fit w-full flex-row items-center gap-2 rounded-2xl border border-[#323232] bg-[#1D1D1D] p-1 lg:w-max'>
      {props.options.map((option) => (
        <CurrencySelectorButton key={option} label={option} {...props} />
      ))}
    </div>
  );
});

const CurrencySelectorButton = ({
  selection,
  onSelectionChange,
  label,
}: CurrencySelectorProps & {
  label: CurrencySelectionOptions;
}) => {
  const option = CurrencyOptions.find((o) => o.label === label);
  if (option === undefined) {
    return null;
  }

  return (
    <Text
      level='buttonSmall'
      className={cn(
        'flex w-full cursor-pointer flex-row items-center justify-center gap-2 px-2 py-1 sm:w-max sm:flex-1',
        selection === label
          ? 'rounded-xl border border-[#00DBBC] !bg-[#00DBBC1A] text-white'
          : 'text-[#777777]'
      )}
      onClick={() => {
        onSelectionChange(label);
      }}
    >
      <span
        className={cn(
          'flex h-6 w-6 items-center justify-center rounded-full text-black',
          selection === label ? 'bg-white' : 'bg-[#777777]'
        )}
      >
        {option.icon}
      </span>
      {label}
    </Text>
  );
};

import type { ComponentProps, Ref } from 'react';
import type { IconType } from 'react-icons';
import { cn } from '../../lib';
import { Button } from '../button';
import { Text } from '../text';

type InputProps = {
  label?: string;
  labelClassName?: string;
  description?: string;
  inputId: string;
  IconLeft?: IconType;
  iconLeftClassName?: string;
  iconLeftConfig?: {
    Icon: IconType;
    className?: string;
  };
  IconRight?: IconType;
  buttonRightProps?: ComponentProps<typeof Button>;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  onFocus?: (e: React.FocusEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  onPaste?: (e: React.ClipboardEvent<HTMLInputElement>) => void;
  value: string;
  error?: string;
  forwardRef?: Ref<HTMLInputElement>;
  className?: string;
  inputClassName?: string;
  placeholder?: string;
  onClick?: (e: React.MouseEvent<HTMLInputElement>) => void;
  rightComponent?: React.ReactNode;
  disabled?: boolean;
  enterKeyHint?:
    | 'enter'
    | 'done'
    | 'go'
    | 'next'
    | 'previous'
    | 'search'
    | 'send';
};

export const Input = ({
  label,
  labelClassName,
  description,
  inputId,
  iconLeftConfig,
  IconLeft,
  iconLeftClassName,
  IconRight,
  buttonRightProps,
  onChange,
  onKeyDown,
  onFocus,
  onBlur,
  onPaste,
  value,
  error,
  forwardRef,
  className,
  inputClassName,
  placeholder,
  onClick,
  rightComponent,
  disabled = false,
  enterKeyHint,
}: InputProps) => {
  return (
    <div className={cn('flex flex-col gap-2', className)}>
      {label ? (
        <Text as='label' level='labelLarge' className={labelClassName}>
          {label}
        </Text>
      ) : null}
      {description ? (
        <Text className='text-label-secondary' level='labelMedium'>
          {description}
        </Text>
      ) : null}
      <div className='grid grid-cols-1'>
        <input
          className={cn(
            'bg-input-primary col-start-1 row-start-1 block w-full rounded-md py-1.5 text-base text-white outline outline-1 -outline-offset-1',
            disabled && 'opacity-50',
            IconLeft || iconLeftConfig ? 'pl-14' : 'pl-3',
            IconRight ? 'pr-10' : 'pr-3',
            error ? 'outline-red-500' : 'outline-input-border-primary',
            'min-h-12 placeholder:text-gray-400 focus:outline focus:outline-1 focus:-outline-offset-1 focus:outline-gray-700 sm:text-sm/6',
            'placeholder:overflow-hidden placeholder:text-ellipsis',
            inputClassName
          )}
          id={inputId}
          name={inputId}
          onClick={onClick}
          onChange={onChange}
          onKeyDown={onKeyDown}
          onFocus={onFocus}
          onBlur={onBlur}
          onPaste={onPaste}
          ref={forwardRef}
          type='text'
          value={value}
          placeholder={placeholder}
          autoComplete='off'
          disabled={disabled}
          enterKeyHint={enterKeyHint}
        />

        {iconLeftConfig ? (
          <iconLeftConfig.Icon
            aria-hidden='true'
            className={cn(
              'pointer-events-none col-start-1 row-start-1 ml-5 size-5 self-center text-gray-400 sm:size-4',
              iconLeftConfig.className
            )}
          />
        ) : null}
        {IconLeft ? (
          <IconLeft
            aria-hidden='true'
            className={cn(
              'pointer-events-none col-start-1 row-start-1 ml-5 size-5 self-center text-gray-400 sm:size-4',
              iconLeftClassName
            )}
          />
        ) : null}
        {IconRight ? (
          <IconRight
            aria-hidden='true'
            className='text-accent-1 pointer-events-none col-start-1 row-start-1 mr-3 size-5 self-center justify-self-end sm:size-4'
          />
        ) : null}
        {buttonRightProps ? (
          <Button
            {...buttonRightProps}
            className={cn(
              'col-start-1 row-start-1 mr-3 w-fit self-center justify-self-end rounded-full',
              buttonRightProps.className ? buttonRightProps.className : ''
            )}
          />
        ) : null}
        {rightComponent ? (
          <div className='col-start-1 row-start-1 mr-3 flex w-fit flex-row items-center gap-2 self-center justify-self-end '>
            {rightComponent}
          </div>
        ) : null}
        {error ? (
          <Text className='mt-1 text-red-500' level='bodySmall'>
            {error}
          </Text>
        ) : null}
      </div>
    </div>
  );
};

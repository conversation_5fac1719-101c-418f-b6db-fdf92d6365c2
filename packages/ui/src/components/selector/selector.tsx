'use client';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { Text } from '@repo/ui/components';
import { cn } from '@repo/ui/lib';

export const pageSizeList = [
  { label: 10, value: 10 },
  { label: 25, value: 25 },
  { label: 50, value: 50 },
  { label: 100, value: 100 },
  { label: 'ALL', value: -1 },
];

export const Selector = ({
  defaultSize = 10,
  onLimitChange,
  selectorType,
}: {
  defaultSize?: number;
  onLimitChange?: (limit: number) => void;
  selectorType?: string;
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [pageSize, setPageSize] = useState<number>(defaultSize);

  const limit = useMemo(() => {
    const limitSize = searchParams.get('limit');

    return Number(limitSize ?? defaultSize);
  }, [searchParams, defaultSize]);

  const handlePageSizeChange = useCallback(
    (value: number) => {
      if (onLimitChange) {
        onLimitChange(value);
        setPageSize(value);
        return;
      }
      const params = new URLSearchParams(searchParams);

      const current = params.get('limit');
      if (
        Number(current) === value ||
        (current === null && value === defaultSize)
      )
        return;

      params.set('limit', `${value}`);
      params.delete('page');

      router.push(`${pathname}?${params.toString()}`, { scroll: false });
      setPageSize(value);
    },
    [searchParams, pathname, router, onLimitChange, defaultSize]
  );

  useEffect(() => {
    setPageSize(limit);
  }, [limit]);

  return (
    <div className='flex w-full flex-row items-center justify-end gap-2 sm:gap-2 lg:w-fit'>
      <Text level='base' className='font-medium leading-[18px] opacity-20'>
        Rows
      </Text>
      {pageSizeList.slice(0, selectorType === 'Subnets' ? 5 : 4).map((item) => (
        <button
          key={item.label}
          className={cn(
            'rounded-lg px-3 py-2.5 text-base font-medium leading-[18px] opacity-40',
            item.value === pageSize
              ? 'border border-[#00DBBC] bg-[#00DBBC1A] opacity-100'
              : 'hover:bg-[#D9D9D90D]'
          )}
          onClick={() => {
            handlePageSizeChange(item.value);
          }}
          type='button'
        >
          {item.label}
        </button>
      ))}
    </div>
  );
};

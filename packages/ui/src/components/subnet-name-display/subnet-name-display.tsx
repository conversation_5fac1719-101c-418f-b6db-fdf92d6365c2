'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import { Link, Text } from '@repo/ui/components';
import { appRoutes, cn } from '@repo/ui/lib';
import { NavigationMenuLink } from '../navigation/secondary-nav-bar-content/navigation-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../tooltip';

export const ImageWithFallback = ({
  netuid,
  height,
  width,
  alt,
  className,
}: {
  netuid: number;
  height: number;
  width: number;
  alt: string;
  className: string;
}) => {
  const [error, setError] = useState<boolean>(false);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    setError(false);
  }, [netuid]);

  return (
    <Image
      loader={({ src, width: loaderWidth, quality }) =>
        `${src}?w=${loaderWidth}&q=${quality || 75}`
      }
      src={error ? '/images/subnets/0.webp' : `/images/subnets/${netuid}.webp`}
      height={height}
      width={width}
      alt={alt}
      className={className}
      onError={() => {
        setError(true);
      }}
    />
  );
};

export const SubnetNameDisplay = ({
  subnetName,
  netuid,
  className,
  isClickable = false,
  marker,
  fromNavMenu = false,
}: {
  subnetName: string;
  netuid: number;
  className?: string;
  isClickable?: boolean;
  marker?: boolean;
  fromNavMenu?: boolean;
}) => {
  const component = (
    <div
      className={cn(
        'group flex flex-row items-center gap-4',
        isClickable && 'cursor-pointer',
        className
      )}
    >
      <ImageWithFallback
        netuid={netuid}
        height={32}
        width={32}
        alt=''
        className='h-8 w-8 rounded-full object-cover'
      />
      <div className='flex flex-1 flex-col gap-0.5 overflow-hidden'>
        <div className='relative flex h-[20px] flex-row items-center gap-2 overflow-hidden'>
          <Text
            level='sm'
            className={cn('truncate', isClickable && 'group-hover:underline')}
          >
            {subnetName}
          </Text>
          {marker ? (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <Text
                    level='xs'
                    className='whitespace-nowrap rounded-[4px] bg-[#EBC247] px-1 py-0.5 font-medium leading-[13px] text-[#000000]'
                  >
                    Not Active
                  </Text>
                </TooltipTrigger>
                <TooltipContent
                  side='bottom'
                  className='inline-flex max-w-[260px] flex-col items-start justify-start gap-3 rounded-xl border border-[#FFFFFF]/20 bg-[#2E2E2E] p-4'
                >
                  <Text
                    level='xs'
                    className='whitespace-break-spaces font-medium leading-[13px] opacity-60'
                  >
                    This subnet is registered but inactive. It can be started
                    after a 7-day waiting period. No emissions occur until
                    activation.
                  </Text>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ) : null}
        </div>
        <Text level='sm' className='text-[#949494]'>
          {netuid}
        </Text>
      </div>
    </div>
  );

  return isClickable ? (
    fromNavMenu ? (
      <NavigationMenuLink asChild>
        <Link href={appRoutes.subnets.chart(netuid.toString())}>
          {component}
        </Link>
      </NavigationMenuLink>
    ) : (
      <Link href={appRoutes.subnets.chart(netuid.toString())}>{component}</Link>
    )
  ) : (
    component
  );
};

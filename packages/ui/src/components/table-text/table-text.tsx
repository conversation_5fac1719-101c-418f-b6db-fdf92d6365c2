import type { ReactNode } from 'react';
import { Text } from '@repo/ui/components';
import { cn } from '@repo/ui/lib';

export const TableText = ({
  info,
  isChange,
  className,
  dividedBy,
  percentage,
  u16Percentage,
  amount,
  suffix,
}: {
  info?: number;
  isChange?: boolean;
  className?: string;
  dividedBy?: number;
  percentage?: boolean;
  u16Percentage?: boolean;
  amount?: number;
  suffix?: string | ReactNode;
}) => {
  const text = info ?? amount;
  const value = Number(text);

  if (text === undefined) return;

  let formattedText = text.toString();
  if (!Number.isNaN(value) && isChange) {
    formattedText =
      (value > 0 ? '+' : '') +
      value.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 4,
      });
  }

  if (dividedBy) {
    formattedText = (value / dividedBy).toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 5,
    });
  }

  if (percentage) {
    formattedText = `${value.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    })}%`;
  }

  if (u16Percentage) {
    formattedText = value.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 4,
    });
  }

  return (
    <Text
      level='sm'
      className={cn(
        className,
        'text-table-cell',
        !Number.isNaN(value) && isChange
          ? value < 0
            ? 'text-fire'
            : value === 0
              ? 'text-gray-500'
              : 'text-lime-green'
          : ''
      )}
    >
      {suffix ? suffix : null}
      {formattedText}
    </Text>
  );
};

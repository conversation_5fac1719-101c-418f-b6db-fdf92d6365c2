import type { Renderable, ToastOptions } from 'react-hot-toast';
import { toast } from 'react-hot-toast';
import { BiInfoCircle } from 'react-icons/bi';

const toastConfig: ToastOptions = {
  duration: 3500,
  position: 'bottom-center',
  style: {
    fontWeight: 'normal',
    fontFamily: 'var(--font-everett)',
    fontSize: '14px',
    background: '#333',
    color: '#fff',
  },
};

const getToastConfig = (message: string) => ({
  ...toastConfig,
  id: message,
});

const success = (message: string) => {
  toast.success(message, getToastConfig(message));
};

const error = (message: string) => {
  toast.error(message, getToastConfig(message));
};

const icon = (message: string, iconToRender: Renderable) => {
  toast.success(message, {
    ...getToastConfig(message),
    icon: iconToRender,
  });
};

const info = (message: string) => {
  icon(message, <BiInfoCircle />);
};

export const notify = { success, error, icon, info };

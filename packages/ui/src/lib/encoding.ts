import {
	decodeAddress,
	encode<PERSON>ddress,
	evmToAddress,
} from "@polkadot/util-crypto";

export function decodeSS58Address(address: string) {
	if (address.startsWith("0x")) {
		return address;
	}
	try {
		const publicKey = decodeAddress(address);
		const hexPublicKey = Buffer.from(publicKey).toString("hex");

		return `0x${hexPublicKey}`;
	} catch (error) {
		console.error("Error decoding address:", error);
		throw error;
	}
}

export function encodeSS58Address(address: string, CHAIN_SS58_CODEC = 42) {
	if (!address.startsWith("0x")) {
		return address;
	}
	try {
		if (!address) return "";

		const hexPublicKey = address.slice(2);
		const publicKey = Buffer.from(hexPublicKey, "hex");

		return encodeAddress(publicKey, CHAIN_SS58_CODEC);
	} catch (error) {
		console.error("Error encoding address:", error);
		throw error;
	}
}

export function encodeH160Address(address: string, CHAIN_SS58_CODEC = 42) {
	if (!address.startsWith("0x")) {
		return address;
	}
	try {
		if (!address) return "";

		const hexPublicKey = address.slice(2);
		const publicKey = Buffer.from(hexPublicKey, "hex");

		return evmToAddress(publicKey, CHAIN_SS58_CODEC);
	} catch (error) {
		console.error("Error encoding address:", error);
		throw error;
	}
}

import type { Route } from 'next';

export const appRoutes = {
  base: {
    landing: '/' as Route<string>,
  },
  subnets: {
    home: '/subnets' as Route<string>,
    detail: (id: string) => `/subnets/${id}/chart` as Route<string>,
    doc: 'https://docs.taostats.io/docs/subnets' as Route<string>,
    coldkey: (id: string) => `/coldkey/${id}` as Route<string>,
    chart: (id: string) => `/subnets/${id}/chart` as Route<string>,
  },
  blockchain: {
    home: '/blocks' as Route<string>,
    accounts: '/accounts' as Route<string>,
    delegation: '/delegation' as Route<string>,
    transfer: '/transfers' as Route<string>,
    blocks: '/blocks' as Route<string>,
    extrinsic: '/extrinsics' as Route<string>,
    event: '/events' as Route<string>,
    runtime: '/runtime' as Route<string>,
    sudo: '/sudo' as Route<string>,
    tokenomics: '/tokenomics' as Route<string>,
    heightDetail: (id: string, network?: string) =>
      `/block/${id}/extrinsics${
        network ? `?network=${network}` : ''
      }` as Route<string>,
    eventDetail: (id: string, network?: string) =>
      `/event/${id}${network ? `?network=${network}` : ''}` as Route<string>,
    accountDetail: (id: string) => `/account/${id}` as Route<string>,
    accountTransfer: (id: string) =>
      `/account/${id}/transfers` as Route<string>,
    accountTransactions: (id: string) =>
      `/account/${id}/transactions` as Route<string>,
    accountExtrinsics: (id: string) =>
      `/account/${id}/extrinsics` as Route<string>,
    extrinsicDetail: (id: string, network?: string) =>
      `/extrinsic/${id}${
        network ? `?network=${network}` : ''
      }` as Route<string>,
    blockHashDetail: (hash: string) => `/hash/${hash}` as Route<string>,
    hotkeyDetail: (address: string) => `/hotkey/${address}` as Route<string>,
    transferDetail: (hash: string) => `/transfer/${hash}` as Route<string>,
    blockExtrinsic: (id: string) => `/block/${id}/extrinsics` as Route<string>,
    blockEvent: (id: string) => `/block/${id}/events` as Route<string>,
  },
  validator: {
    home: '/validators' as Route<string>,
    verifiedValidator: '/verified-validators' as Route<string>,
    detail: (id: string) => `/validators/${id}` as Route<string>,
    stakeTao: (hkey?: string) =>
      (hkey
        ? `https://delegate.taostats.io/staking?hkey=${hkey}`
        : 'https://delegate.taostats.io') as Route<string>,
  },
  analytics: {
    home: '/analytics' as Route<string>,
    exchanges: '/exchanges' as Route<string>,
    deviation: '/deviation' as Route<string>,
    historical: '/historical' as Route<string>,
    chartDetails: (id: string) => `/analytics/${id}` as Route<string>,
  },
  evm: {
    home: '/evm/block' as Route<string>,
    blocks: '/evm/blocks' as Route<string>,
    contracts: '/evm/contracts' as Route<string>,
    heightDetail: (id: string) => `/evm/block/${id}` as Route<string>,
    addressDetail: (id: string) => `/evm/address/${id}` as Route<string>,
    transaction: '/evm/txs' as Route<string>,
    transactionDetail: (hash: string) => `/evm/tx/${hash}` as Route<string>,
  },
  dtao: {
    pool: '/dtao/pool' as Route<string>,
    subnetEmission: '/dtao/subnet-emission' as Route<string>,
    hotkeyEmission: '/dtao/hotkey-emission' as Route<string>,
    hotkeyAlphaShares: '/dtao/hotkey-alpha-shares' as Route<string>,
    coldkeyAlphaShares: '/dtao/coldkey-alpha-shares' as Route<string>,
    stakeBalance: '/dtao/stake-balance' as Route<string>,
    stakeBalanceAggregated: '/dtao/stake-balance-aggregated' as Route<string>,
    subnets: '/dtao/subnets' as Route<string>,
    subnetDetail: (id: string) => `/dtao/subnet/${id}` as Route<string>,
    accountDetail: (id: string) => `/dtao/account/${id}` as Route<string>,
  },
  corcel: 'https://corcel.io/' as Route<string>,
  buy: '/buy' as Route<string>,
  yield: '/yield' as Route<string>,
  delegateDoc: 'https://docs.taostats.io/docs/delegation' as Route<string>,
  doc: {
    dashboard: 'https://dash.taostats.io' as Route<string>,
    api: 'https://dash.taostats.io' as Route<string>,
  },
  terms: '/terms' as Route<string>,
  privacy: '/privacy' as Route<string>,
  notFound: '/not-found' as Route<string>,
};

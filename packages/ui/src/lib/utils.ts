import { type ClassValue, clsx } from 'clsx';
import { generateFromString } from 'generate-avatar';
import { twMerge } from 'tailwind-merge';
import type {
  DtaoValidatorLatest,
  SubnetPool,
  TableData,
} from '@repo/types/website-api-types';
import cortext from '../assets/images/icons/cortext.png';
import foundation from '../assets/images/icons/foundation.jpg';
import foundry from '../assets/images/icons/foundry.jpg';
import holdings from '../assets/images/icons/holdings.jpg';
import kraken from '../assets/images/icons/kraken.png';
import northtensor from '../assets/images/icons/northtensor.png';
import tao5 from '../assets/images/icons/tao5.png';
import tatsu from '../assets/images/icons/tatsu.png';
import type { TableItem } from '../components/navigation/secondary-nav-bar-content/subnets-list';
import { ExchangeAddressMapping } from './exchange-address-mapping';
import { validatorIconMapping } from './validator-icon-mapping';
import Cookies from 'js-cookie';

export const taoDivider = 1_000_000_000;

export const VALIDATOR_MINIMUM_STAKE = 1024;

// Receives a value in raw units and returns the value in TAO
// i.e. divide the value by taoDivider: 1_000_000_000
export const getHumanValueFromChainValue = (value: number | string) => {
  // Check if the value is a string and can be converted to a number
  // Iffso, convert it to a number
  const valueAsNumber = typeof value === 'string' ? Number(value) : value;

  // If the value is not a number, return 0
  if (isNaN(valueAsNumber)) {
    return 0;
  }

  return valueAsNumber / taoDivider;
};

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function truncateString(str: string) {
  if (str && str.length > 15) {
    const first5 = str.substring(0, 5);
    const last5 = str.substring(str.length - 5);
    return `${first5}...${last5}`;
  }
  return str;
}

export function truncateStringWithMax(str: string, max = 10) {
  const ellipsis = '...';
  const trimmedString = str.trim();

  if (trimmedString && trimmedString.length > max) {
    const firstPart = trimmedString.substring(0, max - ellipsis.length);
    return `${firstPart}${ellipsis}`;
  }
  return trimmedString;
}

export function createUrl(site: string, path: string) {
  return `${site.replace(/\/$/, '')}/${path.replace(/^\//, '')}`;
}

export function getIconUrl(address: string) {
  switch (address) {
    case '5Fq5v71D4LX8Db1xsmRSy6udQThcZ8sFDqxQFwnUZ1BuqY5A':
      return northtensor;
    case '5CoZxgtfhcJKX2HmkwnsN18KbaT9aih9eF3b6qVPTgAUbifj':
      return holdings;
    case '5CsvRJXuR955WojnGMdok1hbhffZyB4N5ocrv82f3p5A2zVp':
      return tao5;
    case '5D4gEn5S422dTGR5NJJKZ93FNV6hDmfwDPfxFNgcoVnUkZ4f':
      return tatsu;
    case '5E2b2DcMd5W8MBhzTCFt63t2ZEN8RsRgL7oDd7BFYL9aMQux':
      return kraken;
    case '5HEo565WAy4Dbq3Sv271SAi7syBSofyfhhwRNjFNSM2gP9M2':
      return foundry;
    case '5D4oo3Z5VFUJtFWcK9wtfPqkGnzVubSFMnWnMKUVkDxsrWSj':
      return foundation;
    case '5DM7CPqPKtMSADhFKYsstsCS4Tm4Kd6PMXoh6DdqY4MtxmtX':
      return cortext;
  }

  const exchangeImage = Object.entries(ExchangeAddressMapping).find(
    ([hotkey]) => hotkey === address
  )?.[1]?.icon;

  if (exchangeImage) {
    return `https://taostats-static.b-cdn.net/exchanges/${exchangeImage}.png`;
  }

  const validatorImage = Object.entries(validatorIconMapping).find(
    ([hotkey]) => hotkey === address
  )?.[1];
  if (validatorImage) {
    return `https://taostats-static.b-cdn.net/validators/${validatorImage}.png`;
  }

  return `data:image/svg+xml;utf8,${generateFromString(address)}`;
}

export function getFilter(
  data: TableData | TableItem | SubnetPool | DtaoValidatorLatest,
  search: string,
  searchFields?: string[]
) {
  let result = false;
  Object.entries(data).map(([key, value]) => {
    if (searchFields) {
      if (
        searchFields.includes(key) &&
        value?.toString().toLowerCase().includes(search.toLowerCase())
      ) {
        result = true;
      }
    } else if (value?.toString().toLowerCase().includes(search.toLowerCase())) {
      result = true;
    }
  });

  return result;
}

export function setCookie({ name, value }: { name: string; value: string }) {
  return Cookies.set(name, value, { domain: 'taostats.io' });
}

export function getCookie(name: string) {
  return Cookies.get(name);
}

export const walletFormat = (address: string) => {
  return `${address.slice(0, 6)}...${address.slice(
    address.length - 6,
    address.length
  )}`;
};

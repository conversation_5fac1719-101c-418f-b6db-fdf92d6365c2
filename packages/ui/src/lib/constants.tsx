// Local Storage Keys for Navigation Components
export const LOCAL_STORAGE_KEYS: Record<string, string> = {
  // Subnets related
  SUBNETS_WATCHLIST: 'subnets-watchlist',
  SUBNETS_WATCH_VIEW: 'subnets-watch-view',
  SUBNETS_MENU_SWITCH: 'subnets-menu-switch',

  // Validators related
  VALIDATORS_WATCHLIST: 'validators-watchlist',
  VALIDATORS_WATCH_VIEW: 'validators-watch-view',
  VALIDATORS_MENU_SWITCH: 'validators-menu-switch',
};

// Legacy exports for backward compatibility
export const LOCAL_STORAGE_SUBNETS_WATCHLIST =
  LOCAL_STORAGE_KEYS.SUBNETS_WATCHLIST;
export const LOCAL_STORAGE_SUBNETS_WATCH_VIEW =
  LOCAL_STORAGE_KEYS.SUBNETS_WATCH_VIEW;
export const LOCAL_STORAGE_SUBNETS_MENU_SWITCH =
  LOCAL_STORAGE_KEYS.SUBNETS_MENU_SWITCH;

export const LOCAL_STORAGE_VALIDATORS_WATCHLIST =
  LOCAL_STORAGE_KEYS.VALIDATORS_WATCHLIST;
export const LOCAL_STORAGE_VALIDATORS_WATCH_VIEW =
  LOCAL_STORAGE_KEYS.VALIDATORS_WATCH_VIEW;
export const LOCAL_STORAGE_VALIDATORS_MENU_SWITCH =
  LOCAL_STORAGE_KEYS.VALIDATORS_MENU_SWITCH;

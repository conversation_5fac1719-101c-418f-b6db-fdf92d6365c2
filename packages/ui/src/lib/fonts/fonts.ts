// eslint-disable-next-line camelcase -- this is just how it is defined in the google font package
import { Fira_Code } from 'next/font/google';
import localFont from 'next/font/local';

const everettFont = localFont({
  src: [
    {
      path: './everett/TWKEverett-Black-web.woff2',
      weight: '800',
    },
    {
      path: './everett/TWKEverett-BlackItalic-web.woff2',
      weight: '800',
      style: 'italic',
    },
    {
      path: './everett/TWKEverett-Bold-web.woff2',
      weight: '700',
    },
    {
      path: './everett/TWKEverett-BoldItalic-web.woff2',
      weight: '700',
      style: 'italic',
    },
    {
      path: './everett/TWKEverett-Medium-web.woff2',
      weight: '500',
    },
    {
      path: './everett/TWKEverett-MediumItalic-web.woff2',
      weight: '500',
      style: 'italic',
    },
    {
      path: './everett/TWKEverett-Regular-web.woff2',
      weight: '400',
    },
    {
      path: './everett/TWKEverett-RegularItalic-web.woff2',
      weight: '400',
      style: 'italic',
    },
    {
      path: './everett/TWKEverett-Light-web.woff2',
      weight: '300',
    },
    {
      path: './everett/TWKEverett-LightItalic-web.woff2',
      weight: '300',
      style: 'italic',
    },
    {
      path: './everett/TWKEverett-Thin-web.woff2',
      weight: '200',
    },
    {
      path: './everett/TWKEverett-ThinItalic-web.woff2',
      weight: '200',
      style: 'italic',
    },
    {
      path: './everett/TWKEverett-Ultralight-web.woff2',
      weight: '100',
    },
    {
      path: './everett/TWKEverett-UltralightItalic-web.woff2',
      weight: '100',
      style: 'italic',
    },
    {
      path: './everett/TWKEverett-Hairline-web.woff2',
      weight: '50',
    },
    {
      path: './everett/TWKEverett-HairlineItalic-web.woff2',
      weight: '50',
      style: 'italic',
    },
  ],
  variable: '--font-mai',
});

const fontFira = Fira_Code({
  subsets: ['latin'],
  variable: '--font-fira',
});

export { everettFont, fontFira };

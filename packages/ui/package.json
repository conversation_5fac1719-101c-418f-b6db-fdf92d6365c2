{"name": "@repo/ui", "version": "0.1.0", "sideEffects": ["**/*.css"], "exports": {"./components": "./src/components/index.ts", "./lib": "./src/lib/index.ts"}, "scripts": {"lint": "eslint src/", "type-check": "tsc --noEmit"}, "peerDependencies": {"react": "^18.2.0"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.5.1", "@headlessui/react": "^2.2.0", "@next/font": "15.0.0-rc.0", "@number-flow/react": "^0.4.1", "@polkadot/util-crypto": "^13.4.3", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tooltip": "^1.1.6", "@react-hooks-library/core": "^0.6.2", "@storybook/react": "^7.6.7", "@tanstack/react-table": "^8.20.6", "class-variance-authority": "^0.7.0", "clipboard-copy": "^4.0.1", "clsx": "^2.0.0", "cmdk": "^1.0.4", "emoji-regex": "^10.4.0", "generate-avatar": "^1.4.10", "lucide-react": "^0.479.0", "moment": "^2.30.1", "next": "^14.2.3", "react-hot-toast": "^2.4.1", "react-icons": "^5.3.0", "tailwind-merge": "^1.14.0", "usehooks-ts": "^3.1.1", "vaul": "^1.1.2"}, "devDependencies": {"@headlessui/react": "^1.7.18", "@headlessui/tailwindcss": "^0.2.0", "@heroicons/react": "^2.1.1", "@repo/config": "workspace:*", "@repo/eslint-config": "workspace:*", "@repo/types": "workspace:*", "@types/react": "^18.2.46", "autoprefixer": "^10.4.16", "postcss": "^8.4.33", "react": "^18.2.0", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.6", "typescript": "^5.3.3"}}
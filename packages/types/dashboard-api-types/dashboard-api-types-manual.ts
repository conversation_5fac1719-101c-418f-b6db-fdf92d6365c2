export type GetApiKeysResponse = {
  apiKeys: ApiKey[];
};

export type GetApiKeyUsageResponse = {
  total: number;
  usage: {
    timestamp: number;
    endpoint: string;
    method: string;
  }[];
};

export type ApiKey = {
  apiKeyId: string;
  enabled: boolean;
  name?: string;
  description?: string;
};

export type CreateApiKeyResponse = {
  apiKey: ApiKey;
};

export type CreateStripeCustomerSessionResponse = {
  clientSecret: string;
};

export type GenerateFingerprintResponse = {
  fingerprint: string;
};

export type LoginResponse = {
  token_type: string;
  access_token: string;
  refresh_token: string;
};

export type GetAccountsResponse = {
  accounts: Account[];
  total: number;
};

export type Account = {
  accountId: string;
  type: string;
  email: string;
  rateLimit?: number;
  createdAt: string;
  keyCount: number;
};

export type UpdateAccountRequest = {
  accountId: string;
  rateLimit: number;
};

export type AccountDetails = {
  accountId: string;
  type: string;
  email: string;
  githubId: string | undefined;
  googleId: string | undefined;
  isAdmin: boolean;
  name: string;
  image: string;
  rateLimit: {
    admin: boolean;
    rateLimit: number;
    startDate: string;
    endDate: string;
  };
  stripeCustomerId: string;
  subscription: {
    productName: string;
    isActive: boolean;
  };
  creditBalance: number;
};

export type TaoStatsDashboardProfile = AccountDetails;

export type GetTopUsageResponse = {
  apiKeys: {
    accountId: string;
    apiKeyId: string;
    email: string;
    apiCalls: number;
  }[];
};

export type PortfolioWalletsResponse = {
  total: number;
  wallets: PortfolioWallet[];
};

export type PortfolioWallet = {
  walletId: string;
  address: string;
  name: string;
};

export type AddPortfolioWalletRequest = {
  address: string;
  name: string;
};

export type DeletePortfolioWalletRequest = {
  walletId: string;
};

export type SuccessResponse = {
  success: boolean;
};

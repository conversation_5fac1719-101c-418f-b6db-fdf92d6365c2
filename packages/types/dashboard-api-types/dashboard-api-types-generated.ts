/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

export type QueryParamsType = Record<string | number, any>;
export type ResponseFormat = keyof Omit<Body, 'body' | 'bodyUsed'>;

export interface FullRequestParams extends Omit<RequestInit, 'body'> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseFormat;
  /** request body */
  body?: unknown;
  /** base url */
  baseUrl?: string;
  /** request cancellation token */
  cancelToken?: CancelToken;
}

export type RequestParams = Omit<
  FullRequestParams,
  'body' | 'method' | 'query' | 'path'
>;

export interface ApiConfig<SecurityDataType = unknown> {
  baseUrl?: string;
  baseApiParams?: Omit<RequestParams, 'baseUrl' | 'cancelToken' | 'signal'>;
  securityWorker?: (
    securityData: SecurityDataType | null
  ) => Promise<RequestParams | void> | RequestParams | void;
  customFetch?: typeof fetch;
}

export interface HttpResponse<D extends unknown, E extends unknown = unknown>
  extends Response {
  data: D;
  error: E;
}

type CancelToken = Symbol | string | number;

export enum ContentType {
  Json = 'application/json',
  FormData = 'multipart/form-data',
  UrlEncoded = 'application/x-www-form-urlencoded',
  Text = 'text/plain',
}

export class HttpClient<SecurityDataType = unknown> {
  public baseUrl: string = 'https://management-api.taostats.io';
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>['securityWorker'];
  private abortControllers = new Map<CancelToken, AbortController>();
  private customFetch = (...fetchParams: Parameters<typeof fetch>) =>
    fetch(...fetchParams);

  private baseApiParams: RequestParams = {
    credentials: 'same-origin',
    headers: {},
    redirect: 'follow',
    referrerPolicy: 'no-referrer',
  };

  constructor(apiConfig: ApiConfig<SecurityDataType> = {}) {
    Object.assign(this, apiConfig);
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected encodeQueryParam(key: string, value: any) {
    const encodedKey = encodeURIComponent(key);
    return `${encodedKey}=${encodeURIComponent(typeof value === 'number' ? value : `${value}`)}`;
  }

  protected addQueryParam(query: QueryParamsType, key: string) {
    return this.encodeQueryParam(key, query[key]);
  }

  protected addArrayQueryParam(query: QueryParamsType, key: string) {
    const value = query[key];
    return value.map((v: any) => this.encodeQueryParam(key, v)).join('&');
  }

  protected toQueryString(rawQuery?: QueryParamsType): string {
    const query = rawQuery || {};
    const keys = Object.keys(query).filter(
      (key) => 'undefined' !== typeof query[key]
    );
    return keys
      .map((key) =>
        Array.isArray(query[key])
          ? this.addArrayQueryParam(query, key)
          : this.addQueryParam(query, key)
      )
      .join('&');
  }

  protected addQueryParams(rawQuery?: QueryParamsType): string {
    const queryString = this.toQueryString(rawQuery);
    return queryString ? `?${queryString}` : '';
  }

  private contentFormatters: Record<ContentType, (input: any) => any> = {
    [ContentType.Json]: (input: any) =>
      input !== null && (typeof input === 'object' || typeof input === 'string')
        ? JSON.stringify(input)
        : input,
    [ContentType.Text]: (input: any) =>
      input !== null && typeof input !== 'string'
        ? JSON.stringify(input)
        : input,
    [ContentType.FormData]: (input: any) =>
      Object.keys(input || {}).reduce((formData, key) => {
        const property = input[key];
        formData.append(
          key,
          property instanceof Blob
            ? property
            : typeof property === 'object' && property !== null
              ? JSON.stringify(property)
              : `${property}`
        );
        return formData;
      }, new FormData()),
    [ContentType.UrlEncoded]: (input: any) => this.toQueryString(input),
  };

  protected mergeRequestParams(
    params1: RequestParams,
    params2?: RequestParams
  ): RequestParams {
    return {
      ...this.baseApiParams,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...(this.baseApiParams.headers || {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected createAbortSignal = (
    cancelToken: CancelToken
  ): AbortSignal | undefined => {
    if (this.abortControllers.has(cancelToken)) {
      const abortController = this.abortControllers.get(cancelToken);
      if (abortController) {
        return abortController.signal;
      }
      return void 0;
    }

    const abortController = new AbortController();
    this.abortControllers.set(cancelToken, abortController);
    return abortController.signal;
  };

  public abortRequest = (cancelToken: CancelToken) => {
    const abortController = this.abortControllers.get(cancelToken);

    if (abortController) {
      abortController.abort();
      this.abortControllers.delete(cancelToken);
    }
  };

  public request = async <T = any, E = any>({
    body,
    secure,
    path,
    type,
    query,
    format,
    baseUrl,
    cancelToken,
    ...params
  }: FullRequestParams): Promise<HttpResponse<T, E>> => {
    const secureParams =
      ((typeof secure === 'boolean' ? secure : this.baseApiParams.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const queryString = query && this.toQueryString(query);
    const payloadFormatter = this.contentFormatters[type || ContentType.Json];
    const responseFormat = format || requestParams.format;

    return this.customFetch(
      `${baseUrl || this.baseUrl || ''}${path}${queryString ? `?${queryString}` : ''}`,
      {
        ...requestParams,
        headers: {
          ...(requestParams.headers || {}),
          ...(type && type !== ContentType.FormData
            ? { 'Content-Type': type }
            : {}),
        },
        signal:
          (cancelToken
            ? this.createAbortSignal(cancelToken)
            : requestParams.signal) || null,
        body:
          typeof body === 'undefined' || body === null
            ? null
            : payloadFormatter(body),
      }
    ).then(async (response) => {
      const r = response.clone() as HttpResponse<T, E>;
      r.data = null as unknown as T;
      r.error = null as unknown as E;

      const data = !responseFormat
        ? r
        : await response[responseFormat]()
            .then((data) => {
              if (r.ok) {
                r.data = data;
              } else {
                r.error = data;
              }
              return r;
            })
            .catch((e) => {
              r.error = e;
              return r;
            });

      if (cancelToken) {
        this.abortControllers.delete(cancelToken);
      }

      if (!response.ok) throw data;
      return data;
    });
  };
}

/**
 * @title Taostats Management API
 * @version v1
 * @baseUrl https://management-api.taostats.io
 *
 * API for managing Taostats accounts and related resources
 */
export class Api<
  SecurityDataType extends unknown,
> extends HttpClient<SecurityDataType> {
  api = {
    /**
     * @description Note that this instantly creates a blank user account with the fingerprint associated to it - frontends should not not call it repeatedly for the same user.
     *
     * @tags Account registration
     * @name V1AccountCreateFingerprintCreate
     * @summary Creates a new account with a randomly generated 16-digit fingerprint. This should be shown to the user so they can log in with it at a later time
     * @request POST:/api/v1/account/create/fingerprint
     */
    v1AccountCreateFingerprintCreate: (params: RequestParams = {}) =>
      this.request<
        {
          /** Fingerprint */
          fingerprint?: string;
        },
        any
      >({
        path: `/api/v1/account/create/fingerprint`,
        method: 'POST',
        format: 'json',
        ...params,
      }),

    /**
     * @description Sends a magic link to the user's email address. The urlPrefix is the frontend URL prefix to prepend to the magic link, which can then be used to call the `/login/email` endpoint to obtain a JWT.
     *
     * @tags Account registration
     * @name V1AccountCreateEmailCreate
     * @summary Sends magic link to the email address to initiate account creation
     * @request POST:/api/v1/account/create/email
     */
    v1AccountCreateEmailCreate: (
      data: {
        /** Email address to send the magic link to */
        email: string;
        /** Frontend URL prefix to prepend to the magic link */
        urlPrefix: string;
      },
      params: RequestParams = {}
    ) =>
      this.request<
        {
          /** Whether the link was sent */
          success?: boolean;
        },
        {
          /** Error message */
          error?: string;
        }
      >({
        path: `/api/v1/account/create/email`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Account management
     * @name V1AccountResetEmailCreate
     * @summary Sends a reset link to the user's email address to reset their fingerprint
     * @request POST:/api/v1/account/reset/email
     */
    v1AccountResetEmailCreate: (
      data: {
        /** Email address to tie to account */
        email: string;
      },
      params: RequestParams = {}
    ) =>
      this.request<
        {
          success?: boolean;
        },
        {
          statusCode?: number;
          error?: string;
          message?: string;
        }
      >({
        path: `/api/v1/account/reset/email`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Account management
     * @name V1AccountResetFingerprintCreate
     * @summary Updates the fingerprint on account via reset link from email
     * @request POST:/api/v1/account/reset/fingerprint
     */
    v1AccountResetFingerprintCreate: (
      data: {
        /** Email address to tie to account */
        email: string;
      },
      params: RequestParams = {}
    ) =>
      this.request<
        {
          success?: boolean;
          /** New fingerprint */
          fingerprint?: string;
        },
        {
          statusCode?: number;
          error?: string;
          message?: string;
        }
      >({
        path: `/api/v1/account/reset/fingerprint`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Account login
     * @name V1AccountLoginFingerprintCreate
     * @summary Logs in a user via their fingerprint, receiving a persistent JWT in return to auth to the dashboard
     * @request POST:/api/v1/account/login/fingerprint
     */
    v1AccountLoginFingerprintCreate: (
      data: {
        /** Fingerprint */
        fingerprint: string;
      },
      params: RequestParams = {}
    ) =>
      this.request<
        {
          /** @default "Bearer" */
          token_type?: string;
          /**
           * JWT token
           * @default "ey..."
           */
          access_token?: string;
        },
        {
          /** HTTP status code */
          statusCode?: number;
          /** Error type */
          error?: string;
          /** Error message */
          message?: string;
        }
      >({
        path: `/api/v1/account/login/fingerprint`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * @description To call this endpoint, first obtain the Github URL to initiate the flow from the public config `githubLoginUrl` property, and append your app's own redirect URL route to this. Then direct the user to it. After they have logged in on Github, it will redirect back to your app with a code parameter, which you should pass to this endpoint.
     *
     * @tags Account login
     * @name V1AccountLoginGithubCreate
     * @summary Logs in a user via a Github callback, receiving a persistent JWT in return to auth to the dashboard.
     * @request POST:/api/v1/account/login/github
     */
    v1AccountLoginGithubCreate: (
      data: {
        /** GitHub OAuth code */
        code: string;
      },
      params: RequestParams = {}
    ) =>
      this.request<
        {
          /** @default "Bearer" */
          token_type?: string;
          /**
           * JWT token
           * @default "ey..."
           */
          access_token?: string;
        },
        {
          /** HTTP status code */
          statusCode?: number;
          /** Error type */
          error?: string;
          /** Error message */
          message?: string;
        }
      >({
        path: `/api/v1/account/login/github`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * @description This endpoint should be called by the frontend app after the user clicks the magic link in their email. (The magic link is a signed JWT that contains the user's email address.)
     *
     * @tags Account login
     * @name V1AccountLoginEmailCreate
     * @summary Logs in a user via a magic link sent to their email, receiving a persistent JWT in return to auth to the dashboard
     * @request POST:/api/v1/account/login/email
     */
    v1AccountLoginEmailCreate: (
      data: {
        /** Magic link token */
        token: string;
      },
      params: RequestParams = {}
    ) =>
      this.request<
        {
          /** @default "Bearer" */
          token_type?: string;
          /**
           * JWT token
           * @default "ey..."
           */
          access_token?: string;
        },
        {
          /** HTTP status code */
          statusCode?: number;
          /** Error type */
          error?: string;
          /** Error message */
          message?: string;
        }
      >({
        path: `/api/v1/account/login/email`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * @description To call this endpoint, first obtain the Google URL to initiate the flow from the public config `googleLoginUrl` property, and append your app's own redirect URL route to this. Then direct the user to it. After they have logged in on Google, it will redirect back to your app with an access_token parameter, which you should pass to this endpoint.
     *
     * @tags Account login
     * @name V1AccountLoginGoogleCreate
     * @summary Logs in a user via Google callback, receiving a persistent JWT in return to auth to the dashboard
     * @request POST:/api/v1/account/login/google
     */
    v1AccountLoginGoogleCreate: (
      data: {
        /** Google OAuth code */
        code: string;
        /** Redirect URI */
        redirect_uri: string;
      },
      params: RequestParams = {}
    ) =>
      this.request<
        {
          /** @default "Bearer" */
          token_type?: string;
          /**
           * JWT token
           * @default "ey..."
           */
          access_token?: string;
        },
        {
          /** HTTP status code */
          statusCode?: number;
          /** Error type */
          error?: string;
          /** Error message */
          message?: string;
        }
      >({
        path: `/api/v1/account/login/google`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Account management
     * @name V1AccountRecoveryEmailUpdate
     * @summary Adds a recovery email to an aaccount.
     * @request PUT:/api/v1/account/recovery/email
     * @secure
     */
    v1AccountRecoveryEmailUpdate: (
      data: {
        /** Email address to tie to account */
        email: string;
      },
      params: RequestParams = {}
    ) =>
      this.request<
        {
          success?: boolean;
        },
        {
          statusCode?: number;
          error?: string;
          message?: string;
        }
      >({
        path: `/api/v1/account/recovery/email`,
        method: 'PUT',
        body: data,
        secure: true,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Account management
     * @name V1KeyUsageList
     * @summary Lists API key usage for the last 30 days for the current account. Response will contain up to 100 records at a time.
     * @request GET:/api/v1/key/usage
     * @secure
     */
    v1KeyUsageList: (
      query?: {
        /** API key ID */
        apiKeyId?: string;
      },
      params: RequestParams = {}
    ) =>
      this.request<
        {
          /** Total number of usage records */
          total?: number;
          /**
           * Usage list
           * @example [{"timestamp":**********,"endpoint":"/api/v1/key/usage","method":"GET","count":4}]
           */
          usage?: {
            /** Timestamp */
            timestamp?: number;
            /** Endpoint */
            endpoint?: string;
            /** Method */
            method?: string;
          }[];
        },
        any
      >({
        path: `/api/v1/key/usage`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Account management
     * @name V1KeyListList
     * @summary Lists API keys for a user account
     * @request GET:/api/v1/key/list
     * @secure
     */
    v1KeyListList: (params: RequestParams = {}) =>
      this.request<
        {
          /** API key list */
          apiKeys?: {
            /** @example "tao-abcd-1234-efgh-567890" */
            apiKeyId?: string;
            /** @example true */
            enabled?: boolean;
          }[];
        },
        any
      >({
        path: `/api/v1/key/list`,
        method: 'GET',
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Account management
     * @name V1KeyCreateCreate
     * @summary Creates a new API key for a user account
     * @request POST:/api/v1/key/create
     * @secure
     */
    v1KeyCreateCreate: (params: RequestParams = {}) =>
      this.request<
        {
          success?: boolean;
          /** API key */
          apiKey?: string;
        },
        {
          statusCode?: number;
          error?: string;
          message?: string;
        }
      >({
        path: `/api/v1/key/create`,
        method: 'POST',
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Account management
     * @name V1KeyDeleteDelete
     * @summary Deletes an API key for a user account
     * @request DELETE:/api/v1/key/delete
     * @secure
     */
    v1KeyDeleteDelete: (params: RequestParams = {}) =>
      this.request<
        {
          success?: boolean;
        },
        {
          statusCode?: number;
          error?: string;
          message?: string;
        }
      >({
        path: `/api/v1/key/delete`,
        method: 'DELETE',
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * @description Returns configuration parameters for a frontend to fully interact with the API.
     *
     * @tags Status
     * @name V1ConfigList
     * @summary Get public configuration
     * @request GET:/api/v1/config
     */
    v1ConfigList: (params: RequestParams = {}) =>
      this.request<
        {
          /** GitHub client ID */
          githubClientId?: string;
          /** GitHub app ID */
          githubAppId?: string;
          /** Google client ID */
          googleClientId?: string;
          /** URL to send the user to when logging in via Google */
          googleLoginUrl?: string;
          /** URL to send the user to when logging in via GitHub */
          githubLoginUrl?: string;
        },
        any
      >({
        path: `/api/v1/config`,
        method: 'GET',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Status
     * @name V1StatusList
     * @summary Returns the status of the API
     * @request GET:/api/v1/status
     */
    v1StatusList: (params: RequestParams = {}) =>
      this.request<'OK', 'ERROR'>({
        path: `/api/v1/status`,
        method: 'GET',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Admin
     * @name V1AdminAccountsList
     * @summary List and search for accounts as an admin
     * @request GET:/api/v1/admin/accounts
     * @secure
     */
    v1AdminAccountsList: (
      query?: {
        email?: string | null;
        type?: string | null;
        pageOffset?: number | null;
      },
      params: RequestParams = {}
    ) =>
      this.request<
        {
          accounts?: {
            accountId?: string;
            type?: string;
            email?: string | null;
            createdAt?: string;
            keyCount?: number;
          }[];
        },
        any
      >({
        path: `/api/v1/admin/accounts`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Admin
     * @name V1AdminAccountsCreate
     * @summary Update a user account as an admin
     * @request POST:/api/v1/admin/accounts
     * @secure
     */
    v1AdminAccountsCreate: (
      accountId: string,
      rateLimit: number | null,
      params: RequestParams = {}
    ) =>
      this.request<
        {
          /**
           * Whether the operation was successful
           * @example true
           */
          success?: boolean;
        },
        {
          /** Reason for failure */
          message?: string;
        }
      >({
        path: `/api/v1/admin/accounts`,
        method: 'POST',
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Admin
     * @name V1AdminKeyUsageList
     * @summary Query usage for a specific account as an admin
     * @request GET:/api/v1/admin/key/usage
     * @secure
     */
    v1AdminKeyUsageList: (
      query?: {
        /** API key ID */
        apiKeyId?: string;
        /** Account ID to query */
        accountId?: string;
      },
      params: RequestParams = {}
    ) =>
      this.request<
        {
          /** Total number of usage records */
          total?: number;
          /**
           * Usage list
           * @example [{"timestamp":**********,"endpoint":"/api/v1/key/usage","method":"GET","count":4}]
           */
          usage?: {
            /** Timestamp */
            timestamp?: number;
            /** Endpoint */
            endpoint?: string;
            /** Method */
            method?: string;
          }[];
        },
        any
      >({
        path: `/api/v1/admin/key/usage`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Admin
     * @name V1AdminTopusageList
     * @summary Get top performing accounts and API keys by API usage for a given time period
     * @request GET:/api/v1/admin/topusage
     * @secure
     */
    v1AdminTopusageList: (
      query?: {
        /** Start date to query */
        startDate?: string;
        /** End date to query */
        endDate?: string;
        /** Limit the number of results per category */
        limit?: number;
      },
      params: RequestParams = {}
    ) =>
      this.request<
        {
          /** Top n API account-keys with the highest number of API calls */
          apiKeys?: {
            /** Account ID */
            accountId?: string;
            /** API key ID */
            apiKeyId?: string;
            /** Email */
            email?: string;
            /** Usage */
            apiCalls?: number;
          }[];
        },
        any
      >({
        path: `/api/v1/admin/topusage`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),
  };
}

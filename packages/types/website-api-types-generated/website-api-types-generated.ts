/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

export enum AccountHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface AccountHistoryRequest {
  /** SS58 or hex format */
  address: string;
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  /** @format int32 */
  limit?: number;
  /** finney, nakamoto, kusanagi */
  network?: string | null;
  order?: AccountHistoryOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface AccountItem {
  address: Key;
  balance_free: string;
  balance_staked: string;
  balance_staked_alpha_as_tao: string;
  balance_staked_root: string;
  balance_total: string;
  /** @format int32 */
  block_number: number;
  coldkey_swap?: ColdkeySwapItem | null;
  /** @format date */
  created_on_date: string;
  created_on_network: string;
  network: string;
  /** @format int32 */
  rank: number;
  /** @format date-time */
  timestamp: string;
}

export enum AccountOrder {
  BalanceFreeAsc = 'balance_free_asc',
  BalanceFreeDesc = 'balance_free_desc',
  BalanceStakedAsc = 'balance_staked_asc',
  BalanceStakedDesc = 'balance_staked_desc',
  BalanceStakedRootAsc = 'balance_staked_root_asc',
  BalanceStakedRootDesc = 'balance_staked_root_desc',
  BalanceStakedAlphaAsTaoAsc = 'balance_staked_alpha_as_tao_asc',
  BalanceStakedAlphaAsTaoDesc = 'balance_staked_alpha_as_tao_desc',
  BalanceTotalAsc = 'balance_total_asc',
  BalanceTotalDesc = 'balance_total_desc',
  CreatedAtTimestampAsc = 'created_at_timestamp_asc',
  CreatedAtTimestampDesc = 'created_at_timestamp_desc',
}

export interface AccountRequest {
  /** SS58 or hex format */
  address?: string | null;
  balance_free_max?: string | null;
  balance_free_min?: string | null;
  balance_staked_alpha_as_tao_max?: string | null;
  balance_staked_alpha_as_tao_min?: string | null;
  balance_staked_max?: string | null;
  balance_staked_min?: string | null;
  balance_staked_root_max?: string | null;
  balance_staked_root_min?: string | null;
  balance_total_max?: string | null;
  balance_total_min?: string | null;
  /** finney, nakamoto, kusanagi */
  created_on_network?: string | null;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  created_on_timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  created_on_timestamp_start?: number | null;
  /** @format int32 */
  limit?: number;
  order?: AccountOrder;
  /** @format int32 */
  page?: number;
  /** @format int32 */
  rank?: number | null;
}

export interface AccountResponse {
  data: AccountItem[];
  pagination: Pagination;
}

export interface AccountingItem {
  income: string;
  neuron_registration_cost: string;
  neuron_registrations: SubnetNeuronRegistrationItem[];
  stake_balances: StakeBalanceItem[];
}

export interface AccountingRequest {
  /** SS58 or hex format */
  coldkey: string;
  /**
   * End of date range in YYYY-MM-DD format (inclusive)
   * @format date
   */
  date_end: string;
  /**
   * Start of date range in YYYY-MM-DD format (inclusive)
   * @format date
   */
  date_start: string;
  hotkey?: string | null;
  network?: Network;
}

export interface AccountingResponse {
  data: AccountingItem[];
  pagination: Pagination;
}

export interface BlockIntervalItem {
  /** @format int32 */
  block_number: number;
  /** @format date-time */
  timestamp: string;
}

export enum BlockIntervalOrder {
  DateAsc = 'date_asc',
  DateDesc = 'date_desc',
}

export interface BlockIntervalRequest {
  frequency?: FrequencyHourDay;
  /** @format int32 */
  limit?: number;
  order?: BlockIntervalOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end: number;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start: number;
}

export interface BlockIntervalResponse {
  data: BlockIntervalItem[];
  pagination: Pagination;
}

export interface BlockItem {
  /** @format int32 */
  block_number: number;
  /** @format int32 */
  calls_count: number;
  /** @format int32 */
  events_count: number;
  /** @format int32 */
  extrinsics_count: number;
  extrinsics_root: string;
  hash: string;
  impl_name: string;
  /** @format int32 */
  impl_version: number;
  parent_hash: string;
  spec_name: string;
  /** @format int32 */
  spec_version: number;
  state_root: string;
  /** @format date-time */
  timestamp: string;
  validator?: string | null;
}

export enum BlockOrder {
  SpecVersionAsc = 'spec_version_asc',
  SpecVersionDesc = 'spec_version_desc',
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
  HashAsc = 'hash_asc',
  HashDesc = 'hash_desc',
  EventsCountAsc = 'events_count_asc',
  EventsCountDesc = 'events_count_desc',
  ExtrinsicsCountAsc = 'extrinsics_count_asc',
  ExtrinsicsCountDesc = 'extrinsics_count_desc',
}

export interface BlockRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  hash?: string | null;
  /** @format int32 */
  limit?: number;
  order?: BlockOrder;
  /** @format int32 */
  page?: number;
  /** @format int32 */
  spec_version?: number | null;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
  validator?: string | null;
}

export interface BlockResponse {
  data: BlockItem[];
  pagination: Pagination;
}

export interface CallItem {
  args?: any;
  /** @format int32 */
  block_number: number;
  error?: any;
  extrinsic_id: string;
  /** @format int32 */
  extrinsic_index?: number | null;
  full_name: string;
  id: string;
  name: string;
  origin?: any;
  origin_address?: string | null;
  pallet: string;
  parent_id?: string | null;
  success: boolean;
  /** @format date-time */
  timestamp: string;
}

export enum CallOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface CallRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  extrinsic_id?: string | null;
  full_name?: string | null;
  id?: string | null;
  /** @format int32 */
  limit?: number;
  network?: Network;
  order?: CallOrder;
  /** SS58 or hex format */
  origin_address?: string | null;
  /** @format int32 */
  page?: number;
  parent_id?: string | null;
  success?: boolean | null;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface CallResponse {
  data: CallItem[];
  pagination: Pagination;
}

export interface ColdkeyReportCsvRequest {
  /** SS58 or hex format */
  coldkey: string;
  /**
   * End of date range in YYYY-MM-DD format (inclusive). Must be within 12 calendar months of date_start.
   * @format date
   */
  date_end: string;
  /**
   * Start of date range in YYYY-MM-DD format (inclusive)
   * @format date
   */
  date_start: string;
}

export interface ColdkeyReportItem {
  additional_data?: string | null;
  /** @format int32 */
  block_number: number;
  coldkey: string;
  credit_amount?: string | null;
  daily_income?: string | null;
  daily_income_usd?: string | null;
  /** @format date */
  date?: string | null;
  debit_amount?: string | null;
  extrinsic_id?: string | null;
  free_balance?: string | null;
  locked_balance?: string | null;
  network: string;
  staked_balance?: string | null;
  tao_price?: string | null;
  /** @format date-time */
  timestamp: string;
  total_balance?: string | null;
  transaction_type?: string | null;
}

export interface ColdkeyReportRequest {
  /** SS58 or hex format */
  coldkey: string;
  /**
   * End of date range in YYYY-MM-DD format (inclusive). Must be within 12 calendar months of date_start.
   * @format date
   */
  date_end: string;
  /**
   * Start of date range in YYYY-MM-DD format (inclusive)
   * @format date
   */
  date_start: string;
}

export interface ColdkeyReportResponse {
  data: ColdkeyReportItem[];
  pagination: Pagination;
}

export interface ColdkeySwapItem {
  /** @format int32 */
  block_number: number;
  network: string;
  new_coldkey: Key;
  old_coldkey: Key;
  /** @format date-time */
  timestamp: string;
}

export interface ConfigResponse {
  currency_codes: CurrencyCode[];
  exchanges: string[];
  supported_resolutions: string[];
  supports_group_request: boolean;
  supports_marks: boolean;
  supports_search: boolean;
  supports_timescale_marks: boolean;
}

export interface CurrencyCode {
  code: string;
  description: string;
  id: string;
}

export enum DelegationAction {
  Delegate = 'delegate',
  Undelegate = 'undelegate',
  All = 'all',
}

export interface DelegationBalanceItem {
  balance: string;
  /** @format int32 */
  block_number: number;
  delegate: Key;
  /** @format int32 */
  delegate_from: number;
  /** @format date-time */
  delegate_from_timestamp: string;
  nominator: Key;
  /** @format date-time */
  timestamp: string;
}

export enum DelegationBalanceOrder {
  BalanceAsc = 'balance_asc',
  BalanceDesc = 'balance_desc',
  DelegateFromAsc = 'delegate_from_asc',
  DelegateFromDesc = 'delegate_from_desc',
}

export interface DelegationBalanceRequest {
  /** SS58 or hex format */
  delegate?: string | null;
  /** @format int32 */
  limit?: number;
  /** SS58 or hex format */
  nominator?: string | null;
  order?: DelegationBalanceOrder;
  /** @format int32 */
  page?: number;
}

export interface DelegationBalanceResponse {
  data: DelegationBalanceItem[];
  pagination: Pagination;
}

export interface DelegationItem {
  action: string;
  alpha?: string | null;
  alpha_price_in_tao?: string | null;
  alpha_price_in_usd?: string | null;
  amount: string;
  /** @format int32 */
  block_number: number;
  delegate: Key;
  extrinsic_id?: string | null;
  id: string;
  /** @format int32 */
  netuid?: number | null;
  nominator: Key;
  /** @format date-time */
  timestamp: string;
  usd?: string | null;
}

export enum DelegationOrder {
  AmountAsc = 'amount_asc',
  AmountDesc = 'amount_desc',
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
  ActionAsc = 'action_asc',
  ActionDesc = 'action_desc',
  AlphaAsc = 'alpha_asc',
  AlphaDesc = 'alpha_desc',
  UsdAsc = 'usd_asc',
  UsdDesc = 'usd_desc',
  AlphaPriceInTaoAsc = 'alpha_price_in_tao_asc',
  AlphaPriceInTaoDesc = 'alpha_price_in_tao_desc',
  AlphaPriceInUsdAsc = 'alpha_price_in_usd_asc',
  AlphaPriceInUsdDesc = 'alpha_price_in_usd_desc',
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
}

export interface DelegationRequest {
  action?: DelegationAction;
  /** Maximum amount (inclusive) */
  amount_max?: string | null;
  /** Minimum amount (inclusive) */
  amount_min?: string | null;
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  /** SS58 or hex format */
  delegate?: string | null;
  extrinsic_id?: string | null;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  netuid?: number | null;
  /** SS58 or hex format */
  nominator?: string | null;
  order?: DelegationOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface DelegationResponse {
  data: DelegationItem[];
  pagination: Pagination;
}

export interface DtaoCandlestickRow {
  /** @format int32 */
  period_end: number;
  /** @format double */
  price_close: number;
  /** @format double */
  price_high: number;
  /** @format double */
  price_low: number;
  /** @format double */
  price_open: number;
  /** @format double */
  volume: number;
}

export enum DtaoColdkeyAlphaSharesHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
}

export interface DtaoColdkeyAlphaSharesHistoryRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  coldkey?: string | null;
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  netuid?: number | null;
  order?: DtaoColdkeyAlphaSharesHistoryOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface DtaoColdkeyAlphaSharesItem {
  alpha: string;
  /** @format int32 */
  block_number: number;
  coldkey: Key;
  hotkey: Key;
  /** @format int32 */
  netuid: number;
  shares: any;
  /** @format date-time */
  timestamp: string;
}

export enum DtaoColdkeyAlphaSharesLatestOrder {
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
  SharesAsc = 'shares_asc',
  SharesDesc = 'shares_desc',
  AlphaAsc = 'alpha_asc',
  AlphaDesc = 'alpha_desc',
}

export interface DtaoColdkeyAlphaSharesLatestRequest {
  alpha_max?: string | null;
  alpha_min?: string | null;
  coldkey?: string | null;
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  netuid?: number | null;
  order?: DtaoColdkeyAlphaSharesLatestOrder;
  /** @format int32 */
  page?: number;
}

export interface DtaoColdkeyAlphaSharesResponse {
  data: DtaoColdkeyAlphaSharesItem[];
  pagination: Pagination;
}

export enum DtaoDelegationFrequency {
  Value15Min = '15min',
  Value60Min = '60min',
  Value1Day = '1day',
}

export interface DtaoDelegationVolumeItem {
  alpha_volume_as_tao: string;
  /** @format int32 */
  block_number: number;
  proportion_alpha: string;
  root_volume: string;
  /** @format date-time */
  timestamp: string;
}

export interface DtaoDelegationVolumeRequest {
  frequency?: DtaoDelegationFrequency;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  page?: number;
}

export interface DtaoDelegationVolumeResponse {
  data: DtaoDelegationVolumeItem[];
  pagination: Pagination;
}

export enum DtaoHotkeyAlphaSharesHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
}

export interface DtaoHotkeyAlphaSharesHistoryRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  netuid?: number | null;
  order?: DtaoHotkeyAlphaSharesHistoryOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface DtaoHotkeyAlphaSharesItem {
  alpha: string;
  /** @format int32 */
  block_number: number;
  hotkey: Key;
  /** @format int32 */
  netuid: number;
  shares: any;
  /** @format date-time */
  timestamp: string;
}

export enum DtaoHotkeyAlphaSharesLatestOrder {
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
  SharesAsc = 'shares_asc',
  SharesDesc = 'shares_desc',
  AlphaAsc = 'alpha_asc',
  AlphaDesc = 'alpha_desc',
}

export interface DtaoHotkeyAlphaSharesLatestRequest {
  alpha_max?: string | null;
  alpha_min?: string | null;
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  netuid?: number | null;
  order?: DtaoHotkeyAlphaSharesLatestOrder;
  /** @format int32 */
  page?: number;
}

export interface DtaoHotkeyAlphaSharesResponse {
  data: DtaoHotkeyAlphaSharesItem[];
  pagination: Pagination;
}

export interface DtaoHotkeyEmissionItem {
  /** @format int32 */
  block_number: number;
  emission: string;
  hotkey: Key;
  /** @format int32 */
  netuid: number;
  root_emission: string;
  /** @format date-time */
  timestamp: string;
}

export enum DtaoHotkeyEmissionOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
  EmissionAsc = 'emission_asc',
  EmissionDesc = 'emission_desc',
  RootEmissionAsc = 'root_emission_asc',
  RootEmissionDesc = 'root_emission_desc',
}

export interface DtaoHotkeyEmissionRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  netuid?: number | null;
  order?: DtaoHotkeyEmissionOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface DtaoHotkeyEmissionResponse {
  data: DtaoHotkeyEmissionItem[];
  pagination: Pagination;
}

export interface DtaoPoolDailyPriceItem {
  /** @format int32 */
  block_number: number;
  price: string;
  /** @format date-time */
  timestamp: string;
}

export interface DtaoPoolHistoryItem {
  alpha_in_pool: string;
  alpha_staked: string;
  /** @format int32 */
  block_number: number;
  liquidity: string;
  market_cap: string;
  name: string;
  /** @format int32 */
  netuid: number;
  price: string;
  symbol: string;
  /** @format date-time */
  timestamp: string;
  total_alpha: string;
  total_tao: string;
}

export enum DtaoPoolHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface DtaoPoolHistoryRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  netuid?: number | null;
  order?: DtaoPoolHistoryOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface DtaoPoolHistoryResponse {
  data: DtaoPoolHistoryItem[];
  pagination: Pagination;
}

export interface DtaoPoolItem {
  alpha_in_pool: string;
  alpha_staked: string;
  /** @format int32 */
  block_number: number;
  /** @format int32 */
  buyers_24_hr: number;
  /** @format int32 */
  buys_24_hr: number;
  liquidity: string;
  market_cap: string;
  market_cap_change_1_day?: string | null;
  name: string;
  /** @format int32 */
  netuid: number;
  price: string;
  price_change_1_day?: string | null;
  price_change_1_hour?: string | null;
  price_change_1_week?: string | null;
  /** @format int32 */
  rank: number;
  /** @format int32 */
  sellers_24_hr: number;
  /** @format int32 */
  sells_24_hr: number;
  seven_day_prices: DtaoPoolDailyPriceItem[];
  symbol: string;
  tao_buy_volume_24_hr: string;
  tao_sell_volume_24_hr: string;
  tao_volume_24_hr: string;
  tao_volume_24_hr_change_1_day?: string | null;
  /** @format date-time */
  timestamp: string;
  total_alpha: string;
  total_tao: string;
}

export enum DtaoPoolOrder {
  RankAsc = 'rank_asc',
  RankDesc = 'rank_desc',
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
  PriceAsc = 'price_asc',
  PriceDesc = 'price_desc',
  MarketCapAsc = 'market_cap_asc',
  MarketCapDesc = 'market_cap_desc',
  MarketCapChangeOneDayAsc = 'market_cap_change_one_day_asc',
  MarketCapChangeOneDayDesc = 'market_cap_change_one_day_desc',
  TotalTaoAsc = 'total_tao_asc',
  TotalTaoDesc = 'total_tao_desc',
  TotalAlphaAsc = 'total_alpha_asc',
  TotalAlphaDesc = 'total_alpha_desc',
  AlphaInPoolAsc = 'alpha_in_pool_asc',
  AlphaInPoolDesc = 'alpha_in_pool_desc',
  AlphaStakedAsc = 'alpha_staked_asc',
  AlphaStakedDesc = 'alpha_staked_desc',
  TaoVolumeOneDayAsc = 'tao_volume_one_day_asc',
  TaoVolumeOneDayDesc = 'tao_volume_one_day_desc',
  PriceChangeOneHourAsc = 'price_change_one_hour_asc',
  PriceChangeOneHourDesc = 'price_change_one_hour_desc',
  PriceChangeOneDayAsc = 'price_change_one_day_asc',
  PriceChangeOneDayDesc = 'price_change_one_day_desc',
  PriceChangeOneWeekAsc = 'price_change_one_week_asc',
  PriceChangeOneWeekDesc = 'price_change_one_week_desc',
}

export interface DtaoPoolRequest {
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  netuid?: number | null;
  order?: DtaoPoolOrder;
  /** @format int32 */
  page?: number;
}

export interface DtaoPoolResponse {
  data: DtaoPoolItem[];
  pagination: Pagination;
}

export enum DtaoSlippageDirection {
  TaoToAlpha = 'tao_to_alpha',
  AlphaToTao = 'alpha_to_tao',
}

export interface DtaoSlippageItem {
  alpha_price: string;
  /** @format int32 */
  block_number: number;
  diff: string;
  expected_output_tokens: string;
  /** @format int32 */
  netuid: number;
  output_tokens: string;
  slippage: string;
  /** @format date-time */
  timestamp: string;
}

export interface DtaoSlippageRequest {
  direction: DtaoSlippageDirection;
  input_tokens: string;
  /** @format int32 */
  netuid: number;
}

export interface DtaoSlippageResponse {
  data: DtaoSlippageItem[];
  pagination: Pagination;
}

export interface DtaoStakeBalanceAggregatedItem {
  /** @format int32 */
  block_number: number;
  coldkey: Key;
  /** @format int32 */
  rank: number;
  /** @format date-time */
  timestamp: string;
  total_balance_as_tao: string;
}

export enum DtaoStakeBalanceAggregatedLatestOrder {
  TotalBalanceAsTaoAsc = 'total_balance_as_tao_asc',
  TotalBalanceAsTaoDesc = 'total_balance_as_tao_desc',
}

export interface DtaoStakeBalanceAggregatedLatestRequest {
  /** SS58 or hex format */
  coldkey?: string | null;
  /** @format int32 */
  limit?: number;
  order?: DtaoStakeBalanceAggregatedLatestOrder;
  /** @format int32 */
  page?: number;
  total_balance_as_tao_max?: string | null;
  total_balance_as_tao_min?: string | null;
}

export interface DtaoStakeBalanceAggregatedResponse {
  data: DtaoStakeBalanceAggregatedItem[];
  pagination: Pagination;
}

export interface DtaoStakeBalanceHistoryRequest {
  /** SS58 or hex format */
  coldkey: string;
  /** SS58 or hex format */
  hotkey: string;
  /** @format int32 */
  netuid: number;
}

export interface DtaoStakeBalanceItem {
  balance: string;
  balance_as_tao: string;
  /** @format int32 */
  block_number: number;
  coldkey: Key;
  hotkey: Key;
  hotkey_name?: string | null;
  /** @format int32 */
  netuid: number;
  /** @format date-time */
  timestamp: string;
}

export interface DtaoStakeBalanceLatestItem {
  balance: string;
  balance_as_tao: string;
  /** @format int32 */
  block_number: number;
  coldkey: Key;
  hotkey: Key;
  hotkey_name?: string | null;
  /** @format int32 */
  netuid: number;
  /** @format int32 */
  subnet_rank: number;
  /** @format date-time */
  timestamp: string;
}

export enum DtaoStakeBalanceLatestOrder {
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
  SubnetRankAsc = 'subnet_rank_asc',
  SubnetRankDesc = 'subnet_rank_desc',
  BalanceAsc = 'balance_asc',
  BalanceDesc = 'balance_desc',
  BalanceAsTaoAsc = 'balance_as_tao_asc',
  BalanceAsTaoDesc = 'balance_as_tao_desc',
}

export interface DtaoStakeBalanceLatestRequest {
  balance_as_tao_max?: string | null;
  balance_as_tao_min?: string | null;
  balance_max?: string | null;
  balance_min?: string | null;
  /** SS58 or hex format */
  coldkey?: string | null;
  /** SS58 or hex format */
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  netuid?: number | null;
  order?: DtaoStakeBalanceLatestOrder;
  /** @format int32 */
  page?: number;
}

export interface DtaoStakeBalanceLatestResponse {
  data: DtaoStakeBalanceLatestItem[];
  pagination: Pagination;
}

export interface DtaoStakeBalanceResponse {
  data: DtaoStakeBalanceItem[];
  pagination: Pagination;
}

export interface DtaoSubnetEmissionItem {
  alpha_in_pool: string;
  alpha_rewards: string;
  /** @format int32 */
  block_number: number;
  name: string;
  /** @format int32 */
  netuid: number;
  symbol: string;
  tao_in_pool: string;
  /** @format date-time */
  timestamp: string;
}

export enum DtaoSubnetEmissionOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
  TaoInPoolAsc = 'tao_in_pool_asc',
  TaoInPoolDesc = 'tao_in_pool_desc',
  AlphaInPoolAsc = 'alpha_in_pool_asc',
  AlphaInPoolDesc = 'alpha_in_pool_desc',
  AlphaRewardsAsc = 'alpha_rewards_asc',
  AlphaRewardsDesc = 'alpha_rewards_desc',
}

export interface DtaoSubnetEmissionRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  netuid?: number | null;
  order?: DtaoSubnetEmissionOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface DtaoSubnetEmissionResponse {
  data: DtaoSubnetEmissionItem[];
  pagination: Pagination;
}

export interface DtaoSymbolInfo {
  name: string;
  /** @format int32 */
  netuid: number;
  symbol: string;
}

export interface DtaoValidatorAvailableItem {
  address: Key;
  hotkey_alpha: string;
  name?: string | null;
  /** @format int32 */
  netuid: number;
}

export interface DtaoValidatorAvailableRequest {
  /** @format int32 */
  netuid?: number | null;
}

export interface DtaoValidatorAvailableResponse {
  data: DtaoValidatorAvailableItem[];
  pagination: Pagination;
}

export enum DtaoValidatorHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface DtaoValidatorHistoryRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  /** SS58 or hex format */
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  order?: DtaoValidatorHistoryOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface DtaoValidatorItem {
  /** @format int32 */
  active_subnets: number;
  /** @format int32 */
  alpha_rank: number;
  /** @format int32 */
  block_number: number;
  coldkey: Key;
  /** @format date */
  created_on_date: string;
  dominance: string;
  dominance_24_hr_change: string;
  global_alpha_stake_as_tao: string;
  /** @format int32 */
  global_nominators: number;
  /** @format int32 */
  global_nominators_24_hr_change: number;
  global_weighted_stake: string;
  global_weighted_stake_24_hr_change: string;
  hotkey: Key;
  name?: string | null;
  nominator_return_per_day: string;
  /** @format int32 */
  rank: number;
  /** @format int32 */
  root_rank: number;
  root_stake: string;
  take: string;
  /** @format date-time */
  timestamp: string;
  validator_return_per_day: string;
  weighted_root_stake: string;
}

export enum DtaoValidatorLatestOrder {
  ActiveSubnetsAsc = 'active_subnets_asc',
  ActiveSubnetsDesc = 'active_subnets_desc',
  RankAsc = 'rank_asc',
  RankDesc = 'rank_desc',
  RootRankAsc = 'root_rank_asc',
  RootRankDesc = 'root_rank_desc',
  AlphaRankAsc = 'alpha_rank_asc',
  AlphaRankDesc = 'alpha_rank_desc',
  TakeAsc = 'take_asc',
  TakeDesc = 'take_desc',
  GlobalNominatorsAsc = 'global_nominators_asc',
  GlobalNominatorsDesc = 'global_nominators_desc',
  GlobalWeightedStakeAsc = 'global_weighted_stake_asc',
  GlobalWeightedStakeDesc = 'global_weighted_stake_desc',
  GlobalAlphaStakeAsTaoAsc = 'global_alpha_stake_as_tao_asc',
  GlobalAlphaStakeAsTaoDesc = 'global_alpha_stake_as_tao_desc',
  WeightedRootStakeAsc = 'weighted_root_stake_asc',
  WeightedRootStakeDesc = 'weighted_root_stake_desc',
  RootStakeAsc = 'root_stake_asc',
  RootStakeDesc = 'root_stake_desc',
  DominanceAsc = 'dominance_asc',
  DominanceDesc = 'dominance_desc',
}

export interface DtaoValidatorLatestRequest {
  /** SS58 or hex format */
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  order?: DtaoValidatorLatestOrder;
  /** @format int32 */
  page?: number;
}

export enum DtaoValidatorPerformanceHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface DtaoValidatorPerformanceHistoryRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  /** SS58 or hex format */
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  netuid?: number | null;
  order?: DtaoValidatorPerformanceHistoryOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface DtaoValidatorPerformanceItem {
  alpha: string;
  /** @format int32 */
  block_number: number;
  childkey_take: string;
  coldkey: Key;
  dividends: string;
  dominance: string;
  family_alpha: string;
  family_root_weight: string;
  family_subnet_weight: string;
  hotkey: Key;
  /** @format int32 */
  last_updated?: number | null;
  name?: string | null;
  /** @format int32 */
  netuid: number;
  nominator_return_per_day: string;
  /** @format int32 */
  nominators: number;
  /** @format int32 */
  position: number;
  proportion: string;
  root_weight: string;
  subnet_weight: string;
  take: string;
  /** @format date-time */
  timestamp: string;
  /** @format int32 */
  uid: number;
  validator_return_per_day: string;
  validator_type: string;
  vtrust?: string | null;
}

export enum DtaoValidatorPerformanceLatestOrder {
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
  UidAsc = 'uid_asc',
  UidDesc = 'uid_desc',
  NominatorsAsc = 'nominators_asc',
  NominatorsDesc = 'nominators_desc',
  PositionAsc = 'position_asc',
  PositionDesc = 'position_desc',
  LastUpdatedAsc = 'last_updated_asc',
  LastUpdatedDesc = 'last_updated_desc',
  VTrustAsc = 'v_trust_asc',
  VTrustDesc = 'v_trust_desc',
  TypeAsc = 'type_asc',
  TypeDesc = 'type_desc',
  TakeAsc = 'take_asc',
  TakeDesc = 'take_desc',
  ChildkeyTakeAsc = 'childkey_take_asc',
  ChildkeyTakeDesc = 'childkey_take_desc',
  ProportionAsc = 'proportion_asc',
  ProportionDesc = 'proportion_desc',
  SubnetWeightAsc = 'subnet_weight_asc',
  SubnetWeightDesc = 'subnet_weight_desc',
  RootWeightAsc = 'root_weight_asc',
  RootWeightDesc = 'root_weight_desc',
  AlphaAsc = 'alpha_asc',
  AlphaDesc = 'alpha_desc',
  FamilySubnetWeightAsc = 'family_subnet_weight_asc',
  FamilySubnetWeightDesc = 'family_subnet_weight_desc',
  FamilyRootWeightAsc = 'family_root_weight_asc',
  FamilyRootWeightDesc = 'family_root_weight_desc',
  FamilyAlphaAsc = 'family_alpha_asc',
  FamilyAlphaDesc = 'family_alpha_desc',
  DominanceAsc = 'dominance_asc',
  DominanceDesc = 'dominance_desc',
  DividendsAsc = 'dividends_asc',
  DividendsDesc = 'dividends_desc',
  NominatorReturnPerDayAsc = 'nominator_return_per_day_asc',
  NominatorReturnPerDayDesc = 'nominator_return_per_day_desc',
  ValidatorReturnPerDayAsc = 'validator_return_per_day_asc',
  ValidatorReturnPerDayDesc = 'validator_return_per_day_desc',
}

export interface DtaoValidatorPerformanceLatestRequest {
  /** SS58 or hex format */
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  netuid?: number | null;
  order?: DtaoValidatorPerformanceLatestOrder;
  /** @format int32 */
  page?: number;
}

export interface DtaoValidatorPerformanceResponse {
  data: DtaoValidatorPerformanceItem[];
  pagination: Pagination;
}

export interface DtaoValidatorResponse {
  data: DtaoValidatorItem[];
  pagination: Pagination;
}

export enum DtaoValidatorReturnsHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface DtaoValidatorReturnsHistoryRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  /** SS58 or hex format */
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  netuid?: number | null;
  order?: DtaoValidatorReturnsHistoryOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface DtaoValidatorReturnsItem {
  /** @format int32 */
  block_number: number;
  daily_alpha: string;
  daily_alpha_as_tao: string;
  hotkey: Key;
  hotkey_alpha: string;
  name?: string | null;
  /** @format int32 */
  netuid: number;
  nominator_return_per_day: string;
  nominator_return_per_day_as_tao: string;
  nominator_return_per_day_per_1000_alpha: string;
  take: string;
  /** @format date-time */
  timestamp: string;
  validator_return_per_day: string;
  validator_return_per_day_as_tao: string;
}

export enum DtaoValidatorReturnsLatestOrder {
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
  TakeAsc = 'take_asc',
  TakeDesc = 'take_desc',
  HotkeyAlphaAsc = 'hotkey_alpha_asc',
  HotkeyAlphaDesc = 'hotkey_alpha_desc',
  DailyAlphaAsc = 'daily_alpha_asc',
  DailyAlphaDesc = 'daily_alpha_desc',
  NominatorReturnPerDayAsc = 'nominator_return_per_day_asc',
  NominatorReturnPerDayDesc = 'nominator_return_per_day_desc',
  ValidatorReturnPerDayAsc = 'validator_return_per_day_asc',
  ValidatorReturnPerDayDesc = 'validator_return_per_day_desc',
  DailyAlphaAsTaoAsc = 'daily_alpha_as_tao_asc',
  DailyAlphaAsTaoDesc = 'daily_alpha_as_tao_desc',
  NominatorReturnPerDayAsTaoAsc = 'nominator_return_per_day_as_tao_asc',
  NominatorReturnPerDayAsTaoDesc = 'nominator_return_per_day_as_tao_desc',
  ValidatorReturnPerDayAsTaoAsc = 'validator_return_per_day_as_tao_asc',
  ValidatorReturnPerDayAsTaoDesc = 'validator_return_per_day_as_tao_desc',
  NominatorReturnPerDayPer1000AlphaAsc = 'nominator_return_per_day_per1000_alpha_asc',
  NominatorReturnPerDayPer1000AlphaDesc = 'nominator_return_per_day_per1000_alpha_desc',
}

export interface DtaoValidatorReturnsLatestRequest {
  /** SS58 or hex format */
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  netuid?: number | null;
  order?: DtaoValidatorReturnsLatestOrder;
  /** @format int32 */
  page?: number;
}

export interface DtaoValidatorReturnsResponse {
  data: DtaoValidatorReturnsItem[];
  pagination: Pagination;
}

export interface EVMAddressFromSS58Request {
  ss58_address: string;
}

export interface EVMBlockItem {
  base_fee_per_gas: string;
  difficulty: string;
  extra_data: string;
  gas_limit: string;
  gas_used: string;
  hash: string;
  miner: string;
  nonce?: string | null;
  /** @format int32 */
  number: number;
  parent_hash: string;
  sha_3_uncles: string;
  /** @format int32 */
  size: number;
  state_root: string;
  /** @format date-time */
  timestamp: string;
  total_difficulty: string;
  transaction_root?: string | null;
  uncles?: string | null;
}

export enum EVMBlockOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface EVMBlockRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  /** @format int32 */
  limit?: number;
  order?: EVMBlockOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface EVMBlockResponse {
  data: EVMBlockItem[];
  pagination: Pagination;
}

export interface EVMContractItem {
  abi?: any;
  address: string;
  /** @format int32 */
  block_number: number;
  created_by: string;
  /** @format int32 */
  decimals?: number | null;
  erc1155: boolean;
  erc165: boolean;
  erc20: boolean;
  erc721: boolean;
  input: string;
  name?: string | null;
  owner?: string | null;
  symbol?: string | null;
  /** @format date-time */
  timestamp: string;
  transaction_hash: string;
  uniswap_v2: boolean;
  uniswap_v3: boolean;
}

export enum EVMContractOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface EVMContractRequest {
  address?: string | null;
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  /** @format int32 */
  limit?: number;
  order?: EVMContractOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface EVMContractResponse {
  data: EVMContractItem[];
  pagination: Pagination;
}

export interface EVMERC20AccountItem {
  address: string;
  balance: string;
  /** @format int32 */
  first_active_block_number: number;
  /** @format date-time */
  first_active_timestamp: string;
  /** @format int32 */
  last_active_block_number: number;
  /** @format date-time */
  last_active_timestamp: string;
  token_address: string;
  /** @format int32 */
  token_decimals: number;
  token_name: string;
  token_symbol: string;
  /** @format int32 */
  total_transfers: number;
}

export enum EVMERC20AccountOrder {
  LastActiveBlockNumberAsc = 'last_active_block_number_asc',
  LastActiveBlockNumberDesc = 'last_active_block_number_desc',
  LastActiveTimestampAsc = 'last_active_timestamp_asc',
  LastActiveTimestampDesc = 'last_active_timestamp_desc',
  FirstActiveBlockNumberAsc = 'first_active_block_number_asc',
  FirstActiveBlockNumberDesc = 'first_active_block_number_desc',
  FirstActiveTimestampAsc = 'first_active_timestamp_asc',
  FirstActiveTimestampDesc = 'first_active_timestamp_desc',
  BalanceAsc = 'balance_asc',
  BalanceDesc = 'balance_desc',
}

export interface EVMERC20AccountRequest {
  address?: string | null;
  /** Maximum balance (inclusive) */
  balance_max?: string | null;
  /** Minimum balance (inclusive) */
  balance_min?: string | null;
  /** @format int32 */
  limit?: number;
  order?: EVMERC20AccountOrder;
  /** @format int32 */
  page?: number;
  token_address?: string | null;
  token_name?: string | null;
  token_symbol?: string | null;
}

export interface EVMERC20AccountResponse {
  data: EVMERC20AccountItem[];
  pagination: Pagination;
}

export interface EVMERC20TokenItem {
  address: string;
  /** @format int32 */
  created_at_block_number: number;
  /** @format date-time */
  created_at_timestamp: string;
  created_by: string;
  /** @format int32 */
  decimals: number;
  name: string;
  symbol: string;
  transaction_hash: string;
}

export enum EVMERC20TokenOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface EVMERC20TokenRequest {
  address?: string | null;
  /** @format int32 */
  limit?: number;
  name?: string | null;
  order?: EVMERC20TokenOrder;
  /** @format int32 */
  page?: number;
  symbol?: string | null;
}

export interface EVMERC20TokenResponse {
  data: EVMERC20TokenItem[];
  pagination: Pagination;
}

export interface EVMERC20TransferItem {
  amount: string;
  /** @format int32 */
  block_number: number;
  from: string;
  /** @format date-time */
  timestamp: string;
  to: string;
  token_address: string;
  /** @format int32 */
  token_decimals: number;
  token_name: string;
  token_symbol: string;
  transaction_hash: string;
}

export enum EVMERC20TransferOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
  AmountAsc = 'amount_asc',
  AmountDesc = 'amount_desc',
}

export interface EVMERC20TransferRequest {
  address?: string | null;
  /** Maximum amount (inclusive) */
  amount_max?: string | null;
  /** Minimum amount (inclusive) */
  amount_min?: string | null;
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  from?: string | null;
  /** @format int32 */
  limit?: number;
  order?: EVMERC20TransferOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
  to?: string | null;
  token_address?: string | null;
  token_name?: string | null;
  token_symbol?: string | null;
  transaction_hash?: string | null;
}

export interface EVMERC20TransferResponse {
  data: EVMERC20TransferItem[];
  pagination: Pagination;
}

export interface EVMLogItem {
  abi_json?: any;
  address: string;
  args?: any;
  /** @format int32 */
  block_number: number;
  data?: string | null;
  event_name?: string | null;
  full_signature?: string | null;
  hashable_signature?: string | null;
  id: string;
  /** @format int32 */
  index: number;
  removed: boolean;
  /** @format date-time */
  timestamp: string;
  topic0?: string | null;
  topic1?: string | null;
  topic2?: string | null;
  topic3?: string | null;
  topic4?: string | null;
  transaction_hash: string;
}

export enum EVMLogOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
  IdAsc = 'id_asc',
  IdDesc = 'id_desc',
}

export interface EVMLogRequest {
  address?: string | null;
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  event_name?: string | null;
  /** @format int32 */
  limit?: number;
  order?: EVMLogOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
  topic0?: string | null;
  transaction_hash?: string | null;
}

export interface EVMLogResponse {
  data: EVMLogItem[];
  pagination: Pagination;
}

export interface EVMTransactionItem {
  abi_json?: any;
  args?: any;
  block_hash: string;
  /** @format int32 */
  block_number: number;
  contract_created?: string | null;
  cumulative_gas_used: string;
  effective_gas_price: string;
  from: string;
  full_signature?: string | null;
  gas: string;
  gas_price: string;
  gas_used: string;
  hash: string;
  hashable_signature?: string | null;
  /** @format int32 */
  index: number;
  input: string;
  max_fee_per_gas?: string | null;
  max_priority_fee_per_gas?: string | null;
  method_id?: string | null;
  method_name?: string | null;
  /** @format int32 */
  nonce: number;
  success: boolean;
  /** @format date-time */
  timestamp: string;
  to?: string | null;
  value: string;
}

export enum EVMTransactionOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface EVMTransactionRequest {
  address?: string | null;
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  contract_created?: string | null;
  from?: string | null;
  hash?: string | null;
  /** @format int32 */
  index?: number | null;
  /** @format int32 */
  limit?: number;
  method_id?: string | null;
  method_name?: string | null;
  order?: EVMTransactionOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
  to?: string | null;
}

export interface EVMTransactionResponse {
  data: EVMTransactionItem[];
  pagination: Pagination;
}

export interface EventItem {
  args?: any;
  /** @format int32 */
  block_number: number;
  call_id?: string | null;
  extrinsic_id?: string | null;
  /** @format int32 */
  extrinsic_index?: number | null;
  full_name: string;
  id: string;
  /** @format int32 */
  index: number;
  name: string;
  pallet: string;
  phase: string;
  /** @format date-time */
  timestamp: string;
}

export enum EventOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
  PhaseAsc = 'phase_asc',
  PhaseDesc = 'phase_desc',
  PalletAsc = 'pallet_asc',
  PalletDesc = 'pallet_desc',
  NameAsc = 'name_asc',
  NameDesc = 'name_desc',
  IdAsc = 'id_asc',
  IdDesc = 'id_desc',
  ExtrinsicIdAsc = 'extrinsic_id_asc',
  ExtrinsicIdDesc = 'extrinsic_id_desc',
}

export interface EventRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  call_id?: string | null;
  extrinsic_id?: string | null;
  /** Full name of the event, e.g. "SubtensorModule.AxonServed" */
  full_name?: string | null;
  id?: string | null;
  /** @format int32 */
  limit?: number;
  name?: string | null;
  network?: Network;
  order?: EventOrder;
  /** @format int32 */
  page?: number;
  pallet?: string | null;
  phase?: string | null;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface EventResponse {
  data: EventItem[];
  pagination: Pagination;
}

export interface ExchangeItem {
  coldkey: Key;
  icon?: string | null;
  name: string;
}

export interface ExchangeRequest {
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  page?: number;
}

export interface ExchangeResponse {
  data: ExchangeItem[];
  pagination: Pagination;
}

export interface ExtrinsicItem {
  /** @format int32 */
  block_number: number;
  call_args?: any;
  call_id?: string | null;
  error?: any;
  fee?: string | null;
  full_name: string;
  hash: string;
  id: string;
  /** @format int32 */
  index: number;
  signature?: any;
  signer_address?: string | null;
  success?: boolean | null;
  /** @format date-time */
  timestamp: string;
  tip?: string | null;
  /** @format int32 */
  version: number;
}

export enum ExtrinsicOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
  IdAsc = 'id_asc',
  IdDesc = 'id_desc',
  SuccessAsc = 'success_asc',
  SuccessDesc = 'success_desc',
  SignerAddressAsc = 'signer_address_asc',
  SignerAddressDesc = 'signer_address_desc',
}

export interface ExtrinsicRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  full_name?: string | null;
  hash?: string | null;
  id?: string | null;
  /** @format int32 */
  limit?: number;
  network?: any;
  order?: ExtrinsicOrder;
  /** @format int32 */
  page?: number;
  /** SS58 or hex format */
  signer_address?: string | null;
  success?: boolean | null;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface ExtrinsicResponse {
  data: ExtrinsicItem[];
  pagination: Pagination;
}

export enum FrequencyBlockDay {
  ByBlock = 'by_block',
  ByDay = 'by_day',
}

export enum FrequencyBlockHourDay {
  ByBlock = 'by_block',
  ByHour = 'by_hour',
  ByDay = 'by_day',
}

export enum FrequencyHourDay {
  ByHour = 'by_hour',
  ByDay = 'by_day',
}

export interface HistoryRequest {
  /** @format int32 */
  countback?: number | null;
  /** @format int32 */
  from?: number | null;
  resolution: string;
  symbol: string;
  /** @format int32 */
  to: number;
}

export interface HistoryResponse {
  c?: number[] | null;
  h?: number[] | null;
  l?: number[] | null;
  /** @format int32 */
  nextTime?: number | null;
  o?: number[] | null;
  s: string;
  t?: number[] | null;
  v?: number[] | null;
}

export enum HotkeyFamilyHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
}

export interface HotkeyFamilyHistoryRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  /** SS58 or hex formatted public key */
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  /**
   * Subnet ID
   * @format int32
   */
  netuid?: number | null;
  order?: HotkeyFamilyHistoryOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface HotkeyFamilyItem {
  alpha_stake: string;
  /** @format int32 */
  block_number: number;
  childkey_take: string;
  children: HotkeyRelation[];
  coldkey: Key;
  family_alpha_stake: string;
  family_root_stake: string;
  family_root_stake_as_alpha: string;
  /** Total stake including parent/child relationships */
  family_stake: string;
  family_total_alpha_stake: string;
  hotkey: Key;
  /**
   * The subnet ID
   * @format int32
   */
  netuid: number;
  parents: HotkeyRelation[];
  root_stake: string;
  root_stake_as_alpha: string;
  root_weight: string;
  /** Total stake excluding parent/child relationships */
  stake: string;
  take: string;
  /** @format date-time */
  timestamp: string;
  total_alpha_stake: string;
}

export interface HotkeyFamilyRequest {
  /** SS58 or hex format */
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  /**
   * Subnet ID
   * @format int32
   */
  netuid?: number | null;
  /** @format int32 */
  page?: number;
}

export interface HotkeyFamilyResponse {
  data: HotkeyFamilyItem[];
  pagination: Pagination;
}

export interface HotkeyRelation {
  alpha_stake: string;
  childkey_take: string;
  coldkey: Key;
  family_alpha_stake: string;
  family_root_stake: string;
  family_root_stake_as_alpha: string;
  /** Total stake including parent/child relationships */
  family_stake: string;
  family_total_alpha_stake: string;
  hotkey: Key;
  /** The proportion of the stake given to the child */
  proportion: string;
  proportion_alpha_stake: string;
  proportion_root_stake: string;
  proportion_root_stake_as_alpha: string;
  /** The stake given to the child */
  proportion_staked: string;
  proportion_total_alpha_stake: string;
  root_stake: string;
  root_stake_as_alpha: string;
  root_weight: string;
  /** Total stake excluding parent/child relationships */
  stake: string;
  take: string;
  total_alpha_stake: string;
}

export interface IdentityInfo {
  display?: string | null;
  email?: string | null;
  image?: string | null;
  legal?: string | null;
  riot?: string | null;
  twitter?: string | null;
  web?: string | null;
}

export interface IdentityItem {
  address: Key;
  info: IdentityInfo;
}

export interface IdentityRequest {
  /** SS58 or hex format */
  address?: string | null;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  page?: number;
}

export interface IdentityResponse {
  data: IdentityItem[];
  pagination: Pagination;
}

export interface JsonRPCRequest {
  id: any;
  jsonrpc: string;
  method: string;
  params?: any;
}

export interface JsonRPCResponse {
  error?: any;
  id: any;
  jsonrpc: string;
  result?: any;
}

export interface Key {
  /** The hex format of the hot key */
  hex: string;
  /** The SS58 format of the hot key */
  ss58: string;
}

export enum MetagraphHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface MetagraphHistoryRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  /** SS58 or hex format */
  coldkey?: string | null;
  /** SS58 or hex format */
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  /**
   * Subnet ID
   * @format int32
   */
  netuid?: number | null;
  order?: MetagraphHistoryOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
  /**
   * Neuron ID
   * @format int32
   */
  uid?: number | null;
}

export interface MetagraphHistoryResponse {
  data: MetagraphItem[];
  pagination: Pagination;
}

export interface MetagraphItem {
  active: boolean;
  alpha_stake: string;
  axon?: any;
  /** @format int32 */
  block_number: number;
  coldkey: Key;
  consensus: string;
  daily_reward: string;
  dividends: string;
  emission: string;
  hotkey: Key;
  incentive: string;
  is_child_key: boolean;
  is_immunity_period: boolean;
  /** @format int32 */
  netuid: number;
  /** @format int32 */
  rank: number;
  /** @format int32 */
  registered_at_block: number;
  root_stake: string;
  root_stake_as_alpha: string;
  root_weight: string;
  stake: string;
  /** @format date-time */
  timestamp: string;
  total_alpha_stake: string;
  trust: string;
  /** @format int32 */
  uid: number;
  /** @format int32 */
  updated: number;
  validator_permit: boolean;
  validator_trust: string;
}

export enum MetagraphOrder {
  UpdatedAsc = 'updated_asc',
  UpdatedDesc = 'updated_desc',
  UidAsc = 'uid_asc',
  UidDesc = 'uid_desc',
  StakeAsc = 'stake_asc',
  StakeDesc = 'stake_desc',
  TrustAsc = 'trust_asc',
  TrustDesc = 'trust_desc',
  ValidatorTrustAsc = 'validator_trust_asc',
  ValidatorTrustDesc = 'validator_trust_desc',
  ConsensusAsc = 'consensus_asc',
  ConsensusDesc = 'consensus_desc',
  IncentiveAsc = 'incentive_asc',
  IncentiveDesc = 'incentive_desc',
  DividendsAsc = 'dividends_asc',
  DividendsDesc = 'dividends_desc',
  EmissionAsc = 'emission_asc',
  EmissionDesc = 'emission_desc',
  ActiveAsc = 'active_asc',
  ActiveDesc = 'active_desc',
  HotkeyAsc = 'hotkey_asc',
  HotkeyDesc = 'hotkey_desc',
  ColdkeyAsc = 'coldkey_asc',
  ColdkeyDesc = 'coldkey_desc',
  ValidatorPermitAsc = 'validator_permit_asc',
  ValidatorPermitDesc = 'validator_permit_desc',
  AxonAsc = 'axon_asc',
  AxonDesc = 'axon_desc',
  DailyRewardAsc = 'daily_reward_asc',
  DailyRewardDesc = 'daily_reward_desc',
  RegisteredAtAsc = 'registered_at_asc',
  RegisteredAtDesc = 'registered_at_desc',
  IsImmunityPeriodAsc = 'is_immunity_period_asc',
  IsImmunityPeriodDesc = 'is_immunity_period_desc',
  TotalAlphaStakeAsc = 'total_alpha_stake_asc',
  TotalAlphaStakeDesc = 'total_alpha_stake_desc',
}

export interface MetagraphRequest {
  active?: boolean | null;
  /** SS58 or hex format */
  coldkey?: string | null;
  /** SS58 or hex format */
  hotkey?: string | null;
  is_child_key?: boolean | null;
  is_immunity_period?: boolean | null;
  /** @format int32 */
  limit?: number;
  /**
   * Subnet ID
   * @format int32
   */
  netuid?: number | null;
  order?: MetagraphOrder;
  /** @format int32 */
  page?: number;
  /** Search across UID, hotkey, coldkey, axon_ip */
  search?: string | null;
  /**
   * Neuron ID
   * @format int32
   */
  uid?: number | null;
  validator_permit?: boolean | null;
}

export interface MetagraphResponse {
  data: MetagraphItem[];
  pagination: Pagination;
}

export enum MinerWeightsHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface MinerWeightsHistoryRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  miner_uid?: number | null;
  /** @format int32 */
  netuid: number;
  order?: MinerWeightsHistoryOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
  /** @format int32 */
  validator_uid?: number | null;
}

export interface MinerWeightsItem {
  /** @format int32 */
  block_number: number;
  /** @format int32 */
  miner_uid: number;
  /** @format int32 */
  netuid: number;
  /** @format date-time */
  timestamp: string;
  /** @format int32 */
  validator_uid: number;
  weight: string;
}

export interface MinerWeightsResponse {
  data: MinerWeightsItem[];
  pagination: Pagination;
}

export enum Network {
  Finney = 'finney',
  Nakamoto = 'nakamoto',
  Kusanagi = 'kusanagi',
}

export enum NetworkWithAll {
  All = 'all',
  Finney = 'finney',
  Nakamoto = 'nakamoto',
  Kusanagi = 'kusanagi',
}

export interface Pagination {
  /** @format int32 */
  current_page: number;
  /** @format int32 */
  next_page?: number | null;
  /** @format int32 */
  per_page: number;
  /** @format int32 */
  prev_page?: number | null;
  /** @format int32 */
  total_items: number;
  /** @format int32 */
  total_pages: number;
}

export enum PriceHistoryOrder {
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface PriceHistoryRequest {
  asset: string;
  /** @format int32 */
  limit?: number;
  order?: PriceHistoryOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface PriceItem {
  circulating_supply: string;
  /** @format date-time */
  created_at: string;
  fully_diluted_market_cap: string;
  /** @format date-time */
  last_updated: string;
  market_cap: string;
  market_cap_dominance: string;
  max_supply: string;
  name: string;
  percent_change_1h: string;
  percent_change_24h: string;
  percent_change_30d: string;
  percent_change_60d: string;
  percent_change_7d: string;
  percent_change_90d: string;
  price: string;
  slug: string;
  symbol: string;
  total_supply: string;
  /** @format date-time */
  updated_at: string;
  volume_24h: string;
}

export interface PriceOHLCItem {
  asset: string;
  close: string;
  high: string;
  low: string;
  open: string;
  period: string;
  /** @format date-time */
  timestamp: string;
  volume_24h: string;
}

export enum PriceOHLCPeriod {
  Value1M = '1m',
  Value1H = '1h',
  Value1D = '1d',
}

export interface PriceOHLCRequest {
  asset: string;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  page?: number;
  period: PriceOHLCPeriod;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface PriceOHLCResponse {
  data: PriceOHLCItem[];
  pagination: Pagination;
}

export interface PriceRequest {
  asset: string;
}

export interface PriceResponse {
  data: PriceItem[];
  pagination: Pagination;
}

export interface ProxyCallItem {
  args: any;
  /** @format int32 */
  block_number: number;
  extrinsic_hash: string;
  extrinsic_id: string;
  id: string;
  network: string;
  real_address: Key;
  signer_address: Key;
  /** @format date-time */
  timestamp: string;
}

export enum ProxyCallOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface ProxyCallRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  extrinsic_hash?: string | null;
  extrinsic_id?: string | null;
  id?: string | null;
  /** @format int32 */
  limit?: number;
  network?: string | null;
  order?: ProxyCallOrder;
  /** @format int32 */
  page?: number;
  /** SS58 or hex format */
  real_address?: string | null;
  /** SS58 or hex format */
  signer_address?: string | null;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface ProxyCallResponse {
  data: ProxyCallItem[];
  pagination: Pagination;
}

export interface RPCHypertextRequest {
  request: JsonRPCRequest;
  target: RPCTarget;
}

export interface RPCHypertextResponse {
  response: JsonRPCResponse;
  target: RPCTarget;
}

export enum RPCTarget {
  FinneyLite = 'finney_lite',
  FinneyArchive = 'finney_archive',
}

export enum RootMetagraphHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface RootMetagraphHistoryRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  /** SS58 or hex format */
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  order?: RootMetagraphHistoryOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface RootMetagraphHistoryResponse {
  data: RootMetagraphItem[];
  pagination: Pagination;
}

export interface RootMetagraphItem {
  /** @format int32 */
  block_number: number;
  coldkey: Key;
  consensus: string;
  hotkey: Key;
  pruning_score: string;
  rank: string;
  senator: boolean;
  stake: string;
  subnet_weights: any;
  /** @format date-time */
  timestamp: string;
  /** @format int32 */
  uid: number;
}

export enum RootMetagraphOrder {
  UidAsc = 'uid_asc',
  UidDesc = 'uid_desc',
  StakeAsc = 'stake_asc',
  StakeDesc = 'stake_desc',
}

export interface RootMetagraphRequest {
  /** SS58 or hex format */
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  order?: RootMetagraphOrder;
  /** @format int32 */
  page?: number;
}

export interface RootMetagraphResponse {
  data: RootMetagraphItem[];
  pagination: Pagination;
}

export enum RuntimeVersionHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface RuntimeVersionHistoryRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_start?: number | null;
  /** @format int32 */
  limit?: number;
  order?: RuntimeVersionHistoryOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface RuntimeVersionItem {
  /** @format int32 */
  block_number: number;
  /** @format int32 */
  runtime_version: number;
  /** @format date-time */
  timestamp: string;
}

export interface RuntimeVersionResponse {
  data: RuntimeVersionItem[];
  pagination: Pagination;
}

export interface SidecarAccountBalanceInfo {
  at: SidecarBlockIdentifiers;
  feeFrozen: string;
  free: string;
  frozen: string;
  locks: SidecarBalanceLock[];
  miscFrozen: string;
  nonce: string;
  reserved: string;
  tokenSymbol: string;
}

export interface SidecarBalanceLock {
  amount: string;
  id: string;
  reasons: string;
}

export interface SidecarBlock {
  authorId?: string | null;
  extrinsicRoot?: string | null;
  extrinsics: SidecarExtrinsic[];
  finalized: boolean;
  hash: string;
  logs: SidecarDigestItem[];
  number: string;
  onFinalize: SidecarBlockFinalize;
  onInitialize: SidecarBlockInitialize;
  parentHash: string;
  stateRoot: string;
}

export interface SidecarBlockFinalize {
  events: SidecarSanitizedEvent[];
}

export interface SidecarBlockIdentifiers {
  hash: string;
  height: string;
}

export interface SidecarBlockInitialize {
  events: SidecarSanitizedEvent[];
}

export interface SidecarBlockRaw {
  digest: SidecarBlockRawLogs;
  extrinsicRoot: string;
  extrinsics: string[];
  number: string;
  parentHash: string;
  stateRoot: string;
}

export interface SidecarBlockRawLogs {
  logs: SidecarDigestItem[];
}

export interface SidecarDigestItem {
  index: string;
  type: string;
  value: string[];
}

export interface SidecarExtrinsic {
  args: any;
  era: any;
  events: SidecarSanitizedEvent[];
  hash: string;
  info: any;
  method: SidecarExtrinsicMethod;
  nonce?: string | null;
  paysFee: boolean;
  signature?: SidecarExtrinsicSignature | null;
  success: boolean;
  tip?: string | null;
}

export interface SidecarExtrinsicIndex {
  at: SidecarBlockIdentifiers;
  extrinsic: SidecarExtrinsic;
}

export interface SidecarExtrinsicMethod {
  method: any;
  pallet: string;
}

export interface SidecarExtrinsicSignature {
  signature: string;
  signer: any;
}

export interface SidecarPalletConstant {
  errorItem?: string | null;
  metadata?: SidecarPalletConstantsItemMetadata | null;
  pallet: string;
  palletIndex: string;
}

export interface SidecarPalletConstants {
  at: SidecarBlockIdentifiers;
  items: SidecarPalletConstantsItemMetadata[];
  pallet: string;
  palletIndex: string;
}

export interface SidecarPalletConstantsItemMetadata {
  docs: string[];
  name: string;
  type: string;
  value: string;
}

export interface SidecarPalletEvent {
  errorItem?: string | null;
  metadata?: string | null;
  pallet: string;
  palletIndex: string;
}

export interface SidecarPalletEvents {
  at: SidecarBlockIdentifiers;
  items: SidecarPalletEventsItemMetadata[];
  pallet: string;
  palletIndex: string;
}

export interface SidecarPalletEventsItemMetadata {
  args: string[];
  docs: string[];
  fields: any;
  index: string;
  name: string;
}

export interface SidecarPalletStorageItem {
  at: SidecarBlockIdentifiers;
  keys: string[];
  metadata?: SidecarPalletStorageItemMetadata | null;
  pallet: string;
  palletIndex: string;
  storageItem: string;
  value: any;
}

export interface SidecarPalletStorageItemMetadata {
  docs: string;
  fallback: string;
  modifier: string;
  name: string;
  type: any;
}

export interface SidecarPalletsStorage {
  items: SidecarPalletStorageItemMetadata[];
  pallet: string;
  palletIndex: string;
}

export interface SidecarSanitizedEvent {
  data: any;
  method: any;
}

export interface SidecarTransactionPool {
  pool: SidecarTransactionPoolTransaction[];
}

export interface SidecarTransactionPoolTransaction {
  encodedExtrinsic: string;
  hash: string;
  partialFee?: string | null;
  priority?: string | null;
  tip?: string | null;
}

export interface SidecarVersionInfo {
  chain: string;
  clientImplName: string;
  clientVersion: string;
}

export enum StakeBalanceHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface StakeBalanceHistoryRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  /** SS58 or hex format */
  coldkey: string;
  /** SS58 or hex format */
  hotkey: string;
  /** @format int32 */
  limit?: number;
  order?: StakeBalanceHistoryOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface StakeBalanceItem {
  /** @format int32 */
  block_number: number;
  coldkey: Key;
  /** @format int32 */
  created_at_block_number: number;
  /** @format date-time */
  created_at_timestamp: string;
  hotkey: Key;
  hotkey_name?: string | null;
  stake: string;
  /** @format date-time */
  timestamp: string;
}

export enum StakeBalanceLatestOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
  StakeAsc = 'stake_asc',
  StakeDesc = 'stake_desc',
}

export interface StakeBalanceLatestRequest {
  /** SS58 or hex format */
  coldkey?: string | null;
  /** SS58 or hex format */
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  order?: StakeBalanceLatestOrder;
  /** @format int32 */
  page?: number;
  stake_max?: string | null;
  stake_min?: string | null;
}

export interface StakeBalanceResponse {
  data: StakeBalanceItem[];
  pagination: Pagination;
}

export interface StakeItem {
  /** @format int32 */
  block_number: number;
  coldkey: Key;
  hotkey: Key;
  stake: string;
  /** @format date-time */
  timestamp: string;
}

export enum StakeOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface StakeRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  /** SS58 or hex format */
  coldkey: string;
  /** SS58 or hex format */
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  order?: StakeOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface StakeResponse {
  data: StakeItem[];
  pagination: Pagination;
}

export enum StatsHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface StatsHistoryRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  /** @format int32 */
  limit?: number;
  order?: StatsHistoryOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface StatsItem {
  /** @format int32 */
  accounts: number;
  /** @format int32 */
  balance_holders: number;
  /** @format int32 */
  block_number: number;
  /** @format int32 */
  extrinsics: number;
  free: string;
  issued: string;
  staked: string;
  subnet_locked: string;
  subnet_registration_cost: string;
  /** @format int32 */
  subnets: number;
  /** @format date-time */
  timestamp: string;
  /** @format int32 */
  transfers: number;
}

export interface StatsResponse {
  data: StatsItem[];
  pagination: Pagination;
}

export interface StatusResponse {
  ok: boolean;
  status: SystemStatus;
  version: string;
}

export interface SubnetDescriptionItem {
  bittensor_id: string;
  description: string;
  github: string;
  hw_requirements: string;
  image_url: string;
  name: string;
  /** @format int32 */
  netuid: number;
}

export interface SubnetDescriptionRequest {
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  page?: number;
}

export interface SubnetDescriptionResponse {
  data: SubnetDescriptionItem[];
  pagination: Pagination;
}

export interface SubnetDistributionColdkeyItem {
  coldkey: string;
  /** @format int32 */
  count: number;
}

export interface SubnetDistributionColdkeyRequest {
  /** @format int32 */
  netuid: number;
}

export interface SubnetDistributionColdkeyResponse {
  data: SubnetDistributionColdkeyItem[];
  pagination: Pagination;
}

export interface SubnetDistributionIPItem {
  /** @format int32 */
  count: number;
  ip: string;
}

export interface SubnetDistributionIPRequest {
  /** @format int32 */
  netuid: number;
}

export interface SubnetDistributionIPResponse {
  data: SubnetDistributionIPItem[];
  pagination: Pagination;
}

export interface SubnetDistributionIncentiveItem {
  /** @format int32 */
  block_number: number;
  incentive: string;
  is_immunity_period: boolean;
}

export interface SubnetDistributionIncentiveRequest {
  /** @format int32 */
  netuid: number;
}

export interface SubnetDistributionIncentiveResponse {
  data: SubnetDistributionIncentiveItem[];
  pagination: Pagination;
}

export interface SubnetDominance {
  dominance: string;
  family_stake: string;
  /** @format int32 */
  netuid: number;
}

export enum SubnetHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface SubnetHistoryRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  frequency?: FrequencyBlockHourDay;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  netuid: number;
  order?: SubnetHistoryOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface SubnetItem {
  /** @format int32 */
  active_dual: number;
  /** @format int32 */
  active_keys: number;
  /** @format int32 */
  active_miners: number;
  /** @format int32 */
  active_validators: number;
  /** @format int32 */
  activity_cutoff: number;
  adjustment_alpha?: string | null;
  /** @format int32 */
  adjustment_interval: number;
  alpha_high?: string | null;
  alpha_low?: string | null;
  /** @format int32 */
  block_number: number;
  /** @format int32 */
  blocks_since_last_step: number;
  /** @format int32 */
  blocks_until_next_adjustment: number;
  /** @format int32 */
  blocks_until_next_epoch: number;
  bonds_moving_avg: string;
  commit_reveal_weights_enabled: boolean;
  /** @format int32 */
  commit_reveal_weights_interval?: number | null;
  difficulty: string;
  emission: string;
  /** @format int32 */
  immunity_period: number;
  /** @format int32 */
  kappa: number;
  /** @format int32 */
  last_adjustment_block?: number | null;
  liquid_alpha_enabled?: boolean | null;
  max_burn: string;
  max_difficulty: string;
  /** @format int32 */
  max_neurons: number;
  /** @format int32 */
  max_regs_per_block: number;
  /** @format int32 */
  max_validators: number;
  /** @format int32 */
  max_weights_limit: number;
  /** @format int32 */
  min_allowed_weights: number;
  min_burn: string;
  min_difficulty: string;
  /** @format int32 */
  modality: number;
  /** @format int32 */
  netuid: number;
  neuron_registration_cost: string;
  /** @format int32 */
  neuron_registrations_this_interval: number;
  owner?: Key | null;
  pow_registration_allowed: boolean;
  recycled_24_hours: string;
  recycled_lifetime: string;
  recycled_since_registration: string;
  registration_allowed: boolean;
  /** @format int32 */
  registration_block_number?: number | null;
  registration_cost?: string | null;
  /** @format date-time */
  registration_timestamp?: string | null;
  /** @format int32 */
  rho: number;
  serving_rate_limit: string;
  /** @format int32 */
  target_regs_per_interval: number;
  /** @format int32 */
  tempo: number;
  /** @format date-time */
  timestamp: string;
  /** @format int32 */
  validators: number;
  weights_rate_limit: string;
  weights_version: string;
}

export interface SubnetNeuronDeregistrationItem {
  /** @format int32 */
  block_number: number;
  coldkey: Key;
  emission: string;
  hotkey: Key;
  incentive: string;
  /** @format int32 */
  netuid: number;
  /** @format date-time */
  timestamp: string;
  /** @format int32 */
  uid: number;
  was_drained: boolean;
}

export enum SubnetNeuronDeregistrationOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface SubnetNeuronDeregistrationRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  /** SS58 or hex format */
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  netuid?: number | null;
  order?: SubnetNeuronDeregistrationOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
  /** @format int32 */
  uid?: number | null;
}

export interface SubnetNeuronDeregistrationResponse {
  data: SubnetNeuronDeregistrationItem[];
  pagination: Pagination;
}

export interface SubnetNeuronRegistrationItem {
  /** @format int32 */
  block_number: number;
  coldkey: Key;
  hotkey: Key;
  /** @format int32 */
  netuid: number;
  registration_cost: string;
  /** @format date-time */
  timestamp: string;
  /** @format int32 */
  uid: number;
}

export enum SubnetNeuronRegistrationOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
  RegistrationCostAsc = 'registration_cost_asc',
  RegistrationCostDesc = 'registration_cost_desc',
}

export interface SubnetNeuronRegistrationRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  /** SS58 or hex format */
  coldkey?: string | null;
  /** SS58 or hex format */
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  netuid?: number | null;
  order?: SubnetNeuronRegistrationOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
  /** @format int32 */
  uid?: number | null;
}

export interface SubnetNeuronRegistrationResponse {
  data: SubnetNeuronRegistrationItem[];
  pagination: Pagination;
}

export enum SubnetOrder {
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
}

export interface SubnetOwnerItem {
  /** @format int32 */
  block_number: number;
  is_coldkey_swap: boolean;
  /** @format int32 */
  netuid: number;
  owner: Key;
  /** @format date-time */
  timestamp: string;
}

export enum SubnetOwnerOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface SubnetOwnerRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  is_coldkey_swap?: boolean | null;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  netuid?: number | null;
  order?: SubnetOwnerOrder;
  /** SS58 or hex format */
  owner?: string | null;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface SubnetOwnerResponse {
  data: SubnetOwnerItem[];
  pagination: Pagination;
}

export enum SubnetRegistrationCostHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface SubnetRegistrationCostHistoryRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  /** @format int32 */
  limit?: number;
  order?: SubnetRegistrationCostHistoryOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface SubnetRegistrationCostItem {
  /** @format int32 */
  block_number: number;
  is_purchase: boolean;
  registration_cost: string;
  /** @format date-time */
  timestamp: string;
}

export interface SubnetRegistrationCostResponse {
  data: SubnetRegistrationCostItem[];
  pagination: Pagination;
}

export interface SubnetRegistrationItem {
  /** @format int32 */
  block_number: number;
  /** @format int32 */
  netuid: number;
  owner?: Key | null;
  recycled_at_registration: string;
  registered_by?: Key | null;
  registration_cost: string;
  /** @format date-time */
  timestamp: string;
}

export enum SubnetRegistrationOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
  RegisterCostAsc = 'register_cost_asc',
  RegisterCostDesc = 'register_cost_desc',
}

export interface SubnetRegistrationRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  netuid?: number | null;
  order?: SubnetRegistrationOrder;
  /** SS58 or hex format */
  owner?: string | null;
  /** @format int32 */
  page?: number;
  /** SS58 or hex format */
  registered_by?: string | null;
  /**
   * End of timestamp range (inclusive)
   * @format date-time
   */
  timestamp_end?: string | null;
  /**
   * Start of timestamp range (inclusive)
   * @format date-time
   */
  timestamp_start?: string | null;
}

export interface SubnetRegistrationResponse {
  data: SubnetRegistrationItem[];
  pagination: Pagination;
}

export interface SubnetRequest {
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  netuid?: number | null;
  order?: SubnetOrder;
  /** @format int32 */
  page?: number;
}

export interface SubnetResponse {
  data: SubnetItem[];
  pagination: Pagination;
}

export interface SymbolInfoRequest {
  /** @format int32 */
  netuid?: number | null;
}

export interface SymbolInfoResponse {
  description: string[];
  'exchange-listed': string;
  'exchange-traded': string;
  'has-dwm': boolean;
  'has-intraday': boolean;
  /**
   * @format int32
   * @min 0
   */
  minmovement: number;
  /**
   * @format int64
   * @min 0
   */
  pricescale: number;
  session: string;
  'session-regular': string;
  symbol: string[];
  timezone: string;
  type: string[];
}

export interface SystemStatus {
  /** @format date-time */
  timestamp: string;
}

export interface TransferItem {
  amount: string;
  /** @format int32 */
  block_number: number;
  extrinsic_id: string;
  fee: string;
  from: Key;
  id: string;
  network: string;
  /** @format date-time */
  timestamp: string;
  to: Key;
  transaction_hash: string;
}

export enum TransferOrder {
  AmountAsc = 'amount_asc',
  AmountDesc = 'amount_desc',
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface TransferRequest {
  /** SS58 or hex format */
  address?: string | null;
  /** Maximum amount (inclusive) */
  amount_max?: string | null;
  /** Minimum amount (inclusive) */
  amount_min?: string | null;
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  extrinsic_id?: string | null;
  /** SS58 or hex format */
  from?: string | null;
  /** @format int32 */
  limit?: number;
  network?: NetworkWithAll;
  order?: TransferOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
  /** SS58 or hex format */
  to?: string | null;
  transaction_hash?: string | null;
}

export interface TransferResponse {
  data: TransferItem[];
  pagination: Pagination;
}

export enum ValidatorHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface ValidatorHistoryRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /** @format int32 */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  /** SS58 or hex format */
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  order?: ValidatorHistoryOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface ValidatorIdentityItem {
  description: string;
  hotkey: Key;
  name: string;
  signature: string;
  url: string;
}

export enum ValidatorIdentityOrder {
  NameAsc = 'name_asc',
  NameDesc = 'name_desc',
}

export interface ValidatorIdentityRequest {
  /** SS58 or hex format */
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  name?: string | null;
  order?: ValidatorIdentityOrder;
  /** @format int32 */
  page?: number;
}

export interface ValidatorIdentityResponse {
  data: ValidatorIdentityItem[];
  pagination: Pagination;
}

export interface ValidatorItem {
  apr: string;
  apr_30_day_average: string;
  apr_7_day_average: string;
  /** @format int32 */
  block_number: number;
  /** @format int32 */
  blocks_until_next_reward?: number | null;
  coldkey: Key;
  /** @format date */
  created_on_date: string;
  dominance: string;
  hotkey: Key;
  /** @format int32 */
  last_reward_block?: number | null;
  name?: string | null;
  nominator_return_per_k: string;
  nominator_return_per_k_30_day_average: string;
  nominator_return_per_k_7_day_average: string;
  /** @format int32 */
  nominators: number;
  /** @format int32 */
  nominators_24_hr_change: number;
  pending_emission?: string | null;
  permits: number[];
  /** @format int32 */
  rank: number;
  registrations: number[];
  stake: string;
  stake_24_hr_change: string;
  subnet_dominance: SubnetDominance[];
  system_stake: string;
  take: string;
  /** @format date-time */
  timestamp: string;
  total_daily_return: string;
  validator_return: string;
  validator_stake: string;
}

export enum ValidatorMetricsHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface ValidatorMetricsHistoryRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  /** SS58 or hex format */
  coldkey?: string | null;
  /** SS58 or hex format */
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  netuid?: number | null;
  order?: ValidatorMetricsHistoryOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
}

export interface ValidatorMetricsItem {
  active: boolean;
  axon_info?: any;
  /** @format int32 */
  block_number: number;
  coldkey: Key;
  consensus: string;
  daily_reward: string;
  dividends: string;
  emission: string;
  hotkey: Key;
  incentive: string;
  is_immunity_period: boolean;
  /** @format int32 */
  netuid: number;
  /** @format int32 */
  rank: number;
  /** @format int32 */
  registered_block_number: number;
  stake: string;
  /** @format date-time */
  timestamp: string;
  trust: string;
  /** @format int32 */
  uid: number;
  /** @format int32 */
  updated: number;
  validator_permit: boolean;
  validator_trust: string;
}

export enum ValidatorMetricsOrder {
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
}

export interface ValidatorMetricsRequest {
  /** SS58 or hex format */
  coldkey?: string | null;
  /** SS58 or hex format */
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  netuid?: number | null;
  order?: ValidatorMetricsOrder;
  /** @format int32 */
  page?: number;
}

export interface ValidatorMetricsResponse {
  data: ValidatorMetricsItem[];
  pagination: Pagination;
}

export enum ValidatorOrder {
  RankAsc = 'rank_asc',
  RankDesc = 'rank_desc',
  DominanceAsc = 'dominance_asc',
  DominanceDesc = 'dominance_desc',
  StakeAsc = 'stake_asc',
  StakeDesc = 'stake_desc',
  StakeChangeAsc = 'stake_change_asc',
  StakeChangeDesc = 'stake_change_desc',
  NominatorsAsc = 'nominators_asc',
  NominatorsDesc = 'nominators_desc',
  NominatorsChangeAsc = 'nominators_change_asc',
  NominatorsChangeDesc = 'nominators_change_desc',
  TakeAsc = 'take_asc',
  TakeDesc = 'take_desc',
  NominatorReturnAsc = 'nominator_return_asc',
  NominatorReturnDesc = 'nominator_return_desc',
}

export interface ValidatorPerformanceItem {
  /** @format int32 */
  block_number: number;
  /** @format int32 */
  blocks_since_weights_set: number;
  emission: string;
  hotkey: Key;
  /** @format int32 */
  netuid: number;
  /** @format int32 */
  tempo: number;
  /** @format date-time */
  timestamp: string;
  /** @format int32 */
  update_status: number;
}

export enum ValidatorPerformanceOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface ValidatorPerformanceRequest {
  /** SS58 or hex format */
  hotkey: string;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  netuid: number;
  order?: ValidatorPerformanceOrder;
  /** @format int32 */
  page?: number;
}

export interface ValidatorPerformanceResponse {
  data: ValidatorPerformanceItem[];
  pagination: Pagination;
}

export interface ValidatorRequest {
  apr_max?: string | null;
  apr_min?: string | null;
  /** SS58 or hex format */
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  order?: ValidatorOrder;
  /** @format int32 */
  page?: number;
  stake_max?: string | null;
  stake_min?: string | null;
}

export interface ValidatorResponse {
  data: ValidatorItem[];
  pagination: Pagination;
}

export enum ValidatorWeightsCall {
  SetWeights = 'set_weights',
  RevealWeights = 'reveal_weights',
  CommitWeights = 'commit_weights',
  CommitCrv3Weights = 'commit_crv3_weights',
  RevealCrv3Weights = 'reveal_crv3_weights',
}

export enum ValidatorWeightsHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface ValidatorWeightsHistoryRequest {
  /**
   * End of block range (inclusive)
   * @format int32
   */
  block_end?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_number?: number | null;
  /**
   * Start of block range (inclusive)
   * @format int32
   */
  block_start?: number | null;
  /** SS58 or hex format */
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  netuid?: number | null;
  order?: ValidatorWeightsHistoryOrder;
  /** @format int32 */
  page?: number;
  /**
   * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_end?: number | null;
  /**
   * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
   * @format int64
   */
  timestamp_start?: number | null;
  /** @format int32 */
  uid?: number | null;
}

export interface ValidatorWeightsItem {
  /** @format int32 */
  block_number: number;
  call: ValidatorWeightsCall;
  hotkey: Key;
  /** @format int32 */
  netuid: number;
  reveal_round?: string | null;
  /** @format date-time */
  timestamp: string;
  /** @format int32 */
  uid?: number | null;
  version_key?: string | null;
  weights: Weight[];
  weights_hash?: string | null;
}

export enum ValidatorWeightsOrder {
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
  UidAsc = 'uid_asc',
  UidDesc = 'uid_desc',
}

export interface ValidatorWeightsRequest {
  /** SS58 or hex format */
  hotkey?: string | null;
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  netuid?: number | null;
  order?: ValidatorWeightsOrder;
  /** @format int32 */
  page?: number;
  /** @format int32 */
  uid?: number | null;
}

export interface ValidatorWeightsResponse {
  data: ValidatorWeightsItem[];
  pagination: Pagination;
}

export interface Weight {
  /** @format int32 */
  uid: number;
  weight: string;
}

export interface WeightCopierItem {
  hotkey: Key;
}

export interface WeightCopierRequest {
  /** @format int32 */
  limit?: number;
  /** @format int32 */
  page?: number;
}

export interface WeightCopierResponse {
  data: WeightCopierItem[];
  pagination: Pagination;
}

export type QueryParamsType = Record<string | number, any>;
export type ResponseFormat = keyof Omit<Body, 'body' | 'bodyUsed'>;

export interface FullRequestParams extends Omit<RequestInit, 'body'> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseFormat;
  /** request body */
  body?: unknown;
  /** base url */
  baseUrl?: string;
  /** request cancellation token */
  cancelToken?: CancelToken;
}

export type RequestParams = Omit<
  FullRequestParams,
  'body' | 'method' | 'query' | 'path'
>;

export interface ApiConfig<SecurityDataType = unknown> {
  baseUrl?: string;
  baseApiParams?: Omit<RequestParams, 'baseUrl' | 'cancelToken' | 'signal'>;
  securityWorker?: (
    securityData: SecurityDataType | null
  ) => Promise<RequestParams | void> | RequestParams | void;
  customFetch?: typeof fetch;
}

export interface HttpResponse<D extends unknown, E extends unknown = unknown>
  extends Response {
  data: D;
  error: E;
}

type CancelToken = Symbol | string | number;

export enum ContentType {
  Json = 'application/json',
  FormData = 'multipart/form-data',
  UrlEncoded = 'application/x-www-form-urlencoded',
  Text = 'text/plain',
}

export class HttpClient<SecurityDataType = unknown> {
  public baseUrl: string = '';
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>['securityWorker'];
  private abortControllers = new Map<CancelToken, AbortController>();
  private customFetch = (...fetchParams: Parameters<typeof fetch>) =>
    fetch(...fetchParams);

  private baseApiParams: RequestParams = {
    credentials: 'same-origin',
    headers: {},
    redirect: 'follow',
    referrerPolicy: 'no-referrer',
  };

  constructor(apiConfig: ApiConfig<SecurityDataType> = {}) {
    Object.assign(this, apiConfig);
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected encodeQueryParam(key: string, value: any) {
    const encodedKey = encodeURIComponent(key);
    return `${encodedKey}=${encodeURIComponent(typeof value === 'number' ? value : `${value}`)}`;
  }

  protected addQueryParam(query: QueryParamsType, key: string) {
    return this.encodeQueryParam(key, query[key]);
  }

  protected addArrayQueryParam(query: QueryParamsType, key: string) {
    const value = query[key];
    return value.map((v: any) => this.encodeQueryParam(key, v)).join('&');
  }

  protected toQueryString(rawQuery?: QueryParamsType): string {
    const query = rawQuery || {};
    const keys = Object.keys(query).filter(
      (key) => 'undefined' !== typeof query[key]
    );
    return keys
      .map((key) =>
        Array.isArray(query[key])
          ? this.addArrayQueryParam(query, key)
          : this.addQueryParam(query, key)
      )
      .join('&');
  }

  protected addQueryParams(rawQuery?: QueryParamsType): string {
    const queryString = this.toQueryString(rawQuery);
    return queryString ? `?${queryString}` : '';
  }

  private contentFormatters: Record<ContentType, (input: any) => any> = {
    [ContentType.Json]: (input: any) =>
      input !== null && (typeof input === 'object' || typeof input === 'string')
        ? JSON.stringify(input)
        : input,
    [ContentType.Text]: (input: any) =>
      input !== null && typeof input !== 'string'
        ? JSON.stringify(input)
        : input,
    [ContentType.FormData]: (input: any) =>
      Object.keys(input || {}).reduce((formData, key) => {
        const property = input[key];
        formData.append(
          key,
          property instanceof Blob
            ? property
            : typeof property === 'object' && property !== null
              ? JSON.stringify(property)
              : `${property}`
        );
        return formData;
      }, new FormData()),
    [ContentType.UrlEncoded]: (input: any) => this.toQueryString(input),
  };

  protected mergeRequestParams(
    params1: RequestParams,
    params2?: RequestParams
  ): RequestParams {
    return {
      ...this.baseApiParams,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...(this.baseApiParams.headers || {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected createAbortSignal = (
    cancelToken: CancelToken
  ): AbortSignal | undefined => {
    if (this.abortControllers.has(cancelToken)) {
      const abortController = this.abortControllers.get(cancelToken);
      if (abortController) {
        return abortController.signal;
      }
      return void 0;
    }

    const abortController = new AbortController();
    this.abortControllers.set(cancelToken, abortController);
    return abortController.signal;
  };

  public abortRequest = (cancelToken: CancelToken) => {
    const abortController = this.abortControllers.get(cancelToken);

    if (abortController) {
      abortController.abort();
      this.abortControllers.delete(cancelToken);
    }
  };

  public request = async <T = any, E = any>({
    body,
    secure,
    path,
    type,
    query,
    format,
    baseUrl,
    cancelToken,
    ...params
  }: FullRequestParams): Promise<HttpResponse<T, E>> => {
    const secureParams =
      ((typeof secure === 'boolean' ? secure : this.baseApiParams.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const queryString = query && this.toQueryString(query);
    const payloadFormatter = this.contentFormatters[type || ContentType.Json];
    const responseFormat = format || requestParams.format;

    return this.customFetch(
      `${baseUrl || this.baseUrl || ''}${path}${queryString ? `?${queryString}` : ''}`,
      {
        ...requestParams,
        headers: {
          ...(requestParams.headers || {}),
          ...(type && type !== ContentType.FormData
            ? { 'Content-Type': type }
            : {}),
        },
        signal:
          (cancelToken
            ? this.createAbortSignal(cancelToken)
            : requestParams.signal) || null,
        body:
          typeof body === 'undefined' || body === null
            ? null
            : payloadFormatter(body),
      }
    ).then(async (response) => {
      const r = response.clone() as HttpResponse<T, E>;
      r.data = null as unknown as T;
      r.error = null as unknown as E;

      const data = !responseFormat
        ? r
        : await response[responseFormat]()
            .then((data) => {
              if (r.ok) {
                r.data = data;
              } else {
                r.error = data;
              }
              return r;
            })
            .catch((e) => {
              r.error = e;
              return r;
            });

      if (cancelToken) {
        this.abortControllers.delete(cancelToken);
      }

      if (!response.ok) throw data;
      return data;
    });
  };
}

/**
 * @title taostats
 * @version 1.8.24
 * @license
 * @contact taostats <<EMAIL>>
 */
export class Api<
  SecurityDataType extends unknown,
> extends HttpClient<SecurityDataType> {
  api = {
    /**
     * No description
     *
     * @tags account
     * @name V1
     * @request GET:/api/account/history/v1
     * @secure
     */
    v1: (
      query: {
        /** SS58 or hex format */
        address: string;
        /** finney, nakamoto, kusanagi */
        network?: string | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: AccountHistoryOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<AccountResponse, void>({
        path: `/api/account/history/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags account
     * @name V12
     * @request GET:/api/account/latest/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v12: (
      query?: {
        /** SS58 or hex format */
        address?: string | null;
        balance_free_min?: string | null;
        balance_free_max?: string | null;
        balance_staked_min?: string | null;
        balance_staked_max?: string | null;
        balance_staked_root_min?: string | null;
        balance_staked_root_max?: string | null;
        balance_staked_alpha_as_tao_min?: string | null;
        balance_staked_alpha_as_tao_max?: string | null;
        balance_total_min?: string | null;
        balance_total_max?: string | null;
        /** @format int32 */
        rank?: number | null;
        /** finney, nakamoto, kusanagi */
        created_on_network?: string | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        created_on_timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        created_on_timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: AccountOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<AccountResponse, void>({
        path: `/api/account/latest/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags accounting
     * @name V13
     * @request GET:/api/accounting/coldkey_report/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v13: (
      query: {
        /**
         * Start of date range in YYYY-MM-DD format (inclusive)
         * @format date
         */
        date_start: string;
        /**
         * End of date range in YYYY-MM-DD format (inclusive). Must be within 12 calendar months of date_start.
         * @format date
         */
        date_end: string;
        /** SS58 or hex format */
        coldkey: string;
      },
      params: RequestParams = {}
    ) =>
      this.request<ColdkeyReportResponse, void>({
        path: `/api/accounting/coldkey_report/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags accounting
     * @name V14
     * @request GET:/api/accounting/coldkey_report_csv/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v14: (
      query: {
        /**
         * Start of date range in YYYY-MM-DD format (inclusive)
         * @format date
         */
        date_start: string;
        /**
         * End of date range in YYYY-MM-DD format (inclusive). Must be within 12 calendar months of date_start.
         * @format date
         */
        date_end: string;
        /** SS58 or hex format */
        coldkey: string;
      },
      params: RequestParams = {}
    ) =>
      this.request<void, void>({
        path: `/api/accounting/coldkey_report_csv/v1`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags accounting
     * @name V15
     * @request GET:/api/accounting/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v15: (
      query: {
        /**
         * Start of date range in YYYY-MM-DD format (inclusive)
         * @format date
         */
        date_start: string;
        /**
         * End of date range in YYYY-MM-DD format (inclusive)
         * @format date
         */
        date_end: string;
        /** SS58 or hex format */
        coldkey: string;
        hotkey?: string | null;
        network?: Network;
      },
      params: RequestParams = {}
    ) =>
      this.request<AccountingResponse, void>({
        path: `/api/accounting/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags block
     * @name V16
     * @request GET:/api/block/interval/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v16: (
      query: {
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start: number;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end: number;
        /** Default by_day */
        frequency?: FrequencyHourDay;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: BlockIntervalOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<BlockIntervalResponse, void>({
        path: `/api/block/interval/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags block
     * @name V17
     * @request GET:/api/block/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v17: (
      query?: {
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        hash?: string | null;
        /** @format int32 */
        spec_version?: number | null;
        validator?: string | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: BlockOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<BlockResponse, void>({
        path: `/api/block/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags call
     * @name V18
     * @request GET:/api/call/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v18: (
      query?: {
        /** SS58 or hex format */
        origin_address?: string | null;
        network?: Network;
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        success?: boolean | null;
        full_name?: string | null;
        id?: string | null;
        extrinsic_id?: string | null;
        parent_id?: string | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: CallOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<CallResponse, void>({
        path: `/api/call/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags delegation
     * @name V19
     * @request GET:/api/delegation/balance/latest/v1
     * @deprecated
     * @originalName v1
     * @duplicate
     * @secure
     */
    v19: (
      query?: {
        /** SS58 or hex format */
        delegate?: string | null;
        /** SS58 or hex format */
        nominator?: string | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: DelegationBalanceOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<DelegationBalanceResponse, void>({
        path: `/api/delegation/balance/latest/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags delegation
     * @name V110
     * @request GET:/api/delegation/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v110: (
      query?: {
        /** SS58 or hex format */
        nominator?: string | null;
        /** SS58 or hex format */
        delegate?: string | null;
        action?: DelegationAction;
        extrinsic_id?: string | null;
        /** Minimum amount (inclusive) */
        amount_min?: string | null;
        /** Maximum amount (inclusive) */
        amount_max?: string | null;
        /** @format int32 */
        netuid?: number | null;
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: DelegationOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<DelegationResponse, void>({
        path: `/api/delegation/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags dtao
     * @name V111
     * @request GET:/api/dtao/coldkey_alpha_shares/history/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v111: (
      query?: {
        coldkey?: string | null;
        hotkey?: string | null;
        /** @format int32 */
        netuid?: number | null;
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: DtaoColdkeyAlphaSharesHistoryOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<DtaoColdkeyAlphaSharesResponse, void>({
        path: `/api/dtao/coldkey_alpha_shares/history/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags dtao
     * @name V112
     * @request GET:/api/dtao/coldkey_alpha_shares/latest/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v112: (
      query?: {
        alpha_min?: string | null;
        alpha_max?: string | null;
        coldkey?: string | null;
        hotkey?: string | null;
        /** @format int32 */
        netuid?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: DtaoColdkeyAlphaSharesLatestOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<DtaoColdkeyAlphaSharesResponse, void>({
        path: `/api/dtao/coldkey_alpha_shares/latest/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags dtao
     * @name V113
     * @request GET:/api/dtao/delegation_volume/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v113: (
      query?: {
        /** Default is 60 minutes */
        frequency?: DtaoDelegationFrequency;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
      },
      params: RequestParams = {}
    ) =>
      this.request<DtaoDelegationVolumeResponse, void>({
        path: `/api/dtao/delegation_volume/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags dtao
     * @name V114
     * @request GET:/api/dtao/hotkey_alpha_shares/history/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v114: (
      query?: {
        hotkey?: string | null;
        /** @format int32 */
        netuid?: number | null;
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: DtaoHotkeyAlphaSharesHistoryOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<DtaoHotkeyAlphaSharesResponse, void>({
        path: `/api/dtao/hotkey_alpha_shares/history/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags dtao
     * @name V115
     * @request GET:/api/dtao/hotkey_alpha_shares/latest/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v115: (
      query?: {
        alpha_min?: string | null;
        alpha_max?: string | null;
        hotkey?: string | null;
        /** @format int32 */
        netuid?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: DtaoHotkeyAlphaSharesLatestOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<DtaoHotkeyAlphaSharesResponse, void>({
        path: `/api/dtao/hotkey_alpha_shares/latest/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags dtao
     * @name V116
     * @request GET:/api/dtao/hotkey_emission/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v116: (
      query?: {
        hotkey?: string | null;
        /** @format int32 */
        netuid?: number | null;
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: DtaoHotkeyEmissionOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<DtaoHotkeyEmissionResponse, void>({
        path: `/api/dtao/hotkey_emission/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags dtao
     * @name V117
     * @request GET:/api/dtao/pool/history/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v117: (
      query?: {
        /** @format int32 */
        netuid?: number | null;
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: DtaoPoolHistoryOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<DtaoPoolHistoryResponse, void>({
        path: `/api/dtao/pool/history/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags dtao
     * @name V118
     * @request GET:/api/dtao/pool/latest/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v118: (
      query?: {
        /** @format int32 */
        netuid?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: DtaoPoolOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<DtaoPoolResponse, void>({
        path: `/api/dtao/pool/latest/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags dtao
     * @name V120
     * @request GET:/api/dtao/slippage/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v120: (
      query: {
        /** @format int32 */
        netuid: number;
        input_tokens: string;
        direction: DtaoSlippageDirection;
      },
      params: RequestParams = {}
    ) =>
      this.request<DtaoSlippageResponse, void>({
        path: `/api/dtao/slippage/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags dtao
     * @name V121
     * @request GET:/api/dtao/stake_balance/history/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v121: (
      query: {
        /** SS58 or hex format */
        coldkey: string;
        /** SS58 or hex format */
        hotkey: string;
        /** @format int32 */
        netuid: number;
      },
      params: RequestParams = {}
    ) =>
      this.request<DtaoStakeBalanceResponse, void>({
        path: `/api/dtao/stake_balance/history/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags dtao
     * @name V122
     * @request GET:/api/dtao/stake_balance/latest/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v122: (
      query?: {
        /** SS58 or hex format */
        coldkey?: string | null;
        /** SS58 or hex format */
        hotkey?: string | null;
        /** @format int32 */
        netuid?: number | null;
        balance_min?: string | null;
        balance_max?: string | null;
        balance_as_tao_min?: string | null;
        balance_as_tao_max?: string | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: DtaoStakeBalanceLatestOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<DtaoStakeBalanceLatestResponse, void>({
        path: `/api/dtao/stake_balance/latest/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags dtao
     * @name V123
     * @request GET:/api/dtao/stake_balance_aggregated/latest/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v123: (
      query?: {
        /** SS58 or hex format */
        coldkey?: string | null;
        total_balance_as_tao_min?: string | null;
        total_balance_as_tao_max?: string | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: DtaoStakeBalanceAggregatedLatestOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<DtaoStakeBalanceAggregatedResponse, void>({
        path: `/api/dtao/stake_balance_aggregated/latest/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags dtao
     * @name V124
     * @request GET:/api/dtao/subnet_emission/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v124: (
      query?: {
        /** @format int32 */
        netuid?: number | null;
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: DtaoSubnetEmissionOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<DtaoSubnetEmissionResponse, void>({
        path: `/api/dtao/subnet_emission/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags dtao
     * @name V125
     * @request GET:/api/dtao/tradingview/udf/config
     * @originalName v1
     * @duplicate
     * @secure
     */
    v125: (params: RequestParams = {}) =>
      this.request<ConfigResponse, any>({
        path: `/api/dtao/tradingview/udf/config`,
        method: 'GET',
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags dtao
     * @name V126
     * @request GET:/api/dtao/tradingview/udf/history
     * @originalName v1
     * @duplicate
     * @secure
     */
    v126: (
      query: {
        symbol: string;
        resolution: string;
        /** @format int32 */
        from?: number | null;
        /** @format int32 */
        to: number;
        /** @format int32 */
        countback?: number | null;
      },
      params: RequestParams = {}
    ) =>
      this.request<HistoryResponse, any>({
        path: `/api/dtao/tradingview/udf/history`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags dtao
     * @name V127
     * @request GET:/api/dtao/tradingview/udf/symbol_info
     * @originalName v1
     * @duplicate
     * @secure
     */
    v127: (
      query?: {
        /** @format int32 */
        netuid?: number | null;
      },
      params: RequestParams = {}
    ) =>
      this.request<SymbolInfoResponse, any>({
        path: `/api/dtao/tradingview/udf/symbol_info`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags validator
     * @name V128
     * @request GET:/api/dtao/validator/available/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v128: (
      query?: {
        /** @format int32 */
        netuid?: number | null;
      },
      params: RequestParams = {}
    ) =>
      this.request<DtaoValidatorAvailableResponse, void>({
        path: `/api/dtao/validator/available/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags validator
     * @name V129
     * @request GET:/api/dtao/validator/history/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v129: (
      query?: {
        /** SS58 or hex format */
        hotkey?: string | null;
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: DtaoValidatorHistoryOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<DtaoValidatorResponse, void>({
        path: `/api/dtao/validator/history/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags validator
     * @name V130
     * @request GET:/api/dtao/validator/latest/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v130: (
      query?: {
        /** SS58 or hex format */
        hotkey?: string | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: DtaoValidatorLatestOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<DtaoValidatorResponse, void>({
        path: `/api/dtao/validator/latest/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags validator
     * @name V131
     * @request GET:/api/dtao/validator/performance/history/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v131: (
      query?: {
        /** SS58 or hex format */
        hotkey?: string | null;
        /** @format int32 */
        netuid?: number | null;
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: DtaoValidatorPerformanceHistoryOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<DtaoValidatorPerformanceResponse, void>({
        path: `/api/dtao/validator/performance/history/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags validator
     * @name V132
     * @request GET:/api/dtao/validator/performance/latest/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v132: (
      query?: {
        /** SS58 or hex format */
        hotkey?: string | null;
        /** @format int32 */
        netuid?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: DtaoValidatorPerformanceLatestOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<DtaoValidatorPerformanceResponse, void>({
        path: `/api/dtao/validator/performance/latest/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags validator
     * @name V133
     * @request GET:/api/dtao/validator/returns/history/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v133: (
      query?: {
        /** SS58 or hex format */
        hotkey?: string | null;
        /** @format int32 */
        netuid?: number | null;
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: DtaoValidatorReturnsHistoryOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<DtaoValidatorReturnsResponse, void>({
        path: `/api/dtao/validator/returns/history/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags validator
     * @name V134
     * @request GET:/api/dtao/validator/returns/latest/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v134: (
      query?: {
        /** SS58 or hex format */
        hotkey?: string | null;
        /** @format int32 */
        netuid?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: DtaoValidatorReturnsLatestOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<DtaoValidatorReturnsResponse, void>({
        path: `/api/dtao/validator/returns/latest/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags event
     * @name V135
     * @request GET:/api/event/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v135: (
      query?: {
        network?: Network;
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        pallet?: string | null;
        name?: string | null;
        /** Full name of the event, e.g. "SubtensorModule.AxonServed" */
        full_name?: string | null;
        extrinsic_id?: string | null;
        call_id?: string | null;
        id?: string | null;
        phase?: string | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: EventOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<EventResponse, void>({
        path: `/api/event/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags evm
     * @name V136
     * @request GET:/api/evm/address_from_ss58/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v136: (
      query: {
        ss58_address: string;
      },
      params: RequestParams = {}
    ) =>
      this.request<string, void>({
        path: `/api/evm/address_from_ss58/v1`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags evm
     * @name V137
     * @request GET:/api/evm/block/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v137: (
      query?: {
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: EVMBlockOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<EVMBlockResponse, void>({
        path: `/api/evm/block/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags evm
     * @name V138
     * @request GET:/api/evm/contract/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v138: (
      query?: {
        address?: string | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: EVMContractOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<EVMContractResponse, void>({
        path: `/api/evm/contract/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags erc20
     * @name V139
     * @request GET:/api/evm/erc20/account/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v139: (
      query?: {
        address?: string | null;
        token_name?: string | null;
        token_symbol?: string | null;
        token_address?: string | null;
        /** Minimum balance (inclusive) */
        balance_min?: string | null;
        /** Maximum balance (inclusive) */
        balance_max?: string | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: EVMERC20AccountOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<EVMERC20AccountResponse, void>({
        path: `/api/evm/erc20/account/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags erc20
     * @name V140
     * @request GET:/api/evm/erc20/token/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v140: (
      query?: {
        address?: string | null;
        name?: string | null;
        symbol?: string | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: EVMERC20TokenOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<EVMERC20TokenResponse, void>({
        path: `/api/evm/erc20/token/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags erc20
     * @name V141
     * @request GET:/api/evm/erc20/transfer/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v141: (
      query?: {
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        address?: string | null;
        to?: string | null;
        from?: string | null;
        transaction_hash?: string | null;
        token_name?: string | null;
        token_symbol?: string | null;
        token_address?: string | null;
        /** Minimum amount (inclusive) */
        amount_min?: string | null;
        /** Maximum amount (inclusive) */
        amount_max?: string | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: EVMERC20TransferOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<EVMERC20TransferResponse, void>({
        path: `/api/evm/erc20/transfer/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags evm
     * @name V142
     * @request GET:/api/evm/log/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v142: (
      query?: {
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        transaction_hash?: string | null;
        address?: string | null;
        event_name?: string | null;
        topic0?: string | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: EVMLogOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<EVMLogResponse, void>({
        path: `/api/evm/log/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags evm
     * @name V143
     * @request GET:/api/evm/transaction/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v143: (
      query?: {
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        hash?: string | null;
        address?: string | null;
        to?: string | null;
        from?: string | null;
        method_name?: string | null;
        method_id?: string | null;
        contract_created?: string | null;
        /** @format int32 */
        index?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: EVMTransactionOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<EVMTransactionResponse, void>({
        path: `/api/evm/transaction/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags exchange
     * @name V144
     * @request GET:/api/exchange/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v144: (
      query?: {
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
      },
      params: RequestParams = {}
    ) =>
      this.request<ExchangeResponse, void>({
        path: `/api/exchange/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags extrinsic
     * @name V145
     * @request GET:/api/extrinsic/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v145: (
      query?: {
        network?: any;
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        hash?: string | null;
        full_name?: string | null;
        id?: string | null;
        success?: boolean | null;
        /** SS58 or hex format */
        signer_address?: string | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: ExtrinsicOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<ExtrinsicResponse, void>({
        path: `/api/extrinsic/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags hotkey
     * @name V146
     * @request GET:/api/hotkey/family/history/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v146: (
      query?: {
        /** SS58 or hex formatted public key */
        hotkey?: string | null;
        /**
         * Subnet ID
         * @format int32
         */
        netuid?: number | null;
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: HotkeyFamilyHistoryOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<HotkeyFamilyResponse, void>({
        path: `/api/hotkey/family/history/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags hotkey
     * @name V147
     * @request GET:/api/hotkey/family/latest/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v147: (
      query?: {
        /** SS58 or hex format */
        hotkey?: string | null;
        /**
         * Subnet ID
         * @format int32
         */
        netuid?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
      },
      params: RequestParams = {}
    ) =>
      this.request<HotkeyFamilyResponse, void>({
        path: `/api/hotkey/family/latest/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags identity
     * @name V148
     * @request GET:/api/identity/latest/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v148: (
      query?: {
        /** SS58 or hex format */
        address?: string | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
      },
      params: RequestParams = {}
    ) =>
      this.request<IdentityResponse, void>({
        path: `/api/identity/latest/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags metagraph
     * @name V149
     * @request GET:/api/metagraph/history/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v149: (
      query?: {
        /**
         * Subnet ID
         * @format int32
         */
        netuid?: number | null;
        /**
         * Neuron ID
         * @format int32
         */
        uid?: number | null;
        /** SS58 or hex format */
        hotkey?: string | null;
        /** SS58 or hex format */
        coldkey?: string | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: MetagraphHistoryOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<MetagraphHistoryResponse, void>({
        path: `/api/metagraph/history/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags metagraph
     * @name V150
     * @request GET:/api/metagraph/latest/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v150: (
      query?: {
        /**
         * Subnet ID
         * @format int32
         */
        netuid?: number | null;
        /** Search across UID, hotkey, coldkey, axon_ip */
        search?: string | null;
        /**
         * Neuron ID
         * @format int32
         */
        uid?: number | null;
        active?: boolean | null;
        /** SS58 or hex format */
        hotkey?: string | null;
        /** SS58 or hex format */
        coldkey?: string | null;
        validator_permit?: boolean | null;
        is_immunity_period?: boolean | null;
        is_child_key?: boolean | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: MetagraphOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<MetagraphResponse, void>({
        path: `/api/metagraph/latest/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags metagraph
     * @name V151
     * @request GET:/api/metagraph/root/history/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v151: (
      query?: {
        /** SS58 or hex format */
        hotkey?: string | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: RootMetagraphHistoryOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<RootMetagraphHistoryResponse, void>({
        path: `/api/metagraph/root/history/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags metagraph
     * @name V152
     * @request GET:/api/metagraph/root/latest/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v152: (
      query?: {
        /** SS58 or hex format */
        hotkey?: string | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: RootMetagraphOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<RootMetagraphResponse, void>({
        path: `/api/metagraph/root/latest/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags miner
     * @name V153
     * @request GET:/api/miner/weights/history/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v153: (
      query: {
        /** @format int32 */
        netuid: number;
        /** @format int32 */
        miner_uid?: number | null;
        /** @format int32 */
        validator_uid?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: MinerWeightsHistoryOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<MinerWeightsResponse, void>({
        path: `/api/miner/weights/history/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags price
     * @name V154
     * @request GET:/api/price/history/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v154: (
      query: {
        asset: string;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: PriceHistoryOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<PriceResponse, void>({
        path: `/api/price/history/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags price
     * @name V155
     * @request GET:/api/price/latest/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v155: (
      query: {
        asset: string;
      },
      params: RequestParams = {}
    ) =>
      this.request<PriceResponse, void>({
        path: `/api/price/latest/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags price
     * @name V156
     * @request GET:/api/price/ohlc/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v156: (
      query: {
        asset: string;
        period: PriceOHLCPeriod;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
      },
      params: RequestParams = {}
    ) =>
      this.request<PriceOHLCResponse, void>({
        path: `/api/price/ohlc/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags proxy_call
     * @name V157
     * @request GET:/api/proxy_call/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v157: (
      query?: {
        id?: string | null;
        /** SS58 or hex format */
        signer_address?: string | null;
        /** SS58 or hex format */
        real_address?: string | null;
        network?: string | null;
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        extrinsic_hash?: string | null;
        extrinsic_id?: string | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: ProxyCallOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<ProxyCallResponse, void>({
        path: `/api/proxy_call/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags runtime_version
     * @name V158
     * @request GET:/api/runtime_version/history/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v158: (
      query?: {
        /** @format int32 */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: RuntimeVersionHistoryOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<RuntimeVersionResponse, void>({
        path: `/api/runtime_version/history/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags runtime_version
     * @name V159
     * @request GET:/api/runtime_version/latest/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v159: (params: RequestParams = {}) =>
      this.request<RuntimeVersionResponse, void>({
        path: `/api/runtime_version/latest/v1`,
        method: 'GET',
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags stake
     * @name V160
     * @request GET:/api/stake/v1
     * @deprecated
     * @originalName v1
     * @duplicate
     * @secure
     */
    v160: (
      query: {
        /** SS58 or hex format */
        coldkey: string;
        /** SS58 or hex format */
        hotkey?: string | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: StakeOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<StakeResponse, void>({
        path: `/api/stake/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags stake_balance
     * @name V161
     * @request GET:/api/stake_balance/history/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v161: (
      query: {
        /** SS58 or hex format */
        coldkey: string;
        /** SS58 or hex format */
        hotkey: string;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: StakeBalanceHistoryOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<StakeBalanceResponse, void>({
        path: `/api/stake_balance/history/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags stake
     * @name V162
     * @request GET:/api/stake_balance/latest/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v162: (
      query?: {
        /** SS58 or hex format */
        coldkey?: string | null;
        /** SS58 or hex format */
        hotkey?: string | null;
        stake_min?: string | null;
        stake_max?: string | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: StakeBalanceLatestOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<StakeBalanceResponse, void>({
        path: `/api/stake_balance/latest/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags stats
     * @name V163
     * @request GET:/api/stats/history/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v163: (
      query?: {
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: StatsHistoryOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<StatsResponse, void>({
        path: `/api/stats/history/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags stats
     * @name V164
     * @request GET:/api/stats/latest/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v164: (params: RequestParams = {}) =>
      this.request<StatsResponse, void>({
        path: `/api/stats/latest/v1`,
        method: 'GET',
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags status
     * @name V165
     * @request GET:/api/status/v1
     * @originalName v1
     * @duplicate
     */
    v165: (params: RequestParams = {}) =>
      this.request<StatusResponse, any>({
        path: `/api/status/v1`,
        method: 'GET',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags subnet
     * @name V166
     * @request GET:/api/subnet/description/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v166: (
      query?: {
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
      },
      params: RequestParams = {}
    ) =>
      this.request<SubnetDescriptionResponse, void>({
        path: `/api/subnet/description/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags subnet
     * @name V167
     * @request GET:/api/subnet/distribution/coldkey/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v167: (
      query: {
        /** @format int32 */
        netuid: number;
      },
      params: RequestParams = {}
    ) =>
      this.request<SubnetDistributionColdkeyResponse, void>({
        path: `/api/subnet/distribution/coldkey/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags subnet
     * @name V168
     * @request GET:/api/subnet/distribution/incentive/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v168: (
      query: {
        /** @format int32 */
        netuid: number;
      },
      params: RequestParams = {}
    ) =>
      this.request<SubnetDistributionIncentiveResponse, void>({
        path: `/api/subnet/distribution/incentive/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags subnet
     * @name V169
     * @request GET:/api/subnet/distribution/ip/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v169: (
      query: {
        /** @format int32 */
        netuid: number;
      },
      params: RequestParams = {}
    ) =>
      this.request<SubnetDistributionIPResponse, void>({
        path: `/api/subnet/distribution/ip/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags subnet
     * @name V170
     * @request GET:/api/subnet/history/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v170: (
      query: {
        /** @format int32 */
        netuid: number;
        frequency?: FrequencyBlockHourDay;
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: SubnetHistoryOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<SubnetResponse, void>({
        path: `/api/subnet/history/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags subnet
     * @name V171
     * @request GET:/api/subnet/latest/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v171: (
      query?: {
        /** @format int32 */
        netuid?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: SubnetOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<SubnetResponse, void>({
        path: `/api/subnet/latest/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags subnet
     * @name V172
     * @request GET:/api/subnet/neuron/deregistration/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v172: (
      query?: {
        /** @format int32 */
        netuid?: number | null;
        /** @format int32 */
        uid?: number | null;
        /** SS58 or hex format */
        hotkey?: string | null;
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: SubnetNeuronDeregistrationOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<SubnetNeuronDeregistrationResponse, void>({
        path: `/api/subnet/neuron/deregistration/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags subnet
     * @name V173
     * @request GET:/api/subnet/neuron/registration/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v173: (
      query?: {
        /** @format int32 */
        netuid?: number | null;
        /** @format int32 */
        uid?: number | null;
        /** SS58 or hex format */
        hotkey?: string | null;
        /** SS58 or hex format */
        coldkey?: string | null;
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: SubnetNeuronRegistrationOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<SubnetNeuronRegistrationResponse, void>({
        path: `/api/subnet/neuron/registration/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags subnet
     * @name V174
     * @request GET:/api/subnet/owner/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v174: (
      query?: {
        /** @format int32 */
        netuid?: number | null;
        /** SS58 or hex format */
        owner?: string | null;
        is_coldkey_swap?: boolean | null;
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: SubnetOwnerOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<SubnetOwnerResponse, void>({
        path: `/api/subnet/owner/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags subnet
     * @name V175
     * @request GET:/api/subnet/registration/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v175: (
      query?: {
        /** @format int32 */
        netuid?: number | null;
        /** SS58 or hex format */
        owner?: string | null;
        /** SS58 or hex format */
        registered_by?: string | null;
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range (inclusive)
         * @format date-time
         */
        timestamp_start?: string | null;
        /**
         * End of timestamp range (inclusive)
         * @format date-time
         */
        timestamp_end?: string | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: SubnetRegistrationOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<SubnetRegistrationResponse, void>({
        path: `/api/subnet/registration/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags subnet
     * @name V176
     * @request GET:/api/subnet/registration_cost/history/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v176: (
      query?: {
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: SubnetRegistrationCostHistoryOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<SubnetRegistrationCostResponse, void>({
        path: `/api/subnet/registration_cost/history/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags subnet
     * @name V177
     * @request GET:/api/subnet/registration_cost/latest/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v177: (params: RequestParams = {}) =>
      this.request<SubnetRegistrationCostResponse, void>({
        path: `/api/subnet/registration_cost/latest/v1`,
        method: 'GET',
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags transfer
     * @name V178
     * @request GET:/api/transfer/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v178: (
      query?: {
        network?: NetworkWithAll;
        /** SS58 or hex format */
        address?: string | null;
        /** SS58 or hex format */
        from?: string | null;
        /** SS58 or hex format */
        to?: string | null;
        transaction_hash?: string | null;
        extrinsic_id?: string | null;
        /** Minimum amount (inclusive) */
        amount_min?: string | null;
        /** Maximum amount (inclusive) */
        amount_max?: string | null;
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: TransferOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<TransferResponse, void>({
        path: `/api/transfer/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags live
     * @name AccountBalanceInfo
     * @request GET:/api/v1/live/accounts/{address}/balance-info
     * @secure
     */
    accountBalanceInfo: (address: string, params: RequestParams = {}) =>
      this.request<SidecarAccountBalanceInfo, any>({
        path: `/api/v1/live/accounts/${address}/balance-info`,
        method: 'GET',
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags live
     * @name BlockGetRange
     * @request GET:/api/v1/live/blocks
     * @secure
     */
    blockGetRange: (
      query: {
        /**
         * @format int64
         * @min 0
         */
        block_start: number;
        /**
         * @format int64
         * @min 0
         */
        block_end: number;
      },
      params: RequestParams = {}
    ) =>
      this.request<SidecarBlock[], any>({
        path: `/api/v1/live/blocks`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags live
     * @name BlockGetHead
     * @request GET:/api/v1/live/blocks/head
     * @secure
     */
    blockGetHead: (params: RequestParams = {}) =>
      this.request<SidecarBlock, any>({
        path: `/api/v1/live/blocks/head`,
        method: 'GET',
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags live
     * @name BlockGetByHeight
     * @request GET:/api/v1/live/blocks/{height}
     * @secure
     */
    blockGetByHeight: (height: number, params: RequestParams = {}) =>
      this.request<SidecarBlock, any>({
        path: `/api/v1/live/blocks/${height}`,
        method: 'GET',
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags live
     * @name BlockRawGetByHeight
     * @request GET:/api/v1/live/blocks/{height}/extrinsics-raw
     * @secure
     */
    blockRawGetByHeight: (height: number, params: RequestParams = {}) =>
      this.request<SidecarBlockRaw, any>({
        path: `/api/v1/live/blocks/${height}/extrinsics-raw`,
        method: 'GET',
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags live
     * @name ExtrinsicGetByBlockAndIndex
     * @request GET:/api/v1/live/blocks/{height}/extrinsics/{index}
     * @secure
     */
    extrinsicGetByBlockAndIndex: (
      height: number,
      index: string,
      params: RequestParams = {}
    ) =>
      this.request<SidecarExtrinsicIndex, any>({
        path: `/api/v1/live/blocks/${height}/extrinsics/${index}`,
        method: 'GET',
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags live
     * @name PoolGet
     * @request GET:/api/v1/live/node/transaction-pool
     * @secure
     */
    poolGet: (params: RequestParams = {}) =>
      this.request<SidecarTransactionPool, any>({
        path: `/api/v1/live/node/transaction-pool`,
        method: 'GET',
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags live
     * @name VersionInfo
     * @request GET:/api/v1/live/node/version
     * @secure
     */
    versionInfo: (params: RequestParams = {}) =>
      this.request<SidecarVersionInfo, any>({
        path: `/api/v1/live/node/version`,
        method: 'GET',
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags live
     * @name ConstGetAll
     * @request GET:/api/v1/live/pallets/{pallet_id}/consts
     * @secure
     */
    constGetAll: (palletId: string, params: RequestParams = {}) =>
      this.request<SidecarPalletConstants, any>({
        path: `/api/v1/live/pallets/${palletId}/consts`,
        method: 'GET',
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags live
     * @name ConstGetById
     * @request GET:/api/v1/live/pallets/{pallet_id}/consts/{id}
     * @secure
     */
    constGetById: (palletId: string, id: string, params: RequestParams = {}) =>
      this.request<SidecarPalletConstant, any>({
        path: `/api/v1/live/pallets/${palletId}/consts/${id}`,
        method: 'GET',
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags live
     * @name EventGetAll
     * @request GET:/api/v1/live/pallets/{pallet_id}/events
     * @secure
     */
    eventGetAll: (palletId: string, params: RequestParams = {}) =>
      this.request<SidecarPalletEvents, any>({
        path: `/api/v1/live/pallets/${palletId}/events`,
        method: 'GET',
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags live
     * @name EventGetById
     * @request GET:/api/v1/live/pallets/{pallet_id}/events/{id}
     * @secure
     */
    eventGetById: (palletId: string, id: string, params: RequestParams = {}) =>
      this.request<SidecarPalletEvent, any>({
        path: `/api/v1/live/pallets/${palletId}/events/${id}`,
        method: 'GET',
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags live
     * @name StorageGetAll
     * @request GET:/api/v1/live/pallets/{pallet_id}/storage
     * @secure
     */
    storageGetAll: (palletId: string, params: RequestParams = {}) =>
      this.request<SidecarPalletsStorage, any>({
        path: `/api/v1/live/pallets/${palletId}/storage`,
        method: 'GET',
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags live
     * @name StorageGetById
     * @request GET:/api/v1/live/pallets/{pallet_id}/storage/{id}
     * @secure
     */
    storageGetById: (
      palletId: string,
      id: string,
      params: RequestParams = {}
    ) =>
      this.request<SidecarPalletStorageItem, any>({
        path: `/api/v1/live/pallets/${palletId}/storage/${id}`,
        method: 'GET',
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags rpc
     * @name Http
     * @request POST:/api/v1/rpc/http
     * @secure
     */
    http: (data: RPCHypertextRequest, params: RequestParams = {}) =>
      this.request<RPCHypertextResponse, any>({
        path: `/api/v1/rpc/http`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags rpc
     * @name Ws
     * @request GET:/api/v1/rpc/ws/{target}
     * @secure
     */
    ws: (target: RPCTarget, params: RequestParams = {}) =>
      this.request<any, any>({
        path: `/api/v1/rpc/ws/${target}`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags validator
     * @name V179
     * @request GET:/api/validator/history/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v179: (
      query?: {
        /** SS58 or hex format */
        hotkey?: string | null;
        /** @format int32 */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: ValidatorHistoryOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<ValidatorResponse, void>({
        path: `/api/validator/history/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags validator
     * @name V180
     * @request GET:/api/validator/identity/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v180: (
      query?: {
        /** SS58 or hex format */
        hotkey?: string | null;
        name?: string | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: ValidatorIdentityOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<ValidatorIdentityResponse, void>({
        path: `/api/validator/identity/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags validator
     * @name V181
     * @request GET:/api/validator/latest/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v181: (
      query?: {
        /** SS58 or hex format */
        hotkey?: string | null;
        stake_min?: string | null;
        stake_max?: string | null;
        apr_min?: string | null;
        apr_max?: string | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: ValidatorOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<ValidatorResponse, void>({
        path: `/api/validator/latest/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags validator
     * @name V182
     * @request GET:/api/validator/metrics/history/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v182: (
      query?: {
        /** SS58 or hex format */
        hotkey?: string | null;
        /** SS58 or hex format */
        coldkey?: string | null;
        /** @format int32 */
        netuid?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: ValidatorMetricsHistoryOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<ValidatorMetricsResponse, void>({
        path: `/api/validator/metrics/history/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags validator
     * @name V183
     * @request GET:/api/validator/metrics/latest/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v183: (
      query?: {
        /** SS58 or hex format */
        hotkey?: string | null;
        /** SS58 or hex format */
        coldkey?: string | null;
        /** @format int32 */
        netuid?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: ValidatorMetricsOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<ValidatorMetricsResponse, void>({
        path: `/api/validator/metrics/latest/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags validator
     * @name V184
     * @request GET:/api/validator/performance/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v184: (
      query: {
        /** SS58 or hex format */
        hotkey: string;
        /** @format int32 */
        netuid: number;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: ValidatorPerformanceOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<ValidatorPerformanceResponse, void>({
        path: `/api/validator/performance/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags validator
     * @name V185
     * @request GET:/api/validator/weight_copier/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v185: (
      query?: {
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
      },
      params: RequestParams = {}
    ) =>
      this.request<WeightCopierResponse, void>({
        path: `/api/validator/weight_copier/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags validator
     * @name V186
     * @request GET:/api/validator/weights/history/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v186: (
      query?: {
        /** SS58 or hex format */
        hotkey?: string | null;
        /** @format int32 */
        netuid?: number | null;
        /** @format int32 */
        uid?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_number?: number | null;
        /**
         * Start of block range (inclusive)
         * @format int32
         */
        block_start?: number | null;
        /**
         * End of block range (inclusive)
         * @format int32
         */
        block_end?: number | null;
        /**
         * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_start?: number | null;
        /**
         * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
         * @format int64
         */
        timestamp_end?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: ValidatorWeightsHistoryOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<ValidatorWeightsResponse, void>({
        path: `/api/validator/weights/history/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags validator
     * @name V187
     * @request GET:/api/validator/weights/latest/v1
     * @originalName v1
     * @duplicate
     * @secure
     */
    v187: (
      query?: {
        /** SS58 or hex format */
        hotkey?: string | null;
        /** @format int32 */
        netuid?: number | null;
        /** @format int32 */
        uid?: number | null;
        /** @format int32 */
        page?: number;
        /** @format int32 */
        limit?: number;
        order?: ValidatorWeightsOrder;
      },
      params: RequestParams = {}
    ) =>
      this.request<ValidatorWeightsResponse, void>({
        path: `/api/validator/weights/latest/v1`,
        method: 'GET',
        query: query,
        secure: true,
        format: 'json',
        ...params,
      }),
  };
}

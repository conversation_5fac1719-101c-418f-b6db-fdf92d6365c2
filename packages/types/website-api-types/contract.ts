import type { BaseQueryParams, Pagination } from '.';

/**
 * Enum for ContractOrder
 *
 * This enum represents the sorting options available for Contract,
 * allowing Contract to be ordered according to:
 * - the ascending and descending order of the block number,
 * - the ascending and descending order of the timestamp.
 */
export enum ContractOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

/**
 * Interface representing the query parameters for Contract API.
 */
export interface ContractQueryParams extends BaseQueryParams {
  /**
   * The application binary interface (ABI) of the smart contract (Optional).
   */
  abi?: string;

  /**
   * Address of the smart contract (Optional).
   */
  address?: string;

  /**
   * The block number in which the contract was confirmed (Optional).
   */
  block_number?: number;

  /**
   * The address of the creator of the contract (Optional).
   */
  created_by?: string;

  /**
   * The number of decimals the token uses (Optional).
   */
  decimals?: number;

  /**
   * If true, this is an ERC1155 contract (Optional).
   */
  erc1155?: boolean;

  /**
   * If true, this is an ERC165 contract (Optional).
   */
  erc165?: boolean;

  /**
   * If true, this is an ERC20 contract (Optional).
   */
  erc20?: boolean;

  /**
   * If true, this is an ERC721 contract (Optional).
   */
  erc721?: boolean;

  /**
   * The data payload sent in the contract (Optional).
   */
  input?: string;

  /**
   * The name of the token involved in the contract (Optional).
   */
  name?: string;

  /**
   * The symbol of the token involved in the contract (Optional).
   */
  symbol?: string;

  /**
   * The timestamp when the contract was created (Optional).
   */
  timestamp?: string;

  /**
   * The unique identifier for the contract (Optional).
   */
  transaction_hash?: string;

  /**
   * If true, this involves a UniswapV2 contract (Optional).
   */
  uniswap_v2?: boolean;

  /**
   * If true, this involves a UniswapV3 contract (Optional).
   */
  uniswap_v3?: boolean;

  /**
   * Determines the sort order of the contracts
   */
  order?: ContractOrder;
}

/**
 * Interface representing the Contract structure returned by the API.
 */
export interface Contract {
  abi: string;
  address: string;
  block_number: number;
  created_by: string;
  decimals: number;
  erc1155: boolean;
  erc165: boolean;
  erc20: boolean;
  erc721: boolean;
  input: string;
  name: string;
  symbol: string;
  timestamp: string;
  transaction_hash: string;
  uniswap_v2: boolean;
  uniswap_v3: boolean;
}

/**
 * Interface representing the Contract API response.
 */
export interface ContractAPI {
  pagination: Pagination;
  data: Contract[];
}

/**
 * It represents the specific parameters required for querying Contract.
 */
export interface ContractParamsAtom extends BaseQueryParams {
  /**
   * The sort order for the contract data (Optional).
   */
  order?: ContractOrder;
}

import type { BaseQueryParams, Frequency, Pagination } from ".";

/**
 * Enum for BlockOrder
 *
 * This enum represents the sorting options available for Blocks,
 * allowing blocks to be ordered according to:
 * - the ascending and descending order of the spec version,
 * - the ascending and descending order of the block number,
 * - the ascending and descending order of the timestamp.
 * - the ascending and descending order of the hash.
 * - the ascending and descending order of the events.
 * - the ascending and descending order of the extrinsics.
 */
export enum BlockOrder {
	SpecVersionAsc = "spec_version_asc",
	SpecVersionDesc = "spec_version_desc",
	BlockNumberAsc = "block_number_asc",
	BlockNumberDesc = "block_number_desc",
	TimestampAsc = "timestamp_asc",
	TimestampDesc = "timestamp_desc",
	HashAsc = "hash_asc",
	HashDesc = "hash_desc",
	EventsCountAsc = "events_count_asc",
	EventsCountDesc = "events_count_desc",
	ExtrinsicsCountAsc = "extrinsics_count_asc",
	ExtrinsicsCountDesc = "extrinsics_count_desc",
}

/**
 * Interface representing query parameters for block API.
 */
export interface BlockQueryParams extends BaseQueryParams {
	/**
	 * The block number where the block is included (Optional).
	 */
	block_number?: number;

	/**
	 * The starting block number for the block query range (Optional).
	 */
	block_start?: number;

	/**
	 * The ending block number for the block query range (Optional).
	 */
	block_end?: number;

	/**
	 * The starting timestamp for the block query range (Optional).
	 */
	timestamp_start?: number;

	/**
	 * The ending timestamp for the block query range (Optional).
	 */
	timestamp_end?: number;

	/**
	 * The specific hash being queried (Optional).
	 */
	hash?: string;

	/**
	 * The spec version of the resource being queried (Optional).
	 */
	spec_version?: number;

	/**
	 * The validator for the query (Optional).
	 */
	validator?: string;

	/**
	 * The sort order for the block data (Optional).
	 */
	order?: BlockOrder;
}

/**
 * Interface representing the Block structure returned by the API.
 */
export interface Block {
	block_number: number;
	hash: string;
	parent_hash: string;
	state_root: string;
	extrinsics_root: string;
	spec_name: string;
	spec_version: number;
	impl_name: string;
	impl_version: number;
	timestamp: string;
	validator: string | null;
	events_count: number;
	extrinsics_count: number;
	calls_count: number;
	parent_block?: number;
}

/**
 * Interface representing the Block API response.
 */
export interface BlockAPI {
	pagination: Pagination;
	data: Block[];
}

/**
 * It represents the specific parameters required for querying Blocks.
 */
export interface BlockParamsAtom extends BaseQueryParams {
	/**
	 * Search parameter across block number and hash (Optional)
	 */
	search?: string;

	/**
	 * The sort order for the Block data (Optional)
	 */
	order?: BlockOrder;

	/**
	 * Interval (in milliseconds) at which metagraph data should be fetched again (Optional).
	 */
	refetchInterval?: number;
}

export enum DailyBlockOrder {
	DateAsc = "date_asc",
	DateDesc = "date_desc",
}

export interface DailyBlockQueryParams extends BaseQueryParams {
	/**
	 * Start of date range in YYYY-MM-DD format (inclusive)
	 */
	date_start: string;

	/**
	 * End of date range in YYYY-MM-DD format (inclusive)
	 */
	date_end: string;

	/**
	 * The sort order for the daily Block data (Optional)
	 */
	order?: DailyBlockOrder;
}

export interface DailyBlock {
	block_number: number;
	date: string;
	timestamp: string;
}

export interface DailyBlockAPI {
	data: DailyBlock[];
	pagination: Pagination;
}

export enum BlockFrequency {
	ByHour = "by_hour",
	ByDay = "by_day",
}

export interface BlockIntervalQueryParams extends BaseQueryParams {
	/**
	 * Start of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
	 */
	timestamp_start: number;

	/**
	 * End of timestamp range in Unix timestamp (seconds since 1970-01-01) (inclusive)
	 */
	timestamp_end: number;

	/**
	 * Frequency of block interval data (Optional)
	 */
	frequency?: BlockFrequency;

	/**
	 * The sort order for the daily Block data (Optional)
	 */
	order?: DailyBlockOrder;
}

export interface BlockInterval {
	block_number: 0;
	timestamp: string;
}

export interface BlockIntervalAPI {
	data: BlockInterval[];
	pagination: Pagination;
}

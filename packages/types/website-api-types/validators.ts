import type { Address, Axon, BaseQueryParams, Pagination } from '.';

/**
 * Enum for ValidatorHistoryOrder
 *
 * This enum represents the sorting options available for validator history,
 * allowing validator history to be ordered according to:
 * - the ascending and descending order of the block number.
 * - the ascending and descending order of the timestamp.
 */
export enum ValidatorHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

/**
 * Interface representing query parameters for Validator History API.
 */
export interface ValidatorHistoryQueryParams extends BaseQueryParams {
  /**
   * Hotkey of validator in either SS58 or hex format (Optional).
   */
  hotkey?: string;

  /**
   * The block number where the hotkey history is included (Optional).
   */
  block_number?: number;

  /**
   * The starting block number for the hotkey history query range (Optional).
   */
  block_start?: number;

  /**
   * The ending block number for the hotkey history query range (Optional).
   */
  block_end?: number;

  /**
   * The starting timestamp for the hotkey history query range (Optional).
   */
  timestamp_start?: number;

  /**
   * The ending timestamp for the hotkey history query range (Optional).
   */
  timestamp_end?: number;

  /**
   * The sort order for the validator history data (Optional).
   */
  order?: ValidatorHistoryOrder;
}

export interface SubnetDominance {
  netuid: number;
  dominance: string;
  family_stake: string;
}

export interface Validator {
  name: string;
  hotkey: Address;
  coldkey: Address;
  block_number: number;
  timestamp: string;
  rank: number;
  nominators: number;
  nominators_24_hr_change: number;
  system_stake: string;
  stake: string;
  stake_24_hr_change: string;
  dominance: string;
  created_on_date: string;
  validator_stake: string;
  take: string;
  total_daily_return: string;
  validator_return: string;
  nominator_return_per_k: string;
  apr: string;
  nominator_return_per_k_7_day_average: string;
  nominator_return_per_k_30_day_average: string;
  apr_7_day_average: string;
  apr_30_day_average: string;
  pending_emission: string;
  blocks_until_next_reward: number;
  last_reward_block: number;
  registrations: number[];
  permits: number[];
  subnet_dominance: SubnetDominance[];
}

export interface ValidatorAPI {
  pagination: Pagination;
  data: Validator[];
}

/**
 * Enum for ValidatorIdentityOrder
 *
 * This enum represents the sorting options available for validator identity,
 * allowing validator identity to be ordered according to:
 * - the ascending and descending order of the name.
 */
export enum ValidatorIdentityOrder {
  NameAsc = 'name_asc',
  NameDesc = 'name_desc',
}

export interface ValidatorIdentityQueryParams extends BaseQueryParams {
  /**
   * Hotkey of validator in either SS58 or hex format (Optional).
   */
  hotkey?: string;

  /**
   * The name of validator (Optional).
   */
  name?: string;

  /**
   * The sort order for the validator identity data (Optional).
   */
  order?: ValidatorIdentityOrder;
}

export interface ValidatorIdentity {
  hotkey: Address;
  name: string;
  url: string;
  description: string;
  signature: string;
}

export interface ValidatorIdentityAPI {
  pagination: Pagination;
  data: ValidatorIdentity[];
}

export enum ValidatorLatestOrder {
  RankAsc = 'rank_asc',
  RankDesc = 'rank_desc',
  ActiveSubnetsAsc = 'active_subnets_asc',
  ActiveSubnetsDesc = 'active_subnets_desc',
  RootRankAsc = 'root_rank_asc',
  RootRankDesc = 'root_rank_desc',
  AlphaRankAsc = 'alpha_rank_asc',
  AlphaRankDesc = 'alpha_rank_desc',
  TakeAsc = 'take_asc',
  TakeDesc = 'take_desc',
  GlobalNominatorsAsc = 'global_nominators_asc',
  GlobalNominatorsDesc = 'global_nominators_desc',
  GlobalWeightedStakeAsc = 'global_weighted_stake_asc',
  GlobalWeightedStakeDesc = 'global_weighted_stake_desc',
  GlobalAlphaStakeAsTaoAsc = 'global_alpha_stake_as_tao_asc',
  GlobalAlphaStakeAsTaoDesc = 'global_alpha_stake_as_tao_desc',
  WeightedRootStakeAsc = 'weighted_root_stake_asc',
  WeightedRootStakeDesc = 'weighted_root_stake_desc',
  RootStakeAsc = 'root_stake_asc',
  RootStakeDesc = 'root_stake_desc',
  DominanceAsc = 'dominance_asc',
  DominanceDesc = 'dominance_desc',
}

export interface ValidatorLatestQueryParams extends BaseQueryParams {
  /**
   * Hotkey of validator in either SS58 or hex format (Optional).
   */
  hotkey?: string;

  /**
   * Minimum stake amount (Optional).
   */
  stake_min?: string;

  /**
   * Maximum stake amount (Optional).
   */
  stake_max?: string;

  /**
   * Minimum annual percentage rate (APR) (Optional).
   */
  apr_min?: string;

  /**
   * Maximum annual percentage rate (APR) (Optional).
   */
  apr_max?: string;

  /**
   * The sort order for the validator latest data (Optional).
   */
  order?: ValidatorLatestOrder;
}

/**
 * Enum for ValidatorMetricsHistoryOrder
 *
 * This enum represents the sorting options available for validator metrics history,
 * allowing validator metrics history to be ordered according to:
 * - the ascending and descending order of the block number.
 * - the ascending and descending order of the timestamp.
 */
export enum ValidatorMetricsHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface ValidatorMetricsHistoryQueryParams extends BaseQueryParams {
  /**
   * Hotkey in either SS58 or hex format (Optional).
   */
  hotkey?: string;

  /**
   * Coldkey in either SS58 or hex format (Optional).
   */
  coldkey?: string;

  /**
   * Unique identifier for a subnet (Optional).
   */
  netuid?: number;

  /**
   * The block number where the metrics history is included (Optional).
   */
  block_number?: number;

  /**
   * The starting block number for the metrics history query range (Optional).
   */
  block_start?: number;

  /**
   * The ending block number for the metrics history query range (Optional).
   */
  block_end?: number;

  /**
   * The starting timestamp for the metrics history query range (Optional).
   */
  timestamp_start?: number;

  /**
   * The ending timestamp for the metrics history query range (Optional).
   */
  timestamp_end?: number;

  /**
   * The sort order for the hotkey history data (Optional).
   */
  order?: ValidatorMetricsHistoryOrder;
}

export interface ValidatorMetrics {
  coldkey: Address;
  hotkey: Address;
  netuid: number;
  uid: number;
  block_number: number;
  timestamp: string;
  active: boolean;
  validator_permit: boolean;
  is_immunity_period: boolean;
  updated: number;
  registered_block_number: number;
  rank: number;
  validator_trust: string;
  stake: string;
  emission: string;
  daily_reward: string;
  incentive: string;
  consensus: string;
  dividends: string;
  trust: string;
  axon_info: Axon | null;
}

export interface ValidatorMetricsAPI {
  pagination: Pagination;
  data: ValidatorMetrics[];
}

/**
 * Enum for ValidatorMetricsLatestOrder
 *
 * This enum represents the sorting options available for validator metrics latest,
 * allowing validator metrics latest to be ordered according to:
 * - the ascending and descending order of the subnet uid.
 */
export enum ValidatorMetricsLatestOrder {
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
}

export interface ValidatorMetricsLatestQueryParams extends BaseQueryParams {
  /**
   * Hotkey in either SS58 or hex format (Optional).
   */
  hotkey?: string;

  /**
   * Coldkey in either SS58 or hex format (Optional).
   */
  coldkey?: string;

  /**
   * Unique identifier for a subnet (Optional).
   */
  netuid?: number;

  /**
   * The sort order for the validator metrics data (Optional).
   */
  order?: ValidatorMetricsLatestOrder;
}

/**
 * Enum for ValidatorPerformanceOrder
 *
 * This enum represents the sorting options available for validator performance,
 * allowing validator performance to be ordered according to:
 * - the ascending and descending order of the block number.
 * - the ascending and descending order of the timestamp.
 */
export enum ValidatorPerformanceOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface ValidatorPerformanceQueryParams extends BaseQueryParams {
  /**
   * Hotkey in either SS58 or hex format.
   */
  hotkey: string;

  /**
   * Unique identifier for a subnet.
   */
  netuid: number;

  /**
   * The sort order for the validator performance data (Optional).
   */
  order?: ValidatorPerformanceOrder;
}

export interface ValidatorPerformance {
  netuid: number;
  hotkey: Address;
  block_number: number;
  timestamp: string;
  emission: string;
  blocks_since_weights_set: number;
  update_status: number;
  tempo: number;
}

export interface ValidatorPerformanceAPI {
  pagination: Pagination;
  data: ValidatorPerformance[];
}

/**
 * Enum for ValidatorWeightHistoryOrder
 *
 * This enum represents the sorting options available for validator weight history,
 * allowing validator weight history to be ordered according to:
 * - the ascending and descending order of the block number.
 * - the ascending and descending order of the timestamp.
 */
export enum ValidatorWeightHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface ValidatorWeightsHistoryQueryParams extends BaseQueryParams {
  /**
   * Hotkey in either SS58 or hex format (Optional).
   */
  hotkey?: string;

  /**
   * Unique identifier for a subnet (Optional).
   */
  netuid?: number;

  /**
   * Unique identifier for a neuron (Optional).
   */
  uid?: number;

  /**
   * The block number where the metrics history is included (Optional).
   */
  block_number?: number;

  /**
   * The starting block number for the metrics history query range (Optional).
   */
  block_start?: number;

  /**
   * The ending block number for the metrics history query range (Optional).
   */
  block_end?: number;

  /**
   * The starting timestamp for the metrics history query range (Optional).
   */
  timestamp_start?: number;

  /**
   * The ending timestamp for the metrics history query range (Optional).
   */
  timestamp_end?: number;

  /**
   * The sort order for the validator weight history data (Optional).
   */
  order?: ValidatorWeightHistoryOrder;
}

/**
 * Enum for ValidatorWeightLatestOrder
 *
 * This enum represents the sorting options available for validator weight,
 * allowing validator weight to be ordered according to:
 * - the ascending and descending order of the subnet uid.
 * - the ascending and descending order of the neuron uid.
 */
export enum ValidatorWeightsLatestOrder {
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
  UidAsc = 'uid_asc',
  UidDesc = 'uid_desc',
}

export interface ValidatorWeightsLatestQueryParams extends BaseQueryParams {
  /**
   * Hotkey in either SS58 or hex format (Optional).
   */
  hotkey?: string;

  /**
   * Unique identifier for a subnet (Optional).
   */
  netuid?: number;

  /**
   * The sort order for the validator weight data (Optional).
   */
  order?: ValidatorWeightsLatestOrder;
}

export interface ValidatorWeight {
  block_number: number;
  call: string;
  hotkey: Address;
  netuid: number;
  timestamp: string;
  uid: number;
  version_key: string;
  weights: {
    uid: number;
    weight: string;
  }[];
  weights_hash: string;
}

export interface ValidatorWeightAPI {
  pagination: Pagination;
  data: ValidatorWeight[];
}

export enum PerformanceViewStatus {
  TABLE = 'TABLE',
  BOX = 'BOX',
}

export interface ValidatorPerformanceCardProps {
  netuid: number;
  nominators: number;
  type: string;
  hotkey: string;
  take?: string;
  proportion: string;
  subnet_weight: string;
  // subnet_balance:number
  family_weight: string;
  // family_balance:number
  dominace: string;
  divs: string;
  noms_24h: string;
  val_24h: string;
  uid: number;
  pos: number;
  vTrust: string;
  updated: number;
}

export interface ValidatorChildPerformanceCardProps {
  validatorDominance: number;
  netUid: number;
  uid: number;
  rank: number;
  dividends: number;
  vTrust: number;
  dailyReward: number;
  updated: number;
  isChildkey: boolean;
  hotkey: string;
  parentStake?: string;
  percentage?: string;
  take?: string;
}

export interface ValidatorHotkeyQueryParams {
  hotkey: string;
}

/**
 * Enum for DtaoValidatorHistoryOrder
 *
 * This enum represents the sorting options available when fetching validator history data.
 * It allows the validator history data to be sorted according to the following attributes, in ascending and descending order:
 * - block number
 * - timestamp
 */
export enum DtaoValidatorHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

/**
 * Interface representing the query parameters for fetching validator history data.
 */
export interface DtaoValidatorHistoryQueryParams extends BaseQueryParams {
  /**
   * The address of the hotkey, represented in either SS58 or hex format. This is an optional field.
   */
  hotkey?: string;

  /**
   * The block number. This is an optional field.
   */
  block_number?: number;

  /**
   * The start block number for the query range. This is an optional field.
   */
  block_start?: number;

  /**
   * The end block number for the query range. This is an optional field.
   */
  block_end?: number;

  /**
   * The start timestamp for the query range. This is an optional field.
   */
  timestamp_start?: number;

  /**
   * The end timestamp for the query range. This is an optional field.
   */
  timestamp_end?: number;

  /**
   * Specifies the order in which the validator history data is sorted. This is an optional field.
   */
  order?: DtaoValidatorHistoryOrder;
}

/**
 * Interface representing the structure of validator data as returned by the API.
 */
export interface DtaoValidator {
  hotkey: Address;
  coldkey: Address;
  name: string;
  block_number: number;
  timestamp: string;
  created_on_date: string;
  rank: number;
  global_nominators: number;
  global_nominators_24_hr_change: number;
  take: string;
  global_weighted_stake: string;
  global_weighted_stake_24_hr_change: string;
  global_alpha_stake_as_tao: string;
  root_stake: string;
  weighted_root_stake: string;
  dominance: string;
  dominance_24_hr_change: string;
}

/**
 * Interface representing a validator history API response which includes a page of validator history data.
 */
export interface DtaoValidatorAPI {
  pagination: Pagination;
  data: DtaoValidator[];
}

/**
 * Enum for DtaoValidatorPerformanceLatestOrder
 *
 * This enum represents the sorting options available when fetching the latest validator performance data.
 * It allows the latest validator performance data to be sorted according to the following attributes, in ascending and descending order:
 * - network identifier (netuid)
 * - last updated time
 * - validator trust (vtrust)
 * - validator type
 * - total stake (take)
 * - childkey's stake (childkey_take)
 * - proportion
 * - subnet weight
 * - root weight
 * - family subnet weight
 * - family root weight
 * - family alpha
 * - dominance
 * - dividends
 * - nominator return per day
 * - validator return per day
 * - alpha
 */
export enum DtaoValidatorPerformanceLatestOrder {
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
  LastUpdatedAsc = 'last_updated_asc',
  LastUpdatedDesc = 'last_updated_desc',
  VTrustAsc = 'v_trust_asc',
  VTrustDesc = 'v_trust_desc',
  TypeAsc = 'type_asc',
  TypeDesc = 'type_desc',
  TakeAsc = 'take_asc',
  TakeDesc = 'take_desc',
  ChildkeyTakeAsc = 'childkey_take_asc',
  ChildkeyTakeDesc = 'childkey_take_desc',
  ProportionAsc = 'proportion_asc',
  ProportionDesc = 'proportion_desc',
  SubnetWeightAsc = 'subnet_weight_asc',
  SubnetWeightDesc = 'subnet_weight_desc',
  RootWeightAsc = 'root_weight_asc',
  RootWeightDesc = 'root_weight_desc',
  FamilySubnetWeightAsc = 'family_subnet_weight_asc',
  FamilySubnetWeightDesc = 'family_subnet_weight_desc',
  FamilyRootWeightAsc = 'family_root_weight_asc',
  FamilyRootWeightDesc = 'family_root_weight_desc',
  FamilyAlphaAsc = 'family_alpha_asc',
  FamilyAlphaDesc = 'family_alpha_desc',
  DominanceAsc = 'dominance_asc',
  DominanceDesc = 'dominance_desc',
  DividendsAsc = 'dividends_asc',
  DividendsDesc = 'dividends_desc',
  NominatorReturnPerDayAsc = 'nominator_return_per_day_asc',
  NominatorReturnPerDayDesc = 'nominator_return_per_day_desc',
  ValidatorReturnPerDayAsc = 'validator_return_per_day_asc',
  ValidatorReturnPerDayDesc = 'validator_return_per_day_desc',
  AlphaAsc = 'alpha_asc',
  AlphaDesc = 'alpha_desc',
  UidAsc = 'uid_asc',
  UidDesc = 'uid_desc',
  PositionAsc = 'position_asc',
  PositionDesc = 'position_desc',
  NominatorsAsc = 'nominators_asc',
  NominatorsDesc = 'nominators_desc',
}

/**
 * Interface representing query parameters for the latest Validator Performance API.
 */
export interface DtaoValidatorPerformanceLatestQueryParams
  extends BaseQueryParams {
  /**
   * The address of the hotkey, represented in either SS58 or hex format. This is an optional field.
   */
  hotkey?: string;

  /**
   * The network identifier. This is an optional field.
   */
  netuid?: number;

  /**
   * Specifies the order in which the latest validator performance data is sorted. This is an optional field.
   */
  order?: DtaoValidatorPerformanceLatestOrder;
}

/**
 * Interface representing the structure of the latest validator performance data returned by the API.
 */
export interface DtaoValidatorPerformance {
  hotkey: Address;
  coldkey: Address;
  block_number: number;
  timestamp: string;
  netuid: number;
  uid: number;
  position: number;
  last_updated: number;
  nominators: number;
  vtrust: string;
  validator_type: string;
  take: string;
  childkey_take: string;
  proportion: string;
  subnet_weight: string;
  root_weight: string;
  alpha: string;
  family_subnet_weight: string;
  family_root_weight: string;
  family_alpha: string;
  dominance: string;
  dividends: string;
  nominator_return_per_day: string;
  validator_return_per_day: string;
}

/**
 * Interface representing a latest validator performance API response that includes a page of validator performance data.
 */
export interface DtaoValidatorPerformanceAPI {
  pagination: Pagination;
  data: DtaoValidatorPerformance[];
}

/**
 * Enum for DtaoValidatorLatestOrder
 *
 * This enum represents the sorting options available when fetching the latest validator data.
 * It allows the latest validator data to be sorted in ascending or descending order according to:
 * - rank
 * - active subnets
 * - root rank
 * - alpha rank
 * - take
 * - global nominators
 * - global weighted stake
 * - global alpha stake as tao
 * - weighted root stake
 * - root stake
 * - dominance
 */
export enum DtaoValidatorLatestOrder {
  RankAsc = 'rank_asc',
  RankDesc = 'rank_desc',
  ActiveSubnetsAsc = 'active_subnets_asc',
  ActiveSubnetsDesc = 'active_subnets_desc',
  RootRankAsc = 'root_rank_asc',
  RootRankDesc = 'root_rank_desc',
  AlphaRankAsc = 'alpha_rank_asc',
  AlphaRankDesc = 'alpha_rank_desc',
  TakeAsc = 'take_asc',
  TakeDesc = 'take_desc',
  GlobalNominatorsAsc = 'global_nominators_asc',
  GlobalNominatorsDesc = 'global_nominators_desc',
  GlobalWeightedStakeAsc = 'global_weighted_stake_asc',
  GlobalWeightedStakeDesc = 'global_weighted_stake_desc',
  GlobalAlphaStakeAsTaoAsc = 'global_alpha_stake_as_tao_asc',
  GlobalAlphaStakeAsTaoDesc = 'global_alpha_stake_as_tao_desc',
  WeightedRootStakeAsc = 'weighted_root_stake_asc',
  WeightedRootStakeDesc = 'weighted_root_stake_desc',
  RootStakeAsc = 'root_stake_asc',
  RootStakeDesc = 'root_stake_desc',
  DominanceAsc = 'dominance_asc',
  DominanceDesc = 'dominance_desc',
}

/**
 * Interface representing query parameters for the latest validator API.
 */
export interface DtaoValidatorLatestQueryParams extends BaseQueryParams {
  /**
   * The address of the hotkey, represented in either SS58 or hex format. This is an optional field.
   */
  hotkey?: string;

  /**
   * Specifies the order in which the latest validator data is sorted. This is an optional field.
   */
  order?: DtaoValidatorLatestOrder;
}

/**
 * Interface representing the structure of the latest validator data returned by the API.
 */
export interface DtaoValidatorLatest {
  hotkey: Address;
  coldkey: Address;
  name?: string;
  block_number: number;
  timestamp: string;
  created_on_date: string;
  rank: number;
  root_rank: number;
  alpha_rank: number;
  active_subnets: number;
  global_nominators: number;
  global_nominators_24_hr_change: number;
  take: string;
  global_weighted_stake: string;
  global_weighted_stake_24_hr_change: string;
  global_alpha_stake_as_tao: string;
  root_stake: string;
  weighted_root_stake: string;
  dominance: string;
  dominance_24_hr_change: string;
  nominator_return_per_day: string;
  validator_return_per_day: string;
}

/**
 * Interface representing a latest validator API response that includes a page of the latest validator data.
 */
export interface DtaoValidatorLatestAPI {
  pagination: Pagination;
  data: DtaoValidatorLatest[];
}

export interface DtaoValidatorAvailableItem {
  address: Address;
  hotkey_alpha: string;
  name?: string | null;
  /** @format int32 */
  netuid: number;
}

export interface DtaoValidatorAvailableResponse {
  data: DtaoValidatorAvailableItem[];
  pagination: Pagination;
}

/**
 * Defines the interface for query parameters used when requesting the latest validator data.
 *
 * Available parameters include the address of the hotkey and the validator hotkey, both of which
 * are optional.
 */
export interface IdentityQueryParams extends BaseQueryParams {
  /** Address of the hotkey, can be represented in either SS58 or hex format (optional). */
  address?: string;

  /** The unique identifier for the validator hotkey (optional). */
  validator_hotkey?: string;
}

/**
 * Interface for the structure of the latest validator data returned from the API.
 *
 * The data includes fields about an identity, such as addresses, the identity's name, URL,
 * GitHub repository, image, Discord handle, description, and additional information.
 */
export interface Identity {
  address: Address;
  validator_hotkey: Address | null;
  name: string;
  url: string;
  github_repo: string;
  image: string;
  discord: string;
  description: string;
  additional: string;
}

/**
 * Interface that defines the structure of the API response for a request for the latest validator
 * data.
 *
 * The result will include pagination data and an array of Identity objects.
 */
export interface IdentityAPI {
  pagination: Pagination;
  data: Identity[];
}

export interface ValidatorMetaDataAPI {
  [x: string]: {
    name: string;
    url: string;
    description: string;
    signature: string;
  };
}

export interface ValidatorMetadata {
  address: string;
  name: string;
  url: string;
  description: string;
  signature: string;
}

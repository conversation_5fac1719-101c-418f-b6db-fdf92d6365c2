export * from './account';
export * from './hotkey';
export * from './transfer';
export * from './metagraph';
export * from './blocks';
export * from './events';
export * from './stats';
export * from './extrinsic';
export * from './price';
export * from './delegate';
export * from './validators';
export * from './subnet';
export * from './miners';
export * from './evm';
export * from './transaction';
export * from './contract';

export type { ColumnSchema } from './columnSchema';
export type { TableData } from './tabledata';
export type { Size } from './size';

export type TotalCount =
  | {
      count: number;
    }
  | 'count_too_slow'
  | null;

export interface Pagination {
  current_page: number;
  per_page: number;
  total_items: number;
  total_pages: number;
  next_page: number | null;
  prev_page: number | null;
}

/**
 * Enumeration `NetworkType` represents the possible types of networks.
 */
export enum NetworkType {
  Finney = 'finney',
  <PERSON><PERSON><PERSON> = 'nakamoto',
  <PERSON><PERSON><PERSON> = 'kusanagi',
}

export interface Address {
  ss58: string;
  hex: string;
}

export interface Axon {
  block: number;
  ip: string;
  ipType: number;
  placeholder1: number;
  placeholder2: number;
  port: number;
  protocol: number;
  version: number;
}

/**
 * Basic query parameters for pagination.
 */
export interface BaseQueryParams {
  /**
   * Page number for results pagination,
   * optional parameter, if not provided will use a default.
   */
  page?: number;

  /**
   * Maximum number of results returned per page,
   * optional parameter, if not provided will use a default.
   */
  limit?: number;
}

/**
 * Enum representing the frequency at which data return.
 */
export enum Frequency {
  /**
   * Represents data returning block by block.
   */
  ByBlock = 'by_block',

  /**
   * Represents data returning on a daily basis.
   */
  ByDay = 'by_day',
}

/**
 * Standard format for API responses.
 */
export interface ApiResponse<T> {
  pagination: Pagination;
  data: T[];
}

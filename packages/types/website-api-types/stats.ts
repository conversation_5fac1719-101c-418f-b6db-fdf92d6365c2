import type { BaseQueryParams, Frequency, Pagination } from '.';

/**
 * Interface representing the Stats structure returned by the API
 */
export interface Stats {
  block_number: number;
  timestamp: string;
  issued: string;
  staked: string;
  staked_alpha: string;
  staked_root: string;
  subnet_locked: string;
  free: string;
  accounts: number;
  active_accounts: number;
  active_balance_holders: number;
  balance_holders: number;
  extrinsics: number;
  transfers: number;
  subnets: number;
  subnet_registration_cost: string;
}

/**
 * Interface representing the Stats API response
 */
export interface StatsAPI {
  pagination: Pagination;
  data: Stats[];
}

/**
 * Enum for StatsOrder
 *
 * This enum represents the sort options available for stats,
 * allowing stats to be sorted according to:
 * - the ascending and descending order of the block number,
 * - the ascending and descending order of the timestamp.
 */
export enum StatsOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

/**
 * Interface representing query parameters for Stats API.
 */
export interface StatsHistoryQueryParams extends BaseQueryParams {
  /**
   * The block number where the stats are included (Optional).
   */
  block_number?: number;

  /**
   * The starting block number for the stats query range (Optional).
   */
  block_start?: number;

  /**
   * The ending block number for the stats query range (Optional).
   */
  block_end?: number;

  /**
   * The starting timestamp for the stats query range (Optional).
   */
  timestamp_start?: number;

  /**
   * The ending timestamp for the stats query range (Optional).
   */
  timestamp_end?: number;

  /**
   * The frequency at which data return (Optional).
   */
  frequency?: Frequency;

  /**
   * The sort order for Stats data (Optional).
   */
}

/**
 * Interface representing the Stats History structure returned by the API
 */
export interface StatsHistory {
  block_number: number;
  timestamp: string;
  issued: string;
  staked: string;
  staked_alpha: string;
  staked_root: string;
  subnet_locked: string;
  free: string;
  accounts: number;
  balance_holders: number;
  extrinsics: number;
  transfers: number;
  subnets: number;
  subnet_registration_cost: string;
  active_accounts: number;
  active_balance_holders: number;
}

/**
 * Interface representing the Stats API response
 */
export interface StatsHistoryAPI {
  pagination: Pagination;
  data: StatsHistory[];
}

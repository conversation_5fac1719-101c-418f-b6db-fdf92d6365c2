import type { BaseQueryParams, Pagination } from '.';

/**
 * Interface representing query parameters for Price API.
 */
export interface PriceQueryParams {
  /**
   * The asset for which the latest price is to be fetched (Optional).
   */
  asset: string;
}

/**
 * Interface representing the Price structure returned by the API.
 */
export interface Price {
  created_at: string;
  updated_at: string;
  name: string;
  symbol: string;
  slug: string;
  circulating_supply: string;
  max_supply: string;
  total_supply: string;
  last_updated: string;
  price: string;
  volume_24h: string;
  market_cap: string;
  percent_change_1h: string;
  percent_change_24h: string;
  percent_change_7d: string;
  percent_change_30d: string;
  percent_change_60d: string;
  percent_change_90d: string;
  market_cap_dominance: string;
  fully_diluted_market_cap: string;
}

/**
 * Interface representing the Price API response.
 */
export interface PriceAPI {
  pagination: Pagination;
  data: Price[];
}

/**
 * Enum for PriceOrder
 *
 * This enum represents the sorting options available for Price,
 * allowing price to be ordered according to:
 * - the ascending and descending order of the timestamp.
 */
export enum PriceOrder {
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface PriceHistoryQueryParams extends BaseQueryParams {
  /**
   * The asset for which the price history is to be fetched (Optional).
   */
  asset?: string;

  /**
   * The starting timestamp for the block query range (Optional).
   */
  timestamp_start?: number;

  /**
   * The ending timestamp for the block query range (Optional).
   */
  timestamp_end?: number;

  /**
   * The sort order for the price data (Optional).
   */
  order?: PriceOrder;
}

/**
 * It represents the specific parameters required for querying price history.
 */
export interface PriceHistoryParamsAtom extends BaseQueryParams {
  /**
   * The asset for which the price history is to be fetched (Optional).
   */
  asset: string;

  /**
   * The sort order for the price data (Optional).
   */
  order?: PriceOrder;
}

/**
 * Enum for representing the different periods for price change.
 * - DAY: Represents a period of one day
 * - HOUR: Represents a period of one hour
 * - MONTH: Represents a period of one month
 */
export enum PricePeriod {
  DAY = '1d',
  HOUR = '1h',
  MONTH = '1m',
}

/**
 * Interface representing query parameters for Price Ohlc API.
 */
export interface PriceOhlcQueryParams extends BaseQueryParams {
  /**
   * The asset for which the price OHLC is to be fetched.
   */
  asset: string;

  /**
   * The period of price OHLC.
   */
  period: PricePeriod;

  /**
   * The starting timestamp for the block query range (Optional).
   */
  timestamp_start?: number;

  /**
   * The ending timestamp for the block query range (Optional).
   */
  timestamp_end?: number;
}

/**
 * Interface representing the Price Ohlc structure returned by the API.
 */
export interface PriceOhlc {
  period: string;
  timestamp: string;
  asset: string;
  volume_24h: string;
  open: string;
  high: string;
  low: string;
  close: string;
}

/**
 * Interface representing the Price Ohlc API response.
 */
export interface PriceOhlcAPI {
  pagination: Pagination;
  data: PriceOhlc[];
}

export interface DailyPriceChartParams {
  timestamp_start?: number;
  timestamp_end?: number;
}

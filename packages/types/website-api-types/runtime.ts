import type { BaseQueryParams, Pagination } from ".";

/**
 * Enum for RuntimeOrder
 *
 * This enum represents the sort options available for runtime data. Using this,
 * data can be ordered in terms of:
 * - ascending and descending order of block number,
 * - ascending and descending order of timestamp.
 */
export enum RuntimeOrder {
	BlockNumberAsc = "block_number_asc",
	BlockNumberDesc = "block_number_desc",
	TimestampAsc = "timestamp_asc",
	TimestampDesc = "timestamp_desc",
}

export interface RuntimeQueryParamsAtom extends BaseQueryParams {
	/**
	 * The starting block number for the query range (Optional).
	 */
	block_start?: number;

	/**
	 * The ending block number for the query range (Optional).
	 */
	block_end?: number;

	/**
	 * The starting timestamp for the block query range (Optional).
	 */
	timestamp_start?: number;

	/**
	 * The ending timestamp for the block query range (Optional).
	 */
	timestamp_end?: number;

	/**
	 * The sort order for the runtime data (Optional).
	 */
	order?: RuntimeOrder;
}

/**
 * Interface representing the Runtime structure returned by the API.
 */
export interface Runtime {
	block_number: number;
	timestamp: string;
	runtime_version: number;
}

/**
 * Interface representing the Runtime API.
 */
export interface RuntimeAPI {
	pagination: Pagination;
	data: Runtime[];
}

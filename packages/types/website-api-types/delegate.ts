import type { Address, BaseQueryParams, Pagination } from '.';

/**
 * Enum for DelegateBalanceOrder
 *
 * This enum defines the sorting options for delegation balance.
 * It allows sorting by:
 * - Balance in ascending or descending order.
 * - Delegate (hotkey) in ascending or descending order.
 * - Timestamp (creation or update time) in ascending or descending order.
 */
export enum DelegateBalanceOrder {
  StakeAsc = 'stake_asc',
  StakeDesc = 'stake_desc',
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

/**
 * Interface representing query parameters for fetching delegation balance data.
 */
export interface DelegateBalanceQueryParams extends BaseQueryParams {
  /**
   * The address of the delegate (hotkey) associated with the delegation balance.
   * Can be provided in SS58 or hexadecimal format. (Optional)
   */
  hotkey?: string;

  /**
   * The address of the nominator (coldkey) associated with the delegation balance.
   * Can be provided in SS58 or hexadecimal format. (Optional)
   */
  coldkey?: string;

  /**
   * The minimum stake amount to filter delegation balances. (Optional)
   * This parameter should be passed as a string.
   */
  stake_min?: string;

  /**
   * The maximum stake amount to filter delegation balances. (Optional)
   * This parameter should be passed as a string.
   */
  stake_max?: string;

  /**
   * The sorting order for delegation balance data. (Optional)
   * Acceptable values are defined in the `DelegateBalanceOrder` enum.
   */
  order?: DelegateBalanceOrder;
}

/**
 * Interface representing the Delegate Balance structure returned by the API.
 */
export interface DelegateBalance {
  coldkey: Address;
  hotkey: Address;
  block_number: number;
  timestamp: string;
  stake: string;
  created_at_block_number: number;
  created_at_timestamp: string;
  hotkey_name: string;
}

/**
 * Interface representing the Delegate Balance API response.
 */
export interface DelegateBalanceAPI {
  pagination: Pagination;
  data: DelegateBalance[];
}

/**
 * Enum for DelegateOrder
 *
 * This enum represents the sorting options available for delegations,
 * facilitating delegations to be sorted by:
 * - the ascending and descending order of the amount.
 * - the ascending and descending order of the block number.
 * - the ascending and descending order of the timestamp.
 * - the ascending and descending order of the action.
 * - the ascending and descending order of the alpha.
 * - the ascending and descending order of the usd.
 * - the ascending and descending order of the alpha price in tao.
 * - the ascending and descending order of the alpha price in usd.
 * - the ascending and descending order of the netuid.
 */
export enum DelegateOrder {
  AmountAsc = 'amount_asc',
  AmountDesc = 'amount_desc',
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
  ActionAsc = 'action_asc',
  ActionDesc = 'action_desc',
  AlphaAsc = 'alpha_asc',
  AlphaDesc = 'alpha_desc',
  UsdAsc = 'usd_asc',
  UsdDesc = 'usd_desc',
  AlphaPriceInTaoAsc = 'alpha_price_in_tao_asc',
  AlphaPriceInTaoDesc = 'alpha_price_in_tao_desc',
  AlphaPriceInUsdAsc = 'alpha_price_in_usd_asc',
  AlphaPriceInUsdDesc = 'alpha_price_in_usd_desc',
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
}

/**
 * Enum to define actions that can be taken for delegate operation
 */
export enum DelegateAction {
  Delegate = 'delegate',
  UnDelegate = 'undelegate',
  All = 'all',
}

/**
 * It represents the query parameters needed for fetching delegations.
 */
export interface DelegateQueryParams extends BaseQueryParams {
  /**
   * The delegate address of the account, either in SS58 or hex format (Optional).
   */
  delegate?: string;

  /**
   * The nominator address of the account, either in SS58 or hex format (Optional).
   */
  nominator?: string;

  /**
   * The action related to delegate operation which can be 'delegate', 'undelegate' or 'all' (Optional).
   */
  action?: DelegateAction;

  is_transfer?: boolean;

  /**
   * The unique identifier for the extrinsic where the delegate action has occurred (Optional).
   */
  extrinsic_id?: string;

  /**
   * The minimum delegation amount in the query (Optional).
   */
  amount_min?: string;

  /**
   * The maximum delegation amount in the query (Optional).
   */
  amount_max?: string;

  /**
   * The network identifier. This is an optional field.
   */
  netuid?: number;

  /**
   * The block number where the delegation is included (Optional).
   */
  block_number?: number;

  /**
   * The starting block number for the delegation query range (Optional).
   */
  block_start?: number;

  /**
   * The ending block number for the delegation query range (Optional).
   */
  block_end?: number;

  /**
   * The starting timestamp for the delegation query range (Optional).
   */
  timestamp_start?: number;

  /**
   * The ending timestamp for the delegation query range (Optional).
   */
  timestamp_end?: number;

  /**
   * The sort order for the delegation data (Optional).
   */
  order?: DelegateOrder;
}

/**
 * Interface representing the Delegation structure returned by the API.
 */
export interface Delegate {
  id: string;
  block_number: number;
  timestamp: string;
  action: string;
  nominator: Address;
  delegate: Address;
  amount: string;
  alpha: string | null;
  usd: string | null;
  alpha_price_in_tao: string | null;
  alpha_price_in_usd: string | null;
  netuid: number | null;
  extrinsic_id: string;
  is_transfer: boolean;
  transfer_address: Address | null;
}

/**
 * Interface representing the Delegation API response.
 */
export interface DelegateAPI {
  pagination: Pagination;
  data: Delegate[];
}

export interface TopDelegatorTransactionQueryParams {
  validator: string;
}

/**
 * Enum for DtaoDelegateBalanceOrder
 *
 * This enum defines the sorting options available when fetching delegation balance data.
 * It allows the delegation balance data to be sorted according to the following attributes, in ascending or descending order:
 * - network identifier (netuid)
 * - balance
 * - subnet rank
 * - balance as tao
 */
export enum DtaoDelegateBalanceOrder {
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
  BalanceAsc = 'balance_asc',
  BalanceDesc = 'balance_desc',
  SubnetRankAsc = 'subnet_rank_asc',
  SubnetRankDesc = 'subnet_rank_desc',
  BalanceAsTaoAsc = 'balance_as_tao_asc',
  BalanceAsTaoDesc = 'balance_as_tao_desc',
}

/**
 * Interface representing the query parameters for fetching delegation balance data.
 */
export interface DtaoDelegateBalanceQueryParams extends BaseQueryParams {
  /**
   * The address of the hotkey, represented in either SS58 or hex format. This is an optional field.
   */
  hotkey?: string;

  /**
   * The address of the coldkey, represented in either SS58 or hex format. This is an optional field.
   */
  coldkey?: string;

  /**
   * The network identifier. This is an optional field.
   */
  netuid?: number;

  /**
   * The minimum balance. This is an optional field.
   */
  balance_min?: string;

  /**
   * The maximum balance. This is an optional field.
   */
  balance_max?: string;

  /**
   * The minimum balance as tao. This is an optional field.
   */
  balance_as_tao_min?: string;

  /**
   * The maximum balance as tao. This is an optional field.
   */
  balance_as_tao_max?: string;

  /**
   * Specifies the order in which the delegation balance data is sorted. This is an optional field.
   */
  order?: DtaoDelegateBalanceOrder;
}

/**
 * Interface representing the structure of delegation balance data returned by the API.
 */
export interface DtaoDelegateBalance {
  block_number: number;
  timestamp: string;
  hotkey_name: string;
  hotkey: Address;
  coldkey: Address;
  netuid: number;
  subnet_rank: number;
  subnet_total_holders: number;
  balance: string;
  balance_as_tao: string;
}

/**
 * Interface representing a delegate balance API response which includes a page of delegation balance data.
 */
export interface DtaoDelegateBalanceAPI {
  pagination: Pagination;
  data: DtaoDelegateBalance[];
}

import type { Address, BaseQueryParams, NetworkType, Pagination } from '.';

/**
 * It represents the specific parameters required for querying account history.
 */
export interface AccountHistoryParamsAtom extends BaseQueryParams {
  /**
   * The address of the account, either in SS58 or hex format.
   */
  address: string;
}

/**
 * It represents the specific parameters required for querying accounts.
 */
export interface AccountParamsAtom extends BaseQueryParams {
  /**
   * The network type for the account query (Optional).
   */
  network?: NetworkType;

  /**
   * The address of the account, either in SS58 or hex format.
   */
  address?: string;

  /**
   * The starting timestamp for the query range (Optional).
   */
  timestampStart?: number;

  /**
   * The ending timestamp for the query range (Optional).
   */
  timestampEnd?: number;

  /**
   * The sort order for the account history data (Optional).
   */
  order?: AccountOrder;

  /**
   * The minimum balance for response of the account history query (Optional).
   */
  minBalance?: string;
}

/**
 * Enum for AccountHistoryOrder
 *
 * This enum represents the sorting options available for account histories,
 * facilitating account histories to be sorted by:
 * - the ascending and descending order of the block number.
 */
export enum AccountHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
}

/**
 * It represents the query parameters needed for fetching account history.
 */
export interface AccountHistoryQueryParams extends BaseQueryParams {
  /**
   * The address of the account, either in SS58 or hex format.
   */
  address: string;

  /**
   * The network type for the account query (Optional).
   */
  network?: NetworkType;

  /**
   * The starting point of the block range for the query (Optional).
   */
  block_start?: number;

  /**
   * The endpoint of the block range for the query (Optional).
   */
  block_end?: number;

  /**
   * The starting timestamp for the query range (Optional).
   */
  timestamp_start?: number;

  /**
   * The ending timestamp for the query range (Optional).
   */
  timestamp_end?: number;

  /**
   * The sort order for the account history data (Optional).
   */
  order?: AccountHistoryOrder;
}

/**
 * Enum for AccountOrder
 *
 * This enum represents the sort options available for accounts. Using this,
 * accounts can be ordered in terms of:
 * - ascending and descending order of free balance,
 * - ascending and descending order of staked balance,
 * - ascending and descending order of total balance.
 */
export enum AccountOrder {
  BalanceFreeAsc = 'balance_free_asc',
  BalanceFreeDesc = 'balance_free_desc',
  BalanceStakedAsc = 'balance_staked_asc',
  BalanceStakedDesc = 'balance_staked_desc',
  BalanceTotalAsc = 'balance_total_asc',
  BalanceTotalDesc = 'balance_total_desc',
  CreatedAtTimestampAsc = 'created_at_timestamp_asc',
  CreatedAtTimeStampDesc = 'created_at_timestamp_desc',
}

/**
 * It represents the query parameters needed for fetching accounts.
 */
export interface AccountQueryParams extends BaseQueryParams {
  /**
   * The address of the account, either in SS58 or hex format (Optional).
   */
  address?: string;

  /**
   * The minimum free balance for the account query (Optional).
   */
  balance_free_min?: string;

  /**
   * The maximum free balance for the account query (Optional).
   */
  balance_free_max?: string;

  /**
   * The minimum staked balance for the account query (Optional).
   */
  balance_staked_min?: string;

  /**
   * The maximum staked balance for the account query (Optional).
   */
  balance_staked_max?: string;

  /**
   * The minimum total balance for the account query (Optional).
   */
  balance_total_min?: string;

  /**
   * The maximum total balance for the account query (Optional).
   */
  balance_total_max?: string;

  /**
   * The rank for the account query (Optional).
   */
  rank?: number;

  /**
   * The network type for the account query (Optional).
   */
  created_on_network?: NetworkType;

  /**
   * The starting timestamp for the query range (Optional).
   */
  created_on_timestamp_start?: number;

  /**
   * The ending timestamp for the query range (Optional).
   */
  created_on_timestamp_end?: number;

  /**
   * The sort order for the account data (Optional).
   */
  order?: AccountOrder;
}

export interface ColdkeySwap {
  old_coldkey: Address;
  new_coldkey: Address;
  network: string;
  block_number: number;
  timestamp: string;
}

/**
 * Interface representing the Account structure returned by the API.
 */
export interface Account {
  address: Address;
  balance_free: string;
  balance_free_24hr_ago: string;
  balance_staked: string;
  balance_staked_24hr_ago: string;
  balance_staked_alpha_as_tao: string;
  balance_staked_alpha_as_tao_24hr_ago: string;
  balance_staked_root: string;
  balance_staked_root_24hr_ago: string;
  balance_total: string;
  balance_total_24hr_ago: string;
  block_number: number;
  coldkey_swap: ColdkeySwap | null;
  created_on_date: string;
  created_on_network: string;
  network: string;
  rank: number;
  timestamp: string;
}

/**
 * Interface representing the Account API response.
 */
export interface AccountAPI {
  pagination: Pagination;
  data: Account[];
}

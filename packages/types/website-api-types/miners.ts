// Import type definitions from relevant paths
import type { BaseQueryParams, Metagraph, Pagination } from '.';

/**
 * An enum to represent the sorting options for account history.
 *
 * It includes options for both ascending and descending sorting based on block number and timestamp.
 */
export enum MinerWeightHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface MinerColdkeyQueryParams {
  coldkey: string;
  days: number;
}

export interface MinerColdkey {
  active_subnets: number;
  alpha_balances: AlphaBalance[];
  average_mining_emission_as_tao_per_hotkey: string;
  coldkey: Key;
  free_balance: string;
  hotkeys: HotkeyItem[];
  total_active_hotkeys: number;
  total_balance: string;
  total_deregistered_hotkeys: number;
  total_hotkeys_in_danger: number;
  total_hotkeys_in_danger_during_period: number;
  total_immune_hotkeys: number;
  total_immune_hotkeys_during_period: number;
  total_mining_emission_as_tao: string;
  total_staked_balance_as_tao: string;
  total_staked_mining_balance_as_tao: string;
  total_staked_non_mining_balance_as_tao: string;
}

export interface HotkeyItem {
  alpha_balance: string;
  alpha_balance_as_tao: string;
  axon: string;
  coldkey: Key;
  consensus: string;
  deregistered: boolean;
  deregistration_timestamp: string;
  emission: string;
  hotkey: Key;
  immune: boolean;
  in_danger: boolean;
  incentive: string;
  miner_rank?: number;
  netuid: number;
  registration_block: number;
  total_emission: string;
  total_emission_as_tao: string;
  trust: string;
  uid: number;
  validator_rank?: number;
}

export interface Key {
  hex: string;
  ss58: string;
}

export interface AlphaBalance {
  balance: string;
  balance_as_tao: string;
  hotkey: string;
  coldkey: string;
  netuid: number;
}

export interface MinerColdkeyAPI {
  data: MinerColdkey[];
  pagination: Pagination;
}

/**
 * This interface defines the shape of the query parameters used when requesting account history data.
 * It extends BaseQueryParams to include additional parameters specific to account history.
 */
export interface MinerWeightsHistoryQueryParams extends BaseQueryParams {
  /** The unique identifier corresponding to the account in question. */
  netuid: number;

  /** The unique identifier for the miner. This is an optional parameter. */
  miner_uid?: number;

  /** The unique identifier for the validator, used as the starting point for a block range (optional). */
  validator_uid?: number;

  /** The specific block number to be queried (optional). */
  block_number?: number;

  /** The start of the block range to be queried (optional). */
  block_start?: number;

  /** The end of the block range to be queried (optional). */
  block_end?: number;

  /** The starting timestamp for the query range (optional). */
  timestamp_start?: number;

  /** The ending timestamp for the query range (optional). */
  timestamp_end?: number;

  /** The sorting order desired for the account history data (optional). */
  order?: MinerWeightHistoryOrder;
}

/**
 * MinerWeightHistory interface holds the basic structure for miner's weight history.
 */
export interface MinerWeightHistory {
  block_number: number;
  timestamp: string;
  netuid: number;
  miner_uid: number;
  validator_uid: number;
  weight: string;
}

/**
 * MinerWeightHistoryAPI interface holds the structure for the API response
 * which includes an array of miner's weight history and pagination data.
 */
export interface MinerWeightHistoryAPI {
  data: MinerWeightHistory[];
  pagination: Pagination;
}

export interface MinerWeightChartParams {
  netuid: number;
  minerUid: number;
}

export interface MinerWeightChart {
  chartData: MinerWeightHistory[][];
  validators: Metagraph[];
}

export interface MinerPerformanceByNetuidParams {
  netuid: number;
}

export interface MinerPerformanceByNetuid {
  minerList: {
    label: string;
    color: string;
    uid: number | undefined;
  }[];
  minerHistory: Metagraph[][];
}

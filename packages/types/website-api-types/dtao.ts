import type { Address, BaseQueryParams, Pagination } from '.';

/**
 * Enum for DelegateOrder
 *
 * This enum represents the sorting options available for delegations.
 * It allows the delegations to be sorted by:
 * - the ascending and descending order of the TAO (Token Authenticated Obligation)
 * - the ascending and descending order of the block number
 * - the ascending and descending order of the timestamp
 */
export enum DtaoDelegateOrder {
  TaoAsc = 'tao_asc',
  TaoDesc = 'tao_desc',
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

/**
 * Interface representing the query parameters for fetching delegations.
 */
export interface DtaoDelegateQueryParams extends BaseQueryParams {
  /**
   * The delegate’s address, which can be represented in either SS58 or hex format. This is an optional field.
   */
  hotkey?: string;

  /**
   * The nominator’s address, which can be represented in either SS58 or hex format. This is an optional field.
   */
  coldkey?: string;

  /**
   * The specification of the delegate operation, which can be 'delegate', 'undelegate' or 'all'. This is an optional field.
   */
  block_number?: number;

  /**
   * The unique identifier of the extrinsic that captured the delegate action. This is an optional field.
   */
  block_start?: number;

  /**
   * The minimum delegation amount within the query's scope. This is an optional field.
   */
  block_end?: number;

  /**
   * The maximum delegation amount within the query's scope. This is an optional field.
   */
  timestamp_start?: number;

  /**
   * The block number where the delegation was confirmed. This is an optional field.
   */
  timestamp_end?: number;

  /**
   * The starting block number designated for the delegation query range. This is an optional field.
   */
  netuid?: number;

  /**
   * Defines the order in which the delegations are sorted. This is an optional field.
   */
  order?: DtaoDelegateOrder;
}

/**
 * Interface representing the full structure of a Delegation object returned by the API.
 */
export interface DtaoDelegate {
  block_number: number;
  timestamp: string;
  netuid: number;
  action: string;
  coldkey: Address;
  hotkey: Address;
  tao: string;
  alpha: string;
}

/**
 * Interface representing an API response inclusive of a page of Delegation data.
 */
export interface DtaoDelegateAPI {
  pagination: Pagination;
  data: DtaoDelegate[];
}

/**
 * Enum for PoolOrder
 *
 * This enum represents the sorting options available for pool data.
 * It allows the pool data to be sorted by the following methods in ascending and descending order:
 * - block number
 * - timestamp
 * - netuid
 * - price
 * - market cap
 * - total TAO (Token Authenticated Obligation)
 * - total ALPHA
 * - ALPHA in pool
 * - ALPHA staked
 */
export enum PoolOrder {
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
  PriceAsc = 'price_asc',
  PriceDesc = 'price_desc',
  MarketCapAsc = 'market_cap_asc',
  MarketCapDesc = 'market_cap_desc',
  TotalTaoAsc = 'total_tao_asc',
  TotalTaoDesc = 'total_tao_desc',
  TotalAlphaAsc = 'total_alpha_asc',
  TotalAlphaDesc = 'total_alpha_desc',
  AlphaInPoolAsc = 'alpha_in_pool_asc',
  AlphaInPoolDesc = 'alpha_in_pool_desc',
  AlphaStakedAsc = 'alpha_staked_asc',
  AlphaStakedDesc = 'alpha_staked_desc',
  TaoVolumeOneDayAsc = 'tao_volume_one_day_asc',
  TaoVolumeOneDayDesc = 'tao_volume_one_day_desc',
  PriceChangeOneHourAsc = 'price_change_one_hour_asc',
  PriceChangeOneHourDesc = 'price_change_one_hour_desc',
  PriceChangeOneDayAsc = 'price_change_one_day_asc',
  PriceChangeOneDayDesc = 'price_change_one_day_desc',
  PriceChangeOneWeekAsc = 'price_change_one_week_asc',
  PriceChangeOneWeekDesc = 'price_change_one_week_desc',
  PriceChangeOneMonthAsc = 'price_change_one_month_asc',
  PriceChangeOneMonthDesc = 'price_change_one_month_desc',
  RootPropAsc = 'root_prop_asc',
  RootPropDesc = 'root_prop_desc',
  FearAndGreedIndexAsc = 'fear_and_greed_index_asc',
  FearAndGreedIndexDesc = 'fear_and_greed_index_desc',
}

/**
 * Interface representing the query parameters for fetching pool data.
 */
export interface PoolQueryParams extends BaseQueryParams {
  /**
   * The network identifier. This is an optional field.
   */
  netuid?: number;

  /**
   * Defines the order in which the pool data is sorted. This is an optional field.
   */
  order?: PoolOrder;
}

export interface PriceItem {
  block_number: number;
  timestamp: string;
  price: string;
}

export interface SevenPriceItem {
  block_number: number;
  timestamp: string;
  price: string;
}

/**
 * Interface representing the structure of Pool data returned by the API.
 */
export interface Pool {
  alpha_in_pool: string;
  alpha_staked: string;
  block_number: number;
  buyers_24_hr: number;
  buys_24_hr: number;
  liquidity: string;
  market_cap: string;
  name: string;
  netuid: number;
  price_change_1_day: string;
  price_change_1_hour: string;
  price_change_1_week: string;
  price_change_1_month: string;
  price: string;
  rank: number;
  sellers_24_hr: number;
  sells_24_hr: number;
  seven_day_prices: PriceItem[];
  symbol: string;
  tao_buy_volume_24_hr: string;
  tao_sell_volume_24_hr: string;
  tao_volume_24_hr: string;
  timestamp: string;
  total_alpha: string;
  total_tao: string;
  market_cap_change_1_day: string;
  tao_volume_24_hr_change_1_day: string;
  startup_mode: boolean;
  root_prop: string;
  fear_and_greed_index: string;
  fear_and_greed_sentiment: string;
}

/**
 * Interface representing a Pool API response which includes a page of Pool data.
 */
export interface PoolAPI {
  pagination: Pagination;
  data: Pool[];
}

export interface TotalPriceLatest {
  block_number: number;
  timestamp: string;
  price: string;
  fear_and_greed_index: string;
  fear_and_greed_sentiment: string;
}

export interface TotalPriceLatestAPI {
  pagination: Pagination;
  data: TotalPriceLatest[];
}

/**
 * Enum for SubnetEmissionOrder
 *
 * This enum represents the sorting options available when fetching subnet emission data.
 * It allows the subnet emission data to be sorted according to the following attributes, in ascending and descending order:
 * - block number
 * - timestamp
 * - netuid
 * - TAO (Token Authenticated Obligation) in pool
 * - ALPHA in pool
 * - ALPHA rewards
 */
export enum SubnetEmissionOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
  TaoInPoolAsc = 'tao_in_pool_asc',
  TaoInPoolDesc = 'tao_in_pool_desc',
  AlphaInPoolAsc = 'alpha_in_pool_asc',
  AlphaInPoolDesc = 'alpha_in_pool_desc',
  AlphaRewardsAsc = 'alpha_rewards_asc',
  AlphaRewardsDesc = 'alpha_rewards_desc',
}

/**
 * Interface representing the query parameters for fetching subnet emission data.
 */
export interface SubnetEmissionQueryParams extends BaseQueryParams {
  /**
   * The network identifier. This is an optional field.
   */
  netuid?: number;

  /**
   * The block number. This is an optional field.
   */
  block_number?: number;

  /**
   * The start block number for the query range. This is an optional field.
   */
  block_start?: number;

  /**
   * The end block number for the query range. This is an optional field.
   */
  block_end?: number;

  /**
   * The start timestamp for the query range. This is an optional field.
   */
  timestamp_start?: number;

  /**
   * The end timestamp for the query range. This is an optional field.
   */
  timestamp_end?: number;

  /**
   * Specifies the order in which the subnet emission data is sorted. This is an optional field.
   */
  order?: SubnetEmissionOrder;
}

/**
 * Interface representing the structure of subnet emission data returned by the API.
 */
export interface SubnetEmission {
  netuid: number;
  block_number: number;
  timestamp: string;
  name: string;
  symbol: string;
  tao_in_pool: string;
  alpha_in_pool: string;
  alpha_rewards: string;
}

/**
 * Interface representing a subnet emission API response which includes a page of subnet emission data.
 */
export interface SubnetEmissionAPI {
  pagination: Pagination;
  data: SubnetEmission[];
}

/**
 * Enum for HotkeyEmissionOrder
 *
 * This enum represents the sorting options available when fetching hotkey emission data.
 * It allows the hotkey emission data to be sorted according to the following attributes, in ascending and descending order:
 * - block number
 * - timestamp
 * - netuid
 * - emission
 * - root emission
 */
export enum HotkeyEmissionOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
  EmissionAsc = 'emission_asc',
  EmissionDesc = 'emission_desc',
  RootEmissionAsc = 'root_emission_asc',
  RootEmissionDesc = 'root_emission_desc',
}

/**
 * Interface representing the query parameters for fetching hotkey emission data.
 */
export interface HotkeyEmissionQueryParams extends BaseQueryParams {
  /**
   * The address of the hotkey, represented in either SS58 or hex format. This is an optional field.
   */
  hotkey?: string;

  /**
   * The network identifier. This is an optional field.
   */
  netuid?: number;

  /**
   * The block number. This is an optional field.
   */
  block_number?: number;

  /**
   * The start block number for the query range. This is an optional field.
   */
  block_start?: number;

  /**
   * The end block number for the query range. This is an optional field.
   */
  block_end?: number;

  /**
   * The start timestamp for the query range. This is an optional field.
   */
  timestamp_start?: number;

  /**
   * The end timestamp for the query range. This is an optional field.
   */
  timestamp_end?: number;

  /**
   * Specifies the order in which the hotkey emission data is sorted. This is an optional field.
   */
  order?: HotkeyEmissionOrder;
}

/**
 * Interface representing the structure of hotkey emission data returned by the API.
 */
export interface HotkeyEmission {
  block_number: number;
  timestamp: string;
  netuid: number;
  hotkey: Address;
  emission: string;
  root_emission: string;
}

/**
 * Interface representing a hotkey emission API response which includes a page of hotkey emission data.
 */
export interface HotkeyEmissionAPI {
  pagination: Pagination;
  data: HotkeyEmission[];
}

/**
 * Enum for HotkeyAlphaSharesOrder
 *
 * This enum represents the sorting options available when fetching hotkey alpha shares data.
 * It allows the hotkey alpha shares data to be sorted according to the following attributes, in ascending and descending order:
 * - block number
 * - timestamp
 * - netuid
 */
export enum HotkeyAlphaSharesOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
}

/**
 * Interface representing the query parameters for fetching hotkey alpha shares data.
 */
export interface HotkeyAlphaSharesQueryParams extends BaseQueryParams {
  /**
   * The address of the hotkey, represented in either SS58 or hex format. This is an optional field.
   */
  hotkey?: string;

  /**
   * The network identifier. This is an optional field.
   */
  netuid?: number;

  /**
   * The block number. This is an optional field.
   */
  block_number?: number;

  /**
   * The start block number for the query range. This is an optional field.
   */
  block_start?: number;

  /**
   * The end block number for the query range. This is an optional field.
   */
  block_end?: number;

  /**
   * The start timestamp for the query range. This is an optional field.
   */
  timestamp_start?: number;

  /**
   * The end timestamp for the query range. This is an optional field.
   */
  timestamp_end?: number;

  /**
   * Specifies the order in which the hotkey alpha shares data is sorted. This is an optional field.
   */
  order?: HotkeyAlphaSharesOrder;
}

/**
 * Interface representing the structure of hotkey alpha shares data returned by the API.
 */
export interface HotkeyAlphaShares {
  block_number: number;
  timestamp: string;
  netuid: number;
  hotkey: Address;
  shares: string;
  alpha: string;
}

/**
 * Interface representing a hotkey alpha shares API response which includes a page of hotkey alpha shares data.
 */
export interface HotkeyAlphaSharesAPI {
  pagination: Pagination;
  data: HotkeyAlphaShares[];
}

/**
 * Enum for ColdkeyAlphaSharesOrder
 *
 * This enum represents the sorting options available when fetching coldkey alpha shares data.
 * It allows the coldkey alpha shares data to be sorted according to the following attributes, in ascending and descending order:
 * - block number
 * - timestamp
 * - netuid
 */
export enum ColdkeyAlphaSharesOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
}

/**
 * Interface representing the query parameters for fetching coldkey alpha shares data.
 */
export interface ColdkeyAlphaSharesQueryParams extends BaseQueryParams {
  /**
   * The address of the coldkey, represented in either SS58 or hex format. This is an optional field.
   */
  coldkey?: string;

  /**
   * The address of the hotkey, represented in either SS58 or hex format. This is an optional field.
   */
  hotkey?: string;

  /**
   * The network identifier. This is an optional field.
   */
  netuid?: number;

  /**
   * The block number. This is an optional field.
   */
  block_number?: number;

  /**
   * The start block number for the query range. This is an optional field.
   */
  block_start?: number;

  /**
   * The end block number for the query range. This is an optional field.
   */
  block_end?: number;

  /**
   * The start timestamp for the query range. This is an optional field.
   */
  timestamp_start?: number;

  /**
   * The end timestamp for the query range. This is an optional field.
   */
  timestamp_end?: number;

  /**
   * Specifies the order in which the coldkey alpha shares data is sorted. This is an optional field.
   */
  order?: ColdkeyAlphaSharesOrder;
}

/**
 * Interface representing the structure of coldkey alpha shares data returned by the API.
 */
export interface ColdkeyAlphaShares {
  block_number: number;
  timestamp: string;
  netuid: number;
  coldkey: Address;
  hotkey: Address;
  shares: string;
  alpha: string;
}

/**
 * Interface representing a coldkey alpha shares API response which includes a page of coldkey alpha shares data.
 */
export interface ColdkeyAlphaSharesAPI {
  pagination: Pagination;
  data: ColdkeyAlphaShares[];
}

/**
 * Enum for StakeBalanceOrder
 *
 * This enum represents the sorting options available when fetching stake balance data.
 * It allows the stake balance data to be sorted according to the following attributes, in ascending and descending order:
 * - balance as tao
 * - balance
 * - netuid
 */
export enum StakeBalanceOrder {
  BalanceAsTaoAsc = 'balance_as_tao_asc',
  BalanceAsTaoDesc = 'balance_as_tao_desc',
  BalanceAsc = 'balance_asc',
  BalanceDesc = 'balance_desc',
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
}

/**
 * Interface representing the query parameters for fetching stake balance data.
 */
export interface StakeBalanceQueryParams extends BaseQueryParams {
  /**
   * The address of the coldkey, represented in either SS58 or hex format. This is an optional field.
   */
  coldkey?: string;

  /**
   * The address of the hotkey, represented in either SS58 or hex format. This is an optional field.
   */
  hotkey?: string;

  /**
   * The network identifier. This is an optional field.
   */
  netuid?: number;

  /**
   * The number of days to query. This is an optional field.
   */
  days?: number;

  /**
   * The minimum balance. This is an optional field.
   */
  balance_min?: string;

  /**
   * The maximum balance. This is an optional field.
   */
  balance_max?: string;

  /**
   * The minimum balance as tao for the query range. This is an optional field.
   */
  balance_as_tao_min?: string;

  /**
   * The maximum balance as tao for the query range. This is an optional field.
   */
  balance_as_tao_max?: string;

  /**
   * Specifies the order in which the stake balance data is sorted. This is an optional field.
   */
  order?: StakeBalanceOrder;
}

/**
 * Interface representing the structure of stake balance data returned by the API.
 */
export interface StakeBalance {
  block_number: number;
  timestamp: string;
  hotkey_name: string;
  hotkey: Address;
  coldkey: Address;
  netuid: number;
  balance: string;
  balance_as_tao: string;
  total_sells: number;
  total_buys: number;
  total_transfers_in: number;
  total_transfers_out: number;
  total_earned_alpha: string;
  total_earned_alpha_as_tao: string;
  total_earned_alpha_as_usd: string;
  subnet_rank: number | null;
  subnet_total_holders: number | null;
  total_bought_alpha: string;
  total_bought_alpha_as_tao: string;
  total_bought_alpha_as_usd: string;
  total_transferred_in_alpha: string;
  total_transferred_in_alpha_as_tao: string;
  total_transferred_in_alpha_as_usd: string;
  total_transferred_out_alpha: string;
  total_transferred_out_alpha_as_tao: string;
  total_transferred_out_alpha_as_usd: string;
  total_sold_alpha: string;
  total_sold_alpha_as_tao: string;
  total_sold_alpha_as_usd: string;
  realised_profit_tao: string;
  realised_profit_usd: string;
  unrealised_profit_tao: string;
  unrealised_profit_usd: string;
}

/**
 * Interface representing a stake balance API response which includes a page of stake balance data.
 */
export interface StakeBalanceAPI {
  pagination: Pagination;
  data: StakeBalance[];
}

/**
 * Enum for StakeBalanceAggregatedOrder
 *
 * This enum represents the sorting options available when fetching aggregated stake balance data.
 * It allows the aggregated stake balance data to be sorted according to the following attributes, in ascending and descending order:
 * - total balance as tao
 */
export enum StakeBalanceAggregatedOrder {
  TotalBalanceAsTaoAsc = 'total_balance_as_tao_asc',
  TotalBalanceAsTaoDesc = 'total_balance_as_tao_desc',
}

/**
 * Interface representing the query parameters for fetching aggregated stake balance data.
 */
export interface StakeBalanceAggregatedQueryParams extends BaseQueryParams {
  /**
   * The address of the coldkey, represented in either SS58 or hex format. This is an optional field.
   */
  coldkey?: string;

  /**
   * The minimum total balance as tao. This is an optional field.
   */
  total_balance_as_tao_min?: string;

  /**
   * The maximum total balance as tao. This is an optional field.
   */
  total_balance_as_tao_max?: string;

  /**
   * Specifies the order in which the aggregated stake balance data is sorted. This is an optional field.
   */
  order?: StakeBalanceAggregatedOrder;
}

/**
 * Interface representing the structure of aggregated stake balance data returned by the API.
 */
export interface StakeBalanceAggregated {
  block_number: number;
  timestamp: string;
  coldkey: Address;
  total_balance_as_tao: string;
}

/**
 * Interface representing an aggregated stake balance API response which includes a page of aggregated stake balance data.
 */
export interface StakeBalanceAggregatedAPI {
  pagination: Pagination;
  data: StakeBalanceAggregated[];
}

export interface StakeBalanceHistoryQueryParams {
  coldkey: string;
  hotkey: string;
  netuid: number;
}

export interface StakeBalanceHistoryTableParams {
  coldkey: string;
  subnetHotkeys: {
    hotkey: string;
    netuid: number;
  }[];
}

export interface StakeBalanceHistoryChartParams {
  coldkey: string;
  hotkeys: string[];
  netuid: number;
}

/**
 * Interface representing the structure of aggregated stake balance data returned by the API.
 */
export interface StakeBalanceHistory {
  block_number: number;
  timestamp: string;
  hotkey_name: string;
  hotkey: Address;
  coldkey: Address;
  netuid: number;
  balance: string;
  balance_as_tao: string;
}

/**
 * Interface representing an aggregated stake balance API response which includes a page of aggregated stake balance data.
 */
export interface StakeBalanceHistoryAPI {
  pagination: Pagination;
  data: StakeBalanceHistory[];
}

export interface DtaoSubnet {
  active_keys: number;
  alpha_in_pool: string;
  alpha_staked: string;
  block_number: number;
  buyers_24_hr: number;
  buys_24_hr: number;
  description: string;
  emission: string;
  github: string;
  liquidity: string;
  market_cap: string;
  market_cap_change_1_day: string;
  tao_volume_24_hr_change_1_day: string;
  max_neurons: number;
  name: string;
  netuid: number;
  neuron_registration_cost: string;
  owner: string;
  price_change_1_day: string;
  price_change_1_hour: string;
  price_change_1_week: string;
  price: string;
  rank: number;
  recycled_24_hours: string;
  recycled_lifetime: string;
  recycled_since_registration: string;
  registration_timestamp: string;
  sellers_24_hr: number;
  sells_24_hr: number;
  seven_day_prices: SevenPriceItem[];
  subnet_description: string;
  subnet_name: string;
  symbol: string;
  tao_buy_volume_24_hr: string;
  tao_sell_volume_24_hr: string;
  tao_volume_24_hr: string;
  timestamp: string;
  total_alpha: string;
  total_tao: string;
  discord_url: string;
  subnet_contact: string;
  subnet_url: string;
  price_change_1_month: string;
  startup_mode: boolean;
  root_prop: string;
  fear_and_greed_index: string;
  fear_and_greed_sentiment: string;
}

export interface DtaoSubnetsAPI {
  total: number;
  data: DtaoSubnet[];
}

/**
 * Enum which outlines the possible ways to sort hotkey alpha shares data.
 *
 * The sorting can be done by 'netuid', 'shares', or 'alpha' in either ascending or descending order.
 */
export enum HotkeyAlphaSharesLatestOrder {
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
  SharesAsc = 'shares_asc',
  SharesDesc = 'shares_desc',
  AlphaAsc = 'alpha_asc',
  AlphaDesc = 'alpha_desc',
}

/**
 * This interface defines the structure for query parameters used when hotkey alpha shares data is requested.
 *
 * The parameters include options for alpha minimum, alpha maximum, hotkey, network unique identifier, and order
 * which can be used to customize the data query.
 */
export interface HotkeyAlphaSharesLatestQueryParams extends BaseQueryParams {
  /** The minimum alpha value. This is an optional field. */
  alpha_min?: string;

  /** The maximum alpha value. This is an optional field. */
  alpha_max?: string;

  /** The specific hotkey value string. This is an optional field. */
  hotkey?: string;

  /** The network unique identifier value. This is an optional field. */
  netuid?: number;

  /** An optional sort order for the hotkey alpha shares data. */
  order?: HotkeyAlphaSharesLatestOrder;
}

/**
 * This interface defines the structure of the hotkey alpha shares data that is retrieved from the API.
 *
 * The data includes the block number, timestamp, network unique identifier, hotkey, shares, and alpha value.
 */
export interface HotkeyAlphaSharesLatest {
  block_number: number;
  timestamp: string;
  netuid: number;
  hotkey: Address;
  shares: string;
  alpha: string;
}

/**
 * This interface defines the structure of the API response for a request for hotkey alpha shares data.
 *
 * The response will include pagination information, as well as an array of hotkey alpha shares data.
 */
export interface HotkeyAlphaSharesLatestAPI {
  pagination: Pagination;
  data: HotkeyAlphaSharesLatest[];
}

/**
 * Enum for ValidatorYieldLatestOrder
 *
 * This enum represents the sorting options available when fetching the latest validator yield data.
 * It allows the validator yield data to be sorted according to the following attributes, in ascending and descending order:
 * - network identifier (netuid)
 * - validator name (name)
 * - stake amount (stake)
 * - 1-hour APY (one_hour_apy)
 * - 1-day APY (one_day_apy)
 * - 7-day APY (seven_day_apy)
 * - 30-day APY (thirty_day_apy)
 */
export enum ValidatorYieldLatestOrder {
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
  NameAsc = 'name_asc',
  NameDesc = 'name_desc',
  StakeAsc = 'stake_asc',
  StakeDesc = 'stake_desc',
  OneHourApyAsc = 'one_hour_apy_asc',
  OneHourApyDesc = 'one_hour_apy_desc',
  OneDayApyAsc = 'one_day_apy_asc',
  OneDayApyDesc = 'one_day_apy_desc',
  SevenDayApyAsc = 'seven_day_apy_asc',
  SevenDayApyDesc = 'seven_day_apy_desc',
  ThirtyDayApyAsc = 'thirty_day_apy_asc',
  ThirtyDayApyDesc = 'thirty_day_apy_desc',
}

/**
 * Interface representing the query parameters for fetching the latest validator yield data.
 */
export interface ValidatorYieldLatestQueryParams extends BaseQueryParams {
  /**
   * The address of the hotkey, represented in either SS58 or hex format. This is an optional field.
   */
  hotkey?: string;

  /**
   * The network identifier. This is an optional field.
   */
  netuid?: number;

  min_stake?: string;

  /**
   * Specifies the order in which the latest validator yield data is sorted. This is an optional field.
   */
  order?: ValidatorYieldLatestOrder;
}

/**
 * Interface representing the structure of the latest validator yield data returned by the API.
 */
export interface ValidatorYieldLatest {
  hotkey: Address;
  name: string;
  netuid: number;
  block_number: number;
  timestamp: string;
  stake: string;
  one_hour_apy: string;
  one_day_apy: string;
  seven_day_apy: string;
  thirty_day_apy: string;
}

/**
 * Interface representing a latest validator yield API response that includes a page of validator yield data.
 */
export interface ValidatorYieldLatestAPI {
  pagination: Pagination;
  data: ValidatorYieldLatest[];
}

/**
 * Enum for PoolTotalPriceOrder
 *
 * This enum represents the sorting options available when fetching total pool price data.
 * It allows the pool price data to be sorted according to the following attributes, in ascending or descending order:
 * - timestamp
 */
export enum PoolTotalPriceHistoryOrder {
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

/**
 * Interface representing the query parameters for fetching total pool price data.
 */
export interface PoolTotalPriceHistoryQueryParams extends BaseQueryParams {
  frequency: 'by_block' | 'by_hour' | 'by_day';
  /**
   * Specifies the order in which the total pool price data is sorted. This is an optional field.
   */
  order?: PoolTotalPriceHistoryOrder;
}

/**
 * Interface representing the structure of total pool price data returned by the API.
 */
export interface PoolTotalPriceHistory {
  block_number: number;
  timestamp: string;
  price: string;
  alpha_volume: string;
  alpha_sell_volume: string;
  alpha_buy_volume: string;
  root_volume: string;
  root_sell_volume: string;
  root_buy_volume: string;
}

/**
 * Interface representing a total pool price API response that includes a page of total pool price data.
 */
export interface PoolTotalPriceHistoryAPI {
  pagination: Pagination;
  data: PoolTotalPriceHistory[];
}

/**
 * Interface representing the query parameters for fetching TradingView history data.
 */
export interface TradingviewHistoryQueryParams {
  /**
   * The symbol for the crypto asset. This is a required field.
   */
  symbol: string;

  /**
   * The resolution for historical data, this could be in minute (1, 5, 15, 30),
   * hour (60), day (1D), week (1W), or month (1M). This is a required field.
   */
  resolution: string;

  /**
   * The starting Unix timestamp for the historical data query. This is an optional field.
   */
  from?: number;

  /**
   * The ending Unix timestamp for the historical data query. This is a required field.
   */
  to: number;

  /**
   * Count back for obtaining the historical data. This is an optional field.
   */
  countback?: string;
}

/**
 * Interface representing the structure of TradingView history data returned by the API.
 */
export interface TradingviewHistory {
  s: string;
  t: number[];
  c: number[];
  o: number[];
  h: number[];
  l: number[];
  v: number[];
}

export interface SubnetPriceQueryParams {
  id: number;

  type: string;

  timestamp_start: string;
}

/**
 * Enum for PoolHistoryOrder
 *
 * This enum represents the sorting options available when fetching pool history data.
 * It allows the pool history data to be sorted according to the following attributes, in ascending or descending order:
 * - block number
 * - timestamp
 */
export enum PoolHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

/**
 * Enum representing the frequency at which data return.
 */
export enum PoolHistoryFrequency {
  /**
   * Represents data returning block by block.
   */
  ByBlock = 'by_block',

  /**
   * Represents data returning on a daily basis.
   */
  ByDay = 'by_day',

  /**
   * Represents data returning on a hourly basis.
   */
  ByHour = 'by_hour',
}

/**
 * Interface representing the query parameters for fetching pool history data.
 */
export interface PoolHistoryQueryParams extends BaseQueryParams {
  /**
   * The network identifier. This is an optional field.
   */
  netuid?: number;

  /**
   * The frequency of data, which can be 'by_block', 'by_hour', or 'by_day'. This is an optional field.
   */
  frequency?: PoolHistoryFrequency;

  /**
   * The block number. This is an optional field.
   */
  block_number?: number;

  /**
   * The start block number for the query range. This is an optional field.
   */
  block_start?: number;

  /**
   * The end block number for the query range. This is an optional field.
   */
  block_end?: number;

  /**
   * The start timestamp for the query range. This is an optional field.
   */
  timestamp_start?: number;

  /**
   * The end timestamp for the query range. This is an optional field.
   */
  timestamp_end?: number;

  /**
   * Specifies the order in which the pool history data is sorted. This is an optional field.
   */
  order?: PoolHistoryOrder;
}

/**
 * Interface representing the structure of the pool history data returned by the API.
 */
export interface PoolHistory {
  netuid: number;
  block_number: number;
  timestamp: string;
  name: string;
  symbol: string;
  market_cap: string;
  liquidity: string;
  total_tao: string;
  total_alpha: string;
  alpha_in_pool: string;
  alpha_staked: string;
  price: string;
  root_prop: string;
  rank: number;
  startup_mode: boolean;
}

/**
 * Interface representing a pool history API response that includes a page of pool history data.
 */
export interface PoolHistoryAPI {
  pagination: Pagination;
  data: PoolHistory[];
}

export interface DtaoPoolSubnetQueryParams extends BaseQueryParams {
  /**
   * The network identifier. This is an optional field.
   */
  netuid?: number;

  /**
   * Defines the order in which the pool data is sorted. This is an optional field.
   */
  order?: PoolOrder;
}

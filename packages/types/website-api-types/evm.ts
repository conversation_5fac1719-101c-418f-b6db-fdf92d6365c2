import type { BaseQueryParams, Pagination } from '.';

/**
 * Enum for BlockOrder
 *
 * This enum represents the sorting options available for Blocks,
 * allowing blocks to be ordered according to:
 * - the ascending and descending order of the block number,
 * - the ascending and descending order of the timestamp.
 */
export enum EvmBlockOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

/**
 * Interface representing query parameters for block API.
 */
export interface EvmBlockQueryParams extends BaseQueryParams {
  /**
   * The block number where the block is included (Optional).
   */
  block_number?: number;

  /**
   * The starting block number for the block query range (Optional).
   */
  block_start?: number;

  /**
   * The ending block number for the block query range (Optional).
   */
  block_end?: number;

  /**
   * The starting timestamp for the block query range (Optional).
   */
  timestamp_start?: number;

  /**
   * The ending timestamp for the block query range (Optional).
   */
  timestamp_end?: number;

  /**
   * The sort order for the block data (Optional).
   */
  order?: EvmBlockOrder;
}

/**
 * Interface representing the Block structure returned by the API.
 */
export interface EvmBlock {
  base_fee_per_gas: string;
  difficulty: string;
  extra_data: string;
  gas_limit: string;
  gas_used: string;
  hash: string;
  miner: string;
  nonce: string;
  number: number;
  parent_hash: string;
  sha_3_uncles: string;
  size: number;
  state_root: string;
  timestamp: string;
  total_difficulty: string;
  transaction_root: string;
  uncles: string;
}

/**
 * Interface representing the Block API response.
 */
export interface EvmBlockAPI {
  pagination: Pagination;
  data: EvmBlock[];
}

/**
 * It represents the specific parameters required for querying Blocks.
 */
export interface EvmBlockParamsAtom extends BaseQueryParams {
  /**
   * Search parameter across block number and hash (Optional)
   */
  search?: string;

  /**
   * The sort order for the Block data (Optional)
   */
  order?: EvmBlockOrder;

  /**
   * Interval (in milliseconds) at which metagraph data should be fetched again (Optional).
   */
  refetchInterval?: number;
}

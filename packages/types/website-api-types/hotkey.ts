import type { Address, BaseQueryParams, Pagination } from '.';

/**
 * Interface representing the Parent/Child Hotkey structure returned by the API.
 */
export interface HotkeyRelation {
  hotkey: Address;
  coldkey: Address;
  stake: string;
  family_stake: string;
  take: string;
  childkey_take: string;
  proportion: string;
  proportion_staked: string;
  root_weight: string;
  root_stake: string;
  alpha_stake: string;
  root_stake_as_alpha: string;
  total_alpha_stake: string;
  family_root_stake: string;
  family_alpha_stake: string;
  family_root_stake_as_alpha: string;
  family_total_alpha_stake: string;
  proportion_root_stake: string;
  proportion_alpha_stake: string;
  proportion_root_stake_as_alpha: string;
  proportion_total_alpha_stake: string;
}

/**
 * Interface representing the Hotkey structure returned by the API.
 */
export interface Hotkey {
  hotkey: Address;
  coldkey: Address;
  block_number: number;
  timestamp: string;
  netuid: number;
  stake: string;
  family_stake: string;
  take: string;
  childkey_take: string;
  children: HotkeyRelation[];
  parents: HotkeyRelation[];
  root_weight: string;
  root_stake: string;
  alpha_stake: string;
  root_stake_as_alpha: string;
  total_alpha_stake: string;
  family_root_stake: string;
  family_alpha_stake: string;
  family_root_stake_as_alpha: string;
  family_total_alpha_stake: string;
}

/**
 * Interface representing the Hotkey API response.
 */
export interface HotkeyAPI {
  pagination: Pagination;
  data: Hotkey[];
}

/**
 * Enum for HotkeyOrder
 *
 * This enum represents the sorting options available for hotkeys,
 * allowing hotkeys to be ordered according to:
 * - the ascending and descending order of the block number,
 * - the ascending and descending order of the timestamp,
 * - the ascending and descending order of the subnet id.
 */
export enum HotkeyOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
  SubnetIdAsc = 'subnet_id_asc',
  SubnetIdDesc = 'subnet_id_desc',
}

/**
 * Interface representing query parameters for Hotkey History API.
 */
export interface HotkeyHistoryQueryParams extends BaseQueryParams {
  /**
   * Hotkey in either SS58 or hex format (Optional).
   */
  hotkey?: string;

  /**
   * Unique identifier for a subnet (Optional).
   */
  netuid?: number;

  /**
   * The block number where the hotkey history is included (Optional).
   */
  block_number?: number;

  /**
   * The starting block number for the hotkey history query range (Optional).
   */
  block_start?: number;

  /**
   * The ending block number for the hotkey history query range (Optional).
   */
  block_end?: number;

  /**
   * The starting timestamp for the hotkey history query range (Optional).
   */
  timestamp_start?: number;

  /**
   * The ending timestamp for the hotkey history query range (Optional).
   */
  timestamp_end?: number;

  /**
   * The sort order for the hotkey history data (Optional).
   */
  order?: HotkeyOrder;
}

/**
 * Interface representing query parameters for Hotkey API.
 */
export interface HotkeyQueryParams extends BaseQueryParams {
  /**
   * Hotkey in either SS58 or hex format (Optional).
   */
  hotkey?: string;

  /**
   * Unique identifier for a subnet (Optional).
   */
  netuid?: number;
}

export interface ParentHotkey {
  hotkey: string;
  subnetId: number;
  stake: number;
  proportion: string;
}

export interface ChildHotkey {
  hotkey: string;
  stake: number;
  take: string;
  subnetId: number;
}

export interface HotkeyFamily {
  parent: ParentHotkey[];
  child: ChildHotkey;
}

export interface SubnetChildHotkey {
  hotkey: string;
  subnetId: number;
  stake: string;
  proportion: number;
  take: string;
  root_weight: string;
  alpha_weight: string;
}

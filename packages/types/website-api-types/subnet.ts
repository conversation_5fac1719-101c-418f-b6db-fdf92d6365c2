import type { Address, ApiResponse, BaseQueryParams, Pagination } from '.';
import { SevenPriceItem } from './dtao';

export type SubnetDescriptionQueryParams = BaseQueryParams;

export interface SubnetDescription {
  netuid: number;
  bittensor_id: string;
  name: string;
  description: string;
  hw_requirements: string;
  github: string;
  image_url: string;
}

export interface SubnetDescriptionAPI {
  pagination: Pagination;
  data: SubnetDescription[];
}

export type SubnetIdentityQueryParams = BaseQueryParams;

export interface SubnetIdentityAPI extends ApiResponse<SubnetIdentity> {}

export interface SubnetIdentity {
  netuid: number;
  subnet_name: string;
  github_repo: string;
  subnet_contact: string;
  subnet_url: string;
  discord: string;
  description: string;
  additional: string;
}

export interface SubnetIdentityAPI {
  pagination: Pagination;
  data: SubnetIdentity[];
}

export interface DistributionColdkeyQueryParams {
  /**
   * Unique identifier for a subnet.
   */
  netuid: number;
}

export interface DistributionColdkey {
  coldkey: string;
  count: number;
}

export interface DistributionColdkeyAPI {
  pagination: Pagination;
  data: DistributionColdkey[];
}

export type DistributionIncentiveQueryParams = DistributionColdkeyQueryParams;

export interface DistributionIncentive {
  block_number: number;
  incentive: string;
  is_immunity_period: boolean;
}

export interface DistributionIncentiveAPI {
  pagination: Pagination;
  data: DistributionIncentive[];
}

export type DistributionIPQueryParams = DistributionColdkeyQueryParams;

export interface DistributionIP {
  ip: string;
  count: string;
}

export interface DistributionIPAPI {
  pagination: Pagination;
  data: DistributionIP[];
}

/**
 * Enum representing the frequency at which data return.
 */
export enum SubnetHistoryFrequency {
  /**
   * Represents data returning block by block.
   */
  ByBlock = 'by_block',

  /**
   * Represents data returning on a daily basis.
   */
  ByDay = 'by_day',

  /**
   * Represents data returning on a hourly basis.
   */
  ByHour = 'by_hour',
}

/**
 * Enum for SubnetHistoryOrder
 *
 * This enum represents the sorting options available for Subnet history,
 * allowing subnet history to be ordered according to:
 * - the ascending and descending order of the block number,
 * - the ascending and descending order of the timestamp.
 */
export enum SubnetHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface SubnetHistoryQueryParams extends BaseQueryParams {
  /**
   * Unique identifier for a subnet.
   */
  netuid: number;

  /**
   * The frequency at which data return (Optional).
   */
  frequency?: SubnetHistoryFrequency;

  /**
   * The block number where the subnet history is included (Optional).
   */
  block_number?: number;

  /**
   * The starting block number for the subnet history query range (Optional).
   */
  block_start?: number;

  /**
   * The ending block number for the subnet history query range (Optional).
   */
  block_end?: number;

  /**
   * The starting timestamp for the subnet history query range (Optional).
   */
  timestamp_start?: number;

  /**
   * The ending timestamp for the subnet history query range (Optional).
   */
  timestamp_end?: number;

  /**
   * The sort order for the subnet history data (Optional).
   */
  order?: SubnetHistoryOrder;
}

export interface Subnet {
  block_number: number;
  timestamp: string;
  netuid: number;
  owner: Address | null;
  registration_block_number: number;
  registration_timestamp: string;
  registration_cost: string;
  neuron_registration_cost: string;
  max_neurons: number;
  active_keys: number;
  validators: number;
  active_validators: number;
  active_miners: number;
  active_dual: number;
  modality: number | null;
  emission: string;
  rho: number;
  kappa: number;
  immunity_period: number;
  min_allowed_weights: number;
  max_weights_limit: number;
  tempo: number;
  min_difficulty: string;
  max_difficulty: string;
  weights_version: string;
  weights_rate_limit: string;
  adjustment_interval: number;
  activity_cutoff: number;
  registration_allowed: boolean;
  target_regs_per_interval: number;
  min_burn: string;
  max_burn: string;
  bonds_moving_avg: string;
  max_regs_per_block: number;
  serving_rate_limit: string;
  max_validators: number;
  adjustment_alpha: string;
  difficulty: string;
  last_adjustment_block: number;
  blocks_since_last_step: number;
  blocks_until_next_epoch: number;
  blocks_until_next_adjustment: number;
  recycled_lifetime: string;
  recycled_24_hours: string;
  recycled_since_registration: string;
  neuron_registrations_this_interval: number;
  commit_reveal_weights_enabled: boolean;
  pow_registration_allowed: boolean;
  commit_reveal_weights_interval: number;
  alpha_sigmoid_steepness?: number;
  bonds_reset_on?: boolean;
  yuma3_on?: boolean;
  subtoken_enabled?: boolean;
  symbol: string;
}

export interface SubnetAPI {
  pagination: Pagination;
  data: Subnet[];
}

/**
 * Enum for SubnetOrder
 *
 * This enum represents the sorting options available for Subnet,
 * allowing subnet to be ordered according to:
 * - the ascending and descending order of the subnet id,
 */
export enum SubnetOrder {
  NetuidAsc = 'netuid_asc',
  NetuidDesc = 'netuid_desc',
}

export interface SubnetQueryParams extends BaseQueryParams {
  /**
   * Unique identifier for a subnet.
   */
  netuid?: number;

  /**
   * The sort order for the subnet history data (Optional).
   */
  order?: SubnetOrder;
}

/**
 * Enum for NeuronDeregistrationOrder
 *
 * This enum represents the sorting options available for Neuron deregistration,
 * allowing neuron deregistration to be ordered according to:
 * - the ascending and descending order of the block number,
 * - the ascending and descending order of the timestamp.
 */
export enum NeuronDeregistrationOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

export interface NeuronDeregistrationQueryParams extends BaseQueryParams {
  /**
   * Unique identifier for a subnet.
   */
  netuid?: number;

  /**
   * Unique identifier for a neuron.
   */
  uid?: number;

  /**
   * Hotkey in either SS58 or hex format (Optional).
   */
  hotkey?: string;

  /**
   * The block number where the neuron deregistration is included (Optional).
   */
  block_number?: number;

  /**
   * The starting block number for the neuron deregistration query range (Optional).
   */
  block_start?: number;

  /**
   * The ending block number for the neuron deregistration query range (Optional).
   */
  block_end?: number;

  /**
   * The starting timestamp for the neuron deregistration query range (Optional).
   */
  timestamp_start?: number;

  /**
   * The ending timestamp for the neuron deregistration query range (Optional).
   */
  timestamp_end?: number;

  /**
   * The sort order for the neuron deregistration data (Optional).
   */
  order?: NeuronDeregistrationOrder;
}

export interface NeuronDeregistration {
  block_number: number;
  timestamp: string;
  netuid: number;
  uid: number;
  hotkey: Address;
  incentive: string;
  emission: string;
}

export interface NeuronDeregistrationAPI {
  pagination: Pagination;
  data: NeuronDeregistration[];
}

/**
 * Enum for NeuronRegistrationOrder
 *
 * This enum represents the sorting options available for Neuron registration,
 * allowing neuron registration to be ordered according to:
 * - the ascending and descending order of the block number,
 * - the ascending and descending order of the timestamp.
 * - the ascending and descending order of the registration cost.
 */
export enum NeuronRegistrationOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
  RegistrationCostAsc = 'registration_cost_asc',
  RegistrationCostDesc = 'registration_cost_desc',
}

export interface NeuronRegistrationQueryParams extends BaseQueryParams {
  /**
   * Unique identifier for a subnet.
   */
  netuid?: number;

  /**
   * Unique identifier for a neuron.
   */
  uid?: number;

  /**
   * Hotkey in either SS58 or hex format (Optional).
   */
  hotkey?: string;

  /**
   * Coldkey in either SS58 or hex format (Optional).
   */
  coldkey?: string;

  /**
   * The block number where the subnet history is included (Optional).
   */
  block_number?: number;

  /**
   * The starting block number for the subnet history query range (Optional).
   */
  block_start?: number;

  /**
   * The ending block number for the subnet history query range (Optional).
   */
  block_end?: number;

  /**
   * The starting timestamp for the subnet history query range (Optional).
   */
  timestamp_start?: number;

  /**
   * The ending timestamp for the subnet history query range (Optional).
   */
  timestamp_end?: number;

  /**
   * The sort order for the neuron registration data (Optional).
   */
  order?: NeuronRegistrationOrder;
}

export interface NeuronRegistration {
  block_number: number;
  timestamp: string;
  netuid: number;
  uid: number;
  hotkey: Address;
  coldkey: Address;
  registration_cost: string;
}

export interface NeuronRegistrationAPI {
  pagination: Pagination;
  data: NeuronRegistration[];
}

/**
 * Enum for SubnetOwnerOrder
 *
 * This enum represents the sorting options available for Subnet owner,
 * allowing subnet owner to be ordered according to:
 * - the ascending and descending order of the block number,
 * - the ascending and descending order of the timestamp.
 */
export type SubnetOwnerOrder = SubnetHistoryOrder;

export interface SubnetOwnerQueryParams extends BaseQueryParams {
  /**
   * Unique identifier for a subnet.
   */
  netuid?: number;

  /**
   * The address of subnet owner in either SS58 or hex format (Optional).
   */
  owner?: string;

  /**
   * This flag indicates if a coldkey swap is in progress (Optional).
   */
  is_coldkey_swap?: boolean;

  /**
   * The block number where the subnet history is included (Optional).
   */
  block_number?: number;

  /**
   * The starting block number for the subnet history query range (Optional).
   */
  block_start?: number;

  /**
   * The ending block number for the subnet history query range (Optional).
   */
  block_end?: number;

  /**
   * The starting timestamp for the subnet history query range (Optional).
   */
  timestamp_start?: number;

  /**
   * The ending timestamp for the subnet history query range (Optional).
   */
  timestamp_end?: number;

  /**
   * The sort order for the subnet owner data (Optional).
   */
  order?: SubnetOwnerOrder;
}

export interface SubnetOwner {
  block_number: number;
  timestamp: string;
  netuid: number;
  owner: Address;
  is_coldkey_swap: boolean;
}

export interface SubnetOwnerAPI {
  pagination: Pagination;
  data: SubnetOwner[];
}

export type SubnetRegistrationOrder = NeuronRegistrationOrder;

export interface SubnetRegistrationQueryParams extends BaseQueryParams {
  /**
   * Unique identifier for a subnet.
   */
  netuid?: number;

  /**
   * The address of subnet owner in either SS58 or hex format (Optional).
   */
  owner?: string;

  /**
   * The address of the subnet register in either SS58 or hex format (Optional).
   */
  registered_by?: string;

  /**
   * The block number where the subnet history is included (Optional).
   */
  block_number?: number;

  /**
   * The starting block number for the subnet history query range (Optional).
   */
  block_start?: number;

  /**
   * The ending block number for the subnet history query range (Optional).
   */
  block_end?: number;

  /**
   * The starting timestamp for the subnet history query range (Optional).
   */
  timestamp_start?: number;

  /**
   * The ending timestamp for the subnet history query range (Optional).
   */
  timestamp_end?: number;

  /**
   * The sort order for the subnet registration data (Optional).
   */
  order?: SubnetRegistrationOrder;
}

export interface SubnetRegistration {
  block_number: number;
  timestamp: string;
  netuid: number;
  registration_cost: string;
  recycled_at_registration: string;
  owner: Address;
  registered_by: Address;
}

export interface SubnetRegistrationAPI {
  pagination: Pagination;
  data: SubnetRegistration[];
}

export type RegistrationCostHistoryOrder = SubnetHistoryOrder;

export interface RegistrationCostHistoryQueryParams extends BaseQueryParams {
  /**
   * The block number where the subnet history is included (Optional).
   */
  block_number?: number;

  /**
   * The starting block number for the subnet history query range (Optional).
   */
  block_start?: number;

  /**
   * The ending block number for the subnet history query range (Optional).
   */
  block_end?: number;

  /**
   * The starting timestamp for the subnet history query range (Optional).
   */
  timestamp_start?: number;

  /**
   * The ending timestamp for the subnet history query range (Optional).
   */
  timestamp_end?: number;

  /**
   * The sort order for the subnet registration cost history data (Optional).
   */
  order?: RegistrationCostHistoryOrder;
}

export interface RegistrationCost {
  block_number: number;
  timestamp: string;
  registration_cost: string;
  is_purchase: boolean;
}

export interface RegistrationCostAPI {
  pagination: Pagination;
  data: RegistrationCost[];
}

export interface DailyRegistrationRecycleParamsAtom extends BaseQueryParams {
  order?: SubnetHistoryOrder;
}

export type SubnetMenuSwitchOptions = 'List' | 'Grid';

export interface SubnetMetadataProps {
  name: string;
}

export interface EnrichedSubnet extends Subnet {
  metadata: SubnetMetadataProps | undefined | null;
}

export interface EnrichedSubnetAPI {
  pagination: Pagination;
  data: EnrichedSubnet[];
}

export interface SubnetPool {
  active_keys: number;
  alpha_in_pool: string;
  alpha_staked: string;
  block_number: number;
  buyers_24_hr: number;
  buys_24_hr: number;
  description: string;
  emission: string;
  github: string;
  liquidity: string;
  market_cap: string;
  market_cap_change_1_day: string;
  tao_volume_24_hr_change_1_day: string;
  max_neurons: number;
  name: string;
  netuid: number;
  neuron_registration_cost: string;
  owner: string;
  price_change_1_day: string;
  price_change_1_hour: string;
  price_change_1_week: string;
  price_change_1_month: string;
  price: string;
  rank: number;
  recycled_24_hours: string;
  recycled_lifetime: string;
  recycled_since_registration: string;
  registration_timestamp: string;
  sellers_24_hr: number;
  sells_24_hr: number;
  seven_day_prices: SevenPriceItem[];
  subnet_description: string;
  subnet_name: string;
  symbol: string;
  tao_buy_volume_24_hr: string;
  tao_sell_volume_24_hr: string;
  tao_volume_24_hr: string;
  timestamp: string;
  total_alpha: string;
  total_tao: string;
  discord_url: string;
  subnet_contact: string;
  subnet_url: string;
  startup_mode: boolean;
  root_prop: string;
  fear_and_greed_index: string;
  fear_and_greed_sentiment: string;
}

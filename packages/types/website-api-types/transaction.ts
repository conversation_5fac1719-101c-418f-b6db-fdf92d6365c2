import type { BaseQueryParams, Pagination } from '.';

/**
 * Enum for TransactionOrder
 *
 * This enum represents the sorting options available for Transaction,
 * allowing Transaction to be ordered according to:
 * - the ascending and descending order of the block number,
 * - the ascending and descending order of the timestamp.
 */
export enum TransactionOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

/**
 * Interface representing the query parameters for Transaction API.
 */
export interface TransactionQueryParams extends BaseQueryParams {
  /**
   * The block number where the transaction is included (Optional).
   */
  block_number?: number;

  /**
   * The starting block number for the transaction query range (Optional).
   */
  block_start?: number;

  /**
   * The ending block number for the transaction query range (Optional).
   */
  block_end?: number;

  /**
   * The starting timestamp for the transaction query range (Optional).
   */
  timestamp_start?: number;

  /**
   * The ending timestamp for the transaction query range (Optional).
   */
  timestamp_end?: number;

  /**
   * The transaction hash (Optional).
   */
  hash?: string;

  /**
   * The receiver's address in the transaction (Optional).
   */
  to?: string;

  /**
   * The sender's address in the transaction (Optional).
   */
  from?: string;

  /**
   * The method name of the transaction (Optional).
   */
  method_name?: string;

  /**
   * The method ID of the transaction (Optional).
   */
  method_id?: string;

  /**
   * The contract created in the transaction (Optional).
   */
  contract_created?: string;

  /**
   * The index of the transaction (Optional).
   */
  index?: number;

  /**
   * The order of the transaction details (Optional).
   */
  order?: TransactionOrder;
}

/**
 * Interface representing the Transaction structure returned by the API.
 */
export interface EvmTransaction {
  args: any;
  block_hash: string;
  block_number: number;
  contract_created: string;
  cumulative_gas_used: string;
  effective_gas_price: string;
  from: string;
  gas: string;
  gas_price: string;
  gas_used: string;
  hash: string;
  index: number;
  input: string;
  max_fee_per_gas: string;
  max_priority_fee_per_gas: string;
  method_id: string;
  method_name: string;
  nonce: number;
  success: boolean;
  timestamp: string;
  to: string;
  value: string;
}

/**
 * Interface representing the Transaction API response.
 */
export interface TransactionAPI {
  pagination: Pagination;
  data: EvmTransaction[];
}

/**
 * It represents the specific parameters required for querying Transaction.
 */
export interface TransactionParamsAtom extends BaseQueryParams {
  /**
   * The sort order for the transaction data (Optional).
   */
  order?: TransactionOrder;
}

export interface WalletTransactionParamsAtom extends BaseQueryParams {
  address?: string;
  /**
   * The sort order for the transaction data (Optional).
   */
  order?: TransactionOrder;
}

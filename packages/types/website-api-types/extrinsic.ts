import type { BaseQueryParams, NetworkType, Pagination } from '.';

/**
 * Enum for ExtrinsicOrder
 *
 * This enum represents the sorting options available for Extrinsics,
 * allowing extrinsics to be ordered according to:
 * - the ascending and descending order of the block number.
 * - the ascending and descending order of the timestamp.
 * - the ascending and descending order of the extrinsic id.
 * - the ascending and descending order of the success.
 * - the ascending and descending order of the signer address.
 */
export enum ExtrinsicOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
  IdAsc = 'id_asc',
  IdDesc = 'id_desc',
  SuccessAsc = 'success_asc',
  SuccessDesc = 'success_desc',
  SignerAddressAsc = 'signer_address_asc',
  SignerAddressDesc = 'signer_address_desc',
}

/**
 * Interface representing query parameters for extrinsic API.
 */
export interface ExtrinsicQueryParams extends BaseQueryParams {
  /**
   * Optional. Defines the network filter. The default network is 'finney'.
   */
  network?: NetworkType;

  /**
   * Optional. Specific block number where the queried block is included.
   */
  block_number?: number;

  /**
   * Optional. Starting block number for a range of block queries.
   */
  block_start?: number;

  /**
   * Optional: Ending block number for a range of block queries.
   */
  block_end?: number;

  /**
   * Optional. Starting timestamp (UNIX epoch milliseconds) for a range of block queries.
   */
  timestamp_start?: number;

  /**
   * Optional. Ending timestamp (UNIX epoch milliseconds) for a range of block queries.
   */
  timestamp_end?: number;

  /**
   * Optional. Unique identifier (hash) of the extrinsic.
   */
  hash?: string;

  /**
   * Optional. Full name associated with the extrinsic.
   */
  full_name?: string;

  /**
   * Optional. Unique identifier (id) of the extrinsic.
   */
  id?: string;

  /**
   * Optional. Address associated with the signer of the extrinsic.
   */
  signer_address?: string;

  /**
   * Optional. Defines the sorting order of the extrinsic data.
   */
  order?: ExtrinsicOrder;
}

export interface ExtrinsicParamsAtom extends BaseQueryParams {
  /**
   * Network filter (Optional) Default is finney.
   */
  network?: NetworkType;

  /**
   * The block number where the block is included (Optional).
   */
  blockNumber?: number;

  /**
   * The address associated with the signer of the extrinsic (Optional).
   */
  signerAddress?: string;

  /**
   * The unique identifier (hash) of the extrinsic (Optional).
   */
  hash?: string;

  /**
   * The sort order for the extrinsic data (Optional).
   */
  order?: ExtrinsicOrder;

  /**
   * Interval (in milliseconds) at which metagraph data should be fetched again (Optional).
   */
  refetchInterval?: number;
}

/**
 * Interface representing query parameters for the sudo API applying specifically to Atom.
 */
export interface SudoQueryParamsAtom extends BaseQueryParams {
  /**
   * Optional. Defines the sorting order of the returned sudo data.
   */
  order?: ExtrinsicOrder;
}

/**
 * Interface representing the Extrinsic structure returned by the API.
 */
export interface Extrinsic {
  block_number: number;
  call_args: any;
  call_id: string;
  error: any;
  fee: string;
  full_name: string;
  hash: string;
  id: string;
  index: number;
  signature: any;
  signer_address: string;
  success: boolean;
  timestamp: string;
  tip: string;
  version: number;
}

/**
 * Interface representing the Extrinsic API response.
 */
export interface ExtrinsicAPI {
  pagination: Pagination;
  data: Extrinsic[];
}

export interface WeightEventParams {
  cold_key: string;
}

export interface WeightEvent {
  timestamp: string;
  sns: any;
  weights: any;
  normalized_weights: any;
  subnet_weights: any;
}

import type { ColumnDefTemplate, HeaderContext } from "@tanstack/react-table";
import type { ReactNode } from "react";
import type { TableData } from ".";

export type ColumnSchema = {
  id: string;
  header?: ColumnDefTemplate<HeaderContext<TableData, ReactNode | Date>>;
  footer?:
    | ColumnDefTemplate<HeaderContext<TableData, ReactNode | Date>>;
  cell?: (info: any) => React.ReactNode;
  enableSorting?: boolean;
};

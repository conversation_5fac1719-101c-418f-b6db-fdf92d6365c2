import type { Address, Axon, BaseQueryParams, Pagination } from '.';

/**
 * Enum for RootMetagraphOrder
 *
 * This enum represents the sort options available for root metagraphs,
 * allowing root metagraphs to be sorted according to:
 * - the ascending and descending order of the unique identifier (uid),
 * - the ascending and descending order of the stake.
 */
export enum RootMetagraphOrder {
  UidAsc = 'uid_asc',
  UidDesc = 'uid_desc',
  StakeAsc = 'stake_asc',
  StakeDesc = 'stake_desc',
}

/**
 * Interface representing query parameters for root metagraph API.
 */
export interface RootMetagraphQueryParams extends BaseQueryParams {
  /**
   * The sort order for the root metagraph data (Optional).
   */
  order?: RootMetagraphOrder;
}

/**
 * Enum for RootMetagraphHistoryOrder
 *
 * This enum represents the sort options available for root metagraph history,
 * allowing the data to be sorted by:
 * - the ascending and descending order of the block number,
 * - the ascending and descending order of the timestamp.
 */
export enum RootMetagraphHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

/**
 * Interface representing query parameters for the metagraph API.
 */
export interface RootMetagraphHistoryParams extends BaseQueryParams {
  /**
   * Hotkey in either SS58 or hex format (Optional).
   */
  hotkey?: string;

  /**
   * Starting block number for filtering the history (Optional).
   */
  block_start?: number;

  /**
   * Ending block number for filtering the history (Optional).
   */
  block_end?: number;

  /**
   * Starting timestamp for filtering the history (Optional).
   */
  timestamp_start?: number;

  /**
   * Ending timestamp for filtering the history (Optional).
   */
  timestamp_end?: number;

  /**
   * The sort order for the metagraph data (Optional).
   * Must be one of the values specified in the RootMetagraphHistoryOrder enum.
   */
  order?: RootMetagraphHistoryOrder;
}

/**
 * Interface representing the Root Metagraph structure returned by the API.
 */
export interface RootMetagraph {
  hotkey: Address;
  coldkey: Address;
  uid: number;
  block_number: number;
  timestamp: string;
  stake: string;
  consensus: string;
  senator: boolean;
  rank: string;
  pruning_score: string;
  subnet_weights: {
    [x: string]: string;
  };
}

/**
 * Interface representing the Root Metagraph API response.
 */
export interface RootMetagraphAPI {
  pagination: Pagination;
  data: RootMetagraph[];
}

/**
 * Interface representing the Root Metagraph structure returned by the API.
 */
export interface RootMetagraphHistory {
  hotkey: Address;
  coldkey: Address;
  uid: number;
  block_number: number;
  timestamp: string;
  stake: string;
  consensus: string;
  senator: boolean;
  rank: string;
  pruning_score: string;
  subnet_weights: {
    [x: string]: string;
  };
}

/**
 * Interface representing the Root Metagraph History API response.
 */
export interface RootMetagraphHistoryAPI {
  pagination: Pagination;
  data: RootMetagraphHistory[];
}

/**
 * Enumeration for specifying the sorting order in metagraph queries.
 * It includes options for sorting by properties such as update time, neuron ID, stake amount,
 * trust, validator trust, consensus, incentive, dividends, emission, activity status,
 * hotkey and coldkey, validator permit status, axon, daily reward, registration time,
 * and immune period in both ascending and descending orders.
 */
export enum MetagraphOrder {
  UpdatedAsc = 'updated_asc',
  UpdatedDesc = 'updated_desc',
  UidAsc = 'uid_asc',
  UidDesc = 'uid_desc',
  StakeAsc = 'stake_asc',
  StakeDesc = 'stake_desc',
  TrustAsc = 'trust_asc',
  TrustDesc = 'trust_desc',
  ValidatorTrustAsc = 'validator_trust_asc',
  ValidatorTrustDesc = 'validator_trust_desc',
  ConsensusAsc = 'consensus_asc',
  ConsensusDesc = 'consensus_desc',
  IncentiveAsc = 'incentive_asc',
  IncentiveDesc = 'incentive_desc',
  DividendsAsc = 'dividends_asc',
  DividendsDesc = 'dividends_desc',
  EmissionAsc = 'emission_asc',
  EmissionDesc = 'emission_desc',
  ActiveAsc = 'active_asc',
  ActiveDesc = 'active_desc',
  HotkeyAsc = 'hotkey_asc',
  HotkeyDesc = 'hotkey_desc',
  ColdkeyAsc = 'coldkey_asc',
  ColdkeyDesc = 'coldkey_desc',
  ValidatorPermitAsc = 'validator_permit_asc',
  ValidatorPermitDesc = 'validator_permit_desc',
  AxonAsc = 'axon_asc',
  AxonDesc = 'axon_desc',
  DailyRewardAsc = 'daily_reward_asc',
  DailyRewardDesc = 'daily_reward_desc',
  RegisteredAtAsc = 'registered_at_asc',
  RegisteredAtDesc = 'registered_at_desc',
  IsImmunityPeriodAsc = 'is_immunity_period_asc',
  IsImmunityPeriodDesc = 'is_immunity_period_desc',
}

/**
 * Interface representing query parameters for metagraph API.
 */
export interface MetagraphQueryParams extends BaseQueryParams {
  /**
   * Unique identifier for a subnet.
   */
  netuid?: number;

  /**
   * Search parameter across UID, hotkey, coldkey, axon_ip (Optional).
   */
  search?: string;

  /**
   * Unique identifier for a neuron (Optional).
   */
  uid?: number;

  /**
   * Boolean representing if the neuron is active (Optional).
   */
  active?: boolean;

  /**
   * Hotkey in either SS58 or hex format (Optional).
   */
  hotkey?: string;

  /**
   * Coldkey in either SS58 or hex format (Optional).
   */
  coldkey?: string;

  /**
   * Boolean representing neuron validator permit status (Optional).
   */
  validator_permit?: boolean;

  /**
   * Boolean representing if neuron is in immunity period (Optional).
   */
  is_immunity_period?: boolean;

  /**
   * Boolean representing if the query relates to a child key (Optional).
   */
  is_child_key?: boolean;

  /**
   * The sort order for the metagraph data (Optional).
   */
  order?: MetagraphOrder;
}

/**
 * It represents the specific parameters required for querying Metagraph.
 */
export interface MetagraphParamsAtom extends BaseQueryParams {
  /**
   * Unique identifier for a subnet.
   */
  subnetId?: number;

  /**
   * Hotkey in either SS58 or hex format (Optional).
   */
  hotkey?: string;

  /**
   * Coldkey in either SS58 or hex format (Optional).
   */
  coldkey?: string;

  /**
   * The sort order for the metagraph data (Optional).
   */
  order?: MetagraphOrder;

  /**
   * Search parameter across UID, hotkey, coldkey, axon_ip (Optional).
   */
  search?: string;

  /**
   * Interval (in milliseconds) at which metagraph data should be fetched again (Optional).
   */
  refetchInterval?: number;
}

/**
 * Interface representing the Metagraph structure returned by the API.
 */
export interface Metagraph {
  hotkey: Address;
  coldkey: Address;
  netuid: number;
  uid: number;
  block_number: number;
  timestamp: string;
  stake: string;
  trust: string;
  validator_trust: string;
  consensus: string;
  incentive: string;
  dividends: string;
  emission: string;
  active: boolean;
  validator_permit: boolean;
  updated: number;
  daily_reward: string;
  registered_at_block: number;
  is_immunity_period: boolean;
  rank: number;
  is_child_key: boolean;
  axon: Axon | null;
  root_weight: string;
  alpha_stake: string;
  root_stake: string;
  root_stake_as_alpha: string;
  total_alpha_stake: string;
}

/**
 * Interface representing the Metagraph API response.
 */
export interface MetagraphAPI {
  pagination: Pagination;
  data: Metagraph[];
}

export interface ChildPerformanceProps extends Metagraph {
  parentStake?: string;
  percentage?: string;
  take?: string;
}

/**
 * Enumeration for specifying the sorting order in Metagraph history queries.
 */
export enum MetagraphHistoryOrder {
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

/**
 * Interface representing query parameters for metagraph history API.
 */
export interface MetagraphHistoryQueryParams extends BaseQueryParams {
  /**
   * Unique identifier for a subnet.
   */
  netuid?: number;

  /**
   * Unique identifier for a neuron (Optional).
   */
  uid?: number;

  /**
   * Hotkey in either SS58 or hex format (Optional).
   */
  hotkey?: string;

  /**
   * Coldkey in either SS58 or hex format (Optional).
   */
  coldkey?: string;

  /**
   * The starting point of the block range for the query (Optional).
   */
  block_start?: number;

  /**
   * The endpoint of the block range for the query (Optional).
   */
  block_end?: number;

  /**
   * The starting timestamp for the query range (Optional).
   */
  timestamp_start?: number;

  /**
   * The ending timestamp for the query range (Optional).
   */
  timestamp_end?: number;

  /**
   * The sort order for the metagraph data (Optional).
   */
  order?: MetagraphHistoryOrder;
}

/**
 * Interface representing query parameters for the Metagraph Child Hotkey API.
 */
export interface MetagraphChildHotkeyParams {
  /**
   * The address of the hotkey, represented in either SS58 or hex format. This is an optional field.
   */
  hotkey?: string;

  /**
   * The network identifier. This is an optional field.
   */
  netuid?: number;
}

export interface MetagraphChildHotkey {
  parent: {
    hotkey: string;
    subnetId: number;
    stake: string;
    proportion: number;
    take: string;
    root_weight: string;
    alpha_weight: string;
  }[];
  child: {
    hotkey: string;
    subnetId: number;
    stake: string;
    proportion: number;
    take: string;
    root_weight: string;
    alpha_weight: string;
  };
}

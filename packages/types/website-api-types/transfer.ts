import type { ReactNode } from 'react';
import type { TableData } from './tabledata';
import type { Address, BaseQueryParams, NetworkType, Pagination } from '.';

export type Transaction = TableData & {
  flag: ReactNode;
  extrinsic: number;
  from: string;
  to: string;
  amount: number;
  time: Date;
};

/**
 * Enum for TransferOrder
 *
 * This enum represents the sorting options available for Transfers,
 * allowing transfers to be ordered according to:
 * - the ascending and descending order of the amount,
 * - the ascending and descending order of the block number,
 * - the ascending and descending order of the timestamp.
 */
export enum TransferOrder {
  AmountAsc = 'amount_asc',
  AmountDesc = 'amount_desc',
  BlockNumberAsc = 'block_number_asc',
  BlockNumberDesc = 'block_number_desc',
  TimestampAsc = 'timestamp_asc',
  TimestampDesc = 'timestamp_desc',
}

/**
 * Interface representing query parameters for Transfer API.
 */
export interface TransferQueryParams extends BaseQueryParams {
  /**
   * The network type for the transfer query (Optional).
   */
  network?: NetworkType;

  /**
   * The address involved in the transfer (Optional).
   */
  address?: string;

  /**
   * The sender's address in the transfer (Optional).
   */
  from?: string;

  /**
   * The receiver's address in the transfer (Optional).
   */
  to?: string;

  /**
   * The transaction hash of the transfer (Optional).
   */
  transaction_hash?: string;

  /**
   * The extrinsic ID of the transfer (Optional).
   */
  extrinsic_id?: string;

  /**
   * The minimum transfer amount in the query (Optional).
   */
  amount_min?: string;

  /**
   * The maximum transfer amount in the query (Optional).
   */
  amount_max?: string;

  /**
   * The block number where the transfer is included (Optional).
   */
  block_number?: number;

  /**
   * The starting block number for the transfer query range (Optional).
   */
  block_start?: number;

  /**
   * The ending block number for the transfer query range (Optional).
   */
  block_end?: number;

  /**
   * The starting timestamp for the transfer query range (Optional).
   */
  timestamp_start?: number;

  /**
   * The ending timestamp for the transfer query range (Optional).
   */
  timestamp_end?: number;

  /**
   * The sort order for the transfer data (Optional).
   */
  order?: TransferOrder;
}

/**
 * Interface representing the Transfer structure returned by the API.
 */
export interface Transfer {
  id: string;
  to: Address;
  from: Address;
  network: string;
  block_number: number;
  timestamp: string;
  amount: string;
  fee: string;
  transaction_hash: string;
  extrinsic_id: string;
}

/**
 * Interface representing the Hotkey API response.
 */
export interface TransferAPI {
  pagination: Pagination;
  data: Transfer[];
}

/**
 * It represents the specific parameters required for querying Transfer.
 */
export interface TransferParamsAtom extends BaseQueryParams {
  /**
   * The network type for the transfer query (Optional).
   */
  network?: NetworkType;

  /**
   * The address involved in the transfer (Optional).
   */
  address?: string;

  /**
   * The sender's address in the transfer (Optional).
   */
  addressFrom?: string;

  /**
   * The receiver's address in the transfer (Optional).
   */
  addressTo?: string;

  /**
   * The minimum transfer amount in the query (Optional).
   */
  amountMin?: string;

  /**
   * The starting timestamp for the transfer query range (Optional).
   */
  timestampStart?: number;

  /**
   * The ending timestamp for the transfer query range (Optional).
   */
  timestampEnd?: number;

  /**
   * The sort order for the transfer data (Optional).
   */
  order?: TransferOrder;
}

export enum TransferStatus {
  ALL = 'ALL',
  IN = 'IN',
  OUT = 'OUT',
}

export interface ExchangeProps {
  exchangeId: string;
  balanceTotal: string;
  inflows: string;
  outflows: string;
  inCount: number;
  outCount: number;
}

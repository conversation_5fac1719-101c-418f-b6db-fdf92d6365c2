import { Address, BaseQueryParams } from '.';
import { Pagination } from '../website-api-types-generated';

export interface NeuronHistoryQueryParams extends BaseQueryParams {
  netuid?: number;
  uid?: number;
  hotkey?: string;
  coldkey?: string;
  is_immune?: boolean;
  in_danger?: boolean;
  has_dividends?: boolean;
  has_incentive?: boolean;
  block_start?: number;
  block_end?: number;
  timestamp_start?: number;
  timestamp_end?: number;
  order?: string;
}

export interface NeuronItem {
  active: boolean;
  axon: string;
  block_number: number;
  coldkey: Address;
  consensus: string;
  dividends: string;
  emission: string;
  hotkey: Address;
  in_danger: boolean;
  incentive: string;
  is_immune: boolean;
  miner_rank: number;
  netuid: number;
  pruning_score: string;
  registration_block: number;
  stake_weight: string;
  timestamp: string; // ISO 8601 date string
  trust: string;
  uid: number;
  validator_permit: boolean;
  validator_rank: number;
  validator_trust: string;
}

export interface NeuronHistoryAPI {
  data: NeuronItem[];
  pagination: Pagination;
}

export interface NeuronAggregatedHistoryQueryParams extends BaseQueryParams {
  netuid?: number;
  block_start?: number;
  block_end?: number;
  timestamp_start?: number;
  timestamp_end?: number;
  order?: string;
}

export interface NeuronAggregatedHistoryItem {
  block_number: number;
  immune_count: number;
  last_dereg_incentive: string;
  last_dereg_pruning_score: string;
  max_danger_incentive: string;
  max_danger_pruning_score: string;
  max_immune_incentive: string;
  max_immune_pruning_score: string;
  max_incentive: string;
  max_mining_pruning_score: string;
  max_pruning_score: string;
  min_non_immune_incentive: string;
  min_non_immune_pruning_score: string;
  netuid: number;
  timestamp: string;
}

export interface NeuronAggregatedHistoryAPI {
  data: NeuronAggregatedHistoryItem[];
  pagination: Pagination;
}

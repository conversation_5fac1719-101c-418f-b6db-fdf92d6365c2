import type { BaseQueryParams, NetworkType, Pagination } from ".";

/**
 * Enum for EventOrder
 *
 * This enum represents the sorting options available for Events,
 * allowing events to be ordered according to:
 * - the ascending and descending order of the block number,
 * - the ascending and descending order of the timestamp,
 * - the ascending and descending order of the phase.
 * - the ascending and descending order of the pallet.
 * - the ascending and descending order of the name.
 * - the ascending and descending order of the event id.
 * - the ascending and descending order of the extrinsic id.
 */
export enum EventOrder {
	BlockNumberAsc = "block_number_asc",
	BlockNumberDesc = "block_number_desc",
	TimestampAsc = "timestamp_asc",
	TimestampDesc = "timestamp_desc",
	PhaseAsc = "phase_asc",
	PhaseDesc = "phase_desc",
	PalletAsc = "pallet_asc",
	PalletDesc = "pallet_desc",
	NameAsc = "name_asc",
	NameDesc = "name_desc",
	IdAsc = "id_asc",
	IdDesc = "id_desc",
	ExtrinsicIdAsc = "extrinsic_id_asc",
	ExtrinsicIdDesc = "extrinsic_id_desc",
}

/**
 * Interface representing query parameters for Event API.
 */
export interface EventQueryParams extends BaseQueryParams {
	/**
	 * Network Filter (Optional) Default: finney.
	 */
	network?: NetworkType;

	/**
	 * Unique identifier for the Event (Optional)
	 */
	id?: string;

	/**
	 * The block number where the event is included (Optional).
	 */
	block_number?: number;

	/**
	 * The starting block number for the event query range (Optional).
	 */
	block_start?: number;

	/**
	 * The ending block number for the event query range (Optional).
	 */
	block_end?: number;

	/**
	 * The starting timestamp for the event query range (Optional).
	 */
	timestamp_start?: number;

	/**
	 * The ending timestamp for the event query range (Optional).
	 */
	timestamp_end?: number;

	/**
	 * The pallet from which the event originates (Optional).
	 */
	pallet?: string;

	/**
	 * The name of the event as per the originating pallet (Optional).
	 */
	name?: string;

	/**
	 * Full name of the event, e.g, "SubtensorModule.AxonServed"
	 */
	full_name?: string;

	/**
	 * Unique identifier for the extrinsic where the event occurred (Optional).
	 */
	extrinsic_id?: string;

	/**
	 * A unique identifier associated with the function call that triggered the event (Optional).
	 */
	call_id?: string;

	/**
	 * The sort order for the event data (Optional).
	 */
	order?: EventOrder;
}

/**
 * Interface representing the Event structure returned by the API.
 */
export interface Event {
	id: string;
	extrinsic_index: number;
	index: number;
	phase: string;
	pallet: string;
	name: string;
	full_name: string;
	args: any;
	block_number: number;
	extrinsic_id: string;
	call_id: string | null;
	timestamp: string;
}

/**
 * Interface representing the Event API.
 */
export interface EventAPI {
	pagination: Pagination;
	data: Event[];
}

/**
 * Interface representing the specific parameters required for querying Events.
 */
export interface EventParamsAtom extends BaseQueryParams {
	/**
	 * Network Filter (Optional) Default: finney.
	 */
	network?: NetworkType;

	/**
	 * The block number where the event is included (Optional).
	 */
	blockNumber?: number;

	/**
	 * The name of the event as per the originating pallet (Optional).
	 */
	name?: string;

	/**
	 * Unique identifier for the extrinsic where the event occurred (Optional).
	 */
	extrinsicId?: string;

	/**
	 * The sort order for the event data (Optional).
	 */
	order?: EventOrder;
}

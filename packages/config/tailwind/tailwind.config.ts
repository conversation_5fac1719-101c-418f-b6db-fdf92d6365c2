import type { Config } from 'tailwindcss';
import { fontFamily } from 'tailwindcss/defaultTheme';

// We want each package to be responsible for its own content.
const config: Omit<Config, 'content'> = {
  darkMode: ['class'],
  theme: {
    extend: {
      lineHeight: {
        '78': '78px',
        '60': '60px',
        '48': '48px',
        '44': '44px',
        '29': '29px',
        '26': '26px',
        '24': '24px',
        '21': '21px',
        '20': '20px',
        '18': '18px',
        '16': '16px',
        '13': '13px',
      },
      backgroundImage: {
        'glow-conic-taostats':
          'conic-gradient(from 220deg at 50% 50%, #ff8f2d 31deg, #00ffb29e 259deg, #daef15c9 321deg)',
      },
      borderColor: {
        DEFAULT: '#1E293B',
      },
      backgroundColor: {
        card: '#272727',
        'hero-primary': '#1D1D1D',
        'input-primary': '#2E2E2E',
      },
      colors: {
        'app-bg': '#141414',
        fire: '#F77C36',
        'navitem-selected': '#242424',
        'gray-highlight': '#242424',
        'accent-1': '#00DBBC',
        'accent-2': '#EB5347',
        cta1: '#00DBBC',
        primary: '#171717',
        'primary-hover': '#1D1D1D',
        'primary-foreground': '#FFFFFF',
        'primary-button-border': '#323232',
        'input-border-primary': '#262626',
        'label-primary': '#FFFFFF',
        'label-secondary': '#909090',
        ocean: '#00DBBC',
        'positive-trend': '#00DBBC',
        'negative-trend': '#EB5347',
        grayish: '#909090',
        accent: {
          DEFAULT: '#1D1D1D',
          foreground: '#FFFFFF',
        },
        popover: {
          DEFAULT: '#141414',
          foreground: '#F8FAFC',
        },
        border: {
          DEFAULT: '#1E293B',
        },
        muted: {
          DEFAULT: '#27272a',
        },
        input: '#fffff33',
        ring: '#1E293B',
        foreground: '#F8FAFC',
      },
      textColor: {
        'slippage-warning': '#EBA447',
      },
      fontFamily: {
        everett: ['var(--font-mai)', ...fontFamily.sans],
        fira: ['var(--font-fira)', ...fontFamily.mono],
      },
    },
  },
  plugins: [
    require('tailwindcss-animate'),
    require('tailwind-scrollbar')({
      preferredStrategy: 'pseudoelements',
      nocompatible: true,
    }),
  ],
};
export default config;

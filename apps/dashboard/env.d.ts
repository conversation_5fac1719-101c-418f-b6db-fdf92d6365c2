import '@tanstack/react-table';
import type { RowData } from '@tanstack/react-table';

declare global {
  namespace NodeJS {
    interface ProcessEnv {
      WEBSITE_API_URL?: string;
      WEBSITE_API_TOKEN?: string;
      NEXT_PUBLIC_WEBSITE_BASE_URL?: string;
      DASHBOARD_API_URL?: string;
      NEXT_PUBLIC_DASHBOARD_BASE_URL?: string;
      NEXT_PUBLIC_STRIPE_PUBLIC_KEY?: string;
      STRIPE_SECRET_KEY?: string;
      APP_ENV: 'development' | 'test' | 'beta' | 'production';
      NEXT_PUBLIC_BITTENSOR_SOCKET_URL?: string;
    }
  }
}

declare module '@tanstack/react-table' {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, no-unused-vars -- Allow.
  interface ColumnMeta<TData extends RowData, TValue> {
    align?: 'start' | 'end' | 'center';
    separator?: 'dashed';
  }
}

// Need to export something for TypeScript to recognize this as a module
export {};

FROM node:18-alpine AS base

ARG APP_ENV
ENV APP_ENV=$APP_ENV

ARG NEXT_PUBLIC_BITTENSOR_SOCKET_URL
ENV NEXT_PUBLIC_BITTENSOR_SOCKET_URL=$NEXT_PUBLIC_BITTENSOR_SOCKET_URL

ARG NEXT_PUBLIC_DASHBOARD_BASE_URL
ENV NEXT_PUBLIC_DASHBOARD_BASE_URL=$NEXT_PUBLIC_DASHBOARD_BASE_URL

ARG NEXT_PUBLIC_WEBSITE_BASE_URL
ENV NEXT_PUBLIC_WEBSITE_BASE_URL=$NEXT_PUBLIC_WEBSITE_BASE_URL

FROM base AS builder
RUN apk update
RUN apk add --no-cache libc6-compat

# Set working directory
WORKDIR /app

# Install pnpm and turbo
RUN npm install -g pnpm turbo
COPY . .

# Generate a partial monorepo with a pruned lockfile for a target workspace.
# "dashboard" is the name entered in the project's package.json: { name: "dashboard" }
RUN turbo prune dashboard --docker

# Add lockfile and package.json's of isolated subworkspace
FROM base AS installer
RUN apk update
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install pnpm
RUN npm install -g pnpm

# First install the dependencies (as they change less often)
COPY --from=builder /app/out/json/ .
RUN pnpm install

# Build the project
COPY --from=builder /app/out/full/ .
RUN pnpm turbo run build

FROM base AS runner
WORKDIR /app

# Don't run production as root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
USER nextjs

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=installer --chown=nextjs:nodejs /app/apps/dashboard/.next/standalone ./
COPY --from=installer --chown=nextjs:nodejs /app/apps/dashboard/.next/static ./apps/dashboard/.next/static
COPY --from=installer --chown=nextjs:nodejs /app/apps/dashboard/public ./apps/dashboard/public

CMD node apps/dashboard/server.js
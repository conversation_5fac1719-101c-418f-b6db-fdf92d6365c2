{"name": "dashboard", "version": "2.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "start:prod": "node_modules/next/dist/bin/next start", "lint": "next lint", "type-check": "tsc --noEmit", "copy-subnet-images": "cp -R ../../packages/ui/src/assets/images/subnets ./public/images/subnets"}, "dependencies": {"@headlessui/react": "^2.2.0", "@hookform/resolvers": "^3.9.1", "@number-flow/react": "^0.4.1", "@polkadot/api": "^15.4.1", "@polkadot/extension-dapp": "^0.58.2", "@polkadot/types": "^15.4.1", "@polkadot/ui-keyring": "^3.12.1", "@radix-ui/react-icons": "^1.3.0", "@repo/ui": "workspace:*", "@stripe/stripe-js": "^5.5.0", "@tanstack/react-query": "^5.62.0", "@tanstack/react-query-devtools": "^5.62.0", "@tanstack/react-table": "^8.20.6", "@types/js-cookie": "^3.0.6", "@visx/axis": "^3.12.0", "@visx/curve": "^3.12.0", "@visx/gradient": "^3.12.0", "@visx/group": "^3.12.0", "@visx/mock-data": "^3.12.0", "@visx/responsive": "^3.12.0", "@visx/scale": "^3.12.0", "@visx/shape": "^3.12.0", "@visx/tooltip": "^3.12.0", "@visx/visx": "^3.12.0", "@visx/xychart": "^3.12.0", "axios": "^1.7.8", "bignumber.js": "^9.1.2", "d3-time-format": "^4.1.0", "date-fns": "^4.1.0", "generate-avatar": "^1.4.10", "hono": "^4.6.15", "jotai": "^2.10.3", "jotai-tanstack-query": "^0.9.0", "js-cookie": "^3.0.5", "lucide-react": "^0.479.0", "moment": "^2.30.1", "next": "^14.2.3", "next-auth": "5.0.0-beta.25", "numerable": "^0.3.15", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.54.0", "react-hot-toast": "^2.4.1", "react-icons": "^5.3.0", "stripe": "^17.5.0", "usehooks-ts": "^3.1.1"}, "devDependencies": {"@next/eslint-plugin-next": "^14.2.3", "@repo/config": "workspace:*", "@repo/eslint-config": "workspace:*", "@repo/types": "workspace:*", "@types/node": "^20.10.6", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "postcss": "^8.4.33", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.6", "typescript": "^5.3.3", "zod": "^3.23.8"}}
import { NextResponse } from 'next/server';
import { auth } from '@/auth-config';
// export { auth } from '@/auth-config';

// Or like this if you need to do something here.
export default auth((req, _) => {
  if (!req.auth && !req.nextUrl.pathname.startsWith('/login')) {
    const newUrl = new URL('/login', req.nextUrl.origin);
    newUrl.searchParams.set(
      'redirectTo',
      req.nextUrl.pathname + req.nextUrl.search
    );
    return NextResponse.redirect(newUrl);
  }
});

// Read more: https://nextjs.org/docs/app/building-your-application/routing/middleware#matcher
export const config = {
  matcher: [
    '/((?!api/|_next/static|_next/image|favicon.ico|thumbnail.png|images).*)',
  ],
};

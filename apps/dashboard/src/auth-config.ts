import NextAuth from 'next-auth';
// eslint-disable-next-line @typescript-eslint/no-unused-vars, no-unused-vars -- We need to import this else the next-auth/jwt module augmentation throws an error
import { JWT } from 'next-auth/jwt';
import type { Provider } from 'next-auth/providers';
import type { TaoStatsDashboardProfile } from '@repo/types/dashboard-api-types';
import { getTokenEndpoint } from './lib/config/config-utils';
import { DASHBOARD_BASE_URL } from "@/lib/config"
import { emailConfig } from '@/lib/config/auth-config-email';
import { githubConfig } from '@/lib/config/auth-config-github';
import { googleConfig } from '@/lib/config/auth-config-google';
import { pinConfig } from '@/lib/config/auth-config-pin';

const providers: Provider[] = [
  githubConfig,
  googleConfig,
  emailConfig,
  pinConfig,
];

type RefreshTokenResponse = {
  token_type: string;
  access_token: string;
  refresh_token: string;
};

type RefreshTokenError = {
  statusCode: number;
  error: string;
  message: string;
};

const useSecureCookies = DASHBOARD_BASE_URL.startsWith("https://");
const cookiePrefix = useSecureCookies ? "__Secure-" : ""; 
const hostname = DASHBOARD_BASE_URL.includes("localhost") ? "localhost" : "taostats.io";

export const { handlers, auth, signIn, signOut } = NextAuth({
  debug: false,
  providers,
  cookies: {
    sessionToken: {
      name: `${cookiePrefix}next-auth.session-token`,
      options: {
        domain: `.${hostname}`,
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: useSecureCookies,
      }
    }
  },
  callbacks: {
    async jwt(params) {
      const { token, account, user } = params;

      if (account?.type === 'credentials') {
        // If it's credentials then we're using the magic link or 16 char string login aka fingerprint login
        // In which case the shape of the user object is different than the shape when signing in with oauth providers
        // It will also be a container object that contains the user object and the access token
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition -- user can actually be undefined even though it's not optional in the type
        if (user) {
          const userFromCredentials = user as {
            accessToken: string;
            refreshToken: string;
            userProfile: TaoStatsDashboardProfile;
          };
          return {
            ...token,
            ...userFromCredentials,
          };
        }
      }

      // user is only available upon sign in
      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition -- user can actually be undefined even though it's not optional in the type
      if (user) {
        // Add the user object returned from the profile callback to the next-auth token which is stored in the cookie and can populate the session
        token.userProfile = user as TaoStatsDashboardProfile;
      }

      if (account) {
        // account is only available upon sign in
        // Add the access token and refresh token to the next-auth token which is stored in the cookie
        return {
          ...token,
          accessToken: account.access_token,
          refreshToken: account.refresh_token,
        };
      }

      // Check if the underlying access token is expired
      const accessToken = token.accessToken;
      if (accessToken) {
        try {
          const decodedToken = JSON.parse(
            Buffer.from(accessToken.split('.')[1], 'base64').toString()
          ) as { exp: number } | undefined;
          if (decodedToken?.exp) {
            const expiryDate = new Date(decodedToken.exp * 1000);
            if (expiryDate < new Date()) {
              // Access token is expired
              // If we have a refresh token then try to refresh the access token
              // Otherwise return null to clear the authjs session

              if (token.refreshToken) {
                try {
                  const tokenEndpoint = getTokenEndpoint('refresh');
                  const body = {
                    refresh_token: token.refreshToken,
                  };

                  const response = await fetch(tokenEndpoint, {
                    method: 'POST',
                    body: new URLSearchParams(body),
                  });

                  const tokensOrError = (await response.json()) as
                    | RefreshTokenResponse
                    | RefreshTokenError;

                  if (!response.ok) {
                    const errorMessage =
                      (tokensOrError as RefreshTokenError).message ||
                      'Failed to refresh token';
                    throw new Error(errorMessage);
                  }

                  const newTokens = tokensOrError as RefreshTokenResponse;

                  return {
                    ...token,
                    accessToken: newTokens.access_token,
                    refreshToken: newTokens.refresh_token,
                  };
                } catch (error) {
                  console.error('Error refreshing access_token', error);
                  return null;
                }
              }

              // Returning null will clear the authjs session
              return null;
            }
          }
        } catch (error) {
          console.error(`Error trying to decode token ${accessToken}`, error);
          return null;
        }
      }

      // Underlying access token has not expired, so return the token as is
      return token;
    },
    session(params) {
      const { session, token } = params;
      if (token.userProfile) {
        session.userProfile = token.userProfile;
      }

      if (token.accessToken) {
        session.accessToken = token.accessToken;
      }

      return session;
    },
  },
  pages: {
    signIn: '/login',
    error: '/login',
  },
});

declare module 'next-auth' {
  /**
   * Returned by `auth`, `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */
  interface Session {
    accessToken?: string;
    userProfile?: TaoStatsDashboardProfile;
  }
}

declare module 'next-auth/jwt' {
  /** Returned by the `jwt` callback and `auth`, when using JWT sessions */
  interface JWT {
    accessToken?: string;
    refreshToken?: string;
    userProfile?: TaoStatsDashboardProfile;
  }
}

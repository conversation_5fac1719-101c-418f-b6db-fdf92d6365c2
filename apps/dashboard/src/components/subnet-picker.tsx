import { memo, useMemo, useState } from 'react';
import { BiChevronDown } from 'react-icons/bi';
import {
  Button,
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuItem,
  DropdownMenuContent,
  Text,
} from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { useSubnetLookup } from '@/lib/hooks/global-hooks';
import { useSubnetPickerOptions } from '@/lib/hooks/subnet-hooks';
import { useValidatorBalances } from '@/lib/hooks/validator-hooks';

type SubnetPickerProps = {
  onSelect: (subnetId: number) => void;
  subnetId?: number;
  className?: string;
};

type SubnetItemForPicker = {
  netuid: number;
  subnetName: string;
  subnetSymbol: string;
  hasBalances: boolean;
};

export const SubnetPicker = memo(function SubnetPicker({
  onSelect,
  subnetId,
  className,
}: SubnetPickerProps) {
  const { subnetQuery, isLoading } = useSubnetPickerOptions();
  const { getSubnetSymbol, getSubnetName } = useSubnetLookup();
  const { getValidatorBalances } = useValidatorBalances();
  const [isOpen, setIsOpen] = useState(false);

  const subnetSourceData = useMemo(() => {
    return subnetQuery.data?.data ?? [];
  }, [subnetQuery.data]);

  const subnetData = useMemo(() => {
    return subnetSourceData
      .map(
        (subnet): SubnetItemForPicker => ({
          netuid: subnet.netuid,
          subnetName: getSubnetName(subnet.netuid),
          subnetSymbol: getSubnetSymbol(subnet.netuid),
          hasBalances: getValidatorBalances(subnet.netuid).length > 0,
        })
      )
      .sort((a, b) => {
        // First sort by hasBalances (true values first)
        if (a.hasBalances !== b.hasBalances) {
          return a.hasBalances ? -1 : 1;
        }
        // Then sort by netuid
        return a.netuid - b.netuid;
      });
  }, [getSubnetName, getSubnetSymbol, getValidatorBalances, subnetSourceData]);

  if (isLoading) {
    return null;
  }

  const selectedSubnet = subnetData.find(
    (subnet) => subnet.netuid === subnetId
  );

  return (
    <DropdownMenu modal={false}>
      <DropdownMenuTrigger asChild>
        <Button
          size='sm'
          variant='cta3'
          className={cn('text-white/60', className)}
          onClick={() => {
            setIsOpen(!isOpen);
          }}
        >
          <span>
            {subnetId !== undefined && selectedSubnet ? (
              <SubnetSummary subnet={selectedSubnet} />
            ) : (
              'Choose'
            )}
          </span>
          <BiChevronDown
            aria-hidden='true'
            className={cn(
              'pointer-events-none col-start-1 row-start-1 size-6 self-center justify-self-end transition-transform sm:size-4',
              isOpen && 'rotate-180'
            )}
          />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end' className='h-96 overflow-y-scroll'>
        {subnetData.map((subnet) => (
          <DropdownMenuItem
            key={subnet.netuid}
            onClick={() => {
              onSelect(subnet.netuid);
              setIsOpen(!isOpen);
            }}
          >
            <SubnetSummary subnet={subnet} />
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
});

const SubnetSummary = memo(function SubnetSummary({
  subnet,
}: {
  subnet: SubnetItemForPicker;
}) {
  return (
    <div className='flex flex-row items-center gap-2'>
      <div className='flex flex-row items-center gap-2'>
        <div
          className={cn(
            'size-5 rounded-full bg-white text-center text-sm text-black',
            subnet.hasBalances ? 'bg-accent-1' : 'bg-white/60'
          )}
        >
          {subnet.subnetSymbol}
        </div>
      </div>
      <div className='flex flex-row items-baseline gap-2 text-sm'>
        <Text
          level='bodySmall'
          className={cn(
            'font-mono',
            !subnet.hasBalances ? 'text-white/60' : ''
          )}
        >
          {subnet.netuid}:
        </Text>{' '}
        <Text
          level='bodySmall'
          className={cn(!subnet.hasBalances ? 'text-white/60' : '')}
        >
          {subnet.subnetName}
        </Text>
      </div>
    </div>
  );
});

import type { ComponentProps } from 'react';
import { memo, useMemo, useState } from 'react';
import { BiListPlus, BiPencil } from 'react-icons/bi';
import { CgListTree } from 'react-icons/cg';
import { Button, Combobox, Input, Text } from '@repo/ui/components';
import { useValidatorComboboxOptions } from '@/lib/hooks/global-hooks';

export const ValidatorPicker = memo(function ValidatorPicker({
  subnetId,
  value,
  onChange,
  disabled,
  labelValue = 'Validator',
  labelLevel,
  labelClassName,
  comboboxClassName,
  inputClassName,
}: {
  subnetId?: number;
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  labelValue?: string;
  labelLevel?: ComponentProps<typeof Text>['level'];
  labelClassName?: string;
  comboboxClassName?: string;
  inputClassName?: string;
}) {
  const [fieldType, setFieldType] = useState<'list' | 'manual'>('list');

  const { getValidatorOptions } = useValidatorComboboxOptions();
  const validatorOptions = useMemo(
    () => (subnetId === undefined ? [] : getValidatorOptions(subnetId, value)),
    [getValidatorOptions, subnetId, value]
  );

  return (
    <div className='flex flex-col gap-2'>
      <div className='flex flex-row items-center gap-2'>
        <Text
          as='label'
          level={labelLevel ?? 'labelMedium'}
          className={labelClassName}
        >
          {labelValue}
        </Text>
      </div>
      {fieldType === 'list' ? (
        <Combobox
          leftContent={
            <CgListTree className='pointer-events-none size-5 self-center text-gray-400 sm:size-4' />
          }
          className={comboboxClassName}
          value={value}
          onChange={onChange}
          options={validatorOptions}
          disabled={disabled || subnetId === undefined}
        />
      ) : null}
      {fieldType === 'manual' ? (
        <Input
          iconLeftConfig={{
            Icon: BiPencil,
            className: 'sm:size-5',
          }}
          placeholder='Enter validator hotkey'
          inputId='manual-validator-hotkey'
          onChange={(e) => {
            onChange(e.target.value);
          }}
          value={value}
          inputClassName={inputClassName}
          disabled={disabled}
        />
      ) : null}

      <div className='flex flex-row items-center gap-2'>
        <Button
          variant='cta3'
          size='sm'
          onClick={() => {
            setFieldType(fieldType === 'manual' ? 'list' : 'manual');
          }}
        >
          {fieldType === 'manual' ? (
            <BiListPlus className='mr-2' />
          ) : (
            <BiPencil className='mr-2' />
          )}
          {fieldType === 'manual' ? 'Pick from list' : 'Enter manual hotkey'}
        </Button>
      </div>
    </div>
  );
});

import { Combobox } from '@repo/ui/components';
import { notify } from '@repo/ui/lib';
import { useAntiMevSlippage } from '@/lib/hooks/staking-hooks';

export const AntiMevCombobox = () => {
  const { antiMevSlippageValue, setAntiMevSlippageValue } =
    useAntiMevSlippage();
  return (
    <Combobox
      canSearch={false}
      className='h-10 w-fit bg-[#2E2E2E] text-white/60 hover:bg-[#2E2E2E]'
      value={antiMevSlippageValue?.toString() ?? ''}
      placeholder='Off'
      onChange={(value) => {
        if (value === '') {
          setAntiMevSlippageValue(null);
        } else {
          // check if the value is a number
          const num = Number(value);
          if (!Number.isNaN(num)) {
            setAntiMevSlippageValue(num);
          } else {
            notify.error(`Invalid value: ${value}`);
          }
        }
      }}
      options={[
        {
          value: '',
          label: 'Off',
        },
        {
          value: '0.025',
          label: '0.025%',
        },
        {
          value: '0.05',
          label: '0.05%',
        },
        {
          value: '0.075',
          label: '0.075%',
        },
        {
          value: '0.1',
          label: '0.1%',
        },
      ]}
    />
  );
};

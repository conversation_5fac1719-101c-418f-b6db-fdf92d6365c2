import { BiInfoCircle, BiRefresh, BiWallet } from 'react-icons/bi';
import {
  Button,
  AlertError,
  Card,
  Text,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@repo/ui/components';
import { ConnectWalletButton } from './connect-wallet-button';
import { useChainAndWalletContext } from '@/contexts/chain-and-wallet/chain-and-wallet-context';

export const ConnectWalletCard = () => {
  const { isError, errorType, resetApiState } = useChainAndWalletContext();
  return (
    <Card className='bg-white px-6 py-4'>
      <div className='flex flex-col gap-4'>
        <div className='flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between'>
          <div className='flex w-full flex-col gap-4 sm:w-auto sm:flex-row sm:items-center'>
            <div className='hidden items-center sm:flex'>
              <BiWallet className='size-16 text-black' />
            </div>
            <div className='flex flex-col gap-1'>
              <div className='flex items-center gap-2'>
                <div className='flex items-center sm:hidden'>
                  <BiWallet className='size-16 text-black' />
                </div>
                <Text level='headerMedium' className='text-black'>
                  Connect Wallet
                </Text>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <BiInfoCircle className='size-4 text-black' />
                    </TooltipTrigger>
                    <TooltipContent className='bg-input-primary max-w-[240px] p-4'>
                      <Text
                        level='bodySmall'
                        className='font-medium text-white/60'
                      >
                        Connecting your wallet will enable you to receive
                        rewards from delegating and staking. Your wallet will
                        remain secure and we do not control your wallet.
                      </Text>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Text level='bodyLarge' className='text-black/60'>
                Get started by connecting your wallet.
              </Text>
            </div>
          </div>
          <div className='w-full sm:w-auto'>
            <ConnectWalletButton />
          </div>
        </div>

        {isError && errorType ? (
          <AlertError
            description={
              <div className='flex flex-1 flex-row items-center justify-between'>
                {getErrorDescription(errorType)}
                <Button
                  variant='cta2'
                  size='sm'
                  onClick={resetApiState}
                  className='flex-row gap-2'
                >
                  <BiRefresh className='size-5' />
                  Try again
                </Button>
              </div>
            }
          />
        ) : null}
      </div>
    </Card>
  );
};

const getErrorDescription = (errorType: string) => {
  return errorType === 'apiError'
    ? 'Failed to connect to the Bittensor network.'
    : errorType === 'walletError'
      ? 'No accounts available.'
      : 'No extensions available.';
};

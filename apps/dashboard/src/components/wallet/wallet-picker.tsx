import { BiWallet } from 'react-icons/bi';
import { Button, Combobox } from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { useChainAndWalletContext } from '@/contexts/chain-and-wallet/chain-and-wallet-context';

type WalletPickerProps = {
  hideDisconnectButton?: boolean;
  className?: string;
  containerClassName?: string;
};

export const WalletPicker = ({
  hideDisconnectButton = false,
  className,
  containerClassName,
}: WalletPickerProps) => {
  const {
    state: { accounts, currentAccount },
    setCurrentAccount,
    disconnectWallet,
  } = useChainAndWalletContext();

  return (
    <div
      className={cn(
        'flex flex-col items-center gap-2 sm:flex-row',
        containerClassName
      )}
    >
      <Combobox
        className={className}
        value={accounts
          .findIndex((acct) => currentAccount?.address === acct.address)
          .toString()}
        onChange={(selectedValue) => {
          if (selectedValue === '') {
            return;
          }
          setCurrentAccount(accounts[Number(selectedValue)]);
        }}
        options={accounts.map((acct, index) => ({
          value: index.toString(),
          label: acct.meta.name,
        }))}
      />
      {hideDisconnectButton ? null : (
        <Button
          variant='cta3'
          size='sm'
          className='w-full text-sm sm:w-auto'
          onClick={() => {
            disconnectWallet();
          }}
        >
          Disconnect
          <BiWallet size={20} className='ml-2' />
        </Button>
      )}
    </div>
  );
};

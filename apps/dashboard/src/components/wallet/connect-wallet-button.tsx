import { CgArrowRight } from 'react-icons/cg';
import { But<PERSON>, Spinner } from '@repo/ui/components';
import { useChainAndWalletContext } from '@/contexts/chain-and-wallet/chain-and-wallet-context';

export const ConnectWalletButton = () => {
  const { isLoading, isError, connectWallet } = useChainAndWalletContext();

  if (isError) {
    return null;
  }

  const handleConnectClick = () => {
    connectWallet();
  };

  return (
    <Button
      disabled={isLoading}
      variant='cta3'
      size='lg'
      className='w-full flex-row gap-2 shadow-[0px_5px_23.8px_0px_rgba(0,0,0,0.25)]'
      onClick={() => {
        handleConnectClick();
      }}
    >
      Connect
      {isLoading ? <Spinner /> : <CgArrowRight className='size-6' />}
    </Button>
  );
};

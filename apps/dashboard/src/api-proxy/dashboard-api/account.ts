'use server';

import type {
  AccountDetails,
  AddPortfolioWalletRequest,
  PortfolioWalletsResponse,
  GenerateFingerprintResponse,
  SuccessResponse,
} from '@repo/types/dashboard-api-types';
import { del, get, post, postAnonymous, put } from './_dashboard-api-helpers';
import { DASHBOARD_BASE_URL } from '@/lib/config';

export const getUserAccount = async () => {
  return await get<AccountDetails>('/api/v1/account');
};

export const signInEmail = async ({
  email,
  redirectTo,
}: {
  email: string;
  redirectTo: string;
}) => {
  return await postAnonymous('/api/v1/account/create/email', {
    email,
    urlPrefix: `${DASHBOARD_BASE_URL}/login/email/callback?${redirectTo ? `redirectTo=${redirectTo}&` : ''}token=`,
  });
};

export const generatePin = async () => {
  return await postAnonymous<GenerateFingerprintResponse>(
    '/api/v1/account/create/fingerprint'
  );
};

export const setRecoveryEmail = async (params: { email: string }) => {
  const response = await put('/api/v1/account/recovery/email', params);
  return response;
};

export const getPortfolioWallets = async () => {
  return await get<PortfolioWalletsResponse>('/api/v1/wallet/list');
};

export const addPortfolioWallet = async (data: AddPortfolioWalletRequest) => {
  return await post<SuccessResponse>('/api/v1/wallet/create', data);
};

export const deletePortfolioWallet = async (walletId: string) => {
  return await del<SuccessResponse>('/api/v1/wallet/delete', { walletId });
};

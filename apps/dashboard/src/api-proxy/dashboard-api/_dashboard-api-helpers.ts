import axios from 'axios';
import { logApiCall, requestRaw } from '@/api-proxy/helpers';
import { auth } from '@/auth-config';
import { DASHBOARD_API_URL } from '@/lib/config';

const getSessionOrThrow = async () => {
  const session = await auth();
  if (!session?.accessToken) {
    throw new Error('No session found');
  }
  return session;
};

const getUrl = (path: string) => {
  return new URL(path, DASHBOARD_API_URL);
};

const getHeaders = async (includeAuth = true) => {
  const headers: Record<string, string> = {
    Accept: 'application/json',
    'Content-Type': 'application/json',
  };

  // Check if we need to include auth headers
  if (includeAuth) {
    const session = await getSessionOrThrow();
    headers.Authorization = `Bearer ${session.accessToken}`;
  }

  return { headers };
};

export async function get<ResponseType>(path: string) {
  const url = getUrl(path);
  const response = await axios.get<ResponseType>(
    url.toString(),
    await getHeaders()
  );

  logApiCall('GET', url.pathname, response.data);
  return response.data;
}

export async function post<T>(path: string, data: unknown = {}) {
  const url = getUrl(path);
  const response = await axios.post<T>(
    url.toString(),
    data,
    await getHeaders()
  );

  logApiCall('POST', url.pathname, response.data);
  return response.data;
}

export async function put<T>(path: string, data: unknown) {
  const url = getUrl(path);
  const response = await axios.put<T>(url.toString(), data, await getHeaders());

  logApiCall('PUT', url.pathname, response.data);
  return response.data;
}

export async function postAnonymous<T>(path: string, data: unknown = {}) {
  const url = getUrl(path);
  const response = await axios.post<T>(
    url.toString(),
    data,
    await getHeaders(false)
  );

  logApiCall('POST ANON', url.pathname, response.data);
  return response.data;
}

export async function postManual<T>({
  path,
  data = {},
  headers,
}: {
  path: string;
  data?: unknown;
  headers: Record<string, string>;
}) {
  const url = getUrl(path);
  return requestRaw<T>({ method: 'POST', url, data, headers });
}

export async function del<T>(path: string, data?: unknown) {
  const url = getUrl(path);
  const response = await axios.delete<T>(url.toString(), {
    ...(await getHeaders()),
    data,
  });

  logApiCall('DELETE', url.pathname, response.data);
  return response.data;
}

export function searchParamsToQueryParams(params: string, pageSize?: number) {
  const searchParams = new URLSearchParams(params);

  const currentPage = Number.parseInt(searchParams.get('page') || '1', 10);
  const limitString = searchParams.get('limit');

  let limit = pageSize ?? 10;
  if (limitString) {
    limit = Number.parseInt(limitString);
  }

  searchParams.set('page', (currentPage <= 0 ? 1 : currentPage).toString());
  searchParams.set('limit', (limit <= 0 ? 10 : limit).toString());

  const queryParams = `?${searchParams.toString()}`;
  return queryParams;
}

'use server';

import type {
  CreateApiKeyResponse,
  GetApiKeysResponse,
  GetApiKeyUsageResponse,
} from '@repo/types/dashboard-api-types';
import { del, get, post } from './_dashboard-api-helpers';

export const getApiKeys = async () => {
  const response = await get<GetApiKeysResponse>('/api/v1/key/list');
  return response;
};

export const getApiKeyUsage = async (apiKeyId: string) => {
  const response = await get<GetApiKeyUsageResponse>(
    `/api/v1/key/usage?apiKeyId=${apiKeyId}`
  );
  return response;
};

export const createApiKey = async (data: {
  name: string;
  description: string;
}) => {
  const response = await post<CreateApiKeyResponse>('/api/v1/key/create', data);
  return response;
};

export const deleteApiKey = async (apiKeyId: string) => {
  const response = await del(`/api/v1/key/delete`, { apiKeyId });
  return response;
};

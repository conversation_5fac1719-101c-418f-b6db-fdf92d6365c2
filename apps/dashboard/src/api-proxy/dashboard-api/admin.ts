'use server';

import type {
  UpdateAccountRequest,
  GetAccountsResponse,
  AccountDetails,
  GetTopUsageResponse,
} from '@repo/types/dashboard-api-types';
import { get, post } from './_dashboard-api-helpers';
import { constructURL } from '@/lib/utils';

export const getAccounts = async (pageOffset: number) => {
  const response = await get<GetAccountsResponse>(
    `/api/v1/admin/accounts?pageOffset=${pageOffset}`
  );
  return response;
};

export const getTopUsage = async ({
  startDate,
  endDate,
  limit,
}: {
  startDate: string;
  endDate: string;
  limit: number;
}) => {
  const response = await get<GetTopUsageResponse>(
    constructURL('/api/v1/admin/topusage', {
      startDate,
      endDate,
      limit,
    })
  );
  return response;
};

export const searchAccounts = async (searchTerm: string) => {
  const response = await get<GetAccountsResponse>(
    `/api/v1/admin/accounts?email=${searchTerm}`
  );
  return response;
};

export const updateAccount = async (data: UpdateAccountRequest) => {
  await post('/api/v1/admin/account', data);
};

export const getAccount = async (accountId: string) => {
  const response = await get<AccountDetails>(
    `/api/v1/admin/account?accountId=${accountId}`
  );
  return response;
};

import type { AxiosError } from 'axios';
import axios from 'axios';
import type { User } from 'next-auth';
import type {
  TaoStatsDashboardProfile,
  LoginResponse,
} from '@repo/types/dashboard-api-types';
import { requestRaw } from '@/api-proxy/helpers';
import {
  getTaoStatsUserInfoEndpoint,
  getTokenEndpoint,
} from '@/lib/config/config-utils';

const getErrorToThrow = (message: string, error: unknown) => {
  console.error(message, error);
  if (axios.isAxiosError(error)) {
    const axiosError = error as AxiosError;
    console.error(message, {
      error: axiosError.response?.data,
    });
    const errorData = axiosError.response?.data as { message?: string };
    if (errorData.message) {
      return new Error(errorData.message);
    }
    return new Error(message);
  }
  return error;
};

type HandleAuthoriseParams =
  | {
      type: 'email';
      token: string;
    }
  | {
      type: 'pin';
      pin: string;
    };

export const handleAuthorise = async (params: HandleAuthoriseParams) => {
  const paramsToSend =
    params.type === 'email'
      ? { token: params.token }
      : { fingerprint: params.pin };

  let tokenData: LoginResponse;
  try {
    const response = await requestRaw<LoginResponse>({
      method: 'POST',
      url: new URL(getTokenEndpoint(params.type)),
      data: paramsToSend,
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    });
    tokenData = response;
  } catch (error: unknown) {
    throw getErrorToThrow('Failed to fetch token', error);
  }

  let userProfile: TaoStatsDashboardProfile;
  try {
    const response = await requestRaw<TaoStatsDashboardProfile>({
      method: 'GET',
      url: new URL(getTaoStatsUserInfoEndpoint()),
      headers: {
        Authorization: `${tokenData.token_type} ${tokenData.access_token}`,
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    });
    userProfile = response;
  } catch (error: unknown) {
    throw getErrorToThrow('Failed to fetch user info', error);
  }

  // NextAuth specifies a User object as the return type
  // So tell it that we're returning a User object even though it's not, in order to keep TS happy
  // Then pull out what's required in the jwt callback
  return {
    accessToken: tokenData.access_token,
    refreshToken: tokenData.refresh_token,
    userProfile,
  } as User;
};

'use server';

import type { ValidatorMetadata, ValidatorMetaDataAPI } from "@repo/types/website-api-types";
import { blankGet } from "./website-api/_website-api-helpers";

export async function getValidatorMetadata(): Promise<ValidatorMetadata[]> {
  const result = await blankGet<ValidatorMetaDataAPI | null>(
    "https://raw.githubusercontent.com/opentensor/bittensor-delegates/main/public/delegates.json"
  );

  const validatorMetadata = Object.entries(result ?? {}).map(
    ([key, value]) => ({
      address: key,
      ...value,
    })
  );

  return validatorMetadata;
}
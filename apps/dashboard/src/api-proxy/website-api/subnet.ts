'use server';

import type {
  EnrichedSubnet,
  SubnetAPI,
  SubnetIdentityAPI,
  SubnetIdentityQueryParams,
  SubnetQueryParams,
} from '@repo/types/website-api-types';
import type {
  SubnetRegistrationCostHistoryRequest,
  SubnetRegistrationCostResponse,
} from '@repo/types/website-api-types-generated';
import { get } from './_website-api-helpers';
import { constructURL } from '@/lib/utils';

export async function getSubnets(params: SubnetQueryParams) {
  const [subnetResponse, subnetIdentityResponse] = await Promise.all([
    get<SubnetAPI>(constructURL('/subnet/latest/v1', params)),
    get<SubnetIdentityAPI>(
      constructURL('/subnet/identity/v1', { ...params, limit: 200 })
    ),
  ]);

  // Merge subnet data with metadata
  const enrichedData = subnetResponse.data.map<EnrichedSubnet>((subnet) => {
    const metadata = subnetIdentityResponse.data.find(
      (d) => d.netuid === subnet.netuid
    );

    return {
      ...subnet,
      metadata: metadata
        ? {
            name: metadata.subnet_name
              .replace(
                /[\u{1F300}-\u{1F9FF}]|\p{Emoji_Presentation}|\p{Extended_Pictographic}/gu,
                ''
              )
              .trim(),
          }
        : null,
    };
  });

  return {
    ...subnetResponse,
    data: enrichedData,
  };
}

export async function getSubnetIdentities(params: SubnetIdentityQueryParams) {
  const response = await get<SubnetIdentityAPI>(
    constructURL('/subnet/identity/v1', params)
  );

  return response;
}

export async function getSubnet(params: SubnetQueryParams) {
  const response = await get<SubnetAPI>(
    constructURL('/subnet/latest/v1', params)
  );

  return response;
}

export async function getSubnetRegistrationCostHistory(
  params: SubnetRegistrationCostHistoryRequest
) {
  const response = await get<SubnetRegistrationCostResponse>(
    constructURL('/subnet/registration_cost/history/v1', params)
  );

  return response;
}

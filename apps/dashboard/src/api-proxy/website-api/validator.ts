'use server';

import type {
  DtaoValidatorLatestAPI,
  IdentityAPI,
  IdentityQueryParams,
  ValidatorLatestQueryParams,
} from '@repo/types/website-api-types';
import type { DtaoValidatorAvailableResponse } from '@repo/types/website-api-types-generated';
import { get } from './_website-api-helpers';
import {
  RAO_PER_TAO,
  VALIDATOR_MINIMUM_STAKE,
} from '@/contexts/chain-and-wallet/constants';
import { constructURL } from '@/lib/utils';

export async function getValidators(params: ValidatorLatestQueryParams) {
  const validatorResponse = await get<DtaoValidatorLatestAPI>(
    constructURL('/dtao/validator/latest/v1', params)
  );

  const validators = validatorResponse.data
    .filter(
      (item) =>
        Number(item.global_weighted_stake) / RAO_PER_TAO >
        VALIDATOR_MINIMUM_STAKE
    )
    .sort(
      (a, b) =>
        Number(b.global_weighted_stake) - Number(a.global_weighted_stake)
    );

  return validators;
}

export async function fetchIdentity(params: IdentityQueryParams) {
  const response = await get<IdentityAPI>(
    constructURL('/identity/latest/v1', params)
  );

  return response;
}

export async function getValidatorsIdentity() {
  const response = await Promise.allSettled([fetchIdentity({})]);
  const total =
    response[0].status === 'fulfilled'
      ? response[0].value.pagination.total_items
      : 0;

  const responses = await Promise.allSettled(
    Array.from({ length: Math.ceil(total / 200) }).map((_, index) =>
      fetchIdentity({ page: index + 1, limit: 200 })
    )
  );

  const data = responses.flatMap((item) =>
    item.status === 'fulfilled'
      ? (item.value as IdentityAPI | null)?.data ?? []
      : []
  );

  return data;
}

export async function getTopValidatorsPerSubnet() {
  const response = await get<DtaoValidatorAvailableResponse>(
    constructURL('/dtao/validator/available/v1', {})
  );

  return response;
}

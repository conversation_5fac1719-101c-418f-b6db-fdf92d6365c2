import {
  PoolOrder,
  type DtaoPoolSubnetQueryParams,
  type DtaoSubnetsAPI,
  type SubnetAPI,
  type SubnetIdentityAPI,
} from '@repo/types/website-api-types';
import { get } from './_website-api-helpers';
import { getSubnet, getSubnetIdentities } from './subnet';
import { constructURL } from '@/lib/utils';
// import emojiRegex from 'emoji-regex';

export async function getDtaoPoolForSubnet(netuid?: number) {
  const response = await get<DtaoSubnetsAPI>(
    constructURL('/dtao/pool/latest/v1', netuid ? { netuid } : {})
  );

  return response;
}

export async function getDtaoPoolSubnet(params: DtaoPoolSubnetQueryParams) {
  const response = await get<DtaoSubnetsAPI>(
    constructURL('/dtao/pool/latest/v1', params)
  );

  return response;
}

export async function fetchDtaoSubnet() {
  const response = await Promise.allSettled([
    getDtaoPoolSubnet({ ...{ order: PoolOrder.MarketCapDesc } }),
    getSubnetIdentities({}),
    getSubnet({}),
  ]);

  const res =
    response[0].status === 'fulfilled'
      ? (response[0].value as DtaoSubnetsAPI | null)?.data
      : [];

  const pagination =
    response[0].status === 'fulfilled'
      ? (response[0].value as DtaoSubnetsAPI | null)?.total
      : 0;

  const metadata =
    response[1].status === 'fulfilled'
      ? (response[1].value as SubnetIdentityAPI | null)?.data
      : [];

  const subnet =
    response[2].status === 'fulfilled'
      ? (response[2].value as SubnetAPI | null)?.data
      : [];

  const result = res
    ? res.map((item) => {
        const currentSubnet = subnet?.find((it) => it.netuid === item.netuid);
        const currentMetadata = metadata?.find(
          (it) => it.netuid === item.netuid
        );

        return {
          ...item,
          subnet_name: currentMetadata?.subnet_name ?? '',
          subnet_description: '',
          description: currentMetadata?.description ?? '',
          recycled_lifetime: currentSubnet?.recycled_lifetime ?? '',
          recycled_24_hours: currentSubnet?.recycled_24_hours ?? '',
          recycled_since_registration:
            currentSubnet?.recycled_since_registration ?? '',
          registration_timestamp: currentSubnet?.registration_timestamp ?? '',
          owner: currentSubnet?.owner?.ss58 ?? '',
          emission: currentSubnet?.emission ?? '',
          neuron_registration_cost:
            currentSubnet?.neuron_registration_cost ?? '',
          active_keys: currentSubnet?.active_keys ?? 0,
          max_neurons: currentSubnet?.max_neurons ?? 0,
          github: currentMetadata?.github_repo ?? '',
          discord_url: currentMetadata?.discord ?? '',
          rank: item.rank,
          subnet_contact: currentMetadata?.subnet_contact ?? '',
          subnet_url: currentMetadata?.subnet_url ?? '',
        };
      })
    : [];

  return { data: result, total: pagination };
}

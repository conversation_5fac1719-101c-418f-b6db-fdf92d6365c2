'use server';

import type {
  AccountAPI,
  AccountHistoryQueryParams,
  AccountQueryParams,
} from '@repo/types/website-api-types';
import { get } from './_website-api-helpers';
import { constructURL } from '@/lib/utils';

export async function getAccountLatest(params: AccountQueryParams) {
  const response = await get<AccountAPI>(
    constructURL('/account/latest/v1', params)
  );

  return response;
}

export async function getAccountHistory(params: AccountHistoryQueryParams) {
  const response = await get<AccountAPI>(
    constructURL('/account/history/v1', params)
  );
  return response;
}

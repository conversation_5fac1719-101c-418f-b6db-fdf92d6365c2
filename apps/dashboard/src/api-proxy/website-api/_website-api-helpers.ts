import axios from 'axios';
import { WEBSITE_API_URL, WEBSITE_API_TOKEN } from '@/lib/config';

export async function get<ResponseType>(path: string) {
  const response = await axios.get<ResponseType>(`${WEBSITE_API_URL}${path}`, {
    headers: {
      Authorization: WEBSITE_API_TOKEN,
      Accept: 'application/json',
    },
  });

  return response.data;
}

export async function blankGet<ResponseType>(url: string) {
  const response = await axios.get<ResponseType>(url);
  return response.data;
}

export async function post<T>(url: string, data: unknown) {
  const response = await axios.post<T>(url, data);
  return response.data;
}

export function searchParamsToQueryParams(params: string, pageSize?: number) {
  const searchParams = new URLSearchParams(params);

  const currentPage = Number.parseInt(searchParams.get('page') || '1', 10);
  const limitString = searchParams.get('limit');

  let limit = pageSize ?? 10;
  if (limitString) {
    limit = Number.parseInt(limitString);
  }

  searchParams.set('page', (currentPage <= 0 ? 1 : currentPage).toString());
  searchParams.set('limit', (limit <= 0 ? 10 : limit).toString());

  const queryParams = `?${searchParams.toString()}`;
  return queryParams;
}

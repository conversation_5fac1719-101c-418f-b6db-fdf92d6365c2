'use server';

import type {
  PriceAPI,
  PriceHistoryQueryParams,
  PriceOhlcAPI,
  PriceOhlcQueryParams,
  PriceQueryParams,
} from '@repo/types/website-api-types';
import { get } from './_website-api-helpers';
import { constructURL } from '@/lib/utils';

export async function fetchLatestPrice(params: PriceQueryParams) {
  const response = await get<PriceAPI>(
    constructURL('/price/latest/v1', params)
  );

  return response;
}

export async function fetchPriceHistory(params: PriceHistoryQueryParams) {
  const response = await get(constructURL('/price/history/v1', params));
  return response;
}

export async function fetchPriceOhlc(params: PriceOhlcQueryParams) {
  const response = await get(constructURL('/price/ohlc/v1', params));

  return response;
}

export async function fetchPriceFullOhlc(params: PriceOhlcQueryParams) {
  const response = await Promise.allSettled(
    Array.from({ length: 5 }).map(async (_, index) => {
      return fetchPriceOhlc({ ...params, page: index + 1, limit: 200 });
    })
  );

  return response.flatMap((item) =>
    item.status === 'fulfilled'
      ? (item.value as PriceOhlcAPI | null)?.data ?? []
      : []
  );
}

'use server';

import type {
  ColdkeyReportRequest,
  ColdkeyReportResponse,
} from '@repo/types/website-api-types-generated';
import { get } from './_website-api-helpers';
import { constructURL } from '@/lib/utils';

export async function getColdkeyReport(params: ColdkeyReportRequest) {
  const response = await get<ColdkeyReportResponse>(
    constructURL('/accounting/coldkey_report/v1', params)
  );

  return response;
}

'use server';

import {
  type StakeBalanceQueryParams,
  type StakeBalanceAPI,
  type DelegateAPI,
  type DelegateQueryParams,
  DelegateOrder,
} from '@repo/types/website-api-types';
import { get } from './_website-api-helpers';
import { constructURL } from '@/lib/utils';

export async function getStakeBalanceLatest(params: StakeBalanceQueryParams) {
  const response = await get<StakeBalanceAPI>(
    constructURL('/dtao/stake_balance/portfolio/v1', { ...params, limit: 200 })
  );

  return response;
}

export async function getDelegations(params: DelegateQueryParams) {
  const response = await get<DelegateAPI>(
    constructURL('/delegation/v1', {
      ...params,
      limit: 100,
      order: DelegateOrder.TimestampDesc,
    })
  );

  return response;
}

'use server';

import type {
  NeuronAggregatedHistoryQueryParams,
  NeuronAggregatedHistoryAPI,
  NeuronHistoryQueryParams,
  NeuronHistoryAPI,
} from '@repo/types/website-api-types';
import { get } from './_website-api-helpers';
import { constructURL } from '@/lib/utils';

export async function getNeuronHistory(params: NeuronHistoryQueryParams) {
  const response = await get<NeuronHistoryAPI>(
    constructURL('/neuron/history/v1', params)
  );

  return response;
}

export async function getNeuronAggregatedistory(
  params: NeuronAggregatedHistoryQueryParams
) {
  const response = await get<NeuronAggregatedHistoryAPI>(
    constructURL('/neuron/aggregated/history/v1', params)
  );

  return response;
}

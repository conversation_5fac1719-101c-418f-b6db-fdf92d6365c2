/* eslint-disable @typescript-eslint/no-unused-vars -- allow for this file */
/* eslint-disable no-unused-vars -- allow for this file */

import type { AxiosError } from 'axios';
import axios from 'axios';

export const logApiCall = (method: string, pathname: string, data: unknown) => {
  // console.log(`${method}`, pathname, { data });
};

// Add axios interceptor to log errors
axios.interceptors.response.use(
  (response) => response,
  (error: AxiosError) => {
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('API Error Response:', {
        status: error.response.status,
        data: error.response.data,
        headers: error.response.headers,
        url: error.config?.url,
        method: error.config?.method,
      });
    } else if (error.request) {
      // The request was made but no response was received
      console.error('API No Response:', {
        request: error.request as XMLHttpRequest,
        url: error.config?.url,
        method: error.config?.method,
      });
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('API Request Setup Error:', {
        message: error.message,
        url: error.config?.url,
        method: error.config?.method,
      });
    }

    return Promise.reject(error);
  }
);

export async function requestRaw<T>({
  method,
  url,
  data = {},
  headers,
}: {
  method: 'GET' | 'POST';
  url: URL;
  data?: unknown;
  headers: Record<string, string>;
}) {
  logApiCall(method, url.toString(), { data, headers });
  const response = await axios.request<T>({
    method,
    url: url.toString(),
    data,
    headers,
  });
  logApiCall(`${method} RESPONSE`, url.toString(), response.data);
  return response.data;
}

/* eslint-disable @typescript-eslint/no-explicit-any -- TODO: using `any` here is legacy from the website, fix later */

import type { BigNumber } from 'bignumber.js';

const DEBUG = true;
export const log = (...args: Parameters<typeof console.log>) => {
  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition -- DEBUG is a boolean
  if (DEBUG) {
    // eslint-disable-next-line no-console -- Allow it.
    console.log(...args);
  }
};

export const isDevOrBeta = () => {
  return false;
};

export const HOTKEY_TAOSTATS_VALIDATOR =
  '5GKH9FPPnWSUoeeTJp19wVtd84XqFW4pyK2ijV2GsFbhTrP1';

export const TAO_CURRENCY = '𝞃';

export function constructURL(
  baseURL: string,
  params: Record<string, any>
): string {
  const queryString = new URLSearchParams(
    objectToQueryParams(params)
  ).toString();
  return queryString.length > 0 ? `${baseURL}?${queryString}` : baseURL;
}

function objectToQueryParams(obj: Record<string, any>): Record<string, string> {
  return Object.entries(obj)
    .filter(([_, value]) => value !== undefined && value !== null)
    .reduce<Record<string, string>>((acc, [key, value]) => {
      acc[key] = String(value);
      return acc;
    }, {});
}

export function formatNumber2dp(value: number | string | BigNumber) {
  return formatNumberToSpecifiedDp(value, 2);
}

export function formatNumberToSpecifiedDp(
  value: number | string | BigNumber,
  dp: number
) {
  return formatNumber(value.toString(), {
    minimumFractionDigits: dp,
    maximumFractionDigits: dp,
  });
}

export function formatNumber(
  value: number | string,
  options?: Intl.NumberFormatOptions
) {
  // Check if its a string and if it can be converted to a number
  // If so, convert it to a number
  // If not, return 0
  const num = typeof value === 'string' ? Number(value) : value;

  if (isNaN(num)) {
    return '0';
  }

  return num.toLocaleString(
    'en-US',
    options ?? {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }
  );
}

export function truncateString(str: string, length = 5) {
  if (str && str.length > 15) {
    const first = str.substring(0, length);
    const last = str.substring(str.length - length);
    return `${first}...${last}`;
  }
  return str;
}

export function titleCase(str: string) {
  if (!str) {
    return '';
  }
  return str.charAt(0).toUpperCase() + str.slice(1);
}

export const convertDateRangeToDates = (
  dateRangeSelected: '1d' | '1w' | '1m' | '1y'
) => {
  const now = new Date();
  const to = now.toISOString().split('T')[0];
  const from = new Date();

  switch (dateRangeSelected) {
    case '1d':
      from.setDate(now.getDate() - 1);
      break;
    case '1w':
      from.setDate(now.getDate() - 7);
      break;
    case '1m':
      from.setDate(now.getDate() - 30);
      break;
    case '1y':
      from.setFullYear(now.getFullYear() - 1);
      break;
  }

  return {
    from: from.toISOString().split('T')[0],
    to,
  };
};

export const getNumberOfDaysFromDateRange = (
  dateRange: '1d' | '1w' | '1m' | '1y'
) => {
  switch (dateRange) {
    case '1d':
      return 1;
    case '1w':
      return 7;
    case '1m':
      return 30;
    case '1y':
      return 365;
    default:
      console.error(`Invalid date range: ${JSON.stringify(dateRange)}`);
      return 0;
  }
};

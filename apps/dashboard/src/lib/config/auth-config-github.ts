import GitHub from 'next-auth/providers/github';
import type { TaoStatsDashboardProfile } from '@repo/types/dashboard-api-types';
import { getTokenEndpoint, getTaoStatsUserInfoEndpoint } from './config-utils';

export const githubConfig = GitHub({
  id: 'github-manual',
  name: 'Gith<PERSON>',
  token: getTokenEndpoint('github'),
  userinfo: getTaoStatsUserInfoEndpoint(),
  profile(profile: unknown) {
    return profile as TaoStatsDashboardProfile;
  },
});

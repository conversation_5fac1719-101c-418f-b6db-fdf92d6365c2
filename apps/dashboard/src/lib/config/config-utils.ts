import { DASHBOARD_API_URL } from '.';

const getDashboardApiUrl = (path: string) => {
  const url = new URL(path, DASHBOARD_API_URL);
  return url.toString();
};

export const getTaoStatsUserInfoEndpoint = () => {
  return getDashboardApiUrl(getUserInfoPath());
};

export const getTokenEndpoint = (
  tokenType: 'github' | 'google' | 'email' | 'pin' | 'refresh'
) => {
  return getDashboardApiUrl(getTokenPath(tokenType));
};

export const getTokenPath = (
  tokenType: 'github' | 'google' | 'email' | 'pin' | 'refresh'
) => {
  return `api/v2/account/login/${tokenType === 'pin' ? 'fingerprint' : tokenType}`;
};

export const getUserInfoPath = () => {
  return `api/v1/account`;
};

import Credentials from 'next-auth/providers/credentials';
import { handleAuthorise } from '@/api-proxy/dashboard-api/auth';

export const emailConfig = Credentials({
  id: 'email',
  name: '<PERSON><PERSON>',
  async authorize(credentials, _) {
    // Retrieve token from incoming credentials
    // This is what is auto submitted from the login/email-callback/page.tsx
    const { token } = credentials;

    const response = await handleAuthorise({
      type: 'email',
      token: token as string,
    });

    return response;
  },
});

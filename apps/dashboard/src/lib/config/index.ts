export const APP_ENV = process.env.APP_ENV;

export const DASHBOARD_BASE_URL =
  process.env.NEXT_PUBLIC_DASHBOARD_BASE_URL &&
  process.env.NEXT_PUBLIC_DASHBOARD_BASE_URL.trim() !== ''
    ? process.env.NEXT_PUBLIC_DASHBOARD_BASE_URL
    : 'http://localhost:3000/';

export const DASHBOARD_API_URL =
  process.env.DASHBOARD_API_URL && process.env.DASHBOARD_API_URL.trim() !== ''
    ? process.env.DASHBOARD_API_URL
    : 'https://test-management-api.taostats.io/';

export const WEBSITE_BASE_URL =
  process.env.NEXT_PUBLIC_WEBSITE_BASE_URL &&
  process.env.NEXT_PUBLIC_WEBSITE_BASE_URL.trim() !== ''
    ? process.env.NEXT_PUBLIC_WEBSITE_BASE_URL
    : 'https://beta.taostats.io';

export const WEBSITE_API_URL =
  process.env.WEBSITE_API_URL && process.env.WEBSITE_API_URL.trim() !== ''
    ? process.env.WEBSITE_API_URL
    : 'https://api-dev-v2.taostats.io/api';

export const WEBSITE_API_TOKEN =
  process.env.WEBSITE_API_TOKEN && process.env.WEBSITE_API_TOKEN.trim() !== ''
    ? process.env.WEBSITE_API_TOKEN
    : 'rZob3GNGpTnDrrdkOWz3Zbh1EbV1UVBud7enbsLAxwga0BKyJw0PmLCtAA5yiD5w';

export const NEXT_PUBLIC_STRIPE_PUBLIC_KEY =
  process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY;

export const NEXT_PUBLIC_BITTENSOR_SOCKET_URL =
  process.env.NEXT_PUBLIC_BITTENSOR_SOCKET_URL ??
  'wss://test.chain.opentensor.ai:443';

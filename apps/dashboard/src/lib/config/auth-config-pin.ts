import Credentials from 'next-auth/providers/credentials';
import { handleAuthorise } from '@/api-proxy/dashboard-api/auth';

export const pinConfig = Credentials({
  id: 'pin',
  name: 'P<PERSON>',
  async authorize(credentials, _) {
    // Retrieve pin from incoming credentials
    // This is what is auto submitted from the following pages:
    // - login/pin/generate/page.tsx
    // - login/pin/page.tsx
    const { pin } = credentials;

    const response = await handleAuthorise({
      type: 'pin',
      pin: pin as string,
    });

    return response;
  },
});

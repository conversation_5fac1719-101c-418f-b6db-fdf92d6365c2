import Google from 'next-auth/providers/google';
import type { TaoStatsDashboardProfile } from '@repo/types/dashboard-api-types';
import { getTokenEndpoint, getTaoStatsUserInfoEndpoint } from './config-utils';

const config = {
  id: 'google-manual',
  name: 'Google',
  type: 'oauth',
  authorization: {
    url: 'https://accounts.google.com/o/oauth2/auth',
    params: {
      scope:
        'email profile openid https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/userinfo.email',
    },
  },
  token: getTokenEndpoint('google'),
  userinfo: getTaoStatsUserInfoEndpoint(),
  profile(profile: unknown) {
    return profile as TaoStatsDashboardProfile;
  },
};

export const googleConfig = Google(config);

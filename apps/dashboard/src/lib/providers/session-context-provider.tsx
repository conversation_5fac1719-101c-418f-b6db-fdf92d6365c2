'use client';

import type { ReactNode } from 'react';
import { createContext, useContext, useMemo } from 'react';
import type { Session } from 'next-auth';
import type { AccountDetails } from '@repo/types/dashboard-api-types';
import { useUserAccount } from '@/lib/hooks/user-hooks';

interface SessionContextValue {
  session: Session;
  userAccount: AccountDetails | undefined;
  stripeClientPublicKey: string;
  appEnv: string;
}

const SessionContext = createContext<SessionContextValue | undefined>(
  undefined
);

interface SessionContextProviderProps {
  children: ReactNode;
  session: Session;
  stripeClientPublicKey: string;
  appEnv: string;
}

export const SessionContextProvider = ({
  children,
  session,
  stripeClientPublicKey,
  appEnv,
}: SessionContextProviderProps) => {
  const { data: userAccount } = useUserAccount();
  const value = useMemo<SessionContextValue>(() => {
    return {
      session,
      userAccount,
      stripeClientPublicKey,
      appEnv,
    };
  }, [session, userAccount, stripeClientPublicKey, appEnv]);

  if (!session.userProfile) {
    console.error('No user profile found in session', { session });
  }

  return (
    <SessionContext.Provider value={value}>{children}</SessionContext.Provider>
  );
};

export const useSessionContext = () => {
  const context = useContext(SessionContext);
  if (context === undefined) {
    throw new Error(
      'useSessionContext must be used within a SessionContextProvider'
    );
  }
  return context;
};

import { useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useLocalStorage } from 'usehooks-ts';
import { apiClient, handleResponse } from '@/app/api/[...route]/lib/api-client';
import { useSessionContext } from '@/lib/providers/session-context-provider';
import { HOTKEY_TAOSTATS_VALIDATOR, truncateString } from '@/lib/utils';

export const useAppEnv = () => {
  // This method is used to get the APP_ENV envrionment variable from the session context provider

  const appEnv = useSessionContext().appEnv;

  if (!appEnv) {
    console.error('APP_ENV is not set. Using `development` as fallback');
    return 'development';
  }

  if (
    appEnv === 'development' ||
    appEnv === 'test' ||
    appEnv === 'beta' ||
    appEnv === 'production'
  ) {
    return appEnv;
  }

  console.error(
    `Invalid APP_ENV: ${appEnv}. Using \`development\` as fallback.`
  );
  return 'development';
};

const subnetApiClient = apiClient.subnet;
const validatorApiClient = apiClient.validator;
const dtaoApiClient = apiClient.dtao;

const queryKeys = {
  subnets: 'subnets',
  validators: 'validators',
  validatorsForSubnets: 'validatorsForSubnets',
};

export const useDtaoSubnetHelper = () => {
  const poolQuery = useDtaoSubnetPools();

  const getSubnetPool = useCallback(
    (netuid: number) => {
      const pool = poolQuery.data?.data.find((p) => p.netuid === netuid);
      return pool;
    },
    [poolQuery.data]
  );

  return { getSubnetPool, poolQueryIsLoading: poolQuery.isLoading };
};

export const useSubnets = () => {
  return useQuery({
    queryKey: [queryKeys.subnets],
    queryFn: async () => handleResponse(await subnetApiClient.$get()),
    staleTime: 1000 * 60 * 60 * 24,
  });
};

export const useSubnetLookup = () => {
  const subnetQuery = useSubnets();
  const { getSubnetPool, poolQueryIsLoading } = useDtaoSubnetHelper();

  const getSubnet = useCallback(
    (netuid: number) => {
      const subnet = subnetQuery.data?.data.find((s) => s.netuid === netuid);
      return subnet;
    },
    [subnetQuery.data]
  );

  const getSubnetName = useCallback(
    (netuid: number) => {
      const subnet = getSubnet(netuid);
      return subnet?.metadata?.name ?? 'Unknown';
    },
    [getSubnet]
  );

  const getSubnetSymbol = useCallback(
    (netuid: number) => {
      const subnet = getSubnetPool(netuid);
      return subnet?.symbol ?? '';
    },
    [getSubnetPool]
  );

  const getSubnetRank = useCallback(
    (netuid: number) => {
      const subnet = getSubnetPool(netuid);
      return subnet?.rank !== undefined ? subnet.rank + 1 : undefined;
    },
    [getSubnetPool]
  );

  return {
    isLoading: subnetQuery.isLoading || poolQueryIsLoading,
    isSuccess: subnetQuery.isSuccess,
    error: subnetQuery.error,
    getSubnet,
    getSubnetName,
    getSubnetSymbol,
    getSubnetRank,
    getSubnetPool,
  };
};

export const useDtaoSubnetPools = () => {
  const query = useQuery({
    queryKey: ['dtao-subnet-pools'],
    queryFn: async () => handleResponse(await dtaoApiClient.pool.$get()),
    staleTime: 1000 * 12,
    refetchInterval: 1000 * 12,
  });

  return query;
};

export const useValidators = () => {
  return useQuery({
    queryKey: [queryKeys.validators],
    queryFn: async () => handleResponse(await validatorApiClient.$get()),
    staleTime: 1000 * 60 * 60 * 24,
  });
};

export const useValidatorLookup = () => {
  const validatorQuery = useValidators();

  const getValidator = useCallback(
    (hotkey: string) => {
      const validator = validatorQuery.data?.find(
        (v) => v.hotkey.ss58 === hotkey
      );
      return validator;
    },
    [validatorQuery.data]
  );

  const getValidatorName = useCallback(
    (hotkey: string) => {
      const validator = getValidator(hotkey);
      return validator?.name ?? truncateString(hotkey);
    },
    [getValidator]
  );

  const getTaostatsValidator = useCallback(
    () =>
      validatorQuery.data?.find(
        (validator) => validator.hotkey.ss58 === HOTKEY_TAOSTATS_VALIDATOR
      ),
    [validatorQuery.data]
  );

  return { getValidator, getValidatorName, getTaostatsValidator };
};

export const useValidatorsForSubnet = () => {
  const validatorsForSubnetsQuery = useQuery({
    queryKey: [queryKeys.validatorsForSubnets],
    queryFn: async () =>
      handleResponse(await validatorApiClient.topValidatorsPerSubnet.$get()),
    staleTime: 1000 * 60 * 60 * 24,
  });

  const getValidatorsForSubnet = useCallback(
    (netuid: number) => {
      return validatorsForSubnetsQuery.data?.data.filter(
        (v) => v.netuid === netuid
      );
    },
    [validatorsForSubnetsQuery.data]
  );

  return { getValidatorsForSubnet };
};

export const useLocalManualValidators = () => {
  const [manualValidators, setManualValidators] = useLocalStorage<string[]>(
    'manual-validators',
    []
  );

  const addManualValidator = (hotkey: string) => {
    // Don't add duplicate validators
    if (manualValidators.includes(hotkey)) {
      return;
    }
    setManualValidators([...manualValidators, hotkey]);
  };

  return {
    manualValidators,
    setManualValidators,
    addManualValidator,
  };
};

export const useValidatorComboboxOptions = () => {
  const { getTaostatsValidator } = useValidatorLookup();
  const { getValidatorsForSubnet } = useValidatorsForSubnet();
  const { manualValidators } = useLocalManualValidators();

  const taostatsValidator = getTaostatsValidator();

  const getValidatorOptions = useCallback(
    (netuid: number, selectedHotkey?: string) => {
      const validatorsForSubnet = getValidatorsForSubnet(netuid);

      // Filter out taostats validator if it exists and add it to the top of the list
      const options =
        validatorsForSubnet
          ?.filter(
            (validator) =>
              validator.address.ss58 !== taostatsValidator?.hotkey.ss58
          )
          .map((validator) => ({
            value: validator.address.ss58,
            label: validator.name ?? truncateString(validator.address.ss58),
            hasName: Boolean(validator.name),
          }))
          .sort((a, b) => {
            if (a.hasName && !b.hasName) return -1;
            if (!a.hasName && b.hasName) return 1;
            return a.label.localeCompare(b.label);
          }) ?? [];

      if (taostatsValidator) {
        options.unshift({
          value: taostatsValidator.hotkey.ss58,
          label:
            taostatsValidator.name && taostatsValidator.name !== ''
              ? taostatsValidator.name
              : 'Taostats & Corcel',
          hasName: true,
        });
      }

      const returnOptions = [
        ...options,
        ...manualValidators.map((validator) => ({
          value: validator,
          label: truncateString(validator),
          hasName: false,
        })),
      ];

      // Check if we have a selectedHotkey and it's not in the options
      // If it's not, add it to the end of the list
      if (selectedHotkey) {
        if (!returnOptions.some((option) => option.value === selectedHotkey)) {
          returnOptions.push({
            value: selectedHotkey,
            label: truncateString(selectedHotkey),
            hasName: false,
          });
        }
      }

      // Filter out duplicate options
      return returnOptions.filter(
        (option, index, self) =>
          index === self.findIndex((t) => t.value === option.value)
      );
    },
    [getValidatorsForSubnet, taostatsValidator, manualValidators]
  );

  return { getValidatorOptions };
};

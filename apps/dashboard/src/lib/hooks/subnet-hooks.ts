import { useQuery } from '@tanstack/react-query';
import type { SubnetIdentityQueryParams } from '@repo/types/website-api-types';
import { useSubnets } from './global-hooks';
import { apiClient, handleResponse } from '@/app/api/[...route]/lib/api-client';
import { queryKeys } from '@/app/dashboard/lib/constants';

const subnetApiClient = apiClient.subnet;

export const useSubnetIdentityData = (params: SubnetIdentityQueryParams) => {
  return useQuery({
    queryKey: [queryKeys.subnetIdentities],
    queryFn: async () =>
      handleResponse(
        await subnetApiClient.identity.$get({
          query: { ...params },
        })
      ),
    refetchInterval: 60000, // 1 hour
  });
};

export const useSubnetPoolData = () => {
  return useQuery({
    queryKey: [queryKeys.subnetsPoolData],
    queryFn: async () =>
      handleResponse(await subnetApiClient.dtaoSubnet.$get()),
    refetchInterval: 12 * 1000, // 12 seconds
  });
};

export const useSubnetPickerOptions = () => {
  const subnetQuery = useSubnets();
  const isLoading = subnetQuery.isLoading;
  const isSuccess = subnetQuery.isSuccess;

  return {
    subnetQuery,
    isLoading,
    isSuccess,
  };
};

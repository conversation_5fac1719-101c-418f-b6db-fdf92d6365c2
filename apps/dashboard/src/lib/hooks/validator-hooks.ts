import { useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import type { ValidatorLatestQueryParams } from '@repo/types/website-api-types';
import { apiClient, handleResponse } from '@/app/api/[...route]/lib/api-client';
import { queryKeys } from '@/app/dashboard/lib/constants';
import { useAccountContext } from '@/contexts/chain-and-wallet/account-context';
import { MIN_STAKING_AMOUNT_TAO } from '@/contexts/chain-and-wallet/constants';

const validatorApiClient = apiClient.validator;

export const useValidatorData = (params: ValidatorLatestQueryParams) => {
  return useQuery({
    queryKey: [queryKeys.validators, params],
    queryFn: async () =>
      handleResponse(
        await validatorApiClient.$get({
          query: { ...params },
        })
      ),
    refetchInterval: 60000, // 1 hour
  });
};

export const useValidatorMetadata = () => {
  return useQuery({
    queryKey: [queryKeys.validatorMetadata],
    queryFn: async () =>
      handleResponse(await validatorApiClient.validatorMetadata.$get()),
    refetchInterval: 60000, // 1 hour
  });
};

export const useValidatorIdentityData = () => {
  return useQuery({
    queryKey: [queryKeys.validatorIdentities],
    queryFn: async () =>
      handleResponse(await validatorApiClient.validatorIdentity.$get()),
    refetchInterval: 60000, // 1 hour
  });
};

export const useValidatorBalances = () => {
  const accountContext = useAccountContext();
  const getValidatorBalances = useCallback(
    (subnetId: number) => {
      return accountContext.state.delegateBalances.filter(
        (balance) =>
          balance.netuid === subnetId &&
          balance.alphaAmount > MIN_STAKING_AMOUNT_TAO
      );
    },
    [accountContext.state.delegateBalances]
  );

  return {
    isAccountContextLoading: accountContext.state.loading,
    getValidatorBalances,
  };
};

import { useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import type { PriceQueryParams } from '@repo/types/website-api-types';
import { apiClient, handleResponse } from '@/app/api/[...route]/lib/api-client';
import { queryKeys } from '@/app/dashboard/lib/constants';

const priceApiClient = apiClient.price;

export const useGetPriceData = (params: PriceQueryParams) => {
  return useQuery({
    queryKey: [queryKeys.priceData, 'tao'],
    queryFn: async () =>
      handleResponse(
        await priceApiClient.$get({
          query: {
            asset: params.asset,
          },
        })
      ),
    refetchInterval: 10000,
  });
};

export const useTaoToDollarConverter = () => {
  const { data: priceData } = useGetPriceData({ asset: 'tao' });
  const getDollarValue = useCallback(
    (taoValue: number) => {
      const price = Number(priceData?.data[0]?.price || 0);
      return taoValue * price;
    },
    [priceData]
  );
  return { getDollarValue };
};

import { useCallback } from 'react';
import { BigNumber } from 'bignumber.js';
import { useLocalStorage } from 'usehooks-ts';
import { useAccountContext } from '@/contexts/chain-and-wallet/account-context';
import { log } from '@/lib/utils';

export const useMaxSlippage = () => {
  const [maxSlippage, setMaxSlippage] = useLocalStorage<number | null>(
    'max-slippage',
    0.5
  );
  const updateMaxSlippage = useCallback(
    (maxSlippageInput: number) => {
      setMaxSlippage(
        BigNumber(maxSlippageInput)
          .decimalPlaces(2, BigNumber.ROUND_DOWN)
          .toNumber()
      );
    },
    [setMaxSlippage]
  );

  return { maxSlippage: maxSlippage ?? 0, updateMaxSlippage };
};

const slippageBoundaries = {
  low: 0.5,
  medium: 2,
};

export const useSlippageHelper = () => {
  const getSlippageTextClassName = (slippage: number) => {
    if (slippage <= slippageBoundaries.low) return 'text-accent-1';
    if (slippage <= slippageBoundaries.medium) return 'text-slippage-warning';
    if (slippage > slippageBoundaries.medium) return 'text-accent-2';
  };

  return {
    getSlippageTextClassName,
    slippageBoundaries,
  };
};

export const useAntiMevSlippage = () => {
  const [antiMevSlippageValue, setAntiMevSlippageValue] = useLocalStorage<
    number | null
  >('anti-mev-slippage', null);

  return { antiMevSlippageValue, setAntiMevSlippageValue };
};

export const usePriceLimitCalculator = () => {
  const {
    state: { subnetAmountsMap },
  } = useAccountContext();
  const { maxSlippage: configuredMaxSlippage } = useMaxSlippage();
  const { antiMevSlippageValue } = useAntiMevSlippage();

  const getMaxSlippage = (transactionSlippage: number) => {
    if (antiMevSlippageValue) {
      const slippage = transactionSlippage;
      const antiMevSlippageAsBigNumber = BigNumber(antiMevSlippageValue);
      if (!antiMevSlippageAsBigNumber.isNaN()) {
        const antiMevSlippageNum = antiMevSlippageAsBigNumber.toNumber();
        const antiMevSlippage = Number(slippage) + antiMevSlippageNum;
        if (antiMevSlippage < configuredMaxSlippage) {
          return antiMevSlippage;
        }
      }
    }

    return configuredMaxSlippage;
  };

  const getAddStakePriceLimit = (
    netuid: number,
    transactionSlippage?: number
  ) => {
    const price = subnetAmountsMap[netuid].price;

    const maxSlippage = getMaxSlippage(transactionSlippage ?? 0);
    log('DEBUG', { maxSlippage });
    const slippageMiltiplier = maxSlippage / 100 + 1;

    return BigNumber(price)
      .multipliedBy(slippageMiltiplier)
      .shiftedBy(9)
      .integerValue(BigNumber.ROUND_DOWN);
  };

  const getRemoveStakePriceLimit = (
    netuid: number,
    transactionSlippage?: number
  ) => {
    const price = subnetAmountsMap[netuid].price;

    const maxSlippage = getMaxSlippage(transactionSlippage ?? 0);
    log('DEBUG', { maxSlippage });
    const slippageMiltiplier = 1 - maxSlippage / 100;

    return BigNumber(price)
      .multipliedBy(slippageMiltiplier)
      .shiftedBy(9)
      .integerValue(BigNumber.ROUND_DOWN);
  };

  return {
    getAddStakePriceLimit,
    getRemoveStakePriceLimit,
  };
};

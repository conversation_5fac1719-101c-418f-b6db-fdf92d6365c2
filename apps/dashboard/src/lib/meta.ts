import type { Metadata } from 'next';

export function constructMetadata({
  title = 'Taostats Dashboard & API',
  description = 'Explore and build your own Bittensor analytics with the Taostats Dashboard and API. Access real-time data, insights, and metrics directly via the Taostats API for seamless analytics management.',
  image = '/thumbnail.png',
  noIndex = false,
}: {
  title?: string;
  description?: string;
  image?: string;
  noIndex?: boolean;
} = {}): Metadata {
  return {
    title: {
      template: `%s · Taostats Dashboard & API`,
      default: title,
    },
    description,
    openGraph: {
      title,
      description,
      images: [
        {
          url: image,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [image],
    },
    metadataBase: new URL('https://dash.taostats.io'),
    ...(noIndex && {
      robots: {
        index: false,
        follow: false,
      },
    }),
  };
}

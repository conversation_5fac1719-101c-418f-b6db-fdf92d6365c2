import { BigNumber } from 'bignumber.js';

/**
 * Rounds a number to a specified number of decimal places
 * @param num - The number to round
 * @param decimalPlaces - The number of decimal places to round to
 * @returns The rounded number
 */
export const round = (num: number | string, decimalPlaces: number): number => {
  return BigNumber(num)
    .decimalPlaces(decimalPlaces, BigNumber.ROUND_HALF_UP)
    .toNumber();
};

export const roundDown = (
  num: number | string | BigNumber,
  decimalPlaces: number
): number => {
  return BigNumber(num)
    .decimalPlaces(decimalPlaces, BigNumber.ROUND_DOWN)
    .toNumber();
};

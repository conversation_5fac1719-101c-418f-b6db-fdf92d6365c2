import { generateFromString } from 'generate-avatar';
import { imagePaths } from '@/lib/image-paths';

export const validatorIconMapping: Record<string, string> = {
  '5F4tQyWrhfGVcNhoqeiNsR6KjD4wMZ2kfhLj4oHYuyHbZAc3': 'opentensor',
  '5GKH9FPPnWSUoeeTJp19wVtd84XqFW4pyK2ijV2GsFbhTrP1': 'taostatscorcel',
  '5HEo565WAy4Dbq3Sv271SAi7syBSofyfhhwRNjFNSM2gP9M2': 'foundry',
  '5HK5tp6t2S59DywmHRWPBVJeJ86T61KjurYqeooqj8sREpeN': 'bittensor-guru',
  '5EhvL1FVkQPpMjZX4MAADcW42i3xPSF1KiCpuaxTYVr28sux': 'tao-validator',
  '5FFApaS75bv5pJHfAp2FVLBj9ZaXuFDjEypsaBNc1wCfe52v': 'roundtable21-validator',
  '5DvTpiniW9s3APmHRYn8FroUWyfnLtrsid5Mtn5EwMXHN2ed': '1sttensor',
  '5HbLYXUBy1snPR8nfioQ7GoA9x76EELzEq9j7F32vWUQHm1x': 'tensorplex',
  '5GP7c3fFazW9GXK8Up3qgu2DJBk8inu4aK9TZy3RuoSWVCMi': 'datura',
  '5CXRfP2ekFhe62r7q3vppRajJmGhTi7vwvb2yr79jveZ282w': 'rizzo',
  '5CsvRJXuR955WojnGMdok1hbhffZyB4N5ocrv82f3p5A2zVp': 'owlventures',
  '5HNQURvmjjYhTSksi8Wfsw676b4owGwfLR2BFAQzG7H3HhYf': 'neuralinternet',
  '5FFM6Nvvm78GqyMratgXXvjbqZPi7SHgSQ81nyS96jBuUWgt': 'prvalidator',
  '5Fq5v71D4LX8Db1xsmRSy6udQThcZ8sFDqxQFwnUZ1BuqY5A': 'northtensor',
  '5DnXm2tBGAD57ySJv5SfpTfLcsQbSKKp6xZKFWABw3cYUgqg': 'lovecosmo',
  '5E7v9fjMkA1rSchQiaz9gX1vg62fFz8HrAVipkzpmo1ZRsgG': 'synapse',
  '5ED6jwDECEmNvSp98R2qyEUPHDv9pi14E6n3TS8CicD6YfhL': 'gigacorp',
  '5HeKSHGdsRCwVgyrHchijnZJnq4wiv6GqoDLNah8R5WMfnLB': 'taostation',
  '5CPzGD8sxyv8fKKXNvKem4qJRhCXABRmpUgC1wb1V4YAXLc3': 'chatwithhal',
  '5CXC2quDN5nUTqHMkpP5YRp2atYYicvtUghAYLj15gaUFwe5': 'kooltek68',
  '5FcXnzNo3mrqReTEY4ftkg5iXRBi61iyvM4W1bywZLRqfxAY': 'bittranslate',
};

export function getIconUrl(address: string) {
  const validatorImage = Object.entries(validatorIconMapping).find(
    ([hotkey]) => hotkey === address
  )?.[1];
  if (validatorImage) {
    return `https://taostats-static.b-cdn.net/validators/${validatorImage}.png`;
  }

  if (address === '5D4oo3Z5VFUJtFWcK9wtfPqkGnzVubSFMnWnMKUVkDxsrWSj') {
    return imagePaths.icons.foundation;
  }

  if (address === '5DM7CPqPKtMSADhFKYsstsCS4Tm4Kd6PMXoh6DdqY4MtxmtX') {
    return imagePaths.icons.cortext;
  }

  return `data:image/svg+xml;utf8,${generateFromString(address)}`;
}

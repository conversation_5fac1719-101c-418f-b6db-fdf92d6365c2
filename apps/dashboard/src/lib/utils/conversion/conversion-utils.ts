import { useCallback } from 'react';
import { BigNumber } from 'bignumber.js';
import type { ChainConvertFormat } from './conversion-types';
import { useSubnetLookup } from '@/lib/hooks/global-hooks';

export const chainConvertFormat: {
  cToH: ChainConvertFormat;
  hToC: ChainConvertFormat;
  hToH: ChainConvertFormat;
  cToC: ChainConvertFormat;
} = {
  cToH: {
    inputFormat: 'chain',
    outputFormat: 'human',
  },
  hToC: {
    inputFormat: 'human',
    outputFormat: 'chain',
  },
  hToH: { inputFormat: 'human', outputFormat: 'human' },
  cToC: { inputFormat: 'chain', outputFormat: 'chain' },
};

// A Human amount is a number in human readable format e.g. 5.2
// A Chain amount is a number in chain format e.g. 5.2 * 1_000_000_000 = 5,200,000,000
// This can be used with Tao or Alpha amounts
// Defaults to human input and chain output if no inputFormat or outputFormat is provided
export const useChainAmountConverter = () => {
  const convertAmount = useCallback(
    (
      amount: number,
      {
        inputFormat,
        outputFormat,
      }: ChainConvertFormat = chainConvertFormat.hToC
    ) => {
      const amountInChainFormat =
        inputFormat === 'human'
          ? BigNumber(amount).shiftedBy(9).integerValue(BigNumber.ROUND_FLOOR)
          : BigNumber(amount);

      const outputAmount =
        outputFormat === 'human'
          ? amountInChainFormat.shiftedBy(-9)
          : amountInChainFormat;

      return outputAmount;
    },
    []
  );

  const getHumanFromChainAmount = useCallback(
    (amount: number) => {
      return convertAmount(amount, {
        inputFormat: 'chain',
        outputFormat: 'human',
      });
    },
    [convertAmount]
  );

  return { convertAmount, getHumanFromChainAmount };
};

export const useTaoToAlphaConverterWithApiData = () => {
  const { getSubnetPool } = useSubnetLookup();

  // Converts a tao amount to an alpha amount
  // If inputFormat is human, the taoAmount is in human readable format e.g. 5.2
  // If inputFormat is chain, the taoAmount is in chain format e.g. 5.2 * 1_000_000_000 = 5,200,000,000
  // If outputFormat is human, the alphaAmount is in human readable format e.g. 4.02
  // If outputFormat is chain, the alphaAmount is in chain format e.g. 4.02 * 1_000_000_000 = 4,020,000,000
  const convertTaoToAlpha = useCallback(
    (
      netuid: number,
      taoAmount: number,
      {
        inputFormat,
        outputFormat,
      }: ChainConvertFormat = chainConvertFormat.hToC
    ) => {
      const subnetPool = getSubnetPool(netuid);
      if (!subnetPool) {
        return BigNumber(0);
      }

      const price = BigNumber(subnetPool.price);

      const taoChainFormat =
        inputFormat === 'human'
          ? BigNumber(taoAmount).shiftedBy(9)
          : BigNumber(taoAmount);
      const alphaChain = taoChainFormat.dividedBy(price);
      return outputFormat === 'human'
        ? alphaChain.shiftedBy(-9)
        : alphaChain.integerValue(BigNumber.ROUND_FLOOR);
    },
    [getSubnetPool]
  );

  const convertAlphaToTao = useCallback(
    (
      netuid: number,
      alphaAmount: number | string,
      {
        inputFormat,
        outputFormat,
      }: ChainConvertFormat = chainConvertFormat.hToC
    ) => {
      const subnetPool = getSubnetPool(netuid);
      if (!subnetPool) {
        return BigNumber(0);
      }

      const price = BigNumber(subnetPool.price);
      const alphaChainFormat =
        inputFormat === 'human'
          ? BigNumber(alphaAmount).shiftedBy(9)
          : BigNumber(alphaAmount);

      const taoChain = alphaChainFormat.multipliedBy(price);
      return outputFormat === 'human'
        ? taoChain.shiftedBy(-9)
        : taoChain.integerValue(BigNumber.ROUND_FLOOR);
    },
    [getSubnetPool]
  );

  return {
    convertTaoToAlpha,
    convertAlphaToTao,
    chainConvertFormat,
  };
};

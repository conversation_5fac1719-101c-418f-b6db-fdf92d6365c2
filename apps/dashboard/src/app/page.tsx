import { UserDashboard } from './user-dashboard/user-dashboard';
import { AdminDashboard } from '@/app/admin/dashboard/admin-dashboard';
import { auth } from '@/auth-config';

export const metadata = {
  title: 'Taostats Dashboard & API',
};

export default async function Index() {
  const session = await auth();

  if (session?.accessToken && !session.userProfile?.isAdmin) {
    return <UserDashboard />;
  }

  if (session?.accessToken && session.userProfile?.isAdmin) {
    return <AdminDashboard />;
  }

  return null;
}

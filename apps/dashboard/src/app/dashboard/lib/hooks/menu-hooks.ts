import type { ComponentProps } from 'react';
import { usePathname } from 'next/navigation';
import {
  BiLinkExternal,
  BiKey,
  BiUser,
  BiMoney,
  BiCoinStack,
  BiLinkAlt,
} from 'react-icons/bi';
import { CgMenuGridO, CgFileDocument } from 'react-icons/cg';
import type { MenuNavItem } from '@repo/ui/components';
import { useSessionContext } from '@/lib/providers/session-context-provider';

type MainNavigationItem = {
  title?: string;
  menuItems: ComponentProps<typeof MenuNavItem>['item'][];
};
export type MainNavigationItemWithIndex = MainNavigationItem & {
  index: number;
};

const developerMenuSection = {
  title: 'Developers',
  menuItems: [
    {
      name: 'API Keys',
      href: '/api-keys',
      icon: BiKey,
    },
    {
      name: 'Docs',
      href: 'https://docs.taostats.io/docs/index',
      target: '_blank',
      icon: CgFileDocument,
      rightIcon: BiLinkExternal,
    },
    { name: 'Billing', href: '/billing', icon: <PERSON>i<PERSON><PERSON> },
  ],
};

const useAdminMenuItems = () => {
  const adminMenuItems = [
    {
      menuItems: [
        { index: 1, name: 'Admin Dashboard', href: '/', icon: CgMenuGridO },
        {
          index: 2,
          name: 'Accounts',
          href: '/admin/accounts',
          icon: BiUser,
        },
        { index: 3, name: 'Staking', href: '/stake', icon: BiLinkAlt },
        {
          index: 4,
          name: 'Portfolio',
          href: '/portfolio',
          icon: BiCoinStack,
        },
      ],
    },
    developerMenuSection,
  ];

  return { adminMenuItems };
};

const useUserMenuItems = () => {
  const userMenuItems = [
    {
      menuItems: [
        { name: 'Dashboard', href: '/', icon: CgMenuGridO },
        { name: 'Stake', href: '/stake', icon: BiLinkAlt },
        {
          name: 'Portfolio',
          href: '/portfolio',
          icon: BiCoinStack,
        },
      ],
    },
    developerMenuSection,
  ];

  return { userMenuItems };
};

export const useMainNavigation = () => {
  const currentPath = usePathname();
  const { session } = useSessionContext();
  const { adminMenuItems } = useAdminMenuItems();
  const { userMenuItems } = useUserMenuItems();

  const menuItems = session.userProfile?.isAdmin
    ? adminMenuItems
    : userMenuItems;

  const mainNavigation = menuItems.map<MainNavigationItemWithIndex>(
    (item, index) => {
      return {
        ...item,
        index,
        menuItems: item.menuItems.map((menuItem, itemIndex) => ({
          ...menuItem,
          index: itemIndex,
          current: menuItem.href === currentPath,
        })),
      };
    }
  );

  return { mainNavigation };
};

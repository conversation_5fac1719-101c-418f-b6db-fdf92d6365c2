import { useQuery } from '@tanstack/react-query';
import { apiClient, handleResponse } from '@/app/api/[...route]/lib/api-client';

const blockApiClient = apiClient.blocks;

export const useLatestBlock = (enabled = true) => {
  return useQuery({
    queryKey: ['latest-block'],
    queryFn: async () => handleResponse(await blockApiClient.limit.$get()),
    refetchInterval: 10000,
    enabled,
  });
};

import type { QueryClient } from '@tanstack/react-query';
import { fetchLatestPrice } from '@/api-proxy/website-api/price';
import { queryKeys } from '@/app/dashboard/lib/constants';

export const prefetchLatestPrice = (queryClient: QueryClient) => {
  return queryClient.prefetchQuery({
    queryKey: [queryKeys.priceData, 'tao'],
    queryFn: async () => {
      const price = await fetchLatestPrice({ asset: 'tao' });
      return price;
    },
  });
};

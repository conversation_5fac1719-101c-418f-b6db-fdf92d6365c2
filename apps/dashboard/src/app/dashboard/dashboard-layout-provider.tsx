import {
  dehydrate,
  HydrationBoundary,
  QueryClient,
} from '@tanstack/react-query';
import { prefetchLatestPrice } from './lib/server-utils';
import { DashboardLayoutDynamic } from './ui/dashboard-layout-dynamic';
import { NEXT_PUBLIC_STRIPE_PUBLIC_KEY, APP_ENV } from '@/lib/config';
import { SessionContextProvider } from '@/lib/providers/session-context-provider';
import { getServerSession } from '@/lib/utils/auth-utils';

export const DashboardLayoutProvider = async ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const serverSession = await getServerSession();
  const stripeClientPublicKey = NEXT_PUBLIC_STRIPE_PUBLIC_KEY ?? '';
  const appEnv = APP_ENV;

  // User is not signed in so don't provide the dashboard layout
  if (!serverSession.session) {
    return <>{children}</>;
  }

  const queryClient = new QueryClient();
  await prefetchLatestPrice(queryClient);

  // User is signed in so provide the dashboard layout
  return (
    <SessionContextProvider
      session={serverSession.session}
      stripeClientPublicKey={stripeClientPublicKey}
      appEnv={appEnv}
    >
      <HydrationBoundary state={dehydrate(queryClient)}>
        <DashboardLayoutDynamic session={serverSession.session}>
          {children}
        </DashboardLayoutDynamic>
      </HydrationBoundary>
    </SessionContextProvider>
  );
};

import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  TransitionChild,
} from '@headlessui/react';
import { BiMenu, BiX } from 'react-icons/bi';
import { DashboardMenu } from './dashboard-menu';
import { useSidebar } from './sidebar-context';

export const DashboardSidebarMobile = () => {
  const { closeSidebar, sidebarOpen } = useSidebar();

  return (
    <Dialog
      className='relative z-50 lg:hidden'
      onClose={closeSidebar}
      open={sidebarOpen}
    >
      <DialogBackdrop
        className='fixed inset-0 bg-black transition-opacity duration-300 ease-linear data-[closed]:opacity-0'
        transition
      />

      <div className='fixed inset-0 flex'>
        <DialogPanel
          className='relative mr-16 flex w-full max-w-xs flex-1 transform transition duration-300 ease-in-out data-[closed]:-translate-x-full'
          transition
        >
          <TransitionChild>
            <div className='absolute left-full top-0 flex w-16 justify-center pt-5 duration-300 ease-in-out data-[closed]:opacity-0'>
              <button
                className='-m-2.5 p-2.5'
                onClick={closeSidebar}
                type='button'
              >
                <span className='sr-only'>Close sidebar</span>
                <BiX aria-hidden='true' className='size-6 text-white' />
              </button>
            </div>
          </TransitionChild>

          {/* Sidebar component */}
          <div className='flex grow flex-col gap-y-5 overflow-y-auto bg-black px-6 pb-4 ring-1 ring-white/10'>
            <DashboardMenu />
          </div>
        </DialogPanel>
      </div>
    </Dialog>
  );
};

export const OpenSidebarButtonMobile = () => {
  const { openSidebar } = useSidebar();
  return (
    <button
      className='p-2.5 text-gray-700 lg:hidden'
      onClick={() => {
        openSidebar();
      }}
      type='button'
    >
      <span className='sr-only'>Open sidebar</span>
      <BiMenu aria-hidden='true' className='size-6' />
    </button>
  );
};

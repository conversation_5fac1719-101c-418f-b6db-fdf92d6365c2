'use client';

import dynamic from 'next/dynamic';
import type { Session } from 'next-auth';
import { Spinner } from '@repo/ui/components';

export function DashboardLayoutDynamic({
  children,
  session,
}: {
  children: React.ReactNode;
  session: Session;
}) {
  // Lazy load with ssr: false to get around the issue with polkadot js lib throwing an error when being rendered on the server
  const DashboardLayout = dynamic(
    () => import('./dashboard-layout').then((mod) => mod.DashboardLayout),
    {
      ssr: false,
      // eslint-disable-next-line react/no-unstable-nested-components -- false positive
      loading: () => <LoadingIndicator />,
    }
  );

  return <DashboardLayout session={session}>{children}</DashboardLayout>;
}

const LoadingIndicator = () => {
  return (
    <div className='flex h-screen w-full items-center justify-center'>
      <Spinner size='lg' />
    </div>
  );
};

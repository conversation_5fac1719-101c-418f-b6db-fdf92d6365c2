'use client';

import { usePathname } from 'next/navigation';
import type { Session } from 'next-auth';
import { ValidatorLatestOrder } from '@repo/types/website-api-types';
import { Skeleton, NavigationBar } from '@repo/ui/components';
import { useLatestBlock } from '@/app/dashboard/lib/hooks/block-hooks';
import { useChainAndWalletContext } from '@/contexts/chain-and-wallet/chain-and-wallet-context';
import { DASHBOARD_BASE_URL, WEBSITE_BASE_URL } from '@/lib/config';
import { useGetPriceData } from '@/lib/hooks/price-hooks';
import {
  useSubnetIdentityData,
  useSubnetPoolData,
} from '@/lib/hooks/subnet-hooks';
import {
  useValidatorData,
  useValidatorMetadata,
  useValidatorIdentityData,
} from '@/lib/hooks/validator-hooks';

export function MetaDataBarContainer({
  session,
  signOut,
}: {
  session: Session;
  signOut: () => Promise<void>;
}) {
  const pathname = usePathname();
  const isBlocksPage = pathname.includes('/blocks');
  const getPriceData = useGetPriceData({ asset: 'tao' });
  const getBlockData = useLatestBlock(isBlocksPage);
  const subnetIdentityData = useSubnetIdentityData({ limit: 200 });
  const validatorData = useValidatorData({
    limit: 200,
    order: ValidatorLatestOrder.GlobalWeightedStakeDesc,
  });
  const validatorMetadata = useValidatorMetadata();
  const validatorIdentityData = useValidatorIdentityData();
  const subnetPoolData = useSubnetPoolData();

  const {
    connectWallet,
    disconnectWallet,
    state: { currentAccount },
  } = useChainAndWalletContext();

  if (getPriceData.isLoading || (isBlocksPage && getBlockData.isLoading)) {
    return <Skeleton className='w-70 h-16' />;
  }

  const gotBlockData = Boolean(getBlockData.data?.data[0]);
  const latestBlock = getBlockData.data?.data[0].block_number;

  if (getPriceData.isSuccess && getPriceData.data.data[0]) {
    return (
      <NavigationBar
        latestBlock={latestBlock}
        priceData={getPriceData.data.data[0]}
        subnetPoolData={subnetPoolData.data?.data ?? []}
        showBlockData={gotBlockData ? isBlocksPage : null}
        subnetData={subnetIdentityData.data?.data ?? null}
        dashboardUrl={DASHBOARD_BASE_URL}
        websiteUrl={WEBSITE_BASE_URL}
        validatorData={validatorData.data ?? null}
        userProfile={session.userProfile ?? null}
        validatorMetadata={validatorMetadata.data ?? null}
        validatorIdentityData={validatorIdentityData.data ?? null}
        signOut={signOut}
        walletConnected={Boolean(currentAccount)}
        connectWallet={connectWallet}
        disconnectWallet={disconnectWallet}
      />
    );
  }

  return null;
}

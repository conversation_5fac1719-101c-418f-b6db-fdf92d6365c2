import Image from 'next/image';
import { MenuNavItem } from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { useSidebar } from './sidebar-context';
import type { MainNavigationItemWithIndex } from '@/app/dashboard/lib/hooks/menu-hooks';
import { useMainNavigation } from '@/app/dashboard/lib/hooks/menu-hooks';
import { imagePaths } from '@/lib/image-paths';

export function DashboardMenu() {
  const { mainNavigation } = useMainNavigation();
  return (
    <>
      <div className='mt-6 flex h-16 shrink-0 items-center lg:mt-0'>
        <div className='h-8 w-auto'>
          <Image
            alt='Taostats'
            height={20}
            width={130}
            src={imagePaths.logo.taostats}
          />
        </div>
      </div>
      <nav className='flex flex-1 flex-col'>
        <ul className='flex flex-1 flex-col gap-y-7'>
          {mainNavigation.map((item) => (
            <li
              className={cn(item.title === 'Developers' && 'mt-auto')}
              key={item.index}
            >
              <MenuSection
                index={item.index}
                menuItems={item.menuItems}
                title={item.title}
              />
            </li>
          ))}
        </ul>
      </nav>
    </>
  );
}

function MenuSection({ title, menuItems }: MainNavigationItemWithIndex) {
  const { closeSidebar } = useSidebar();

  return (
    <ul className='-mx-2 space-y-1'>
      {title ? (
        <li>
          <MenuSectionTitle title={title} />
        </li>
      ) : null}
      <ul className='-mx-2 space-y-1'>
        {menuItems.map((item) => (
          <MenuNavItem
            item={{
              ...item,
              onNavigate: () => {
                // Close sidebar when navigating
                closeSidebar();
              },
            }}
            key={item.index}
          />
        ))}
      </ul>
    </ul>
  );
}

function MenuSectionTitle({ title }: { title: string }) {
  return <div className='text-xs/6 uppercase text-gray-500'>{title}</div>;
}

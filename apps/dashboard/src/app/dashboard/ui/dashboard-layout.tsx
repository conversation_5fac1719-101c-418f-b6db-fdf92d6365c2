'use client';

import { usePathname } from 'next/navigation';
import type { Session } from 'next-auth';
import { signOut } from 'next-auth/react';
import { MetaDataBarContainer } from './metadata-bar-container';
import { ChainAndWalletContextProvider } from '@/contexts/chain-and-wallet/chain-and-wallet-context';

export const DashboardLayout = ({
  children,
  session,
}: {
  children: React.ReactNode;
  session: Session;
}) => {
  const pathname = usePathname();

  // Don't provide dashboard layout for set-recovery-email page
  if (pathname === '/login/pin/set-recovery-email') {
    return <>{children}</>;
  }

  return (
    <ChainAndWalletContextProvider>
      <MetaDataBarContainer session={session} signOut={signOut} />
      {children}
    </ChainAndWalletContextProvider>
  );
};

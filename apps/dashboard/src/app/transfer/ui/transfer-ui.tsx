import { memo } from 'react';
// import { BiCog } from 'react-icons/bi';
import { useRouter } from 'next/navigation';
import { z } from 'zod';
import { Button } from '@repo/ui/components';
// import { ReceiveUi } from './receive-ui';
import { cn } from '@repo/ui/lib';
import { SendTaoUi } from './send-tao-ui';
import { TransferAlphaUi } from './transfer-alpha-ui';
import { WalletPicker } from '@/components/wallet/wallet-picker';
import { AccountContextProvider } from '@/contexts/chain-and-wallet/account-context';

const tabSchema = z.enum(['transfer-alpha', 'send-tao']);
type Tab = z.infer<typeof tabSchema>;

export const TransferUi = memo(function TransferUi({
  tab,
}: {
  tab?: string[];
}) {
  const tabParam = tab?.[0];
  const tabResult = tabSchema.safeParse(tabParam);
  const tabLocal = tabResult.data ?? 'send-tao';
  return (
    <div className='flex flex-col gap-8'>
      <Header />
      <TabHeader tab={tabLocal} />
      <AccountContextProvider>
        {tabLocal === 'transfer-alpha' && <TransferAlphaUi />}
        {tabLocal === 'send-tao' && <SendTaoUi />}
        {/* {tab === 'receive' && <ReceiveUi />} */}
      </AccountContextProvider>
    </div>
  );
});

const Header = memo(function Header() {
  return (
    <div className='flex flex-row items-center justify-center gap-4 sm:justify-between'>
      <WalletPicker
        className='bg-input-primary rounded-full'
        containerClassName='w-full justify-between'
      />
      {/* <Button variant='cta3' size='sm'>
        <BiCog size={16} className='text-white/60' />
      </Button> */}
    </div>
  );
});

export const TabHeader = memo(function TabHeader({ tab }: { tab: Tab }) {
  return (
    <div className='-mx-6 flex flex-row'>
      <TabButton activeTab={tab} tab='send-tao' />
      <TabButton activeTab={tab} tab='transfer-alpha' />
    </div>
  );
});

const TabButton = memo(function TabButton({
  activeTab,
  tab,
}: {
  activeTab: Tab;
  tab: Tab;
}) {
  const router = useRouter();
  return (
    <Button
      variant='cta3'
      className={cn(
        ' bg-card flex-1 rounded-none border-0 border-b-2 text-sm transition-colors duration-700',
        tab === activeTab
          ? 'border-b-cta1 text-white'
          : 'hover:border-b-cta1/80 border-b-white/40 text-white/60 hover:text-white'
      )}
      onClick={() => {
        router.push(`/transfer/${tab}`);
      }}
    >
      {tab === 'transfer-alpha' ? 'Transfer Alpha' : 'Send Unstaked Tao'}
    </Button>
  );
});

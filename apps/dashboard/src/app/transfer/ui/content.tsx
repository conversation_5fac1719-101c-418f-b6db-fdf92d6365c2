'use client';

import { memo } from 'react';
import { Al<PERSON>, Card } from '@repo/ui/components';
import { TransferUi } from './transfer-ui';
import { ConnectWalletCard } from '@/components/wallet/connect-wallet-card';
import { useChainAndWalletContext } from '@/contexts/chain-and-wallet/chain-and-wallet-context';

export const TransferContent = memo(function TransferContent({
  tab,
}: {
  tab?: string[];
}) {
  const {
    state: { currentAccount },
  } = useChainAndWalletContext();
  return (
    <div className='mx-auto w-full max-w-4xl'>
      <Card>
        {!currentAccount ? <ConnectWalletUi /> : <TransferUi tab={tab} />}
      </Card>
    </div>
  );
});

const ConnectWalletUi = memo(function ConnectWalletUi() {
  return (
    <div className='flex flex-col gap-4'>
      <Alert
        type='info'
        description='You do not have a wallet connected. Click below to get started.'
      />
      <ConnectWalletCard />
    </div>
  );
});

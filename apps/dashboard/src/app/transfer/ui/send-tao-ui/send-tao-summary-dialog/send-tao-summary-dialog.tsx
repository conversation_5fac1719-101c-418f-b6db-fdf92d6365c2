import type { ReactNode } from 'react';
import { memo, useMemo } from 'react';
import { BigNumber } from 'bignumber.js';
import { useFormContext } from 'react-hook-form';
import { BiListPlus } from 'react-icons/bi';
import {
  AlertError,
  Button,
  Checkbox,
  Dialog,
  Flex,
  Spinner,
  TaoOrAlphaValueDisplay,
  Text,
} from '@repo/ui/components';
import { useSendTaoLogic } from './lib';
import { useSendTaoContext } from '@/app/transfer/ui/send-tao-ui/lib/context';
import { useSendTaoMaths } from '@/app/transfer/ui/send-tao-ui/lib/hooks';
import {
  formName,
  type SendTaoForm,
} from '@/app/transfer/ui/send-tao-ui/lib/types';
import { useAccountContext } from '@/contexts/chain-and-wallet/account-context';
import { useTransactionSuccessLogic } from '@/contexts/chain-and-wallet/use-transaction-success-logic';
import { truncateString } from '@/lib/utils';

export const SendTaoSummaryDialog = memo(function SendTaoSummaryDialog({
  closeFn,
  isOpen,
  fee,
  amountAdjustedBy,
}: {
  closeFn: () => void;
  isOpen: boolean;
  fee: number | null;
  amountAdjustedBy: number | null;
}) {
  const {
    state: { availableBalance },
  } = useAccountContext();

  const {
    handleConfirmClick: handleSendClick,
    isPending,
    txStatus,
    termsAccepted,
    setTermsAccepted,
  } = useSendTaoLogic();

  const { total } = useSendTaoContext();

  const { calculateSummary } = useSendTaoMaths();

  const { reset } = useFormContext();

  const resetSendTaoState = () => {
    reset();
  };

  const {
    fetchInfo,
    wasSuccessful,
    setWasSuccessful,
    renderMessageIfSuccessful,
  } = useTransactionSuccessLogic(() => {
    resetSendTaoState();
    closeFn();
  });

  const title = txStatus?.status ? txStatus.status : 'Sending Summary';

  const handleConfirmClick = async () => {
    const onSuccessCallback = () => {
      setWasSuccessful(true);
    };
    await handleSendClick({
      onSuccessCallback,
      doFullRefetchOnSuccess: false,
    });
  };

  const handleClose = () => {
    if (wasSuccessful) {
      void fetchInfo();
      resetSendTaoState();
      closeFn();
    } else {
      closeFn();
    }
  };

  const { totalWithFee, remainingBalance } = useMemo(
    () => calculateSummary(fee ?? 0),
    [calculateSummary, fee]
  );

  const isOverBalance = remainingBalance < 0;

  const isButtonDisabled = !termsAccepted || isOverBalance || isPending;

  return (
    <Dialog
      HeaderIcon={isPending ? undefined : BiListPlus}
      HeaderNode={
        isPending ? (
          <div className='flex h-12 items-center justify-center'>
            <Spinner className='h-8 w-8' />
          </div>
        ) : null
      }
      closeFn={handleClose}
      isOpen={isOpen}
      title={title}
      className='sm:w-[600px]'
    >
      {!isOpen ? (
        <div className='flex justify-center py-4'>
          <Spinner />
        </div>
      ) : (
        <>
          <Flex col>
            <div className='mb-2'>{renderMessageIfSuccessful()}</div>

            <TransactionsSummary amountAdjustedBy={amountAdjustedBy} />

            <SummaryRow
              label='Starting Balance'
              value={<SummaryTaoOrAlphaDisplay value={availableBalance} />}
            />

            <SummaryRow
              label='Sub Total'
              value={<SummaryTaoOrAlphaDisplay value={total} />}
            />

            <div className='flex flex-col'>
              {fee !== null ? (
                <SummaryRow
                  label='Estimated Fee'
                  value={<SummaryTaoOrAlphaDisplay value={fee} />}
                />
              ) : null}

              <SummaryRow
                label='Total'
                value={<SummaryTaoOrAlphaDisplay value={totalWithFee} />}
              />

              <SummaryRow
                label='Remaining Balance'
                value={
                  <SummaryTaoOrAlphaDisplay
                    value={remainingBalance}
                    className={
                      isOverBalance ? 'text-accent-2' : 'text-accent-1'
                    }
                  />
                }
              />

              {isOverBalance ? (
                <AlertError
                  description='The total amount including fee exceeds your remaining balance. Please make sure there is enough balance in your account to cover fees.'
                  className='mt-6'
                />
              ) : null}

              {wasSuccessful ? null : (
                <div className='my-6 flex flex-col gap-4'>
                  <div className='flex items-start gap-3'>
                    <Checkbox
                      id='terms'
                      checked={termsAccepted}
                      onCheckedChange={() => {
                        setTermsAccepted(!termsAccepted);
                      }}
                    />
                    <div className='grid gap-1.5 leading-none'>
                      <label htmlFor='terms' className='cursor-pointer'>
                        <Text level='buttonSmall' className='text-white/60'>
                          I understand and agree to the terms and conditions.
                        </Text>
                      </label>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </Flex>

          {txStatus?.message ? (
            <AlertError description={txStatus.message} />
          ) : null}
        </>
      )}

      {wasSuccessful ? null : (
        <div className='flex flex-col gap-4'>
          <Button
            className='w-full'
            disabled={isButtonDisabled}
            onClick={() => {
              void handleConfirmClick();
            }}
            size='lg'
            type='button'
            variant='cta2'
          >
            {isPending ? 'Confirming...' : 'Confirm'}
            <BiListPlus className='ml-2' size={20} />
          </Button>
        </div>
      )}
    </Dialog>
  );
});

const TransactionsSummary = memo(function TransactionsSummary({
  amountAdjustedBy,
}: {
  amountAdjustedBy: number | null;
}) {
  const { recipientCount } = useSendTaoContext();
  const { watch } = useFormContext<SendTaoForm>();

  const values = watch(formName);

  return (
    <div className='-mr-4 flex flex-col gap-2'>
      <div className='flex flex-row items-center justify-between pr-4 text-white/60'>
        <Text level='labelLarge' className='text-white/60'>
          {recipientCount} Recipient{recipientCount === 1 ? '' : 's'}
        </Text>
        <Text level='labelLarge' className='text-white/60'>
          Amount
        </Text>
      </div>
      <div className='scrollbar-thin scrollbar-track-rounded-full scrollbar-track-white/20 scrollbar-thumb-white/60 scrollbar-thumb-rounded-full hover:scrollbar-thumb-white/80 max-h-[30vh] overflow-y-auto pr-4'>
        {values.map((item, index) => (
          <TransferItemSummary
            key={`${item.recipient}-${item.amount}`}
            toAddress={item.recipient}
            amount={Number(item.amount)}
            isFirst={index === 0}
            amountAdjustedBy={amountAdjustedBy}
          />
        ))}
      </div>
    </div>
  );
});

const TransferItemSummary = ({
  toAddress,
  amount,
  isFirst,
  amountAdjustedBy,
}: {
  toAddress: string;
  amount: number;
  isFirst: boolean;
  amountAdjustedBy: number | null;
}) => {
  return (
    <div className='flex flex-col gap-2 border-b border-white/20 py-4'>
      {/* Main row */}
      <div className='flex flex-row items-center justify-between gap-4'>
        {/* First column */}
        <div className='w-[60%] min-w-0 truncate'>
          <div className='flex flex-row items-baseline gap-1'>
            <Text level='labelSmall' className='flex-shrink-0 text-white/60'>
              {truncateString(toAddress)}
            </Text>
          </div>
        </div>

        {/* Second column */}
        <div className='flex w-[40%] flex-row items-center justify-end text-white'>
          <TaoOrAlphaValueDisplay
            value={
              isFirst && amountAdjustedBy
                ? BigNumber(amountAdjustedBy + amount).toNumber()
                : amount
            }
            valueTextLevel='labelMedium'
            areDecimalsSmall
            iconClassName='text-xs'
            valueFormattingOptions={{
              minimumFractionDigits: 2,
              maximumFractionDigits: 5,
            }}
          />
        </div>
      </div>

      {/* Adjustment row */}
      {isFirst && amountAdjustedBy ? (
        <>
          <div className='flex flex-row items-center justify-between gap-4'>
            <div className='w-[60%] min-w-0 truncate'>
              <Text level='labelMedium' className='italic text-white/60'>
                Adjustment for fee
              </Text>
            </div>
            <div className='flex w-[40%] flex-row items-center justify-end'>
              <TaoOrAlphaValueDisplay
                value={amountAdjustedBy}
                valueTextLevel='labelMedium'
                colourClassName='text-white/60 italic'
                areDecimalsSmall
                iconClassName='text-xs'
                valueFormattingOptions={{
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 5,
                }}
              />
            </div>
          </div>
          <div className='flex flex-row items-center justify-between gap-4'>
            <div className='w-[60%] min-w-0 truncate'>
              <Text level='labelMedium' className='text-white/60'>
                {truncateString(toAddress)} Total
              </Text>
            </div>
            <div className='flex w-[40%] flex-row items-center justify-end'>
              <TaoOrAlphaValueDisplay
                value={amount}
                valueTextLevel='labelMedium'
                areDecimalsSmall
                iconClassName='text-xs'
                valueFormattingOptions={{
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 5,
                }}
              />
            </div>
          </div>
        </>
      ) : null}
    </div>
  );
};

const SummaryTaoOrAlphaDisplay = memo(function SummaryTaoOrAlphaDisplay({
  value,
  symbol,
  className,
}: {
  value: number;
  symbol?: string;
  className?: string;
}) {
  return (
    <TaoOrAlphaValueDisplay
      value={value}
      symbol={symbol}
      valueTextLevel='labelMedium'
      areDecimalsSmall
      iconClassName='text-xs'
      valueFormattingOptions={{
        minimumFractionDigits: 2,
        maximumFractionDigits: 5,
      }}
      colourClassName={className}
    />
  );
});

const SummaryRow = memo(function SummaryRow({
  label,
  value,
}: {
  label: string;
  value: ReactNode;
}) {
  return (
    <div className='flex flex-row items-center justify-between gap-4 border-b border-white/20 py-4'>
      <Text level='labelLarge' className='text-white/60'>
        {label}
      </Text>
      {typeof value === 'string' ? (
        <Text level='labelMedium' className='text-white/60'>
          {value}
        </Text>
      ) : (
        value
      )}
    </div>
  );
});

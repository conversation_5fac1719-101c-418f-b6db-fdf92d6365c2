import { useState } from 'react';
import { BigNumber } from 'bignumber.js';
import { useFormContext } from 'react-hook-form';
import { notify } from '@repo/ui/lib';
import type { SendTaoForm } from '@/app/transfer/ui/send-tao-ui/lib/types';
import { useChainAndWalletContext } from '@/contexts/chain-and-wallet/chain-and-wallet-context';
import { useTransactionSubmitter } from '@/contexts/chain-and-wallet/use-transaction-submitter';
import { log } from '@/lib/utils';
import { useChainAmountConverter } from '@/lib/utils/conversion/conversion-utils';

export const useTransactionBuilder = () => {
  const {
    state: { api },
  } = useChainAndWalletContext();

  const { getValues } = useFormContext<SendTaoForm>();
  const values = getValues();
  const { convertAmount } = useChainAmountConverter();

  const buildTransaction = () => {
    if (!api) {
      notify.error('Could not find connection');
      return null;
    }

    log('DEBUG', { values });

    const txs = values.send.map((s) => {
      const amount = convertAmount(BigNumber(s.amount).toNumber()).toNumber();

      return api.tx.balances.transferAllowDeath(s.recipient, amount);
    });

    log('DEBUG', { txsLength: txs.length });

    if (txs.length === 0) {
      notify.error('No transactions to submit');
      return null;
    }

    // If there is only one transaction, then don't batch it
    // Otherwise batch all the transactions
    const tx = txs.length > 1 ? api.tx.utility.batchAll(txs) : txs[0];

    return { tx };
  };

  return {
    buildTransaction,
  };
};

export const useSendTaoLogic = () => {
  const { buildTransaction } = useTransactionBuilder();
  const {
    state: { api },
  } = useChainAndWalletContext();
  const { submitTx, isPending, txStatus } = useTransactionSubmitter();

  const [termsAccepted, setTermsAccepted] = useState(false);

  const handleConfirmClick = async ({
    onSuccessCallback,
    doFullRefetchOnSuccess = true,
  }: {
    onSuccessCallback?: () => void;
    doFullRefetchOnSuccess?: boolean;
  }) => {
    if (!api) {
      notify.error('Could not find connection');
      return;
    }

    if (!termsAccepted) {
      notify.error('Terms not accepted');
      return;
    }

    const buildTransactionResult = buildTransaction();
    if (!buildTransactionResult) {
      notify.error('Could not build transaction');
      return;
    }

    // Submit the transaction
    await submitTx(buildTransactionResult.tx, {
      onSuccessCallback,
      doFullRefetchOnSuccess,
    });
  };

  return {
    termsAccepted,
    setTermsAccepted,
    handleConfirmClick,
    isPending,
    txStatus,
  };
};

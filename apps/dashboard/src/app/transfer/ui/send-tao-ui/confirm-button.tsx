import { memo, useState } from 'react';
import { BigNumber } from 'bignumber.js';
import { useFormContext } from 'react-hook-form';
import { BiSend } from 'react-icons/bi';
import { Button } from '@repo/ui/components';
import { notify } from '@repo/ui/lib';
import { useSendTaoContext } from './lib/context';
import { useSendTaoMaths } from './lib/hooks';
import { formName, type SendTaoForm } from './lib/types';
import { useTransactionBuilder } from './send-tao-summary-dialog/lib';
import { SendTaoSummaryDialog } from './send-tao-summary-dialog/send-tao-summary-dialog';
import { useTransactionSubmitter } from '@/contexts/chain-and-wallet/use-transaction-submitter';

export const ConfirmButton = memo(function ConfirmButton() {
  const { trigger, setValue, getValues } = useFormContext<SendTaoForm>();
  const { buildTransaction } = useTransactionBuilder();
  const { calculateTransactionFee } = useTransactionSubmitter();
  const [isBuildingTransaction, setIsBuildingTransaction] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [amountAdjustedBy, setAmountAdjustedBy] = useState<number | null>(null);
  const [calculatedFee, setCalculatedFee] = useState<number | null>(null);
  const { recipientCount } = useSendTaoContext();

  const { calculateSummary } = useSendTaoMaths();

  const handleConfirmClick = async () => {
    const isValid = await trigger();
    if (!isValid) {
      notify.error('Please check all fields have a value');
      return;
    }

    try {
      setIsBuildingTransaction(true);
      const buildTransactionResult = buildTransaction();
      if (!buildTransactionResult?.tx) {
        notify.error('Could not build transaction');
        return;
      }
      const fee = await calculateTransactionFee(buildTransactionResult.tx);
      setCalculatedFee(fee);

      const { amountOver } = calculateSummary(fee ?? 0);

      let amountAdjustedByLocal: number | null = null;
      if (amountOver > 0 && recipientCount === 1) {
        // There isn't enough balance to cover amount and fee
        // There is only one recipient, so take the amount over from the amount being sent
        const currentValues = getValues(formName);
        const firstTransaction = currentValues[0];
        const firstAmount = BigNumber(firstTransaction.amount);
        if (firstAmount.gt(amountOver)) {
          // There is enough balance to cover the amount and fee
          // Adjust the amount to be sent to cover the fee
          setValue(
            `${formName}.0.amount`,
            firstAmount.minus(amountOver).toString()
          );
          amountAdjustedByLocal = amountOver;
        }
      }

      setIsOpen(true);
      setAmountAdjustedBy(amountAdjustedByLocal);
    } catch (e) {
      notify.error('There was an error when building the transaction');
      console.error(e);
    } finally {
      setIsBuildingTransaction(false);
    }
  };

  const closeDialog = () => {
    setIsOpen(false);
  };

  const isDisabled = isBuildingTransaction;

  return (
    <>
      <Button
        disabled={isDisabled}
        variant='cta2'
        size='lg'
        onClick={() => {
          void handleConfirmClick();
        }}
      >
        Calculate Fee & Confirm
        <BiSend className='ml-2 text-lg' />
      </Button>
      <SendTaoSummaryDialog
        isOpen={isOpen}
        closeFn={closeDialog}
        fee={calculatedFee}
        amountAdjustedBy={amountAdjustedBy}
      />
    </>
  );
});

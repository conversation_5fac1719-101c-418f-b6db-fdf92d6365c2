import type { ReactNode } from 'react';
import { Fragment, memo, useMemo } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { BigNumber } from 'bignumber.js';
import {
  Controller,
  FormProvider,
  useForm,
  useFormContext,
} from 'react-hook-form';
import { BiPlus, BiRefresh, BiTrashAlt, BiWalletAlt } from 'react-icons/bi';
import { CgCreditCard } from 'react-icons/cg';
import {
  AlertError,
  Button,
  Card,
  Input,
  Skeleton,
  TaoOrAlphaValueDisplay,
  Text,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@repo/ui/components';
import { notify } from '@repo/ui/lib';
import { ConfirmButton } from './confirm-button';
import { SendTaoContextProvider, useSendTaoContext } from './lib/context';
import type { SendTaoForm } from './lib/types';
import { SendTaoSchema, formName } from './lib/types';
import { useAccountContext } from '@/contexts/chain-and-wallet/account-context';
import { TAO_CURRENCY } from '@/lib/utils';

export const SendTaoUi = memo(function SendTaoUi() {
  const form = useForm<SendTaoForm>({
    mode: 'onChange',
    resolver: zodResolver(SendTaoSchema),
    defaultValues: {
      send: [{ amount: '', recipient: '' }],
    },
  });
  return (
    <FormProvider {...form}>
      <SendTaoContextProvider>
        <SendTaoUiContent />
      </SendTaoContextProvider>
    </FormProvider>
  );
});

const SendTaoUiContent = memo(function SendTaoUiContent() {
  const {
    state: { availableBalance, loading },
    fetchInfo,
  } = useAccountContext();

  const { total, recipientCount, fieldArray } = useSendTaoContext();

  const {
    control,
    formState: { errors },
    setValue,
    getValues,
  } = useFormContext<SendTaoForm>();

  const { fields, append, remove } = fieldArray;

  const remainingBalance = useMemo(() => {
    return BigNumber(availableBalance - total)
      .dp(5)
      .toNumber();
  }, [availableBalance, total]);

  const handleQuickEnterButtonClick = (
    percentageAmount: number,
    leaveSomeDust = false
  ) => {
    if (remainingBalance <= 0) {
      notify.error(
        'You have no remaining balance. Please adjust your amounts and try again.'
      );
      return;
    }

    const amountToSet = BigNumber(remainingBalance)
      .times(percentageAmount / 100)
      .minus(leaveSomeDust ? 0.01 : 0);

    if (amountToSet.lt(0)) {
      notify.error('The result would be less than 0, restricting to 0.');
    }

    const amountToSetStr = amountToSet.lt(0) ? '0' : amountToSet.toString();
    const currentValues = getValues(formName);

    // If there is only one recipient, set the amount to the max amount
    if (recipientCount === 1 && currentValues[0].amount === '') {
      setValue(`${formName}.0.amount`, amountToSetStr);
    } else {
      // Get the next recipient that has no amount
      const nextRecipientIndex = currentValues.findIndex(
        (field) => !field.amount
      );

      if (nextRecipientIndex > -1) {
        const key = `${formName}.${nextRecipientIndex}.amount` as const;
        setValue(key, amountToSetStr);
      } else {
        append({ amount: amountToSetStr, recipient: '' });
        notify.success('Added new recipient with max amount');
      }
    }
  };

  return (
    <div className='animate-in fade-in flex flex-col gap-4 duration-500'>
      <div className='flex flex-col items-center gap-2 sm:flex-row sm:justify-between'>
        <div className='flex flex-row items-center gap-2'>
          <Text as='label' level='labelMedium' className='text-white/60'>
            Available Balance:
          </Text>
          <TaoOrAlphaValueDisplay
            valueTextLevel='headerMedium'
            value={availableBalance}
            valueFormattingOptions={{
              minimumFractionDigits: 0,
              maximumFractionDigits: 5,
            }}
            areDecimalsSmall
            iconClassName='text-[12px]'
          />
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={() => {
                    void fetchInfo();
                  }}
                >
                  {loading ? (
                    <BiRefresh className='size-5 animate-spin' />
                  ) : (
                    <BiRefresh className='size-5' />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent className='max-w-[300px]'>
                Refresh your balance from the chain.
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <div className='flex flex-row gap-2'>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <Button
                  variant='cta3'
                  size='sm'
                  onClick={() => {
                    handleQuickEnterButtonClick(100, true);
                  }}
                >
                  Max
                </Button>
              </TooltipTrigger>
              <TooltipContent className='max-w-[300px]'>
                This will set the next available row to 100% of the remaining
                amount less 0.01{TAO_CURRENCY}.
              </TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger>
                <Button
                  variant='cta3'
                  size='sm'
                  onClick={() => {
                    handleQuickEnterButtonClick(100);
                  }}
                >
                  100%
                </Button>
              </TooltipTrigger>
              <TooltipContent className='max-w-[300px]'>
                This will set the next available row to 100% of the remaining
                amount.
              </TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger>
                <Button
                  variant='cta3'
                  size='sm'
                  onClick={() => {
                    handleQuickEnterButtonClick(50);
                  }}
                >
                  50%
                </Button>
              </TooltipTrigger>
              <TooltipContent className='max-w-[300px]'>
                This will set the next available row to 50% of the remaining
                amount.
              </TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger>
                <Button
                  variant='cta3'
                  size='sm'
                  onClick={() => {
                    handleQuickEnterButtonClick(25);
                  }}
                >
                  25%
                </Button>
              </TooltipTrigger>
              <TooltipContent className='max-w-[300px]'>
                This will set the next available row to 25% of the remaining
                amount.
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      <div className='flex flex-col gap-2'>
        {/* Scrollable Fields Container */}
        <div className='scrollbar-thin scrollbar-thumb-neutral-600 scrollbar-track-neutral-800 scrollbar-track-rounded-full scrollbar-thumb-rounded-full max-h-[500px] overflow-y-auto'>
          <div className='grid grid-cols-[1fr_auto] gap-2 sm:grid-cols-[1fr_auto_1fr_auto]'>
            {fields.map((item, i) => (
              <Fragment key={item.id}>
                {/* Add separator above each row except the first */}
                {i > 0 && (
                  <div className='col-span-2 my-2 border-t-2 border-white/20 sm:hidden' />
                )}

                <Controller
                  name={`${formName}.${i}.amount`}
                  control={control}
                  render={({ field }) => (
                    <Input
                      iconLeftConfig={{
                        Icon: CgCreditCard,
                        className: 'sm:size-5',
                      }}
                      placeholder={`Enter ${TAO_CURRENCY} amount e.g. 100`}
                      inputId={`amount-${i}`}
                      onChange={field.onChange}
                      value={field.value}
                      inputClassName='py-2 sm:pl-12 focus:outline-accent-1 transition-[outline] duration-500'
                      error={errors.send?.[i]?.amount?.message}
                    />
                  )}
                />

                <Text as='label' level='labelLarge' className='self-center'>
                  to
                </Text>

                <Controller
                  name={`${formName}.${i}.recipient`}
                  control={control}
                  render={({ field }) => (
                    <Input
                      iconLeftConfig={{
                        Icon: BiWalletAlt,
                        className: 'sm:size-5',
                      }}
                      placeholder='To address e.g. 5CFHgzvoF3MyomyFGgvBkVDWGzW5xE28RafSWxACT2ZtFkag'
                      inputId={`recipient-${i}`}
                      onChange={field.onChange}
                      value={field.value}
                      inputClassName='py-2 sm:pl-12 focus:outline-accent-1 transition-[outline] duration-500'
                      error={errors.send?.[i]?.recipient?.message}
                      onKeyDown={(e) => {
                        if (i === fields.length - 1) {
                          if (e.key === 'Enter' || e.key === 'Tab') {
                            e.preventDefault();
                            append({ amount: '', recipient: '' });
                            setTimeout(() => {
                              document
                                .getElementById(`amount-${i + 1}`)
                                ?.focus();
                            }, 0);
                          }
                        }
                      }}
                    />
                  )}
                />

                <div className='flex flex-row items-center justify-end'>
                  <Button
                    tabIndex={-1}
                    variant='ghost'
                    className='hover:border-accent-1/60 mr-1 border text-white/20 transition-colors duration-500 hover:text-white/80'
                    size='sm'
                    onClick={() => {
                      remove(i);
                    }}
                  >
                    <BiTrashAlt className='size-5' />
                  </Button>
                </div>
              </Fragment>
            ))}
          </div>
        </div>
      </div>

      <div className='flex flex-row justify-end gap-2'>
        <Button
          className='flex flex-row items-center gap-2'
          variant='cta3'
          size='sm'
          onClick={() => {
            append(
              Array.from({ length: 5 }).map(() => ({
                amount: '',
                recipient: '',
              }))
            );
          }}
        >
          Add 5 rows
          <BiPlus className='ml-1 size-4' />
        </Button>
        <Button
          variant='cta3'
          size='sm'
          onClick={() => {
            append({ amount: '', recipient: '' });
          }}
        >
          Add row
          <BiPlus className='ml-1 size-4' />
        </Button>
      </div>

      <Card className='bg-input-primary p-2'>
        <div className='flex flex-col gap-2 sm:flex-row sm:gap-5'>
          <div className='flex flex-1 flex-col'>
            <SummaryRow label='Recipients' value={recipientCount} />
            <SummaryRow
              label='Average/Recipient'
              value={
                <TaoDisplay
                  value={recipientCount > 0 ? total / recipientCount : 0}
                />
              }
            />
          </div>
          <div className='bg-accent-1/20 flex flex-1 flex-col rounded-lg p-2'>
            <SummaryRow
              label='Starting Balance'
              value={
                loading ? (
                  <Skeleton className='h-5 w-16' />
                ) : (
                  <TaoDisplay value={availableBalance} />
                )
              }
            />
            <SummaryRow label='Total' value={<TaoDisplay value={total} />} />
            <SummaryRow
              label='Remaining Balance'
              value={
                loading ? (
                  <Skeleton className='h-5 w-16' />
                ) : (
                  <TaoDisplay
                    value={remainingBalance}
                    className={
                      remainingBalance < 0 ? 'text-accent-2' : 'text-accent-1'
                    }
                  />
                )
              }
            />
          </div>
        </div>
      </Card>

      {errors.send ? (
        <AlertError description='Please check all fields have a value' />
      ) : null}

      {remainingBalance < 0 ? (
        <AlertError description='There is not enough balance to send the selected amounts. Please adjust and try again.' />
      ) : null}

      <ConfirmButton />
    </div>
  );
});

const SummaryRow = memo(function SummaryRow({
  label,
  value,
}: {
  label: string;
  value: ReactNode;
}) {
  return (
    <div className='hover:bg-accent-1/10 flex flex-row justify-between gap-2 rounded-lg p-3 transition-colors duration-700'>
      <Text as='label' level='labelMedium' className='text-white/60'>
        {label}
      </Text>
      {value}
    </div>
  );
});

const TaoDisplay = memo(function TaoDisplay({
  value,
  className,
}: {
  value: number;
  className?: string;
}) {
  return (
    <TaoOrAlphaValueDisplay
      value={value}
      valueFormattingOptions={{
        minimumFractionDigits: 2,
        maximumFractionDigits: 5,
      }}
      areDecimalsSmall
      valueTextLevel='labelLarge'
      iconClassName='text-[10px]'
      colourClassName={className}
    />
  );
});

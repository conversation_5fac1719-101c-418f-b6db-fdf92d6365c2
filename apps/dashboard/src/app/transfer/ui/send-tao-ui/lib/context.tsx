'use client';

import { createContext, useContext, useMemo } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import type { SendTaoForm } from './types';
import { formName } from './types';

const useSendTaoFields = () => {
  const { control } = useFormContext<SendTaoForm>();

  return useFieldArray({
    control,
    name: formName,
  });
};

type SendTaoContextType = {
  fieldArray: ReturnType<typeof useSendTaoFields>;
  total: number;
  recipientCount: number;
};

export const SendTaoContext = createContext<SendTaoContextType | undefined>(
  undefined
);

export const SendTaoContextProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const { watch } = useFormContext<SendTaoForm>();
  const fieldArray = useSendTaoFields();
  const { fields } = fieldArray;

  const amounts = watch(
    fields.map((_, index) => `${formName}.${index}.amount` as const)
  );

  const total = useMemo(() => {
    return amounts.reduce((acc, amount) => {
      const value = amount ? Number(amount) : 0;
      return isNaN(value) ? acc : acc + value;
    }, 0);
  }, [amounts]);

  const contextValue = useMemo(() => {
    return {
      total,
      recipientCount: amounts.length,
      fieldArray,
    };
  }, [total, amounts, fieldArray]);

  return (
    <SendTaoContext.Provider value={contextValue}>
      {children}
    </SendTaoContext.Provider>
  );
};

export const useSendTaoContext = () => {
  const context = useContext(SendTaoContext);
  if (!context) {
    throw new Error(
      'useSendTaoContext must be used within a SendTaoContextProvider'
    );
  }
  return context;
};

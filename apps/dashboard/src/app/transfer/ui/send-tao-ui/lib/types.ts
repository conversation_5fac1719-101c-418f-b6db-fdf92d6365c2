import { z } from 'zod';

export const formName = 'send';

export const SendTaoSchema = z.object({
  send: z.array(
    z.object({
      amount: z
        .string()
        .refine((val) => !isNaN(Number(val)) && Number(val) > 0, {
          message: 'Amount must be a positive number',
        }),
      recipient: z
        .string()
        .min(1, { message: 'Recipient address is required' }),
    })
  ),
});

export type SendTaoForm = z.infer<typeof SendTaoSchema>;

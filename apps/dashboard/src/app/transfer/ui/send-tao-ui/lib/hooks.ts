import { useCallback } from 'react';
import { BigNumber } from 'bignumber.js';
import { useSendTaoContext } from './context';
import { useAccountContext } from '@/contexts/chain-and-wallet/account-context';

export const useSendTaoMaths = () => {
  const {
    state: { availableBalance },
  } = useAccountContext();

  const { total } = useSendTaoContext();

  const calculateSummary = useCallback(
    (fee: number) => {
      const totalWithFee = BigNumber(fee).plus(total).toNumber();

      const remainingBalance = BigNumber(availableBalance - totalWithFee)
        .dp(5)
        .toNumber();

      const amountOver =
        remainingBalance < 0 ? BigNumber(remainingBalance).abs().toNumber() : 0;

      return {
        totalWithFee,
        remainingBalance,
        amountOver,
      };
    },
    [availableBalance, total]
  );

  return { calculateSummary };
};

import type { ReactNode } from 'react';
import { memo, useMemo } from 'react';
import { BigNumber } from 'bignumber.js';
import { BiListPlus } from 'react-icons/bi';
import {
  AlertError,
  Button,
  Checkbox,
  Dialog,
  Flex,
  Spinner,
  TaoOrAlphaValueDisplay,
  Text,
} from '@repo/ui/components';
import { useTransferAlphaLogic } from './lib';
import { useTransferAlphaContext } from '@/app/transfer/ui/transfer-alpha-ui/lib/context';
import { useTransactionSuccessLogic } from '@/contexts/chain-and-wallet/use-transaction-success-logic';
import { useSubnetLookup } from '@/lib/hooks/global-hooks';
import { truncateString } from '@/lib/utils';

export const TransferAlphaSummaryDialog = memo(
  function TransferAlphaSummaryDialog({
    closeFn,
    isOpen,
    fee,
  }: {
    closeFn: () => void;
    isOpen: boolean;
    fee: number | null;
  }) {
    const { transferAlphaStates, resetTransferAlphaState } =
      useTransferAlphaContext();
    const {
      handleConfirmClick: handleSendClick,
      isPending,
      txStatus,
      termsAccepted,
      setTermsAccepted,
    } = useTransferAlphaLogic();
    const {
      fetchInfo,
      wasSuccessful,
      setWasSuccessful,
      renderMessageIfSuccessful,
    } = useTransactionSuccessLogic(() => {
      resetTransferAlphaState();
      closeFn();
    });

    const title = txStatus?.status ? txStatus.status : 'Transfer Summary';

    const handleConfirmClick = async () => {
      const onSuccessCallback = () => {
        setWasSuccessful(true);
      };
      await handleSendClick({
        onSuccessCallback,
        doFullRefetchOnSuccess: false,
      });
    };

    const total = useMemo(() => {
      return BigNumber(fee ?? 0)
        .plus(
          transferAlphaStates.reduce((acc, item) => {
            return acc.plus(BigNumber(item.amount));
          }, new BigNumber(0))
        )
        .toNumber();
    }, [fee, transferAlphaStates]);

    const handleClose = () => {
      if (wasSuccessful) {
        void fetchInfo();
        resetTransferAlphaState();
        closeFn();
      } else {
        closeFn();
      }
    };

    return (
      <Dialog
        HeaderIcon={isPending ? undefined : BiListPlus}
        HeaderNode={
          isPending ? (
            <div className='flex h-12 items-center justify-center'>
              <Spinner className='h-8 w-8' />
            </div>
          ) : null
        }
        closeFn={handleClose}
        isOpen={isOpen}
        title={title}
        className='sm:w-[600px]'
      >
        {!isOpen ? (
          <div className='flex justify-center py-4'>
            <Spinner />
          </div>
        ) : (
          <>
            <Flex col className='gap-2'>
              {renderMessageIfSuccessful()}

              <TransferSummary />

              <Flex col>
                {fee !== null ? (
                  <SummaryRow
                    label='Fee'
                    value={<SummaryTaoOrAlphaDisplay value={fee} />}
                  />
                ) : null}

                <SummaryRow
                  label='Total'
                  value={<SummaryTaoOrAlphaDisplay value={total} />}
                />

                {wasSuccessful ? null : (
                  <div className='mb-6 mt-6 flex flex-col gap-4'>
                    <div className='flex items-start gap-3'>
                      <Checkbox
                        id='terms'
                        checked={termsAccepted}
                        onCheckedChange={() => {
                          setTermsAccepted(!termsAccepted);
                        }}
                      />
                      <div className='grid gap-1.5 leading-none'>
                        <label htmlFor='terms' className='cursor-pointer'>
                          <Text level='buttonSmall' className='text-white/60'>
                            I understand and agree to the terms and conditions.
                          </Text>
                        </label>
                      </div>
                    </div>
                  </div>
                )}
              </Flex>
            </Flex>

            {txStatus?.message ? (
              <AlertError description={txStatus.message} />
            ) : null}
          </>
        )}

        {wasSuccessful ? null : (
          <div className='flex flex-col gap-4'>
            <Button
              className='w-full'
              disabled={isPending || !termsAccepted}
              onClick={() => {
                void handleConfirmClick();
              }}
              size='lg'
              type='button'
              variant='cta2'
            >
              {isPending ? 'Confirming...' : 'Confirm'}
              <BiListPlus className='ml-2' size={20} />
            </Button>
          </div>
        )}
      </Dialog>
    );
  }
);

const TransferSummary = memo(function TransferSummary() {
  const { transferAlphaStates: sendState } = useTransferAlphaContext();
  return (
    <div className='flex flex-col gap-2'>
      <div className='flex flex-row items-center justify-between text-white/60'>
        <Text level='labelLarge' className='text-white/60'>
          {sendState.length} Recipient{sendState.length === 1 ? '' : 's'}
        </Text>
        <Text level='labelLarge' className='text-white/60'>
          Amount
        </Text>
      </div>
      <div className='scrollbar-thin scrollbar-track-rounded-full scrollbar-track-white/20 scrollbar-thumb-white scrollbar-thumb-rounded-full hover:scrollbar-thumb-white/80 -pr-4 -mr-4 max-h-[30vh] overflow-y-auto pr-2'>
        {sendState.map((item) => (
          <TransferItemSummary
            key={`${item.subnetId}-${item.toAddress}-${item.validatorAddressFrom}-${item.amount}`}
            toAddress={item.toAddress}
            amount={item.amount}
            subnetId={item.subnetId}
          />
        ))}
      </div>
    </div>
  );
});

const TransferItemSummary = ({
  toAddress,
  amount,
  subnetId,
}: {
  toAddress: string;
  amount: string;
  subnetId?: number;
}) => {
  const { getSubnetSymbol } = useSubnetLookup();

  if (subnetId === undefined) {
    return null;
  }

  return (
    <div className='flex flex-row items-center justify-between gap-4 border-b border-white/20 py-4'>
      {/* First column */}
      <div className='w-[60%] min-w-0 truncate'>
        <div className='flex flex-row items-baseline gap-1 text-white/60'>
          <Text level='labelSmall' className='flex-shrink-0 text-white/60'>
            {truncateString(toAddress)}
          </Text>
        </div>
      </div>

      {/* Second column */}
      <div className='flex w-[40%] flex-row items-center justify-end text-white'>
        <SummaryTaoOrAlphaDisplay
          value={BigNumber(amount).toNumber()}
          symbol={getSubnetSymbol(subnetId)}
        />
      </div>
    </div>
  );
};

const SummaryTaoOrAlphaDisplay = memo(function SummaryTaoOrAlphaDisplay({
  value,
  symbol,
}: {
  value: number;
  symbol?: string;
}) {
  return (
    <TaoOrAlphaValueDisplay
      value={value}
      symbol={symbol}
      valueTextLevel='labelMedium'
      areDecimalsSmall
      iconClassName='text-xs'
      valueFormattingOptions={{
        minimumFractionDigits: 2,
        maximumFractionDigits: 5,
      }}
    />
  );
});

const SummaryRow = memo(function SummaryRow({
  label,
  value,
}: {
  label: string;
  value: ReactNode;
}) {
  return (
    <div className='flex flex-row items-center justify-between gap-4 border-b border-white/20 py-4'>
      <Text level='labelLarge' className='text-white/60'>
        {label}
      </Text>
      {typeof value === 'string' ? (
        <Text level='labelMedium' className='text-white/60'>
          {value}
        </Text>
      ) : (
        value
      )}
    </div>
  );
});

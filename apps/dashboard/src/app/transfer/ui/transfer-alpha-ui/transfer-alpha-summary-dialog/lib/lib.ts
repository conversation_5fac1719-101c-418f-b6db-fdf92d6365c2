import { useState } from 'react';
import { BigNumber } from 'bignumber.js';
import { notify } from '@repo/ui/lib';
import { useTransferAlphaContext } from '@/app/transfer/ui/transfer-alpha-ui/lib/context';
import { useChainAndWalletContext } from '@/contexts/chain-and-wallet/chain-and-wallet-context';
import { useTransactionSubmitter } from '@/contexts/chain-and-wallet/use-transaction-submitter';
import { log } from '@/lib/utils';
import { useChainAmountConverter } from '@/lib/utils/conversion/conversion-utils';

export const useTransactionBuilder = () => {
  const {
    state: { currentAccount, api },
  } = useChainAndWalletContext();

  const { transferAlphaStates } = useTransferAlphaContext();
  const { convertAmount } = useChainAmountConverter();

  const buildTransaction = () => {
    if (!api) {
      notify.error('Could not find connection');
      return null;
    }

    log('DEBUG', { sendState: transferAlphaStates });

    const txs = transferAlphaStates
      .map((s) => {
        const alphaAmount = convertAmount(
          BigNumber(s.amount).toNumber()
        ).toNumber();

        // If there's a destination validator address and it is different to the source validator address, then we need to move and then transfer
        if (
          s.validatorAddressTo &&
          s.validatorAddressFrom !== s.validatorAddressTo
        ) {
          const innerTxs = [
            api.tx.subtensorModule.moveStake(
              s.validatorAddressFrom,
              s.validatorAddressTo,
              s.subnetId,
              s.subnetId,
              alphaAmount
            ),
          ];

          // Only need to transfer if the destination address is different the source address
          if (s.toAddress !== currentAccount?.address) {
            innerTxs.push(
              api.tx.subtensorModule.transferStake(
                s.toAddress,
                s.validatorAddressTo,
                s.subnetId,
                s.subnetId,
                alphaAmount
              )
            );
          }

          return innerTxs;
        }

        // If there's no destination validator address, or the destination validator address is the same as the source validator address, then we just transfer
        return [
          api.tx.subtensorModule.transferStake(
            s.toAddress,
            s.validatorAddressFrom,
            s.subnetId,
            s.subnetId,
            alphaAmount
          ),
        ];
      })
      .flat();

    log('DEBUG', { txsLength: txs.length });

    if (txs.length === 0) {
      notify.error('No transactions to submit');
      return null;
    }

    // If there is only one transaction, then don't batch it
    // Otherwise batch all the transactions
    const tx = txs.length > 1 ? api.tx.utility.batchAll(txs) : txs[0];

    return { tx };
  };

  return {
    buildTransaction,
  };
};

export const useTransferAlphaLogic = () => {
  const { buildTransaction } = useTransactionBuilder();
  const {
    state: { api },
  } = useChainAndWalletContext();
  const { submitTx, isPending, txStatus } = useTransactionSubmitter();

  const [termsAccepted, setTermsAccepted] = useState(false);

  const handleConfirmClick = async ({
    onSuccessCallback,
    doFullRefetchOnSuccess = true,
  }: {
    onSuccessCallback?: () => void;
    doFullRefetchOnSuccess?: boolean;
  }) => {
    if (!api) {
      notify.error('Could not find connection');
      return;
    }

    if (!termsAccepted) {
      notify.error('Terms not accepted');
      return;
    }

    const buildTransactionResult = buildTransaction();
    if (!buildTransactionResult) {
      notify.error('Could not build transaction');
      return;
    }

    // Submit the transaction
    await submitTx(buildTransactionResult.tx, {
      onSuccessCallback,
      doFullRefetchOnSuccess,
    });
  };

  return {
    termsAccepted,
    setTermsAccepted,
    handleConfirmClick,
    isPending,
    txStatus,
  };
};

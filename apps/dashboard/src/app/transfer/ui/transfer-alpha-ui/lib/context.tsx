'use client';

import {
  createContext,
  useCallback,
  useContext,
  useMemo,
  useState,
} from 'react';
import { useValidatorBalances } from '@/lib/hooks/validator-hooks';

type TransferAlphaState = {
  amount: string;
  subnetId?: number;
  toAddress: string;
  validatorAddressFrom: string;
  validatorAddressTo: string;
};

type TransferAlphaContextType = {
  transferAlphaStates: TransferAlphaState[];
  resetTransferAlphaState: () => void;
  handleUpdateAmount: (index: number, amount: string) => void;
  handleUpdateSubnetId: (index: number, subnetId: number) => void;
  handleUpdateToAddress: (index: number, toAddress: string) => void;
  handleUpdateValidatorAddressFrom: (
    index: number,
    validatorAddressFrom: string
  ) => void;
  handleUpdateValidatorAddressTo: (
    index: number,
    validatorAddressTo: string
  ) => void;
  validatorBalancesHelper: ReturnType<typeof useValidatorBalances>;
  isDestinationValidatorShowing: boolean;
  handleSetIsDestinationValidatorShowing: (
    isDestinationValidatorShowing: boolean
  ) => void;
};

export const TransferAlphaContext = createContext<
  TransferAlphaContextType | undefined
>(undefined);

export const TransferAlphaContextProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const validatorBalancesHelper = useValidatorBalances();

  const transferAlphaState = useTransferAlphaState();

  const [isDestinationValidatorShowing, setIsDestinationValidatorShowing] =
    useState(false);

  const handleSetIsDestinationValidatorShowing = useCallback(
    (newIsDestinationValidatorShowing: boolean) => {
      setIsDestinationValidatorShowing(newIsDestinationValidatorShowing);
      if (
        newIsDestinationValidatorShowing &&
        !transferAlphaState.state[0].validatorAddressTo
      ) {
        transferAlphaState.handleUpdateValidatorAddressTo(
          0,
          transferAlphaState.state[0].validatorAddressFrom
        );
      }
    },
    [setIsDestinationValidatorShowing, transferAlphaState]
  );

  const contextValue = useMemo(() => {
    const { state: transferAlphaStates, ...rest } = transferAlphaState;
    return {
      transferAlphaStates,
      ...rest,
      validatorBalancesHelper,
      isDestinationValidatorShowing,
      handleSetIsDestinationValidatorShowing,
    };
  }, [
    transferAlphaState,
    validatorBalancesHelper,
    isDestinationValidatorShowing,
    handleSetIsDestinationValidatorShowing,
  ]);

  return (
    <TransferAlphaContext.Provider value={contextValue}>
      {children}
    </TransferAlphaContext.Provider>
  );
};

export const useTransferAlphaContext = () => {
  const context = useContext(TransferAlphaContext);
  if (!context) {
    throw new Error(
      'useTransferAlphaContext must be used within a TransferAlphaContextProvider'
    );
  }
  return context;
};

const defaultState: TransferAlphaState[] = [
  {
    amount: '',
    subnetId: undefined,
    toAddress: '',
    validatorAddressFrom: '',
    validatorAddressTo: '',
  },
];

const useTransferAlphaState = () => {
  const validatorBalancesHelper = useValidatorBalances();
  const [state, setState] = useState<TransferAlphaState[]>(defaultState);

  const handleUpdateState = useCallback(
    (index: number, newState: TransferAlphaState) => {
      setState((prevState) => {
        const stateToMutate = [...prevState];
        stateToMutate[index] = newState;
        return stateToMutate;
      });
    },
    []
  );

  const handleUpdateAmount = useCallback(
    (index: number, amount: string) => {
      handleUpdateState(index, {
        ...state[index],
        amount,
      });
    },
    [state, handleUpdateState]
  );

  const handleUpdateSubnetId = useCallback(
    (index: number, subnetId: number) => {
      // Updating subnetId
      // Get validator balances for subnetId
      // If there is only one validator balance, set validatorAddressFrom to that validator
      // Otherwise, set validatorAddressFrom to the validator with the highest balance

      const validatorBalances =
        validatorBalancesHelper.getValidatorBalances(subnetId);
      const validatorAddressFrom =
        validatorBalances.length === 0
          ? undefined
          : validatorBalances.length === 1
            ? validatorBalances[0].hotkey
            : [...validatorBalances].sort(
                (a, b) => b.alphaAmount - a.alphaAmount
              )[0].hotkey;

      handleUpdateState(index, {
        ...state[index],
        subnetId,
        ...(validatorAddressFrom ? { validatorAddressFrom } : {}),
      });
    },
    [validatorBalancesHelper, handleUpdateState, state]
  );

  const handleUpdateToAddress = useCallback(
    (index: number, toAddress: string) => {
      handleUpdateState(index, {
        ...state[index],
        toAddress,
      });
    },
    [state, handleUpdateState]
  );

  const handleUpdateValidatorAddressFrom = useCallback(
    (index: number, validatorAddressFrom: string) => {
      handleUpdateState(index, {
        ...state[index],
        validatorAddressFrom,
      });
    },
    [state, handleUpdateState]
  );

  const handleUpdateValidatorAddressTo = useCallback(
    (index: number, validatorAddressTo: string) => {
      handleUpdateState(index, {
        ...state[index],
        validatorAddressTo,
      });
    },
    [state, handleUpdateState]
  );

  const returnValue = useMemo(() => {
    const resetTransferAlphaState = () => {
      setState(defaultState);
    };

    return {
      state,
      handleUpdateAmount,
      handleUpdateSubnetId,
      handleUpdateToAddress,
      handleUpdateValidatorAddressFrom,
      handleUpdateValidatorAddressTo,
      resetTransferAlphaState,
    };
  }, [
    state,
    handleUpdateAmount,
    handleUpdateSubnetId,
    handleUpdateToAddress,
    handleUpdateValidatorAddressFrom,
    handleUpdateValidatorAddressTo,
  ]);

  return returnValue;
};

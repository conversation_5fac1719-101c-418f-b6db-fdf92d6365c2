import { memo } from 'react';
import { BiWalletAlt } from 'react-icons/bi';
import { CgCreditCard } from 'react-icons/cg';
import { Button, Input } from '@repo/ui/components';
import { notify } from '@repo/ui/lib';
import { ConfirmButton } from './confirm-button';
import { AvailableBalanceDisplay } from '@/app/transfer/ui/transfer-alpha-ui/components/available-balance-display';
import { SourceValidatorPicker } from '@/app/transfer/ui/transfer-alpha-ui/components/source-validator-picker';
import {
  TransferAlphaContextProvider,
  useTransferAlphaContext,
} from '@/app/transfer/ui/transfer-alpha-ui/lib/context';
import { SubnetPicker } from '@/components/subnet-picker';
import { ValidatorPicker } from '@/components/validator-picker';
import { useChainAndWalletContext } from '@/contexts/chain-and-wallet/chain-and-wallet-context';

export const TransferAlphaUi = memo(function TransferAlphaUi() {
  return (
    <TransferAlphaContextProvider>
      <TransferAlphaContent />
    </TransferAlphaContextProvider>
  );
});

export const TransferAlphaContent = memo(function TransferAlphaContent() {
  const {
    state: { currentAccount },
  } = useChainAndWalletContext();
  const {
    transferAlphaStates,
    handleUpdateAmount,
    handleUpdateSubnetId,
    handleUpdateToAddress,
    isDestinationValidatorShowing,
    handleUpdateValidatorAddressTo,
  } = useTransferAlphaContext();

  return (
    <div className='animate-in fade-in flex flex-col gap-4 duration-500'>
      <div className='flex flex-col gap-2'>
        <Input
          label='Amount'
          labelClassName='text'
          iconLeftConfig={{
            Icon: CgCreditCard,
            className: 'sm:size-5',
          }}
          placeholder='Enter amount e.g. 100'
          inputId='amount'
          onChange={(e) => {
            handleUpdateAmount(0, e.target.value);
          }}
          value={transferAlphaStates[0].amount}
          inputClassName='pr-32 py-4 sm:pl-12 focus:outline-accent-1'
          rightComponent={
            <SubnetPicker
              subnetId={transferAlphaStates[0].subnetId}
              onSelect={(subnetId) => {
                handleUpdateSubnetId(0, subnetId);
              }}
            />
          }
        />
        {transferAlphaStates[0].subnetId !== undefined ? (
          <div className='flex flex-row justify-end'>
            <AvailableBalanceDisplay
              subnetId={transferAlphaStates[0].subnetId}
            />
          </div>
        ) : null}
      </div>

      <div className='flex flex-col gap-2'>
        <Input
          label='To Address'
          labelClassName='text'
          iconLeftConfig={{
            Icon: BiWalletAlt,
            className: 'sm:size-5',
          }}
          placeholder='To address e.g. 5CFHgzvoF3MyomyFGgvBkVDWGzW5xE28RafSWxACT2ZtFkag'
          inputId='to-address'
          onChange={(e) => {
            handleUpdateToAddress(0, e.target.value);
          }}
          value={transferAlphaStates[0].toAddress}
          inputClassName='py-4 focus:outline-accent-1 rounded-full sm:pl-12'
        />
        <div className='flex flex-row justify-end'>
          <Button
            variant='cta3'
            size='sm'
            onClick={() => {
              if (!currentAccount?.address) {
                notify.error(
                  'Could not get current account address. Please refresh and try again.'
                );
                return;
              }
              handleUpdateToAddress(0, currentAccount.address);
            }}
          >
            Use Source Wallet
          </Button>
        </div>
      </div>

      {transferAlphaStates[0].subnetId !== undefined ? (
        <>
          <SourceValidatorPicker subnetId={transferAlphaStates[0].subnetId} />

          {isDestinationValidatorShowing ? (
            <ValidatorPicker
              subnetId={transferAlphaStates[0].subnetId}
              value={transferAlphaStates[0].validatorAddressTo}
              onChange={(value) => {
                handleUpdateValidatorAddressTo(0, value);
              }}
              labelValue='To Validator'
              labelLevel='labelLarge'
              comboboxClassName='bg-input-primary min-h-12 w-full gap-2 rounded-full py-4 text-sm font-normal text-white/60'
            />
          ) : null}
        </>
      ) : null}

      <ConfirmButton />
    </div>
  );
});

import { memo, useState } from 'react';
import { BiChevronDown } from 'react-icons/bi';
import { CgListTree } from 'react-icons/cg';
import {
  Badge,
  Button,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Spinner,
  TaoOrAlphaValueDisplay,
  Text,
} from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { useTransferAlphaContext } from '@/app/transfer/ui/transfer-alpha-ui/lib/context';
import { useSubnetLookup, useValidatorLookup } from '@/lib/hooks/global-hooks';

export const SourceValidatorPicker = memo(function SourceValidatorPicker({
  subnetId,
}: {
  subnetId: number;
}) {
  const {
    transferAlphaStates: sendState,
    handleUpdateValidatorAddressFrom,
    validatorBalancesHelper,
    isDestinationValidatorShowing,
    handleSetIsDestinationValidatorShowing,
  } = useTransferAlphaContext();
  const { getSubnetName, getSubnetSymbol } = useSubnetLookup();
  const { getValidatorName } = useValidatorLookup();
  const [open, setOpen] = useState(false);

  // Get validator balances for selected subnet
  const validatorBalances =
    validatorBalancesHelper.getValidatorBalances(subnetId);

  if (validatorBalancesHelper.isAccountContextLoading) {
    return (
      <div className='flex flex-row gap-2'>
        <Spinner />
        <Text level='bodyMedium'>Accessing chain data. Please wait.</Text>
      </div>
    );
  }

  if (validatorBalances.length === 0) {
    return (
      <div className='flex flex-row gap-2'>
        <Text level='bodyMedium'>
          No stake found on {getSubnetName(subnetId)}
        </Text>
      </div>
    );
  }

  return (
    <div className='flex flex-col gap-2'>
      <Text as='label' level='labelLarge'>
        From Validator
      </Text>

      <div className='grid grid-cols-1 grid-rows-1'>
        <Popover open={open} onOpenChange={setOpen}>
          <>
            <PopoverTrigger asChild>
              <Button
                disabled={validatorBalances.length <= 1}
                className={cn(
                  'bg-input-primary col-start-1 row-start-1 block min-h-12 w-full overflow-hidden py-4 text-left text-base font-normal text-white outline outline-1 -outline-offset-1',
                  'pl-10 sm:pl-9',
                  'pr-32',
                  'placeholder:text-gray-400 sm:text-sm/6',
                  'outline-input-border-primary',
                  'placeholder:overflow-hidden placeholder:text-ellipsis',
                  'rounded-full',
                  'disabled:bg-input-primary disabled:opacity-100'
                )}
              >
                <Text
                  as='span'
                  className='block truncate font-normal'
                  level='labelLarge'
                >
                  {sendState[0].validatorAddressFrom
                    ? getValidatorName(sendState[0].validatorAddressFrom)
                    : null}
                </Text>
              </Button>
            </PopoverTrigger>

            <CgListTree className='pointer-events-none col-start-1 row-start-1 ml-3 size-5 self-center text-gray-400 sm:size-4' />

            <Badge
              variant='green3'
              className={cn(
                'col-start-1 row-start-1 self-center justify-self-end',
                validatorBalances.length > 1 ? 'mr-10' : 'mr-3'
              )}
            >
              {validatorBalances.length}
            </Badge>

            {validatorBalances.length > 1 ? (
              <BiChevronDown
                aria-hidden='true'
                className={cn(
                  'pointer-events-none col-start-1 row-start-1 mr-3 size-5 self-center justify-self-end transition-transform sm:size-4',
                  open && 'rotate-180'
                )}
              />
            ) : null}

            <PopoverContent
              align='start'
              className='absolute z-10 mt-1 w-[var(--radix-popover-trigger-width)] rounded-2xl border border-[#323232] bg-[#1D1D1DCC] p-4 shadow-lg backdrop-blur-2xl'
            >
              <div className='max-h-60 overflow-auto py-1'>
                {validatorBalances
                  .sort((a, b) => b.alphaAmount - a.alphaAmount)
                  .map((balance) => (
                    <div
                      key={balance.hotkey}
                      className='hover:border-accent-1 flex flex-row items-center justify-between gap-4 rounded-lg border border-transparent bg-transparent px-2 py-1 transition-[border-color] duration-300'
                    >
                      <Button
                        className={cn(
                          'border-1 flex flex-1 flex-row justify-between overflow-hidden truncate border-transparent bg-transparent px-1 shadow-none hover:bg-transparent'
                        )}
                        onClick={() => {
                          handleUpdateValidatorAddressFrom(0, balance.hotkey);
                          setOpen(false);
                        }}
                      >
                        <Text level='labelLarge' className='truncate'>
                          {getValidatorName(balance.hotkey)}
                        </Text>
                        <TaoOrAlphaValueDisplay
                          value={balance.alphaAmount}
                          symbol={getSubnetSymbol(subnetId)}
                          valueTextLevel='labelLarge'
                          valueFormattingOptions={{
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 5,
                          }}
                          areDecimalsSmall
                        />
                      </Button>
                    </div>
                  ))}
              </div>
            </PopoverContent>
          </>
        </Popover>
      </div>

      <div className='flex flex-row items-center justify-between gap-2'>
        {!isDestinationValidatorShowing ? (
          <Button
            variant='ghost'
            size='sm'
            onClick={() => {
              handleSetIsDestinationValidatorShowing(true);
            }}
          >
            Show Destination Validator
            <BiChevronDown className='text-accent-1 text-base' />
          </Button>
        ) : (
          <div />
        )}
      </div>
    </div>
  );
});

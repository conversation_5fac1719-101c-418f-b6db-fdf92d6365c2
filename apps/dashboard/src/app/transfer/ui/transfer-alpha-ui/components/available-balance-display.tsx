import { memo } from 'react';
import { BigNumber } from 'bignumber.js';
import { Button, TaoOrAlphaValueDisplay, Text } from '@repo/ui/components';
import { useTransferAlphaContext } from '@/app/transfer/ui/transfer-alpha-ui/lib/context';
import { useSubnetLookup } from '@/lib/hooks/global-hooks';

export const AvailableBalanceDisplay = memo(function AvailableBalanceDisplay({
  subnetId,
}: {
  subnetId: number;
}) {
  const { getSubnetSymbol } = useSubnetLookup();
  const {
    transferAlphaStates: sendState,
    handleUpdateAmount,
    validatorBalancesHelper,
  } = useTransferAlphaContext();

  // Get validator balances for selected subnet
  const validatorBalances =
    validatorBalancesHelper.getValidatorBalances(subnetId);

  const validatorBalance = validatorBalances.find(
    (v) => v.hotkey === sendState[0].validatorAddressFrom
  );

  const validatorBalanceAmount = validatorBalance
    ? BigNumber(validatorBalance.alphaAmount).toNumber()
    : 0;

  return (
    <div className='flex flex-row items-baseline gap-2'>
      <Text level='labelLarge' className='text-white/60'>
        Available:{' '}
      </Text>
      <TaoOrAlphaValueDisplay
        value={validatorBalanceAmount}
        symbol={getSubnetSymbol(subnetId)}
        valueTextLevel='labelLarge'
        valueFormattingOptions={{
          minimumFractionDigits: 2,
          maximumFractionDigits: 5,
        }}
        areDecimalsSmall
      />
      <Button
        variant='cta3'
        size='sm'
        onClick={() => {
          handleUpdateAmount(0, validatorBalanceAmount.toString());
        }}
      >
        Max
      </Button>
    </div>
  );
});

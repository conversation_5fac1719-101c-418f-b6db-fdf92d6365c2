import { memo, useState } from 'react';
import { BiSend } from 'react-icons/bi';
import { z } from 'zod';
import { Button } from '@repo/ui/components';
import { notify } from '@repo/ui/lib';
import { useTransactionBuilder } from './transfer-alpha-summary-dialog/lib';
import { TransferAlphaSummaryDialog } from './transfer-alpha-summary-dialog/transfer-alpha-summary-dialog';
import { useTransferAlphaContext } from '@/app/transfer/ui/transfer-alpha-ui/lib/context';
import { useTransactionSubmitter } from '@/contexts/chain-and-wallet/use-transaction-submitter';

const amountSchema = z.coerce.number().nonnegative();

export const ConfirmButton = memo(function ConfirmButton() {
  const { transferAlphaStates } = useTransferAlphaContext();
  const { buildTransaction } = useTransactionBuilder();
  const { calculateTransactionFee } = useTransactionSubmitter();
  const [isBuildingTransaction, setIsBuildingTransaction] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [calculatedFee, setCalculatedFee] = useState<number | null>(null);

  const handleConfirmClick = async () => {
    if (transferAlphaStates.length <= 0) {
      notify.error('Please enter an amount and a validator address');
      return;
    }

    if (transferAlphaStates[0].amount === '') {
      notify.error('Please enter an amount');
      return;
    }

    const amountResult = amountSchema.safeParse(transferAlphaStates[0].amount);
    if (!amountResult.success) {
      notify.error('Amount must be a valid number greater than 0');
      return;
    }

    if (transferAlphaStates[0].subnetId === undefined) {
      notify.error('Please select a "Subnet"');
      return;
    }

    if (transferAlphaStates[0].toAddress === '') {
      notify.error('Please enter a "To Address"');
      return;
    }

    if (transferAlphaStates[0].validatorAddressFrom === '') {
      notify.error('Please select a "From Validator"');
      return;
    }

    try {
      setIsBuildingTransaction(true);
      const buildTransactionResult = buildTransaction();
      if (!buildTransactionResult?.tx) {
        notify.error('Could not build transaction');
        return;
      }
      const fee = await calculateTransactionFee(buildTransactionResult.tx);
      setCalculatedFee(fee);
      setIsOpen(true);
    } catch (e) {
      notify.error('There was an error when building the transaction');
      console.error(e);
    } finally {
      setIsBuildingTransaction(false);
    }
  };
  const closeDialog = () => {
    setIsOpen(false);
  };

  const isDisabled = isBuildingTransaction || transferAlphaStates.length <= 0;

  return (
    <>
      <Button
        variant='cta2'
        size='lg'
        onClick={() => {
          void handleConfirmClick();
        }}
        disabled={isDisabled}
      >
        Confirm
        <BiSend className='ml-2 text-lg' />
      </Button>
      <TransferAlphaSummaryDialog
        isOpen={isOpen}
        closeFn={closeDialog}
        fee={calculatedFee}
      />
    </>
  );
});

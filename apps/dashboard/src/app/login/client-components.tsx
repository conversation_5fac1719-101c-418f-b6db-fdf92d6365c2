'use client';

import { useState, useCallback, useEffect } from 'react';
import Image from 'next/image';
import { Text } from '@repo/ui/components';
import { imagePaths } from '@/lib/image-paths';

export const Logo = () => {
  const [clicks, setClicks] = useState(0);
  const [showUserAgent, setShowUserAgent] = useState(false);
  const [lastClickTime, setLastClickTime] = useState(0);

  const handleLogoClick = useCallback(() => {
    if (showUserAgent) {
      setShowUserAgent(false);
      setClicks(0);
      return;
    }

    const currentTime = Date.now();

    // Reset clicks if more than 2 seconds have passed since last click
    if (currentTime - lastClickTime > 2000) {
      setClicks(1);
    } else {
      setClicks((prev) => prev + 1);
    }

    setLastClickTime(currentTime);
  }, [lastClickTime, showUserAgent]);

  useEffect(() => {
    if (clicks >= 7) {
      setShowUserAgent(true);
      setClicks(0);
    }
  }, [clicks]);

  return (
    <>
      {/* eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions -- want this to be secretly clickable */}
      <div className='z-[999]' onClick={handleLogoClick}>
        <Image
          alt='Taostats'
          height={38}
          width={215}
          src={imagePaths.logo.taostats}
        />
      </div>
      {showUserAgent ? (
        <div className='fixed bottom-4 left-4 right-4 z-50 rounded-lg bg-black/80 p-4 text-white'>
          <Text level='bodySmall'>User Agent: {navigator.userAgent}</Text>
        </div>
      ) : null}
    </>
  );
};

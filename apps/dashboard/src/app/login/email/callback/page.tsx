'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { BiArrowBack } from 'react-icons/bi';
import { AlertError, But<PERSON>, Spinner } from '@repo/ui/components';
import { signInEmailAction } from '@/app/login/actions';
import { LoginForm } from '@/app/login/form';
import { LoginLayout } from '@/app/login/login-layout';

const formId = 'email-signin-form';

export default function SignInPage(props: {
  searchParams: {
    token: string | undefined;
    redirectTo: string | undefined;
  };
}) {
  const { token, redirectTo } = props.searchParams;

  useEffect(() => {
    const form = document.getElementById(formId);
    if (token && form instanceof HTMLFormElement) {
      form.requestSubmit();
    }
  }, [token]);

  const router = useRouter();

  return (
    <LoginLayout>
      {token ? (
        <>
          <LoginForm
            formId={formId}
            value={token}
            action={signInEmailAction}
            fieldId='token'
            redirectTo={redirectTo}
          />

          <div className='flex flex-col items-center gap-2'>
            <Spinner />
            <p className='text-gray-300'>Signing in, please wait...</p>
          </div>
        </>
      ) : null}

      {!token ? (
        <div>
          <AlertError
            className='m-10'
            description='No sign in token was found. Please try signing in again.'
            title='Sign In Error'
          />
          <div className='mt-10 flex justify-center'>
            <Button
              onClick={() => {
                router.push('/login');
              }}
              variant='default'
            >
              <BiArrowBack size={20} />
              <span className='ml-2'>Return to Login</span>
            </Button>
          </div>
        </div>
      ) : null}
    </LoginLayout>
  );
}

import type { ReactNode } from 'react';
import { Gradient, Text } from '@repo/ui/components';
import { Logo } from './client-components';

export function LoginLayout({
  title,
  children,
}: {
  title?: ReactNode;
  children: ReactNode;
}) {
  return (
    <>
      <Gradient
        className='top-[-755px] h-[1000px] w-full opacity-[0.20]'
        conic
      />
      <div className='mt-8 flex min-h-[60vh] flex-col items-center justify-center gap-12'>
        <Logo />

        <div className='flex flex-col items-center gap-8 p-4'>
          {title ? (
            <Text
              as='h1'
              className='text-center font-medium'
              level='displayMedium'
            >
              {title}
            </Text>
          ) : null}
          <div className='min-w-80 max-w-80 space-y-4'>{children}</div>
        </div>
      </div>
    </>
  );
}

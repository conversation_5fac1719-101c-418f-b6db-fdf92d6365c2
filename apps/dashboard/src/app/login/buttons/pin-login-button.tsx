'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { GiDominoMask } from 'react-icons/gi';
import { Button } from '@repo/ui/components';

export const PinLoginButton = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectTo = searchParams.get('redirectTo') ?? '';
  return (
    <Button
      onClick={() => {
        router.push(
          `/login/pin${redirectTo ? `?redirectTo=${redirectTo}` : ''}`
        );
      }}
      variant='cta2'
      size='lg'
    >
      <GiDominoMask className='mr-2' size={20} />
      Anonymous Sign in
    </Button>
  );
};

/* eslint-disable @typescript-eslint/no-misused-promises -- auth methods have to be async */

import type { IconType } from 'react-icons';
import { Button } from '@repo/ui/components';
import { signIn, signOut } from '@/auth-config';

export function SignInButton({
  provider,
  title,
  Icon,
  redirectTo,
}: {
  provider?: string;
  title?: string;
  Icon?: IconType;
  redirectTo?: string;
} & React.ComponentPropsWithRef<typeof Button>) {
  return (
    <form
      action={async () => {
        'use server';
        await signIn(provider, { redirectTo: redirectTo ?? '/' });
      }}
    >
      <Button className='w-full' size='lg' variant='default'>
        {Icon ? <Icon size={20} /> : null}
        <span className='ml-2'>{title ? `${title} ` : ''}Sign In</span>
      </Button>
    </form>
  );
}

export function SignOutButton() {
  return (
    <form
      action={async () => {
        'use server';
        await signOut();
      }}
    >
      <Button className='w-full' size='lg' variant='default'>
        Sign Out
      </Button>
    </form>
  );
}

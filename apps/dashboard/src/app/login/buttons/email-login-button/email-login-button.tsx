'use client';

import { useEffect, useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useSearchParams } from 'next/navigation';
import type { SubmitHandler } from 'react-hook-form';
import { useForm, Controller } from 'react-hook-form';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, BiEnvelopeOpen } from 'react-icons/bi';
import { z } from 'zod';
import {
  AlertError,
  Button,
  Dialog,
  Input,
  Spinner,
  Text,
} from '@repo/ui/components';
import { notify } from '@repo/ui/lib';
import { useEmailLogin } from './hooks';

const emailSchema = z.object({
  email: z.string().min(1, 'Email is required').email('Invalid email format'),
});

type EmailSchema = z.infer<typeof emailSchema>;

const formId = 'email-login-form';

export const EmailLoginButton = () => {
  const [isOpen, setIsOpen] = useState(false);
  const searchParams = useSearchParams();

  const form = useForm<EmailSchema>({
    defaultValues: { email: '' },
    resolver: zodResolver(emailSchema),
  });

  useEffect(() => {
    if (isOpen) {
      setTimeout(() => {
        form.setFocus('email');
      }, 500);
    }
  }, [form, isOpen]);

  const emailLogin = useEmailLogin();
  const handleSignIn: SubmitHandler<EmailSchema> = async (data) => {
    const isValid = await form.trigger();
    if (!isValid) {
      return;
    }

    const { email } = data;
    const redirectTo = searchParams.get('redirectTo') ?? '';
    emailLogin.mutate(
      { email, redirectTo },
      {
        onSuccess: () => {
          notify.success('Please check your email for a link to sign in.');
          setIsOpen(false);
          form.reset();
        },
      }
    );
  };

  return (
    <>
      <Button
        className='w-full'
        onClick={() => {
          setIsOpen(true);
        }}
        size='lg'
        variant='default'
      >
        <BiEnvelopeOpen size={20} />
        <span className='ml-2'>Email Sign In</span>
      </Button>
      <Dialog
        HeaderIcon={BiEnvelopeOpen}
        closeFn={() => {
          setIsOpen(false);
        }}
        isOpen={isOpen}
        title='Sign In with Email'
      >
        <div className='flex flex-col gap-6'>
          <Text as='p' level='bodyMedium'>
            Enter your email address and we&apos;ll send you a link to sign in.
          </Text>
          <form
            id={formId}
            onSubmit={(e) => {
              void form.handleSubmit(handleSignIn)(e);
            }}
          >
            <Controller
              control={form.control}
              name='email'
              render={({ field }) => (
                <Input
                  IconLeft={BiEnvelopeOpen}
                  IconRight={field.value ? BiCheck : undefined}
                  error={form.formState.errors.email?.message}
                  forwardRef={field.ref}
                  inputId='email'
                  onChange={field.onChange}
                  value={field.value}
                />
              )}
            />
          </form>
        </div>

        <div className='flex flex-col gap-4'>
          <Button
            className='flex w-full items-center justify-center gap-2'
            disabled={emailLogin.isPending}
            form={formId}
            size='lg'
            type='submit'
            variant='default'
          >
            {emailLogin.isPending ? <Spinner /> : <BiCheck size={20} />}
            Sign In
          </Button>
          {emailLogin.isError ? (
            <AlertError
              description='An error occurred while signing in.'
              title='Error'
            />
          ) : null}
        </div>
      </Dialog>
    </>
  );
};

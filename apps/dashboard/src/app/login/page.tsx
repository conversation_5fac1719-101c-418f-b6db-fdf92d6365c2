import { CgGoogle } from 'react-icons/cg';
import { FaGithub } from 'react-icons/fa';
import { AlertError } from '@repo/ui/components';
import { LoginLayout } from './login-layout';
import {
  EmailLoginButton,
  PinLoginButton,
  SignInButton,
  SignOutButton,
} from '@/app/login/buttons';
import { auth } from '@/auth-config';

export const metadata = {
  title: 'Login',
};

export default async function LoginPage(props: {
  searchParams: {
    error: string | undefined;
    redirectTo: string | undefined;
  };
}) {
  const { error, redirectTo } = props.searchParams;
  const session = await auth();

  return (
    <LoginLayout
      title={
        <>
          Manage your Stake, Portfolio <br />
          and API access all in one place.
        </>
      }
    >
      <div className='flex flex-col gap-20'>
        {!session?.user ? (
          <div className='flex flex-col gap-4'>
            <SignInButton
              Icon={CgGoogle}
              provider='google-manual'
              title='Google'
              redirectTo={redirectTo}
            />
            <SignInButton
              Icon={FaGithub}
              provider='github-manual'
              title='GitHub'
              redirectTo={redirectTo}
            />
            <EmailLoginButton />
            <PinLoginButton />
          </div>
        ) : (
          <SignOutButton />
        )}

        {error ? (
          <AlertError
            description={
              error === 'OAuthCallbackError'
                ? 'There was a problem with the auth process. Please try signing in again.'
                : error
            }
            title='Sign In Error'
          />
        ) : null}
      </div>
    </LoginLayout>
  );
}

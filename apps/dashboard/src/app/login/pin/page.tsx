'use client';

import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { BiLeftArrowAlt, BiRightArrowAlt } from 'react-icons/bi';
import { <PERSON><PERSON>, Spinner } from '@repo/ui/components';
import { Fingerprint } from './fingerprint';
import { signInPinAction } from '@/app/login/actions';
import { LoginSubHeader } from '@/app/login/components';
import { LoginForm } from '@/app/login/form';
import { LoginLayout } from '@/app/login/login-layout';

const formId = 'pin-form';

export default function PinLoginPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectTo = searchParams.get('redirectTo') ?? '';
  const [fingerprint, setFingerprint] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  return (
    <LoginLayout title='Sign in'>
      <div className='flex flex-col items-center justify-center gap-12'>
        <LoginSubHeader>Sign in using your 16 digit pin.</LoginSubHeader>
        <div className='flex flex-col items-center justify-center'>
          <Fingerprint onFingerprintChange={setFingerprint} />
          <LoginForm
            formId={formId}
            value={fingerprint}
            action={signInPinAction}
            fieldId='pin'
            redirectTo={redirectTo}
          />
        </div>
        <div className='flex w-full flex-col items-center justify-center gap-4'>
          <Button
            disabled={isLoading || fingerprint.length !== 16}
            variant='default'
            size='lg'
            className='flex w-full items-center justify-center gap-2'
            onClick={() => {
              setIsLoading(true);
              const form = document.getElementById(formId);
              if (form instanceof HTMLFormElement) {
                form.requestSubmit();
              }
            }}
          >
            Log in
            {isLoading ? (
              <Spinner />
            ) : (
              <BiRightArrowAlt className='ml-2' size={20} />
            )}
          </Button>
          <Button
            className='text-white'
            onClick={() => {
              router.push('/login/pin/generate');
            }}
            variant='link'
          >
            Don&apos;t have a pin?
          </Button>
          <Button
            className='text-white'
            onClick={() => {
              router.back();
            }}
            variant='link'
          >
            <BiLeftArrowAlt className='text-accent-1 mr-2' size={20} />
            Login another way
          </Button>
        </div>
      </div>
    </LoginLayout>
  );
}

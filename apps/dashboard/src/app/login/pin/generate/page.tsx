'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Bi<PERSON>ingerprint, BiLeftArrowAlt, BiRightArrowAlt } from 'react-icons/bi';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Spinner } from '@repo/ui/components';
import { notify } from '@repo/ui/lib';
import { useGeneratePin } from './hooks';
import { signInPinAction } from '@/app/login/actions';
import { LoginSubHeader } from '@/app/login/components';
import { LoginForm } from '@/app/login/form';
import { LoginLayout } from '@/app/login/login-layout';
import { Fingerprint } from '@/app/login/pin/fingerprint';

export default function GeneratePinPage() {
  const [generatedPin, setGeneratedPin] = useState<string | null>(null);

  return (
    <LoginLayout title='Generate your pin'>
      {!generatedPin ? (
        <GeneratePinUi setGeneratedPin={setGeneratedPin} />
      ) : (
        <PinGeneratedUi pin={generatedPin} />
      )}
    </LoginLayout>
  );
}

const GeneratePinUi = ({
  setGeneratedPin,
}: {
  setGeneratedPin: (pin: string) => void;
}) => {
  const generatePin = useGeneratePin();
  const handleGeneratePinClick = () => {
    generatePin.mutate(undefined, {
      onSuccess: (data) => {
        setGeneratedPin(data.fingerprint);
        notify.success(
          'PIN generated successfully. Please ensure you copy and save it somewhere safe.'
        );
      },
    });
  };
  return (
    <div className='flex flex-col items-center justify-center gap-12'>
      <LoginSubHeader>
        Click on the button below to generate your 16 digit pin.
      </LoginSubHeader>
      <div className='flex flex-col items-center justify-center gap-4 w-full'>
        {generatePin.isError ? (
          <AlertError
            title='Error'
            description='An error occurred while generating your pin. Please try again later.'
          />
        ) : null}

        <Button
          variant='cta2'
          size='lg'
          className='w-full flex items-center justify-center gap-2'
          disabled={generatePin.isPending}
          onClick={handleGeneratePinClick}
        >
          Generate your pin
          {generatePin.isPending ? <Spinner /> : <BiFingerprint size={20} />}
        </Button>
        <AlreadyHavePinButton />
      </div>
    </div>
  );
};

const formId = 'pin-signin-form';

const PinGeneratedUi = ({ pin }: { pin: string }) => {
  const [isLoading, setIsLoading] = useState(false);
  return (
    <div className='flex flex-col items-center justify-center gap-12'>
      <LoginSubHeader>Don&apos;t forget to save your pin</LoginSubHeader>
      <div className='flex flex-col items-center justify-center gap-4 w-full border border-accent-1 rounded-2xl p-1'>
        <Fingerprint defaultFingerprint={pin} copyMode />
        <LoginForm
          formId={formId}
          value={pin}
          action={signInPinAction}
          fieldId='pin'
          redirectTo='/login/pin/set-recovery-email'
        />
      </div>

      <Button
        disabled={isLoading}
        variant='cta2'
        size='lg'
        className='w-full flex items-center justify-center gap-2'
        type='button'
        onClick={() => {
          setIsLoading(true);
          const form = document.getElementById(formId);
          if (form instanceof HTMLFormElement) {
            form.requestSubmit();
          }
        }}
      >
        Login
        {isLoading ? <Spinner /> : <BiRightArrowAlt size={20} />}
      </Button>
      <AlreadyHavePinButton />
    </div>
  );
};

const AlreadyHavePinButton = () => {
  const router = useRouter();

  return (
    <Button
      className='text-white'
      onClick={() => {
        router.back();
      }}
      variant='link'
    >
      <BiLeftArrowAlt className='text-accent-1 mr-2' size={20} />
      Already have a pin?
    </Button>
  );
};

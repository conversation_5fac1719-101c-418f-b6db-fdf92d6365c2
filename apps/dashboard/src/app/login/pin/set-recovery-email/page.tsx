'use client';

import { useEffect, useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { Controller, useForm } from 'react-hook-form';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, BiEnvelopeOpen, Bi<PERSON>ock<PERSON><PERSON> } from 'react-icons/bi';
import { CgArrowRight } from 'react-icons/cg';
import { z } from 'zod';
import { Button, Input, Spinner } from '@repo/ui/components';
import { notify } from '@repo/ui/lib';
import { LoginSubHeader } from '@/app/login/components';
import { LoginLayout } from '@/app/login/login-layout';
import { useSetRecoveryEmail } from '@/app/login/pin/generate/hooks';

const setRecoveryEmailSchema = z.object({
  email: z.string().min(1, 'Email is required').email('Invalid email format'),
});

type SetRecoveryEmailSchema = z.infer<typeof setRecoveryEmailSchema>;

export default function SetRecoveryEmailPage() {
  const router = useRouter();
  const [wasSkipPressed, setWasSkipPressed] = useState(false);
  const form = useForm<SetRecoveryEmailSchema>({
    defaultValues: { email: '' },
    resolver: zodResolver(setRecoveryEmailSchema),
  });
  const setRecoveryEmail = useSetRecoveryEmail();

  const handleSetRecoveryEmailClick = async () => {
    const isValid = await form.trigger();
    if (!isValid) {
      return;
    }

    const { email } = form.getValues();
    await setRecoveryEmail.mutateAsync(
      { email },
      {
        onSuccess: () => {
          notify.success('Recovery email set successfully.');
          router.push('/');
        },
      }
    );
  };

  useEffect(() => {
    setTimeout(() => {
      form.setFocus('email');
    }, 500);
  }, [form]);

  return (
    <LoginLayout title='What if you forget your pin?'>
      <div className='flex flex-col items-center justify-center gap-12'>
        <LoginSubHeader>
          Just in case you lose your 16 digit pin, we recommend using your email
          to retrieve it.
        </LoginSubHeader>

        <div className='flex w-3/4 flex-col items-center gap-6'>
          <Controller
            control={form.control}
            name='email'
            render={({ field }) => (
              <Input
                className='w-full'
                IconLeft={BiEnvelopeOpen}
                IconRight={field.value ? BiCheck : undefined}
                error={form.formState.errors.email?.message}
                forwardRef={field.ref}
                inputId='email'
                onChange={field.onChange}
                value={field.value}
              />
            )}
          />

          <div className='flex w-3/4 flex-col gap-4'>
            <Button
              disabled={
                setRecoveryEmail.isPending || setRecoveryEmail.isSuccess
              }
              variant='cta2'
              size='lg'
              className='flex items-center justify-center gap-2'
              onClick={() => void handleSetRecoveryEmailClick()}
            >
              Save
              {setRecoveryEmail.isPending || setRecoveryEmail.isSuccess ? (
                <Spinner />
              ) : (
                <BiLockOpen size={20} />
              )}
            </Button>
            <Button
              size='lg'
              variant='default'
              className='flex items-center justify-center gap-2'
              disabled={wasSkipPressed}
              onClick={() => {
                setWasSkipPressed(true);
                router.push('/');
              }}
            >
              Skip
              {wasSkipPressed ? <Spinner /> : <CgArrowRight size={20} />}
            </Button>
          </div>
        </div>
      </div>
    </LoginLayout>
  );
}

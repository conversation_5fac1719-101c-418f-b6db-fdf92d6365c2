/* eslint-disable react/no-array-index-key -- allow for this file */

'use client';

import { useEffect, useState } from 'react';
import { B<PERSON>Check, BiFingerprint } from 'react-icons/bi';
import { cn } from '@repo/ui/lib';
import { useFingerprint } from './hooks';

interface FingerprintProps {
  defaultFingerprint?: string;
  onFingerprintChange?: (fingerprint: string) => void;
  copyMode?: boolean;
}

export function Fingerprint({
  defaultFingerprint = '',
  onFingerprintChange,
  copyMode = false,
}: FingerprintProps) {
  const {
    fingerprintGrid,
    setFingerprintGrid,
    handlePaste,
    focusedCell,
    setFocusedCell,
    handleInput,
    handleKeydown,
  } = useFingerprint(defaultFingerprint, onFingerprintChange);

  const [copied, setCopied] = useState(false);

  useEffect(() => {
    if (defaultFingerprint) {
      const chars = defaultFingerprint.split('');
      setFingerprintGrid(
        Array(4)
          .fill(null)
          .map((_, i) => chars.slice(i * 4, (i + 1) * 4))
      );
    }
  }, [defaultFingerprint, setFingerprintGrid]);

  function handleCopy() {
    void navigator.clipboard.writeText(fingerprintGrid.flat().join(''));
    setCopied(true);
    setTimeout(() => {
      setCopied(false);
    }, 1000);
  }

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
      }}
      role='group'
      aria-label='Fingerprint'
      className='group relative'
    >
      <fieldset className='m-0 flex flex-col gap-2.5 rounded-2xl border-0 p-6'>
        <legend className='sr-only'>Enter fingerprint pin</legend>
        {fingerprintGrid.map((row, rowIndex) => (
          <div key={rowIndex} className='flex gap-2.5'>
            {row.map((cell, colIndex) => (
              <div key={`${rowIndex}-${colIndex}`}>
                <label
                  htmlFor={`cell-${rowIndex}-${colIndex}`}
                  className='sr-only'
                >
                  Character {rowIndex * 4 + colIndex + 1} of fingerprint
                </label>
                <input
                  type='text'
                  maxLength={1}
                  className={cn(
                    'bg-hero-primary hover:bg-accent-1/20 h-[60px] w-[60px] rounded-full border text-center text-lg caret-transparent shadow-[inset_0px_5px_5px_rgba(0,0,0,0.2)] transition-colors duration-150 ease-in-out focus:outline-none',
                    focusedCell[0] === rowIndex && focusedCell[1] === colIndex
                      ? 'border-accent-1 bg-accent-1/20'
                      : 'border-primary-button-border'
                  )}
                  id={`cell-${rowIndex}-${colIndex}`}
                  value={fingerprintGrid[rowIndex][colIndex]}
                  onChange={(e) => {
                    handleInput(e, rowIndex, colIndex);
                  }}
                  onKeyDown={(e) => {
                    handleKeydown(e, rowIndex, colIndex);
                  }}
                  onPaste={(e) => {
                    handlePaste(e);
                  }}
                  onClick={() => {
                    setFocusedCell([rowIndex, colIndex]);
                  }}
                  inputMode='text'
                  autoComplete='off'
                />
              </div>
            ))}
          </div>
        ))}
      </fieldset>
      {copyMode ? (
        <button
          type='button'
          onClick={() => {
            handleCopy();
          }}
          className={cn(
            'absolute left-0 top-0 flex h-full w-full flex-col items-center justify-center rounded-2xl bg-neutral-900 opacity-0 transition-opacity duration-150 ease-in-out group-hover:opacity-100',
            copied ? 'opacity-100' : ''
          )}
        >
          {copied ? (
            <div className='flex items-center justify-center gap-2'>
              <BiCheck className='text-accent-1 size-8' />
              <p className='text-white'>Copied</p>
            </div>
          ) : (
            <div className='flex items-center justify-center gap-2'>
              <BiFingerprint className='size-8' />
              <p className='text-white'>Click to Copy</p>
            </div>
          )}
        </button>
      ) : null}
    </form>
  );
}

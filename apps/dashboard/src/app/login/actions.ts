'use server';

import { redirect } from 'next/navigation';
import { AuthError } from 'next-auth';
import { signIn } from '@/auth-config';

export const signInEmailAction = async (formData: FormData) => {
  await signInAction({
    type: 'email',
    redirectTo: formData.get('redirectTo')?.toString() ?? '/',
    formData,
  });
};

export const signInPinAction = async (formData: FormData) => {
  await signInAction({
    type: 'pin',
    redirectTo: formData.get('redirectTo')?.toString() ?? '/',
    formData,
  });
};

const signInAction = async ({
  type,
  redirectTo,
  formData,
}: {
  type: 'email' | 'pin';
  redirectTo: string;
  formData: FormData;
}) => {
  try {
    formData.set('redirectTo', redirectTo);
    await signIn(type, formData);
  } catch (error) {
    if (error instanceof AuthError) {
      return redirect(
        `/login?error=${error.cause?.err?.message ?? error.type}`
      );
    }
    throw error;
  }
};

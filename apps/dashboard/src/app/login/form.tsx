type LoginFormProps = {
  formId: string;
  action: (formData: FormData) => Promise<void>;
  fieldId: string;
  value: string;
  redirectTo?: string;
};

export const LoginForm = ({
  formId,
  action,
  fieldId,
  value,
  redirectTo,
}: LoginFormProps) => {
  return (
    <div className='hidden'>
      <form
        action={(formData) => {
          if (redirectTo) {
            formData.set('redirectTo', redirectTo);
          }
          void action(formData);
        }}
        id={formId}
      >
        <label htmlFor='token'>
          {fieldId}
          <input
            readOnly
            className='text-black'
            value={value}
            id={fieldId}
            name={fieldId}
          />
        </label>
        <input type='submit' value='Sign In' />
      </form>
    </div>
  );
};

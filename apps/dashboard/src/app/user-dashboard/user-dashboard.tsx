'use client';

import Link from 'next/link';
import { CgArrowRight } from 'react-icons/cg';
import { Text } from '@repo/ui/components';
import { Background } from '@/components/background-image';
import { PageLayout } from '@/components/page-layout';

export const UserDashboard = () => {
  return (
    <>
      <Background />
      <div className='w-full'>
        <PageLayout title='Welcome to your dashboard'>
          <div className='grid w-full grid-cols-1 gap-6 pt-12 sm:grid-cols-3 lg:grid-cols-4'>
            <DashboardCard
              title='Stake'
              description='Connect your wallet, manage your staking and dTao in one place. Easily balance and adjust your allocations.'
              buttonText='Delegate'
              buttonHref='/stake'
            />

            <DashboardCard
              title='Portfolio'
              description='View a detailed breakdown of your tao or alpha holdings across Validators and Subnets.'
              buttonText='View Earnings'
              buttonHref='/portfolio'
            />

            <DashboardCard
              title='API'
              description='Access our API and plug into various data points, including blockchain information, subnet details, and validator statistics.'
              buttonText='Access API'
              buttonHref='/api-keys'
            />

            <DashboardCard
              title='Send & Transfer'
              description='Send unstaked Tao or transfer staked Tao/alpha to other wallets and/or validators.'
              buttonText='Send & Transfer'
              buttonHref='/transfer'
            />
          </div>
        </PageLayout>
      </div>
    </>
  );
};

const DashboardCard = ({
  title,
  description,
  buttonText,
  buttonHref,
}: {
  title: string;
  description: string;
  buttonText: string;
  buttonHref: string;
}) => {
  return (
    <Link href={buttonHref} className='block'>
      <div className='bg-hero-primary group flex h-full min-h-[290px] flex-col justify-between gap-9 rounded-3xl p-10 transition-all duration-500 ease-in-out hover:scale-[1.03]'>
        <div className='flex flex-col gap-5'>
          <Text as='h3' level='headerMedium' className=''>
            {title}
          </Text>
          <Text
            as='p'
            level='bodyMedium'
            className='font-normal text-[#A2A2A2]'
          >
            {description}
          </Text>
        </div>
        <div>
          <span className='flex flex-row items-center'>
            <Text level='buttonMedium'>{buttonText}</Text>
            <CgArrowRight
              size={24}
              className='text-accent-1 ml-2 transition-transform duration-500 group-hover:translate-x-6'
            />
          </span>
        </div>
      </div>
    </Link>
  );
};

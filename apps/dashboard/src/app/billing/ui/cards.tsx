import { PricingCard } from './card';
import { usePricingMap } from '@/app/billing/lib/config';
import type { PaymentInterval } from '@/app/billing/lib/types';

export function Cards({
  paymentInterval,
}: {
  paymentInterval: PaymentInterval;
}) {
  const { pricingMap } = usePricingMap();
  const pricing = pricingMap[paymentInterval];

  return (
    <div className='animate-in fade-in grid h-full min-h-[560px] grid-cols-1 items-stretch gap-8 duration-300 md:grid-cols-3 lg:grid-cols-1 xl:grid-cols-3 2xl:gap-12'>
      {pricing.map((item) => {
        return <PricingCard key={item.title} {...item} />;
      })}
    </div>
  );
}

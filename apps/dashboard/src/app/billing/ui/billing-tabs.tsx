'use client';

import { Button, Text } from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import {
  paymentIntervals,
  type PaymentInterval,
} from '@/app/billing/lib/types';

export const BillingTabs = ({
  paymentInterval,
  setPaymentInterval,
}: {
  paymentInterval: PaymentInterval;
  setPaymentInterval: (paymentInterval: PaymentInterval) => void;
}) => {
  return (
    <div className='flex w-full justify-center'>
      <div className='inline-flex flex-col gap-2 rounded-xl border border-[#323232] bg-[#1D1D1D] p-2 sm:flex-row'>
        {paymentIntervals.map((tab, index) => (
          <div key={tab} className='flex items-center'>
            <Button
              className={cn(
                paymentInterval === tab
                  ? 'border-[#00DBBC] bg-[#00DBBC1A] opacity-100 hover:bg-[#00DBBC1A]'
                  : 'border-transparent bg-transparent opacity-40',
                'flex h-[43px] w-full flex-row gap-2 rounded-xl border px-3 py-2 transition-all duration-300 ease-in-out sm:w-auto'
              )}
              onClick={() => {
                setPaymentInterval(tab);
              }}
            >
              <div className='flex flex-row items-baseline gap-2'>
                <Text
                  as='p'
                  level='headerSmall'
                  className='text-[18px] leading-[22px]'
                >
                  {tab}
                </Text>
                {tab === 'Quarterly' ? (
                  <Text as='p' level='labelMedium'>
                    Save 10%
                  </Text>
                ) : null}
                {tab === 'Annual' ? (
                  <Text as='p' level='labelMedium'>
                    Save 15%
                  </Text>
                ) : null}
              </div>
            </Button>
            {index === paymentIntervals.length - 2 && (
              <div className='mx-2 hidden h-8 w-px bg-[#323232] sm:block' />
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

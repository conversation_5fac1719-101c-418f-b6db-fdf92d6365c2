import NumberFlow from '@number-flow/react';
import { useRouter } from 'next/navigation';
import { BiRightArrowAlt, BiCheck, BiStar, BiInfoCircle } from 'react-icons/bi';
import {
  <PERSON><PERSON>,
  Spinner,
  Text,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@repo/ui/components';
import { useStripeCheckoutSession } from '@/app/billing/lib/hooks';

export type PricingCardProps = {
  title: string;
  price: number;
  priceIntervalText: string;
  features: string[];
  action:
    | { type: 'link'; link: string; label: string }
    | {
        type: 'checkout';
        label: string;
        stripePriceId: string;
        mode: 'subscription' | 'payment';
        invoiceCreation?: boolean;
      }
    | { type: 'contact'; label: string };
};

export const PricingCard = (props: PricingCardProps) => {
  const { handleCheckout } = useStripeCheckoutSession();
  const router = useRouter();
  return (
    <div className='flex h-full flex-col justify-between gap-10 rounded-2xl border border-transparent bg-[#1C1C1C] px-6 pb-6 pt-8 2xl:px-6'>
      <div className='flex flex-col gap-4'>
        <div className='flex flex-row items-center gap-2'>
          <Text as='p' level='labelHeaderExtraSmall' className='uppercase'>
            {props.title}
          </Text>
          {props.title === 'Pro User' ? (
            <BiStar color='#00DBBC' size={24} />
          ) : null}
        </div>
        <div className='flex flex-col gap-8'>
          <div>
            <Text
              as='p'
              level='displayLarge'
              className='text-[58px] leading-[64px]'
            >
              $
              <NumberFlow
                value={props.price}
                format={{
                  minimumFractionDigits: props.price % 1 === 0 ? 0 : 2,
                  maximumFractionDigits: 2,
                }}
              />
            </Text>

            <Text as='p' level='buttonLarge' className='opacity-40'>
              {props.priceIntervalText}
            </Text>
          </div>
          <div className='flex flex-col gap-[2px]'>
            {props.features.map((item) => (
              <div key={item} className='flex flex-row items-center gap-4'>
                <BiCheck color='#00DBBC' size={24} className='flex-shrink-0' />
                <Text
                  as='p'
                  level='bodyLarge'
                  className='py-2 text-[16px] leading-[14px] text-[#A2A2A2]'
                >
                  {item}
                </Text>
                {item === 'Pro endpoints' ? (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <BiInfoCircle className='text-white/60' fontSize={16} />
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className='flex flex-col gap-2 p-4'>
                          <Text
                            as='p'
                            level='bodyLarge'
                            className='text-[#A2A2A2]'
                          >
                            Pro endpoints
                          </Text>
                          <ul className='list-inside list-disc'>
                            <Text as='li'>miner-weights</Text>
                            <Text as='li'>accounting/*</Text>
                          </ul>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                ) : null}
              </div>
            ))}
          </div>
        </div>
      </div>
      <div>
        <Button
          disabled={handleCheckout.isPending || handleCheckout.isSuccess}
          variant='cta2'
          className='flex h-10 w-full flex-row items-center gap-2 rounded-lg px-4 py-2'
          onClick={() => {
            if (props.action.type === 'link') {
              router.push(props.action.link);
            }

            if (props.action.type === 'checkout') {
              handleCheckout.mutate({
                priceId: props.action.stripePriceId,
                mode: props.action.mode,
                invoiceCreation: props.action.invoiceCreation,
              });
            }

            if (props.action.type === 'contact') {
              window.location.href =
                'mailto:<EMAIL>?subject=Enterprise Plan Enquiry';
            }
          }}
        >
          <Text level='headerExtraSmall' className='text-[#000000]'>
            {props.action.label}
          </Text>

          {handleCheckout.isPending ? <Spinner /> : null}
          {handleCheckout.isSuccess ? (
            <BiCheck className='text-green-500' />
          ) : null}
          {!handleCheckout.isPending && !handleCheckout.isSuccess ? (
            <BiRightArrowAlt size={24} />
          ) : null}
        </Button>
      </div>
    </div>
  );
};

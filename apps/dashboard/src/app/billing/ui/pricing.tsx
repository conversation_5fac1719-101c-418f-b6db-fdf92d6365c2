import { useState } from 'react';
import { Text } from '@repo/ui/components';
import { BillingTabs } from './billing-tabs';
import { Cards } from './cards';
import { EnterpriseBanner } from './enterprise-banner';
import type { PaymentInterval } from '@/app/billing/lib/types';

export const Pricing = () => {
  return (
    <div className='mx-auto flex max-w-[1400px] flex-col gap-8 p-4 2xl:px-0'>
      <Text level='headerMedium' className='text-center text-[#CACACA]'>
        The API for the taostats explorer. Your gateway to the Bittensor
        network.
      </Text>
      <BillingCards />
    </div>
  );
};

export const BillingCards = () => {
  const [paymentInterval, setPaymentInterval] =
    useState<PaymentInterval>('Monthly');
  return (
    <>
      <BillingTabs
        paymentInterval={paymentInterval}
        setPaymentInterval={setPaymentInterval}
      />
      <Cards paymentInterval={paymentInterval} />
      <EnterpriseBanner />
    </>
  );
};

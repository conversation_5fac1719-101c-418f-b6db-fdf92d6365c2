import { BiBriefcase, BiEnvelope } from 'react-icons/bi';
import { Button, Text } from '@repo/ui/components';

export const EnterpriseBanner = () => {
  return (
    <div className='mb-20'>
      <div className='w-full overflow-hidden rounded-3xl border border-neutral-900 bg-[#1C1C1C] p-12'>
        <div className='flex flex-col items-start justify-between gap-8 lg:flex-row lg:items-center lg:gap-4'>
          <div className='flex flex-col gap-2'>
            <div className='flex flex-row items-center gap-2'>
              <Text as='p' level='xs' className='uppercase'>
                Enterprise
              </Text>
              <BiBriefcase color='#00DBBC' size={24} />
            </div>
            <Text
              as='h3'
              level='displayLarge'
              className='text-[36px] leading-[48px]'
            >
              Custom Plans
            </Text>
            <Text as='p' level='bodyLarge' className='py-2 text-[#A2A2A2]'>
              Ideal for enterprise users who require large-scale Bittensor data,
              our premium offering features application specific rate limit and
              dedicated SLA support to ensure seamless integration.
            </Text>
          </div>
          <Button
            className='flex w-full flex-row items-center gap-2 lg:w-auto lg:min-w-60'
            variant='cta2'
            onClick={() => {
              window.location.href =
                'mailto:<EMAIL>?subject=Enterprise Plan Enquiry';
            }}
          >
            Contact Us
            <BiEnvelope size={16} />
          </Button>
        </div>
      </div>
    </div>
  );
};

import { type ReactNode } from 'react';
import NumberFlow from '@number-flow/react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, BiRightArrowAlt } from 'react-icons/bi';
import { Al<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ner, Text } from '@repo/ui/components';
import { notify } from '@repo/ui/lib';
import { PricingCard } from './card';
import { usePricingMap } from '@/app/billing/lib/config';
import { useStripePortalSessionBuilder } from '@/app/billing/lib/hooks';
import { PageLayout } from '@/components/page-layout';
import { useUserAccount } from '@/lib/hooks/user-hooks';

export function BillingAccount() {
  const { data: userAccount } = useUserAccount();
  const { mutate, isPending, isSuccess, isError } =
    useStripePortalSessionBuilder();
  const { pricingMap } = usePricingMap();

  if (!userAccount) {
    return null;
  }

  return (
    <PageLayout title='Billing'>
      <div>
        <Text level='bodyMedium' className='text-gray-400'>
          Information about your current plan
        </Text>
      </div>
      <div className='mt-6 border-t border-white/10'>
        <dl className='divide-y divide-white/10'>
          <AccountRow
            label='Plan'
            value={userAccount.subscription.productName}
          />
          <AccountRow
            label='Credit Balance'
            value={<NumberFlow value={userAccount.creditBalance} />}
          />
          <AccountRow
            label='Rate Limit (per min)'
            value={<NumberFlow value={userAccount.rateLimit.rateLimit} />}
          />

          <AccountRow
            label='Purchase Add-ons'
            value={
              <div className='flex flex-col gap-4 sm:flex-row'>
                {pricingMap['Add-ons'].map((addOn) => {
                  return <PricingCard key={addOn.title} {...addOn} />;
                })}
              </div>
            }
          />

          <AccountRow
            value={
              <Button
                disabled={isPending || isSuccess}
                className='flex items-center gap-2'
                variant='cta2'
                onClick={() => {
                  mutate(
                    {
                      customerId: userAccount.stripeCustomerId,
                    },
                    {
                      onSuccess: (data) => {
                        document.location.href = data.session.url;
                        notify.success('Redirecting to billing portal...');
                      },
                    }
                  );
                }}
              >
                Manage Plan
                {isPending ? <Spinner /> : null}
                {isSuccess ? (
                  <BiCheck className='ml-2 inline text-green-500' />
                ) : null}
                {!isPending && !isSuccess && (
                  <BiRightArrowAlt className='ml-2 inline' />
                )}
              </Button>
            }
          />

          {isError ? (
            <AccountRow
              value={
                <AlertError
                  title='Error'
                  description='Error retrieving your billing information. Please try again later.'
                />
              }
            />
          ) : null}
        </dl>
      </div>
    </PageLayout>
  );
}

const AccountRow = ({
  label = '',
  value,
}: {
  label?: string;
  value: ReactNode;
}) => {
  return (
    <div className='px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0'>
      <dt className='text-sm/6 font-medium text-white'>{label}</dt>
      <dd className='mt-1 text-sm/6 text-gray-400 sm:col-span-2 sm:mt-0'>
        {value}
      </dd>
    </div>
  );
};

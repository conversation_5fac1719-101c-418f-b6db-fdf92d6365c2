import type { PaymentInterval } from './types';
import type { PricingCardProps } from '@/app/billing/ui/card';
import { useAppEnv } from '@/lib/hooks/global-hooks';

const basicFeatures = [
  '5 API calls/min',
  '10k API calls/month',
  'Attribution appreciated',
  'Public endpoints',
  'Community support',
];

const standardFeatures = [
  '60 API calls/min',
  '50k API calls/month',
  'Public endpoints',
  'Escalated support',
];

const proFeatures = [
  '240 API calls/min',
  '500k API calls/month',
  'Public endpoints',
  'Pro endpoints',
  'RPC archive endpoint',
  'Priority support',
  'Scalable API calls',
];

const pricingIdMap = {
  development: {
    monthlyStandard: 'price_1Qcpw1PVG4Hj5rvCRvcZmBne',
    monthlyPro: 'price_1Qcpw1PVG4Hj5rvCRvcZmBne',
    quarterlyStandard: 'price_1Qcpw1PVG4Hj5rvCRvcZmBne',
    quarterlyPro: 'price_1Qcpw1PVG4Hj5rvCRvcZmBne',
    annualStandard: 'price_1Qcq34PVG4Hj5rvCoyk0Ov6H',
    annualPro: 'price_1Qcq34PVG4Hj5rvCoyk0Ov6H',
    addOn250k: 'price_1QcrwBPVG4Hj5rvCRJLdWinj',
    addOn1m: 'price_1QcrwBPVG4Hj5rvCRJLdWinj',
  },
  // test is the same as development
  test: {
    monthlyStandard: 'price_1Qcpw1PVG4Hj5rvCRvcZmBne',
    monthlyPro: 'price_1Qcpw1PVG4Hj5rvCRvcZmBne',
    quarterlyStandard: 'price_1Qcpw1PVG4Hj5rvCRvcZmBne',
    quarterlyPro: 'price_1Qcpw1PVG4Hj5rvCRvcZmBne',
    annualStandard: 'price_1Qcq34PVG4Hj5rvCoyk0Ov6H',
    annualPro: 'price_1Qcq34PVG4Hj5rvCoyk0Ov6H',
    addOn250k: 'price_1QcrwBPVG4Hj5rvCRJLdWinj',
    addOn1m: 'price_1QcrwBPVG4Hj5rvCRJLdWinj',
  },
  // beta is the same as production
  beta: {
    monthlyStandard: 'price_1QiDryBPOIYksFeauYnKQDzy',
    monthlyPro: 'price_1QiDtPBPOIYksFeatrRO8KPi',
    quarterlyStandard: 'price_1QiE5RBPOIYksFeaW9U4QVTr',
    quarterlyPro: 'price_1QiE3nBPOIYksFeaEnXUcywC',
    annualStandard: 'price_1QiE57BPOIYksFeaW6SajbIl',
    annualPro: 'price_1QiE4QBPOIYksFea49L8r568',
    addOn250k: 'price_1QiDujBPOIYksFeaGgqJ67bh',
    addOn1m: 'price_1QiE0FBPOIYksFeaAl4HEn53',
  },
  production: {
    monthlyStandard: 'price_1QiDryBPOIYksFeauYnKQDzy',
    monthlyPro: 'price_1QiDtPBPOIYksFeatrRO8KPi',
    quarterlyStandard: 'price_1QiE5RBPOIYksFeaW9U4QVTr',
    quarterlyPro: 'price_1QiE3nBPOIYksFeaEnXUcywC',
    annualStandard: 'price_1QiE57BPOIYksFeaW6SajbIl',
    annualPro: 'price_1QiE4QBPOIYksFea49L8r568',
    addOn250k: 'price_1QiDujBPOIYksFeaGgqJ67bh',
    addOn1m: 'price_1QiE0FBPOIYksFeaAl4HEn53',
  },
};

const usePricingIds = () => {
  const appEnv = useAppEnv();
  const pricingIds = pricingIdMap[appEnv];
  return { pricingIds };
};

const useBasePricingModels = () => {
  const { pricingIds } = usePricingIds();

  const basePricingModels: Record<
    'free' | 'standard' | 'pro',
    PricingCardProps
  > = {
    free: {
      title: 'Free',
      price: 0,
      priceIntervalText: 'Free forever',
      features: basicFeatures,
      action: { type: 'link', link: '/api-keys', label: 'Get Started' },
    },
    standard: {
      title: 'Standard',
      price: 49,
      priceIntervalText: 'per month',
      features: standardFeatures,
      action: {
        type: 'checkout',
        label: 'Upgrade',
        stripePriceId: pricingIds.monthlyStandard,
        mode: 'subscription',
      },
    },
    pro: {
      title: 'Pro User',
      price: 199,
      priceIntervalText: 'per month',
      features: proFeatures,

      action: {
        type: 'checkout',
        label: 'Upgrade',
        stripePriceId: pricingIds.monthlyPro,
        mode: 'subscription',
      },
    },
  } as const;

  const getPricingModel = (
    pricingModelKey: keyof typeof basePricingModels,
    overrides?: {
      price?: number;
      priceIntervalText?: string;
      stripePriceId?: string;
      mode?: 'subscription' | 'payment';
    }
  ) => {
    let pricingModel = structuredClone(basePricingModels[pricingModelKey]);

    if (overrides) {
      pricingModel = { ...pricingModel, ...overrides };
      if (pricingModel.action.type === 'checkout' && overrides.stripePriceId) {
        pricingModel.action.stripePriceId = overrides.stripePriceId;
      }
      if (pricingModel.action.type === 'checkout' && overrides.mode) {
        pricingModel.action.mode = overrides.mode;
      }
    }

    return pricingModel;
  };

  return { basePricingModels, getPricingModel };
};

type PricingMap = Record<PaymentInterval, PricingCardProps[]>;

export const usePricingMap = () => {
  const { getPricingModel } = useBasePricingModels();
  const { pricingIds } = usePricingIds();

  const pricingMap: PricingMap = {
    Monthly: [
      getPricingModel('free'),
      getPricingModel('standard', {
        stripePriceId: pricingIds.monthlyStandard,
      }),
      getPricingModel('pro', {
        stripePriceId: pricingIds.monthlyPro,
      }),
    ],
    Quarterly: [
      getPricingModel('free'),
      getPricingModel('standard', {
        price: 132,
        priceIntervalText: 'per quarter',
        stripePriceId: pricingIds.quarterlyStandard,
      }),
      getPricingModel('pro', {
        price: 537,
        priceIntervalText: 'per quarter',
        stripePriceId: pricingIds.quarterlyPro,
      }),
    ],
    Annual: [
      getPricingModel('free'),
      getPricingModel('standard', {
        price: 499,
        priceIntervalText: 'per year',
        stripePriceId: pricingIds.annualStandard,
      }),
      getPricingModel('pro', {
        price: 2029,
        priceIntervalText: 'per year',
        stripePriceId: pricingIds.annualPro,
      }),
    ],
    'Add-ons': [
      {
        title: 'API credit top-up - 250k',
        price: 99,
        priceIntervalText: 'add-on',
        features: [
          '250k API credits',
          'Uses your plan’s per-second request cap',
        ],
        action: {
          type: 'checkout',
          label: 'Pay now',
          stripePriceId: pricingIds.addOn250k,
          mode: 'payment',
          invoiceCreation: true,
        },
      },
      {
        title: 'API credit top-up - 1m',
        price: 199,
        priceIntervalText: 'add-on',
        features: ['1m API credits', 'Uses your plan’s per-second request cap'],
        action: {
          type: 'checkout',
          label: 'Pay now',
          stripePriceId: pricingIds.addOn1m,
          mode: 'payment',
          invoiceCreation: true,
        },
      },
    ],
  };

  return { pricingMap };
};

import { loadStripe } from '@stripe/stripe-js';
import { useMutation } from '@tanstack/react-query';
import { notify } from '@repo/ui/lib';
import { apiClient, handleResponse } from '@/app/api/[...route]/lib/api-client';
import { useUserAccount } from '@/lib/hooks/user-hooks';
import { useSessionContext } from '@/lib/providers/session-context-provider';

const stripeClient = apiClient.stripe;

export const useStripePortalSessionBuilder = () => {
  return useMutation({
    mutationFn: async (
      data: Parameters<typeof stripeClient.createPortalSession.$post>[0]['json']
    ) =>
      handleResponse(
        await stripeClient.createPortalSession.$post({
          json: data,
        })
      ),
  });
};

const useStripeCheckoutSessionBuilder = () => {
  return useMutation({
    mutationFn: async (
      data: Parameters<
        typeof stripeClient.createCheckoutSession.$post
      >[0]['json']
    ) =>
      handleResponse(
        await stripeClient.createCheckoutSession.$post({
          json: data,
        })
      ),
  });
};

const useStripeCustomerSessionBuilder = () => {
  return useMutation({
    mutationFn: async () =>
      handleResponse(await stripeClient.getClientSecret.$post()),
  });
};

export const useStripeCheckoutSession = () => {
  const { stripeClientPublicKey } = useSessionContext();
  const checkoutSessionBuilder = useStripeCheckoutSessionBuilder();
  const stripeCustomerSessionBuilder = useStripeCustomerSessionBuilder();
  const userAccountQuery = useUserAccount();
  const { session } = useSessionContext();

  const handleCheckout = useMutation({
    mutationFn: async ({
      priceId,
      mode,
      invoiceCreation = false,
    }: {
      priceId: string;
      mode: 'subscription' | 'payment';
      invoiceCreation?: boolean;
    }) => {
      // Check if we already have a stripe customer id
      let customerId = session.userProfile?.stripeCustomerId;

      if (!customerId) {
        // We don't have a stripe customer id, so we need to create one
        try {
          await stripeCustomerSessionBuilder.mutateAsync();

          // If the stripeCustomerSessionBuilder mutation succeeded, it means the userAccount should now have a stripeCustomerId
          // So re-fetch the user account to get the new stripeCustomerId
          const refetchedCustomerAccount = await userAccountQuery.refetch();
          if (!refetchedCustomerAccount.data?.stripeCustomerId) {
            notify.error(
              'Created stripe customer session, but could not get stripe customer id. Please try again later.'
            );
            return;
          }

          customerId = refetchedCustomerAccount.data.stripeCustomerId;
        } catch (err) {
          notify.error('Error creating stripe customer session');
          return;
        }
      }

      if (!stripeClientPublicKey) {
        notify.error('Stripe public key not found');
        return;
      }

      const stripe = await loadStripe(stripeClientPublicKey);

      if (!stripe) {
        notify.error('Could not get Stripe client');
        return;
      }

      try {
        // Create checkout session
        const checkoutSession = await checkoutSessionBuilder.mutateAsync({
          priceId,
          customerId,
          mode,
          invoiceCreation,
        });

        // Redirect to checkout
        await stripe
          .redirectToCheckout({ sessionId: checkoutSession.sessionId })
          .catch(() => {
            notify.error(
              'Error redirecting to checkout. Please try again later.'
            );
          });
      } catch (err: unknown) {
        const error = err as { message: string };
        notify.error(`Error creating checkout session. ${error.message}`);
        throw err;
      }
    },
  });

  return { handleCheckout };
};

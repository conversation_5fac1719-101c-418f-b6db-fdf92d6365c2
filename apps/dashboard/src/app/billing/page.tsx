'use client';

import { AlertError, Skeleton } from '@repo/ui/components';
import { BillingAccount } from './ui/billing-account';
import { Pricing } from './ui/pricing';
import { useUserAccount } from '@/lib/hooks/user-hooks';

const PricingPage = () => {
  const { isLoading, isError, data: userAccount } = useUserAccount();

  if (isLoading) {
    return (
      <div className='flex flex-col gap-4'>
        <Skeleton className='h-[100px] w-full' />
        <Skeleton className='h-[100px] w-full' />
      </div>
    );
  }

  if (isError) {
    return (
      <AlertError title='Error' description='Error fetching user account' />
    );
  }

  if (userAccount && userAccount.subscription.isActive) {
    return <BillingAccount />;
  }

  return <Pricing />;
};

export default PricingPage;

@tailwind base;
@tailwind components;
@tailwind utilities;

@keyframes fadeInBg {
  from {
    opacity: 0;
  }
  to {
    opacity: 0.75;
  }
}

.gradient-container {
  position: fixed;
  inset: 0;
  z-index: 0;
  pointer-events: none;
}

.with-gradient::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('/images/background/bottom-gradient.png');
  background-position: bottom;
  background-repeat: no-repeat;
  background-size: contain;
  animation: fadeInBg 1s ease-in;
  opacity: 0.75;
}

.border-right-dashed {
  border-right-style: dashed;
}

* {
	scrollbar-width: thin;
	scrollbar-color: #656565 #2b2b2b;
}

::-webkit-scrollbar {
	width: 4px; 
	height: 8px; 
}

::-webkit-scrollbar-thumb {
	background: #656565;
	border-radius: 1px;
}
import '@/app/globals.css';
import type { Viewport } from 'next';
import { SessionProvider } from 'next-auth/react';
import { Toaster } from 'react-hot-toast';
import { cn, everettFont, fontFira } from '@repo/ui/lib';
import { DashboardLayoutProvider } from '@/app/dashboard/dashboard-layout-provider';
import { constructMetadata } from '@/lib/meta';
import QueryProvider from '@/lib/providers/query-provider';
import { getServerSession } from '@/lib/utils/auth-utils';

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

export const metadata = constructMetadata({});

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { session } = await getServerSession();

  return (
    <html className='bg-app-bg h-full' lang='en'>
      <body
        className={cn(
          'font-everett',
          everettFont.className,
          fontFira.variable,
          'h-full',
          'bg-app-bg min-h-screen overflow-x-hidden text-white'
        )}
      >
        <Toaster
          toastOptions={{
            style: {
              maxWidth: 500,
            },
          }}
        />
        <SessionProvider basePath='/api/auth' session={session}>
          <QueryProvider>
            <DashboardLayoutProvider>{children}</DashboardLayoutProvider>
          </QueryProvider>
        </SessionProvider>
      </body>
    </html>
  );
}

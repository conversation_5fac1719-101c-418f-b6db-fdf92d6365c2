import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { queryKeys } from './constants';
import { apiClient, handleResponse } from '@/app/api/[...route]/lib/api-client';

const client = apiClient.apiKeys;

export const useApiKeys = () => {
  return useQuery({
    queryKey: [queryKeys.apiKeys],
    queryFn: async () => handleResponse(await client.$get()),
  });
};

export const useCreateApiKey = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: Parameters<typeof client.$post>[0]['json']) =>
      (
        await client.$post({
          json: data,
        })
      ).json(),
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: [queryKeys.apiKeys] });
    },
  });
};

export const useDeleteApiKey = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (apiKeyId: string) => {
      await client.$delete({
        json: { apiKeyId },
      });
    },
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: [queryKeys.apiKeys] });
    },
  });
};

export const useApiKeysUsage = (apiKeyId: string) => {
  return useQuery({
    queryKey: [queryKeys.apiKeysUsage, apiKeyId],
    queryFn: async () =>
      handleResponse(
        await client[':apiKeyId'].usage.$get({
          param: { apiKeyId },
        })
      ),
    enabled: Boolean(apiKeyId),
  });
};

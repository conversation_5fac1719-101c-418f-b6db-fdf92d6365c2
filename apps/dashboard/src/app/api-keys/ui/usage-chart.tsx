import { useCallback, useMemo } from 'react';
import { ParentSize } from '@visx/responsive';
import { defaultStyles as tooltipDefaultStyles } from '@visx/tooltip';
import {
  AreaSeries,
  Axis,
  Grid,
  Tooltip,
  type TooltipData,
  XYChart,
  buildChartTheme,
} from '@visx/xychart';
import { timeFormat } from 'd3-time-format';
import { Alert, Text } from '@repo/ui/components';
import { useApiKeysUsage } from '@/app/api-keys/lib/hooks';

const formatDate = timeFormat("%b %d, '%y");

interface UsageData {
  date: Date;
  count: number;
}

export const UsageChart = ({ apiKey }: { apiKey: string }) => {
  const { data: usageData } = useApiKeysUsage(apiKey);
  const logs = usageData?.usage;

  const aggregatedLogs = logs?.reduce(
    (acc, log) => {
      const date = new Date(log.timestamp).setHours(0, 0, 0, 0);
      if (!acc[date]) {
        acc[date] = 0;
      }
      acc[date]++;
      return acc;
    },
    {} as Record<number, number>
  );

  const data: UsageData[] = Object.entries(aggregatedLogs || {})
    .map(([date, count]) => ({
      date: new Date(Number(date)),
      count,
    }))
    .sort((a, b) => a.date.getTime() - b.date.getTime());

  if (data.length === 0) {
    return (
      <Alert
        type='default'
        title='No usage data'
        description={
          <Text as='span' level='labelMedium' className='text-white/60]'>
            No usage data available for this API key. Copy your API Key above
            and head to{' '}
            <a
              className='text-accent-1 underline'
              href='https://docs.taostats.io'
              target='_blank'
              rel='noreferrer'
            >
              docs.taostats.io
            </a>{' '}
            to get started.
          </Text>
        }
      />
    );
  }

  return <ChartMain data={data} />;
};

const ChartMain = ({ data }: { data: UsageData[] }) => {
  const customTheme = useMemo(
    () =>
      buildChartTheme({
        backgroundColor: '',
        colors: ['#FFCD29'],
        gridColor: '#FFFFFF33',
        gridColorDark: '#FFFFFF33',
        tickLength: 1,
        svgLabelSmall: { fill: '#CDCDCD' },
        svgLabelBig: { fill: '#CDCDCD' },
        xAxisLineStyles: { stroke: '#323232' },
        yAxisLineStyles: { stroke: '#323232' },
      }),
    []
  );

  const handleRenderTooltip = useCallback(
    ({ tooltipData }: { tooltipData?: TooltipData }) => {
      if (!tooltipData || !tooltipData.nearestDatum) {
        return null;
      }

      const { key } = tooltipData.nearestDatum;
      const datum = tooltipData.nearestDatum.datum as UsageData;
      const date = datum.date;
      const count = datum.count;

      return (
        <div
          key={key}
          className='min-w-60.75 bg-[#0E0E0E] bg-opacity-50 px-5 py-3 backdrop-blur-xl'
        >
          <div className='flex flex-col gap-2'>
            <div className=''>
              <Text level='bodySmall'>{formatDate(date)}</Text>
            </div>
            <div className=''>
              <Text level='bodySmall'>{count} Requests</Text>
            </div>
          </div>
        </div>
      );
    },
    []
  );

  return (
    <ParentSize>
      {({ width }) => {
        return (
          <XYChart
            data-testid='xy-chart'
            width={width}
            height={400}
            xScale={{ type: 'time' }}
            yScale={{ type: 'linear' }}
            theme={customTheme}
          >
            <Grid columns={false} numTicks={5} />
            <AreaSeries
              dataKey='Area'
              data={data}
              xAccessor={(d) => d.date}
              yAccessor={(d) => d.count}
              fillOpacity={0.4}
            />
            <Axis
              orientation='bottom'
              tickFormat={formatDate}
              numTicks={width <= 768 ? 3 : 4}
            />
            <Axis orientation='left' />
            <Tooltip
              style={{
                ...tooltipDefaultStyles,
                backgroundColor: '#0E0E0E',
                borderRadius: '8px',
                padding: '10px',
              }}
              snapTooltipToDatumX
              snapTooltipToDatumY
              showVerticalCrosshair
              showSeriesGlyphs
              renderTooltip={handleRenderTooltip}
            />
          </XYChart>
        );
      }}
    </ParentSize>
  );
};

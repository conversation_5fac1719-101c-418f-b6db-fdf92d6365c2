'use client';

import { <PERSON><PERSON><PERSON><PERSON> } from 'react-icons/bi';
import type { Api<PERSON><PERSON> } from '@repo/types/dashboard-api-types';
import {
  <PERSON><PERSON>,
  Button,
  Card,
  CopyToClipboardField,
  Text,
} from '@repo/ui/components';

export const ApiKeyCard = ({
  apiKey,
  handleDeleteClick: handeDeleteClick,
}: {
  apiKey: ApiKey;
  handleDeleteClick: (apiKeyId: string) => void;
}) => {
  const areOldKeysDeprecated = new Date() > new Date('2025-01-14');

  return (
    <Card
      title={apiKey.name ?? 'No Name Specified'}
      subTitle={apiKey.description}
      headerRight={
        <Button
          onClick={() => {
            handeDeleteClick(apiKey.apiKeyId);
          }}
          size='sm'
          type='button'
          variant='default'
        >
          Delete API Key
        </Button>
      }
    >
      <div className='flex flex-col gap-6'>
        <CopyToClipboardField LeftIcon={BiKey} value={apiKey.apiKeyId} />

        {!apiKey.apiKeyId.startsWith('tao') ? (
          <Alert
            type='warning'
            title='Warning'
            description={
              <div className='flex flex-col gap-2'>
                {areOldKeysDeprecated ? (
                  <Text level='bodyMedium'>
                    This API key is deprecated and will no longer work. Please
                    create a new API key to continue using the Taostats API.
                  </Text>
                ) : (
                  <>
                    <Text level='bodyMedium'>
                      This API key will be deprecated on Jan 14th 2025. Please
                      create a new API key to continue using the Taostats API.
                    </Text>
                    <Text level='bodyMedium'>
                      You may see some reduced rate limits. If you had a custom
                      rate limit with your old API keys, these limits will also
                      apply to your new API keys.
                    </Text>
                  </>
                )}
              </div>
            }
          />
        ) : null}
      </div>
    </Card>
  );
};

import { useState } from 'react';
import type { Api<PERSON><PERSON> } from '@repo/types/dashboard-api-types';
import { Alert } from '@repo/ui/components';
import { ApiKeyCard } from './api-key-card';
import { CreateApiKeyButton } from '@/app/api-keys/ui/create-api-key-button';
import { DeleteApiKeyDialog } from '@/app/api-keys/ui/delete-api-key-dialog';
import { useUserAccount } from '@/lib/hooks/user-hooks';

export const ApiKeysList = ({ apiKeys }: { apiKeys: ApiKey[] }) => {
  const [apiKeyToDelete, setApiKeyToDelete] = useState<string | null>(null);
  const handleDeleteClick = (apiKeyId: string) => {
    setApiKeyToDelete(apiKeyId);
  };
  const { data: userAccount } = useUserAccount();

  return (
    <>
      <div className='flex flex-col gap-8'>
        <div className='flex flex-col items-center gap-4 sm:flex-row'>
          <CreateApiKeyButton btnVariant='default' title='Add an API Key' />
          {userAccount?.rateLimit.rateLimit !== undefined ? (
            <Alert
              type='info'
              description={`Your rate limit is ${userAccount.rateLimit.rateLimit} requests per minute.`}
              className='animate-in fade-in max-w-xl flex-1 duration-300'
            />
          ) : null}
        </div>

        <div className='flex flex-col gap-8'>
          {apiKeys.map((apiKey) => (
            <ApiKeyCard
              apiKey={apiKey}
              handleDeleteClick={handleDeleteClick}
              key={apiKey.apiKeyId}
            />
          ))}
        </div>
      </div>

      <DeleteApiKeyDialog
        apiKeyToDelete={apiKeyToDelete}
        clearApiKeyToDelete={() => {
          setApiKeyToDelete(null);
        }}
      />
    </>
  );
};

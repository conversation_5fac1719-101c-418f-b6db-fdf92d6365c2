import { useEffect } from 'react';
import { BiTrash } from 'react-icons/bi';
import { Alert, Button, Dialog, Text } from '@repo/ui/components';
import { notify } from '@repo/ui/lib';
import { useDeleteApiKey } from '@/app/api-keys/lib/hooks';

export const DeleteApiKeyDialog = ({
  apiKeyToDelete,
  clearApiKeyToDelete,
}: {
  apiKeyToDelete: string | null;
  clearApiKeyToDelete: () => void;
}) => {
  const deleteApiKey = useDeleteApiKey();
  const handleDeleteApiKey = () => {
    if (!apiKeyToDelete) {
      notify.error('Did not receive an API key to delete');
      return;
    }

    deleteApiKey.mutate(apiKeyToDelete, {
      onSuccess: () => {
        notify.success('API key deleted');
        clearApiKeyToDelete();
      },
    });
  };

  useEffect(() => {
    if (apiKeyToDelete === null) {
      deleteApiKey.reset();
    }
  }, [apiKeyToDelete, deleteApiKey]);

  return (
    <Dialog
      HeaderIcon={BiTrash}
      closeFn={clearApiKeyToDelete}
      isOpen={apiKeyToDelete !== null}
      title='Delete API Key'
    >
      <div className='flex flex-col gap-6'>
        <Text as='p' level='bodyMedium'>
          You&apos;re going to delete the API key:{' '}
        </Text>
        {apiKeyToDelete ? (
          <div className='bg-gray-highlight flex items-center gap-2 rounded-md border border-white/10 p-3'>
            <code className='truncate text-center'>{apiKeyToDelete}</code>
          </div>
        ) : null}

        <Alert
          type='warning'
          description={`Please note: This can't be undone. Are you sure you want to proceed?`}
        />
      </div>

      <div className='flex flex-col gap-4'>
        <Button
          className='group w-full hover:bg-red-500 hover:text-white'
          disabled={!apiKeyToDelete || deleteApiKey.isPending}
          onClick={handleDeleteApiKey}
          size='lg'
          type='button'
          variant='default'
        >
          <BiTrash className='mr-2 h-5 w-5 text-red-500 group-hover:text-white' />
          Yes, Delete API Key
        </Button>
        {deleteApiKey.isError ? (
          <Alert
            type='error'
            description='An error occurred while deleting the API key.'
            title='Error'
          />
        ) : null}
      </div>
    </Dialog>
  );
};

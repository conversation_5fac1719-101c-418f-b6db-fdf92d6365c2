import { useState } from 'react';
import type { ApiKey } from '@repo/types/dashboard-api-types';
import { Alert, Card, Combobox, Text } from '@repo/ui/components';
import { UsageChart } from './usage-chart';

export const Usage = ({ apiKeys }: { apiKeys: ApiKey[] }) => {
  const [selectedKey, setSelectedKey] = useState(apiKeys[0].apiKeyId);

  return (
    <Card title='Usage Chart'>
      <div className='flex flex-col gap-8'>
        <div>
          <Combobox
            value={selectedKey}
            onChange={setSelectedKey}
            options={apiKeys.map((apiKey) => ({
              value: apiKey.apiKeyId,
              label: apiKey.name ?? 'No Name Specified',
            }))}
          />
        </div>
        <div className='flex w-full flex-col gap-4 bg-[#272727]'>
          <Text level='headerExtraSmall'>Daily token usage</Text>
          {selectedKey ? <UsageChart apiKey={selectedKey} /> : null}
          {!selectedKey ? (
            <Alert
              type='info'
              title='No API key selected.'
              description='Please select an API key to view usage data.'
            />
          ) : null}
        </div>
      </div>
    </Card>
  );
};

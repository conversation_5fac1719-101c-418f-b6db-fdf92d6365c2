'use client';

import { useEffect, useRef, useState } from 'react';
import {
  BiArrowBack,
  BiBuildings,
  BiCheck,
  BiKey,
  BiPen,
} from 'react-icons/bi';
import {
  <PERSON><PERSON>,
  <PERSON>ertError,
  Button,
  CopyToClipboardField,
  Dialog,
  Input,
  Spinner,
  Text,
} from '@repo/ui/components';
import { notify } from '@repo/ui/lib';
import { useApiKeys, useCreateApiKey } from '@/app/api-keys/lib/hooks';

const defaultData = {
  name: '',
  description: '',
};

export default function CreateApiKeyDialog({
  isOpen,
  setOpen,
}: {
  isOpen: boolean;
  setOpen: (open: boolean) => void;
}) {
  const { data: existingApiKeys } = useApiKeys();
  const [data, setData] = useState(defaultData);
  const [state, setState] = useState<null | 'key-exists' | 'key-created'>(null);
  const [newApi<PERSON>ey, setNew<PERSON>piKey] = useState<string | null>(null);
  const nameInputRef = useRef<HTMLInputElement>(null);

  const closeAndResetState = () => {
    setOpen(false);
    setTimeout(() => {
      setState(null);
      setData(defaultData);
      setNewApiKey(null);
    }, 200);
  };

  useEffect(() => {
    if (isOpen) {
      setTimeout(() => {
        nameInputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setData({ ...data, [e.target.name]: e.target.value });
  };

  const createApiKey = useCreateApiKey();
  const handleCreateApiKey = ({
    overrideDupliateWarning = false,
  }: {
    overrideDupliateWarning?: boolean;
  } = {}) => {
    if (existingApiKeys?.apiKeys.find((apiKey) => apiKey.name === data.name)) {
      if (!overrideDupliateWarning) {
        setState('key-exists');
        return;
      }
    }

    createApiKey.mutate(data, {
      onSuccess: (responseData) => {
        notify.icon('API key created', '🎉');
        if (responseData.apiKey.apiKeyId) {
          setState('key-created');
          setNewApiKey(responseData.apiKey.apiKeyId);
        } else {
          closeAndResetState();
        }
      },
    });
  };

  return (
    <Dialog
      closeFn={() => {
        if (state === 'key-created') {
          closeAndResetState();
        } else {
          setOpen(false);
          setTimeout(() => {
            setState(null);
          }, 200);
        }
      }}
      isOpen={isOpen}
      title='Create My API Key'
      HeaderIcon={BiKey}
    >
      <div className='flex flex-col gap-6 text-left'>
        {state === 'key-exists' ? (
          <>
            <Alert
              type='info'
              description={
                <Text as='span'>
                  An API key called &quot;<strong>{data.name}</strong>&quot;
                  already exists.
                  <br />
                  <br />
                  Are you sure you want to create another one with the same
                  name?
                </Text>
              }
              title='API key already exists'
            />

            <div className='flex flex-col gap-4 lg:flex-row'>
              <Button
                onClick={() => {
                  setState(null);
                }}
                size='lg'
                type='button'
                className='flex w-full items-center justify-center gap-2'
              >
                <BiArrowBack />
                Back
              </Button>
              <ConfirmApiKeyButtonCreate
                data={data}
                handleCreateApiKey={() => {
                  handleCreateApiKey({ overrideDupliateWarning: true });
                }}
                isPending={createApiKey.isPending}
                title='Yes, Create API Key'
              />
            </div>
          </>
        ) : null}

        {!newApiKey && state !== 'key-exists' ? (
          <>
            <Input
              IconLeft={BiBuildings}
              IconRight={data.name ? BiCheck : undefined}
              description='Choose a unique name for your API key.'
              inputId='name'
              label='Name'
              onChange={handleChange}
              value={data.name}
              forwardRef={nameInputRef}
            />
            <Input
              IconLeft={BiPen}
              IconRight={data.description ? BiCheck : undefined}
              description='Briefly describe how you’ll use your API Key'
              inputId='description'
              label='Description'
              onChange={handleChange}
              value={data.description}
            />

            {createApiKey.isError ? (
              <AlertError
                description='There was an error creating the API key. Please try again later.'
                title='Error creating API key'
              />
            ) : null}

            <ConfirmApiKeyButtonCreate
              data={data}
              handleCreateApiKey={() => {
                handleCreateApiKey();
              }}
              isPending={createApiKey.isPending}
            />
          </>
        ) : null}

        {newApiKey ? (
          <div className='flex flex-col gap-6'>
            <Text as='p' level='bodyMedium'>
              API Key
            </Text>
            <CopyToClipboardField
              LeftIcon={BiKey}
              value={newApiKey}
              className='text-center'
            />
            <Button
              className='w-full'
              onClick={closeAndResetState}
              size='lg'
              type='button'
              variant='cta2'
            >
              Done
            </Button>
          </div>
        ) : null}
      </div>
    </Dialog>
  );
}

const ConfirmApiKeyButtonCreate = ({
  data,
  handleCreateApiKey,
  isPending,
  title = 'Create API Key',
}: {
  data: typeof defaultData;
  handleCreateApiKey: () => void;
  isPending: boolean;
  title?: string;
}) => {
  return (
    <div>
      <Button
        className='flex w-full items-center justify-center gap-2'
        disabled={!data.name || isPending}
        onClick={handleCreateApiKey}
        size='lg'
        type='button'
        variant='cta2'
      >
        {title}
        {isPending ? <Spinner /> : <BiKey />}
      </Button>
    </div>
  );
};

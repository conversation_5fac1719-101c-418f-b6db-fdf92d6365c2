'use client';

import '@/app/api-keys/style.css';
import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import { BiLinkExternal } from 'react-icons/bi';
import {
  Alert,
  AlertError,
  Flex,
  Link,
  Skeleton,
  Text,
} from '@repo/ui/components';
import { ApiKeysList } from './api-keys-list';
import heroImage from './images/api-keys-hero.png';
import { Usage } from './usage';
import { useApiKeys } from '@/app/api-keys/lib/hooks';
import { CreateApiKeyButton } from '@/app/api-keys/ui/create-api-key-button';
import { BillingCards } from '@/app/billing/ui/pricing';
import { PageLayout } from '@/components/page-layout';
import { useUserAccount } from '@/lib/hooks/user-hooks';

export function ApiKeysPage() {
  const query = useApiKeys();

  const searchParams = useSearchParams();
  const sessionId = searchParams.get('session_id');

  const hasApiKeys = query.isSuccess && query.data.apiKeys.length > 0;

  return (
    <PageLayout title={hasApiKeys ? 'API Keys' : ''}>
      {sessionId ? (
        <Alert
          type='success'
          title='Payment Complete!'
          description='Thank you for your purchase 🎉'
        />
      ) : null}

      {query.isLoading ? (
        <div className='flex flex-col gap-4 '>
          <Skeleton className='h-16 w-full' />
          <Skeleton className='h-16 w-full' />
          <Skeleton className='h-16 w-full' />
        </div>
      ) : null}

      {/* 
        We keep this in the DOM but hide it rather than conditionally rendering
        to avoid the modal from being unmounted when the first api key is created
        and before the confirmation screen is rendered into the modal
      */}
      <NoRecordsUi
        shouldHide={query.isLoading || (query.data?.apiKeys.length ?? 0) > 0}
      />

      {query.isSuccess && query.data.apiKeys.length > 0 ? (
        <>
          <ApiKeysList apiKeys={query.data.apiKeys} />
          <Usage apiKeys={query.data.apiKeys} />
        </>
      ) : null}

      {query.isError ? (
        <AlertError
          className='lg:w-[575px]'
          description='There was an error fetching the API keys.'
          title='Error fetching API keys'
        />
      ) : null}
    </PageLayout>
  );
}

const NoRecordsUi = ({ shouldHide }: { shouldHide: boolean }) => {
  const { data: userAccount } = useUserAccount();

  return (
    <div className={shouldHide ? 'hidden' : ''}>
      <div className='gradient-container-full with-gradient-full' />
      <Flex col className='relative gap-12'>
        <Flex col className='justify-between gap-2 sm:flex-row-reverse'>
          <div className='flex-1'>
            <Image src={heroImage} alt='Hero Image' />
          </div>

          <Flex col className='flex-1 gap-12'>
            <Flex col className='gap-8'>
              <Text
                as='h2'
                level='displayMedium'
                className='text-center sm:text-left'
              >
                Build on Bittensor with <br />
                the taostats API
              </Text>
              <Text
                as='h2'
                level='headerSmall'
                className='text-center text-white/60 sm:text-left'
              >
                Access our API and plug into various data points including
                blockchain information, subnet details, and validator
                statistics.
                <br />
                <br />
                Get started for free!
              </Text>
            </Flex>

            <Flex col className='items-center gap-8 sm:flex-row'>
              <CreateApiKeyButton btnVariant='cta2' title='Add an API Key' />
              <Link
                href='https://docs.taostats.io/docs/index'
                target='_blank'
                className='inline'
              >
                Access documentation
                <BiLinkExternal className='text-accent-1 ml-1 inline' />
              </Link>
            </Flex>
          </Flex>
        </Flex>

        <Flex col className='gap-8 sm:flex-row'>
          <ApiKeyCard
            position='01'
            title='Source of Truth'
            description={`You can have the confidence that taostats' data is accurate, pulled straight from the chain, with added analysis from our team of experts.`}
          />
          <ApiKeyCard
            position='02'
            title='Fastest Insights'
            description='All of the hard-to-get data is at your fingertips,  in milliseconds. From your portfolio returns and accounting to miner stats and APY.'
          />
          <ApiKeyCard
            position='03'
            title='Robust under pressure'
            description='Our API powers the largest Bittensor apps, and is battle tested for heavy traffic. You can build with confidence.'
          />
        </Flex>

        {userAccount && !userAccount.subscription.isActive ? (
          <BillingCards />
        ) : null}
      </Flex>
    </div>
  );
};

const ApiKeyCard = ({
  position,
  title,
  description,
}: {
  position: string;
  title: string;
  description: string;
}) => {
  return (
    <div className='bg-hero-primary flex-1 rounded-2xl px-10 py-8'>
      <Flex col className='gap-6'>
        <Flex className='gap-1'>
          <Text as='h3' level='headerMedium' className='text-accent-1'>
            {position}
          </Text>
          <Text as='h3' level='headerMedium'>
            {title}
          </Text>
        </Flex>
        <Text
          as='p'
          level='bodyMedium'
          className='tracking-[-0.03em] text-[#a2a2a2]'
        >
          {description}
        </Text>
      </Flex>
    </div>
  );
};

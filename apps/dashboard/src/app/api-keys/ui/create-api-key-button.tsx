import { useState } from 'react';
import { Button } from '@repo/ui/components';
import CreateApiKeyDialog from './create-api-key-dialog';

export const CreateApiKeyButton = ({
  title = 'Create API Key',
  btnVariant = 'cta2',
}: {
  title?: string;
  btnVariant?: 'default' | 'cta2';
}) => {
  const [isOpen, setIsOpen] = useState(false);
  return (
    <>
      <Button
        className='flex items-center gap-2'
        onClick={() => {
          setIsOpen(true);
        }}
        size='lg'
        variant={btnVariant}
      >
        <span>{title}</span>
        <span className='text-xl'>+</span>
      </Button>
      <CreateApiKeyDialog isOpen={isOpen} setOpen={setIsOpen} />
    </>
  );
};

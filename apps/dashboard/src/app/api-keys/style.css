.gradient-container-full {
  position: fixed;
  inset: 0;
  z-index: 0;
  pointer-events: none;
}

.with-gradient-full::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('/images/background/full-gradient.png');
  background-position: top;
  background-repeat: no-repeat;
  background-size: cover;
  animation: fadeInBg 1s ease-in;
}
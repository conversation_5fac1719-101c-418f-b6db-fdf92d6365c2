import {
  dehydrate,
  HydrationBoundary,
  QueryClient,
} from '@tanstack/react-query';
import { preFetchApiKeys } from '@/app/api-keys/lib/server-utils';
import { ApiKeysPage } from '@/app/api-keys/ui/api-keys-page';

export const metadata = {
  title: 'API Keys',
};

export default async function ApiKeys() {
  const queryClient = new QueryClient();
  await preFetchApiKeys(queryClient);

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <ApiKeysPage />
    </HydrationBoundary>
  );
}

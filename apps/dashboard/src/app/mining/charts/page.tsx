import { memo } from 'react';
import { Card } from '@repo/ui/components';
import { DateRangeSelector } from '@/app/mining/ui/date-range-selector';
import { HotkeyChart } from '@/app/mining/ui/hotkey-charts/hotkey-chart';
import { ChartTypeOptions } from '@/app/mining/ui/hotkey-charts/utils';
import { PageLayout } from '@/app/mining/ui/page-layout';

export const metadata = {
  title: 'Mining Hotkey Chart',
};

const HotkeyChartSection = ({
  chartType,
  title,
}: {
  chartType: ChartTypeOptions;
  title: string;
}) => (
  <Card
    className='h-[400px] w-full bg-transparent'
    contentContainerClassName='h-full'
  >
    <HotkeyChart chartType={chartType} title={title} />
  </Card>
);

function MiningChartPage() {
  return (
    <PageLayout
      title='Hotkey Chart'
      snap='sm'
      headerRight={
        <div className='flex w-full flex-col items-end gap-8 sm:flex-row-reverse sm:items-center'>
          <DateRangeSelector />
        </div>
      }
      showNavigation
    >
      <HotkeyChartSection
        title='Incentive'
        chartType={ChartTypeOptions.Incentive}
      />
      <HotkeyChartSection
        title='Emission'
        chartType={ChartTypeOptions.Emission}
      />
      <HotkeyChartSection
        title='Weights'
        chartType={ChartTypeOptions.Weights}
      />
      <HotkeyChartSection title='Trust' chartType={ChartTypeOptions.Trust} />
      <HotkeyChartSection title='Rank' chartType={ChartTypeOptions.Rank} />
      <HotkeyChartSection
        title='Pruning Score'
        chartType={ChartTypeOptions.PruningScore}
      />
    </PageLayout>
  );
}

export default memo(MiningChartPage);

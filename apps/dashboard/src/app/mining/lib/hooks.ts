import { useCallback } from 'react';
import { notify } from '@repo/ui/lib';
import { useSavedWalletsQuery } from './query-hooks';

export const useExistingWalletCheck = () => {
  const walletsQuery = useSavedWalletsQuery();

  const doesWalletExist = useCallback(
    (address: string) => {
      // Check if wallet already exists
      const existingWallet = walletsQuery.data?.wallets.find(
        (wallet) => wallet.address === address
      );

      if (existingWallet) {
        notify.error(
          `Wallet already exists for this address. It's called "${existingWallet.name}".`
        );
        return true;
      }

      return false;
    },
    [walletsQuery.data?.wallets]
  );

  return { doesWalletExist };
};

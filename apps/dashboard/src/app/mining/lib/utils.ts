import { useLocalStorage } from 'usehooks-ts';
import { miningDateRanges, miningDays } from './constants';
import type { DateRange } from './types';

export const getDateRangeLabel = (dateRange: DateRange) =>
  miningDateRanges.find((dr) => dr.value === dateRange)?.label;

const TICKS_IN_A_DAY = 24 * 60 * 60 * 1000;

const getStartingTicksForDateRange = (dateRange: DateRange) => {
  const currentDateTicks = Date.now();
  const ticksToSubtract = miningDays[dateRange] * TICKS_IN_A_DAY;
  return currentDateTicks - ticksToSubtract;
};

export const getStartingUnixTimestampForDateRange = (dateRange: DateRange) =>
  Math.floor(getStartingTicksForDateRange(dateRange) / 1000);

export const useLastUsedWallet = () => {
  const [latestWallet, setLatestWallet] = useLocalStorage(
    'miningLatestWallet',
    ''
  );

  return {
    latestWallet,
    setLatestWallet,
  };
};

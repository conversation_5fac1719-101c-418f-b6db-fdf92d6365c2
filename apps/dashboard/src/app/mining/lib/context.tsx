'use client';

import type { ReactNode } from 'react';
import { createContext, useContext, useEffect, useMemo, useState } from 'react';
import type { ColumnFiltersState, SortingState } from '@tanstack/react-table';
import { useLocalStorage, useSessionStorage } from 'usehooks-ts';
import type { PortfolioWalletsResponse } from '@repo/types/dashboard-api-types';
import type {
  Account,
  Block,
  HotkeyItem,
  MinerColdkey,
} from '@repo/types/website-api-types';
import type { CurrencySelectionOptions } from '@repo/ui/components';
import {
  useAccountLatestQuery,
  useBlockQuery,
  useMinerColdkeyQuery,
  useSavedWalletsQuery,
} from './query-hooks';
import type { DateRange, SortAndSelectOptionValues } from './types';
import { useLastUsedWallet } from './utils';
import useDebounce from '@/lib/hooks/use-debounce';

interface SelectedColdkey {
  coldkey: string;
  netuid: number;
}

interface SelectedHotkey {
  coldkey: string;
  hotkey: string;
  netuid: number;
  uid: number;
}

interface MiningContextValue {
  currencySelected: CurrencySelectionOptions;
  setCurrencySelected: (currency: CurrencySelectionOptions) => void;
  dateRangeSelected: DateRange;
  dates: {
    is1d: boolean;
    is1w: boolean;
    is1m: boolean;
  };
  setDateRangeSelected: (dateRange: DateRange) => void;
  walletAddress: string;
  setWalletAddress: (walletAddress: string) => void;
  setLatestWallet: (walletAddress: string) => void;
  accountLatestQuery: ReturnType<typeof useAccountLatestQuery>;
  accountData?: Account;
  walletsQuery: ReturnType<typeof useSavedWalletsQuery>;
  walletsData?: PortfolioWalletsResponse;
  minerColdkeyQuery: ReturnType<typeof useMinerColdkeyQuery>;
  minerColdkeyData: MinerColdkey[];
  activeMinerColdkeyData?: MinerColdkey;
  selectedColdkeys: SelectedColdkey[];
  setSelectedColdkeys: (coldkeys: SelectedColdkey[]) => void;
  hotkeyData: HotkeyItem[];
  blockData: Block[];
  selectedHotkeys: SelectedHotkey[];
  setSelectedHotkeys: (hotkeys: SelectedHotkey[]) => void;
  selectedSubnet: number | undefined;
  hotkeyTableSortAndSelect: SortAndSelectOptionValues;
  setHotkeyTableSortAndSelect: (sort: SortAndSelectOptionValues) => void;
  hotkeyTableColumnFilters: ColumnFiltersState;
  setHotkeyTableColumnFilters: (filters: ColumnFiltersState) => void;
  hotkeyTableSorting: SortingState;
  setHotkeyTableSorting: (sorting: SortingState) => void;
}

const MiningContext = createContext<MiningContextValue | undefined>(undefined);

interface MiningContextProviderProps {
  children: ReactNode;
}

export const MiningContextProvider = ({
  children,
}: MiningContextProviderProps) => {
  const [currencySelected, setCurrencySelected] =
    useLocalStorage<CurrencySelectionOptions>(
      'mining-selected-currency',
      'ALPHA'
    );
  const [dateRangeSelected, setDateRangeSelected] = useLocalStorage<DateRange>(
    'mining-selected-date-range',
    '1d'
  );

  // Locally stored last wallet address
  const { setLatestWallet, latestWallet } = useLastUsedWallet();
  // Query for wallets saved to API
  const walletsQuery = useSavedWalletsQuery();

  // Active wallet address, defaulted to the last used wallet address
  const [walletAddress, setWalletAddress] = useState<string>(latestWallet);
  const walletAddressDebounced = useDebounce(walletAddress, 300);

  // Query for the account for the active wallet address
  const accountLatestQuery = useAccountLatestQuery(walletAddressDebounced);

  // All wallet addresses (saved and active, de-duped)
  const allColdkeys = useMemo(() => {
    const walletData = walletsQuery.data?.wallets ?? [];
    return Array.from(
      new Set([...walletData.map((w) => w.address), walletAddressDebounced])
    );
  }, [walletAddressDebounced, walletsQuery.data?.wallets]);

  // Miner coldkey query for all wallet addresses
  const minerColdkeyQuery = useMinerColdkeyQuery(
    allColdkeys,
    dateRangeSelected
  );

  // Miner coldkey data for all wallet addresses
  const minerColdkeyData = useMemo(() => {
    return minerColdkeyQuery.map((q) => q.data).filter((q) => q !== undefined);
  }, [minerColdkeyQuery]) as MinerColdkey[];

  // Miner coldkey data for the active wallet address
  const activeMinerColdkeyData = useMemo(
    () =>
      minerColdkeyData.find((ck) => ck.coldkey.ss58 === walletAddressDebounced),
    [minerColdkeyData, walletAddressDebounced]
  );

  // Session store coldkeys selected in the coldkey table
  const [selectedColdkeys, setSelectedColdkeys] = useSessionStorage<
    SelectedColdkey[]
  >('mining-selected-coldkeys', []);

  // If the active wallet changes, and there's only one netuid, select it. Otherwise, clear the selected coldkeys.
  useEffect(() => {
    if (activeMinerColdkeyData === undefined) {
      return;
    }

    const netuids = [
      ...new Set(activeMinerColdkeyData.hotkeys.map((hk) => hk.netuid)),
    ];

    if (netuids.length === 1) {
      setSelectedColdkeys([
        { coldkey: activeMinerColdkeyData.coldkey.ss58, netuid: netuids[0] },
      ]);
    } else {
      setSelectedColdkeys([]);
    }
  }, [activeMinerColdkeyData, setSelectedColdkeys]);

  // Hotkey data for the selected coldkeys
  const hotkeyData = useMemo(
    () =>
      selectedColdkeys.flatMap((selectedColdkey) =>
        minerColdkeyData
          .filter((ckd) => ckd.coldkey.ss58 === selectedColdkey.coldkey)
          .flatMap((ckd) => ckd.hotkeys)
          .filter(
            (hk) =>
              hk.coldkey.ss58 === selectedColdkey.coldkey &&
              hk.netuid === selectedColdkey.netuid
          )
      ),
    [minerColdkeyData, selectedColdkeys]
  );

  // State store hotkeys selected in the hotkey table
  const [selectedHotkeys, setSelectedHotkeys] = useSessionStorage<
    SelectedHotkey[]
  >('mining-selected-hotkeys', []);

  const [hotkeyTableSortAndSelect, setHotkeyTableSortAndSelect] =
    useSessionStorage<SortAndSelectOptionValues>(
      'mining-hotkey-table-sort-and-select',
      ''
    );
  const [hotkeyTableColumnFilters, setHotkeyTableColumnFilters] =
    useSessionStorage<ColumnFiltersState>('mining-hotkey-table-filter', []);
  const [hotkeyTableSorting, setHotkeyTableSorting] =
    useSessionStorage<SortingState>('mining-hotkey-table-sorting-state', [
      { id: 'incentive-col', desc: false },
    ]);

  useEffect(() => {
    setHotkeyTableSortAndSelect('');
    setHotkeyTableColumnFilters([]);
    setHotkeyTableSorting([{ id: 'incentive-col', desc: false }]);

    const preselectedHotkeys = hotkeyData
      .sort((a, b) => Number(a.incentive) - Number(b.incentive))
      .slice(0, 1)
      .map((row) => ({
        coldkey: row.coldkey.ss58,
        hotkey: row.hotkey.ss58,
        uid: row.uid,
        netuid: row.netuid,
      }));
    setSelectedHotkeys(preselectedHotkeys);
    // eslint-disable-next-line react-hooks/exhaustive-deps -- we only want to run this if the user changes the selected coldkeys
  }, [selectedColdkeys]);

  const blockQuery = useBlockQuery([
    ...new Set(
      hotkeyData.map((hk) => hk.registration_block).filter((b) => b > 0)
    ),
  ]);

  const blockData = blockQuery
    .map((q) => q.data?.data[0])
    .filter((d) => d !== undefined) as Block[];

  const value = useMemo<MiningContextValue>(() => {
    return {
      dateRangeSelected,
      setDateRangeSelected,
      dates: {
        is1d: dateRangeSelected === '1d',
        is1w: dateRangeSelected === '1w',
        is1m: dateRangeSelected === '1m',
      },
      walletAddressInput: walletAddress,
      walletAddress: walletAddressDebounced,
      setWalletAddress,
      setLatestWallet,
      accountLatestQuery,
      accountData: accountLatestQuery.data?.data[0],
      walletsQuery,
      walletsData: walletsQuery.data,
      currencySelected,
      setCurrencySelected,
      minerColdkeyQuery,
      minerColdkeyData,
      activeMinerColdkeyData,
      selectedColdkeys,
      setSelectedColdkeys,
      hotkeyData,
      blockData,
      selectedHotkeys,
      setSelectedHotkeys,
      selectedSubnet: selectedColdkeys[0]?.netuid,
      hotkeyTableSortAndSelect,
      setHotkeyTableSortAndSelect,
      hotkeyTableColumnFilters,
      setHotkeyTableColumnFilters,
      hotkeyTableSorting,
      setHotkeyTableSorting,
    };
  }, [
    dateRangeSelected,
    setDateRangeSelected,
    walletAddress,
    walletAddressDebounced,
    setLatestWallet,
    accountLatestQuery,
    walletsQuery,
    currencySelected,
    setCurrencySelected,
    minerColdkeyQuery,
    minerColdkeyData,
    activeMinerColdkeyData,
    selectedColdkeys,
    setSelectedColdkeys,
    hotkeyData,
    blockData,
    selectedHotkeys,
    setSelectedHotkeys,
    hotkeyTableSortAndSelect,
    setHotkeyTableSortAndSelect,
    hotkeyTableColumnFilters,
    setHotkeyTableColumnFilters,
    hotkeyTableSorting,
    setHotkeyTableSorting,
  ]);

  return (
    <MiningContext.Provider value={value}>{children}</MiningContext.Provider>
  );
};

export const useMiningContext = () => {
  const context = useContext(MiningContext);
  if (context === undefined) {
    throw new Error(
      'useMiningContext must be used within a MiningContextProvider'
    );
  }
  return context;
};

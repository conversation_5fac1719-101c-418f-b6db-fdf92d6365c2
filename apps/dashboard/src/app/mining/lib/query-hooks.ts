import {
  keepPreviousData,
  useMutation,
  useQueries,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import type {
  AddPortfolioWalletRequest,
  DeletePortfolioWalletRequest,
} from '@repo/types/dashboard-api-types';
import type { RegistrationCostHistoryQueryParams } from '@repo/types/website-api-types';
import { SubnetHistoryOrder } from '@repo/types/website-api-types';
import { miningDays } from './constants';
import type { DateRange } from './types';
import { getStartingUnixTimestampForDateRange } from './utils';
import { apiClient, handleResponse } from '@/app/api/[...route]/lib/api-client';

export const queryKeys = {
  account: 'account',
  minerColdkey: 'minerColdkey',
  blocks: 'blocks',
  neuronHistory: 'neuronHistory',
  aggregatedNeuronHistory: 'aggregatedNeuronHistory',
  subnetRegistrationCostHistory: 'subnetRegistrationCostHistory',
  wallets: 'wallets',
} as const;

export const useAccountLatestQuery = (address: string) => {
  return useQuery({
    queryKey: [queryKeys.account, address],
    queryFn: async () =>
      handleResponse(
        await apiClient.account[`:address`].latest.$get({
          param: {
            address,
          },
        })
      ),
    enabled: Boolean(address) && address.length > 40,
    placeholderData:
      Boolean(address) && address.length > 40 ? keepPreviousData : undefined,
  });
};

export const useMinerColdkeyQuery = (
  coldkeys: string[],
  dateRange: DateRange
) => {
  return useQueries({
    queries: coldkeys
      .filter((coldkey) => coldkey.length > 40)
      .map((coldkey) => ({
        //placeholderData:
        //  Boolean(coldkey) && coldkey.length > 20 ? keepPreviousData : undefined,
        queryKey: [queryKeys.minerColdkey, coldkey, dateRange],
        queryFn: async () => {
          const result = await handleResponse(
            await apiClient.miner[`:coldkey`][`:days`].$get({
              param: {
                coldkey,
                days: `${miningDays[dateRange]}`,
              },
            })
          );
          // Individual API only returns one item (for a single coldkey) and so data array and pagination are not needed
          return result.data[0];
        },
      })),
  });
};

export const useBlockQuery = (blockNumbers: number[]) => {
  return useQueries({
    queries: blockNumbers.map((blockNumber) => ({
      queryKey: [queryKeys.blocks, blockNumber],
      queryFn: async () =>
        handleResponse(
          await apiClient.blocks[':block_number'].$get({
            param: {
              block_number: `${blockNumber}`,
            },
          })
        ),
    })),
  });
};

export const useNeuronHistoryDataForHotkeys = (
  selectedHotkeys: {
    coldkey: string;
    hotkey: string;
    netuid: number;
    uid: number;
  }[],
  dateRange: DateRange
) => {
  const startingUnixTimestamp = getStartingUnixTimestampForDateRange(dateRange);

  return useQueries({
    queries: selectedHotkeys.map(({ netuid, uid, coldkey, hotkey }) => ({
      queryKey: [
        queryKeys.neuronHistory,
        netuid,
        uid,
        coldkey,
        hotkey,
        dateRange,
      ],
      queryFn: async () => {
        const query = {
          netuid: `${netuid}`,
          uid: `${uid}`,
          hotkey,
          coldkey,
          timestampStart: `${startingUnixTimestamp}`,
          page: '1',
        };

        const result = await handleResponse(
          await apiClient.neuron.history.$get({
            query: {
              ...query,
            },
          })
        );

        const pages = Array.from(
          { length: result.pagination.total_pages - 1 },
          (_, i) => i + 2
        );

        const pageQueries = await Promise.all(
          pages.map(async (page) =>
            handleResponse(
              await apiClient.neuron.history.$get({
                query: {
                  ...query,
                  page: `${page}`,
                },
              })
            )
          )
        );

        const results = result.data.concat(
          pageQueries.flatMap((pageQuery) => pageQuery.data)
        );

        return results;
      },
    })),
  });
};

export const useAggregatedNeuronHistoryDataForSubnet = (
  netuid: number | undefined,
  dateRange: DateRange
) => {
  // hardocoded to 1d for now as massive data hit
  const startingUnixTimestamp = getStartingUnixTimestampForDateRange(dateRange);

  return useQuery({
    enabled: Boolean(netuid),
    queryKey: [queryKeys.aggregatedNeuronHistory, netuid, dateRange],
    queryFn: async () => {
      const query: {
        netuid: string;
        timestampStart: string;
        page: string;
      } = {
        netuid: `${netuid}`,
        timestampStart: `${startingUnixTimestamp}`,
        page: '1',
      };

      const result = await handleResponse(
        await apiClient.neuron.aggregatedHistory.$get({
          query: {
            ...query,
          },
        })
      );

      const pages = Array.from(
        { length: result.pagination.total_pages - 1 },
        (_, i) => i + 2
      );

      const pageQueries = await Promise.all(
        pages.map(
          async (page) =>
            await handleResponse(
              await apiClient.neuron.aggregatedHistory.$get({
                query: {
                  ...query,
                  page: `${page}`,
                },
              })
            )
        )
      );

      const results = result.data.concat(
        pageQueries.flatMap((pageQuery) => pageQuery.data)
      );

      return results;
    },
  });
};

export const useSubnetRegistrationCostHistoryQuery = (dateRange: DateRange) => {
  const startingUnixTimestamp = getStartingUnixTimestampForDateRange(dateRange);
  return useQuery({
    queryKey: [queryKeys.subnetRegistrationCostHistory, dateRange],
    queryFn: async () => {
      const query: RegistrationCostHistoryQueryParams = {
        timestamp_start: startingUnixTimestamp,
        limit: 200,
        order: SubnetHistoryOrder.TimestampAsc,
        page: 1,
      };
      const result = await handleResponse(
        await apiClient.subnet.registration_cost.history.$get({
          query: { ...query },
        })
      );
      const pages = Array.from(
        { length: result.pagination.total_pages - 1 },
        (_, i) => i + 2
      );

      const pageQueries = await Promise.all(
        pages.map(async (page) =>
          handleResponse(
            await apiClient.subnet.registration_cost.history.$get({
              query: {
                ...query,
                page,
              },
            })
          )
        )
      );

      const results = result.data.concat(
        pageQueries.flatMap((pageQuery) => pageQuery.data)
      );

      return results;
    },
  });
};

export const useSavedWalletsQuery = () => {
  return useQuery({
    queryKey: [queryKeys.wallets],
    queryFn: async () =>
      handleResponse(await apiClient.account.portfolio.wallets.$get()),
  });
};

export const useAddPortfolioWallet = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ address, name }: AddPortfolioWalletRequest) => {
      return handleResponse(
        await apiClient.account.portfolio.wallet.$post({
          json: { name, address },
        })
      );
    },
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: [queryKeys.account] });
      void queryClient.invalidateQueries({ queryKey: [queryKeys.wallets] });
    },
  });
};

export const useDeletePortfolioWallet = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ walletId }: DeletePortfolioWalletRequest) => {
      return handleResponse(
        await apiClient.account.portfolio.wallet[`:walletId`].$delete({
          param: { walletId },
        })
      );
    },
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: [queryKeys.account] });
      void queryClient.invalidateQueries({ queryKey: [queryKeys.wallets] });
    },
  });
};

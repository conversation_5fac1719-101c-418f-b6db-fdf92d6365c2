'use client';

import { memo, useMemo, useState } from 'react';
import { BiPlus, BiSearch, BiTrash } from 'react-icons/bi';
import { CgChevronDown, CgSearch } from 'react-icons/cg';
import { useWindowSize } from 'usehooks-ts';
import type { PortfolioWallet } from '@repo/types/dashboard-api-types';
import {
  Badge,
  Button,
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { CreateWalletDialog } from './create-wallet-dialog';
import { DeleteWalletDialog } from './delete-wallet-dialog';
import { WalletText } from './wallet-text';
import { useExistingWalletCheck } from '@/app/mining/lib/hooks';
import { useSavedWalletsQuery } from '@/app/mining/lib/query-hooks';
import useDebounce from '@/lib/hooks/use-debounce';
import { truncateString } from '@/lib/utils';

type WalletPickerProps = {
  allowAccountAdd?: boolean;
  allowAccountDelete?: boolean;
  setWalletAddress: (address: string) => void;
  walletAddressInput: string;
};

export const WalletPicker = memo(function WalletPicker({
  allowAccountAdd = true,
  allowAccountDelete = true,
  setWalletAddress,
  walletAddressInput,
}: WalletPickerProps) {
  const [walletAddressLocal, setWalletAddressLocal] =
    useState(walletAddressInput);

  const walletAddressLocalDebounced = useDebounce(walletAddressLocal, 300);
  const [isCreateWalletDialogOpen, setIsCreateWalletDialogOpen] =
    useState(false);
  const [isWalletPickerOpen, setIsWalletPickerOpen] = useState(false);
  const [walletToDelete, setWalletToDelete] = useState<PortfolioWallet | null>(
    null
  );
  const walletsQuery = useSavedWalletsQuery();
  const { doesWalletExist } = useExistingWalletCheck();
  const walletsCount = walletsQuery.data?.total ?? 0;

  const handleAddWalletClick = () => {
    if (doesWalletExist(walletAddressInput)) {
      return;
    }

    setIsCreateWalletDialogOpen(true);
  };

  const handleWalletClick = (address: string) => {
    setWalletAddressLocal(address);
    setWalletAddress(address);
    setIsWalletPickerOpen(false);
  };

  const handleWalletDelete = (wallet: PortfolioWallet) => {
    setWalletToDelete(wallet);
  };

  const windowSize = useWindowSize();
  const [isBeingEdited, setIsBeingEdited] = useState(false);
  const isSmallScreen = useMemo(
    () => windowSize.width < 640,
    [windowSize.width]
  );

  const isValueDifferent = useMemo(
    () => walletAddressLocalDebounced !== walletAddressInput,
    [walletAddressInput, walletAddressLocalDebounced]
  );

  const handleEnterPress = () => {
    setWalletAddress(walletAddressLocal);
  };

  return (
    <>
      <Popover open={isWalletPickerOpen}>
        <>
          <PopoverTrigger asChild disabled>
            <div className='w-full'>
              <Input
                IconLeft={CgSearch}
                placeholder='Enter wallet address E.g. 5CFHgzvoF3MyomyFGgvBkVDWGzW5xE28RafSWxACT2ZtFkag'
                inputId='wallet-address'
                onChange={(e) => {
                  setWalletAddressLocal(e.target.value);
                  if (e.target.value === '') {
                    setWalletAddress(e.target.value);
                  }
                }}
                value={
                  isBeingEdited
                    ? walletAddressLocal
                    : isSmallScreen
                      ? truncateString(walletAddressInput, 6)
                      : walletAddressInput
                }
                inputClassName='rounded-full pr-32 pl-14 py-4'
                onFocus={() => {
                  setIsBeingEdited(true);
                }}
                onBlur={() => {
                  setIsBeingEdited(false);
                }}
                enterKeyHint='done'
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.code === 'Enter') {
                    handleEnterPress();
                  }
                }}
                onPaste={(e) => {
                  e.preventDefault();
                  const text = e.clipboardData.getData('text/plain');
                  setWalletAddressLocal(text);
                }}
                rightComponent={
                  <>
                    {isValueDifferent ? (
                      <Button
                        variant='cta2'
                        size='sm'
                        className={cn(
                          'rounded-full',
                          'bg-opacity-90 disabled:text-gray-400'
                        )}
                        onClick={() => {
                          if (walletAddressLocal) {
                            setWalletAddress(walletAddressLocal);
                          }
                        }}
                      >
                        <BiSearch className='mr-1 size-4' />
                        Find Account
                      </Button>
                    ) : null}
                    {!isValueDifferent && allowAccountAdd ? (
                      <Button
                        variant='cta2'
                        size='sm'
                        className={cn(
                          'rounded-full',
                          'bg-opacity-90 disabled:text-gray-400'
                        )}
                        onClick={handleAddWalletClick}
                      >
                        Add Account
                        <BiPlus />
                      </Button>
                    ) : null}

                    <Badge variant='green5'>{walletsCount}</Badge>

                    <button
                      type='button'
                      onClick={() => {
                        setIsWalletPickerOpen(!isWalletPickerOpen);
                      }}
                    >
                      <CgChevronDown
                        aria-hidden='true'
                        className={cn(
                          'text-grayish pointer-events-none col-start-1 row-start-1 mr-3 size-5 self-center justify-self-end transition-transform sm:size-4',
                          isWalletPickerOpen && 'rotate-180'
                        )}
                      />
                    </button>
                  </>
                }
              />
            </div>
          </PopoverTrigger>
          <PopoverContent
            align='start'
            className={cn(
              'z-10 mt-1 w-[var(--radix-popover-trigger-width)] rounded-2xl border border-[#323232] bg-[#1D1D1DCC] p-6 shadow-lg backdrop-blur-sm'
            )}
          >
            <div className='max-h-60 overflow-auto py-1'>
              {walletsQuery.data?.wallets
                .sort((a, b) => a.name.localeCompare(b.name))
                .map((wallet) => (
                  <div
                    key={wallet.walletId}
                    className={cn(
                      'hover:border-accent-1 flex flex-row items-center justify-between gap-4 rounded-lg border border-transparent bg-transparent px-2 py-1 transition-[border-color] duration-300',
                      wallet.address === walletAddressInput && 'border-accent-1'
                    )}
                  >
                    <Button
                      key={wallet.walletId}
                      className={cn(
                        'border-1 flex-1 justify-start overflow-hidden truncate border-transparent bg-transparent px-1 shadow-none hover:bg-transparent'
                      )}
                      onClick={() => {
                        handleWalletClick(wallet.address);
                      }}
                    >
                      <WalletText
                        name={wallet.name}
                        address={wallet.address}
                        truncateAddress
                      />
                    </Button>
                    {allowAccountDelete ? (
                      <div className='flex items-center justify-start'>
                        <BiTrash
                          className='size-6 cursor-pointer text-white/80 hover:text-red-200'
                          onClick={() => {
                            handleWalletDelete(wallet);
                          }}
                        />
                      </div>
                    ) : null}
                  </div>
                ))}
            </div>
          </PopoverContent>
        </>
      </Popover>
      {allowAccountAdd ? (
        <CreateWalletDialog
          closeFn={() => {
            setIsCreateWalletDialogOpen(false);
          }}
          isOpen={isCreateWalletDialogOpen}
          address={walletAddressInput}
        />
      ) : null}

      {allowAccountDelete ? (
        <DeleteWalletDialog
          walletToDelete={walletToDelete}
          clearWalletToDelete={() => {
            setWalletToDelete(null);
            setIsWalletPickerOpen(false);
          }}
        />
      ) : null}
    </>
  );
});

'use client';

import { useMiningContext } from '@/app/mining/lib/context';
import { WalletPicker } from '@/app/mining/ui/wallet-picker/wallet-picker';

export default function Index() {
  const { walletAddress, setWalletAddress, setLatestWallet, accountData } =
    useMiningContext();

  const handleSetWalletAddress = (address: string) => {
    setWalletAddress(address);
    setLatestWallet(address);
  };

  return (
    <WalletPicker
      allowAccountAdd={Boolean(accountData)}
      setWalletAddress={handleSetWalletAddress}
      walletAddressInput={walletAddress}
    />
  );
}

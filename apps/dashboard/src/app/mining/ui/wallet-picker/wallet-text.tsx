import { CopyButton } from '@repo/ui/components';
import { truncateString } from '@/lib/utils';

type WalletTextProps = {
  name?: string;
  address: string;
  truncateAddress?: boolean;
  allowCopy?: boolean;
};

// export const WalletHeaderText = ({
//   name = '',
//   address,
//   ...props
// }: WalletTextProps) => {
//   return (
//     <Text level='labelMedium' className='truncate text-[#707070]'>
//       <WalletText name={name} address={address} {...props} />
//     </Text>
//   );
// };

export const WalletText = ({
  name = '',
  address,
  truncateAddress = false,
  allowCopy = false,
}: WalletTextProps) => {
  return (
    <span className='flex flex-row items-center gap-1 truncate'>
      <span>{name ? name : ''}</span>
      <span className='text-accent-1'>{name ? `//` : ''}</span>
      <span className='font-mono'>
        {truncateAddress ? truncateString(address, 6) : address}
      </span>
      {allowCopy ? (
        <CopyButton size={14} value={address} className='text-white' />
      ) : null}
    </span>
  );
};

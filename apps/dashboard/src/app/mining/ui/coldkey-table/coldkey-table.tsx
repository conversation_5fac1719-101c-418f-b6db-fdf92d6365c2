import { useMemo } from 'react';
import type { Row } from '@tanstack/react-table';
import { getCoreRowModel, useReactTable } from '@tanstack/react-table';
import type { AlphaBalance, HotkeyItem } from '@repo/types/website-api-types';
import { CurrencySelector, DataTable, Text } from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import type { ColdkeyTableColumns } from './utils';
import { useColumns } from './utils';
import { useMiningContext } from '@/app/mining/lib/context';
import { DateRangeSelector } from '@/app/mining/ui/date-range-selector';
import { RAO_PER_TAO } from '@/contexts/chain-and-wallet/constants';
import { useSubnetLookup } from '@/lib/hooks/global-hooks';
import { useTaoToDollarConverter } from '@/lib/hooks/price-hooks';

type GroupedData = {
  coldkey: string;
  netuid: number;
  hotkeys: HotkeyItem[];
  alpha_balances: AlphaBalance[];
  free_balance: number;
};

export const ColdkeyTable = () => {
  const columns = useColumns();
  const {
    minerColdkeyData,
    currencySelected,
    setCurrencySelected,
    selectedColdkeys,
    walletsData,
  } = useMiningContext();
  const { getDollarValue } = useTaoToDollarConverter();
  const { getSubnetSymbol } = useSubnetLookup();

  const tableData = useMemo(() => {
    const groupedData: Map<string, GroupedData> = minerColdkeyData.reduce<
      Map<string, GroupedData>
    >((acc, item) => {
      item.hotkeys.forEach((hotkey) => {
        const key = `${hotkey.coldkey.ss58}-${hotkey.netuid}`;
        if (!acc.has(key)) {
          acc.set(key, {
            coldkey: hotkey.coldkey.ss58,
            netuid: hotkey.netuid,
            hotkeys: [],
            alpha_balances: [],
            free_balance: Number(item.free_balance),
          });
        }

        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion -- because the key has just been added
        const group = acc.get(key)!;
        group.hotkeys.push(hotkey);
        group.alpha_balances.push(
          ...item.alpha_balances.filter((ab) => ab.netuid === hotkey.netuid)
        );
      });
      return acc;
    }, new Map());

    // Mining Alpha is the current balance on active mining hotkeys
    const calculateMiningAlphaBalance = (group: GroupedData) =>
      group.hotkeys.reduce((acc, cur) => acc + Number(cur.alpha_balance), 0);

    const calculateMiningAlphaBalanceAsTao = (group: GroupedData) =>
      group.hotkeys.reduce(
        (acc, cur) => acc + Number(cur.alpha_balance_as_tao),
        0
      );

    // Other alpha needs to come from summing balance_as_tao from alpha balances where netuid matches and the hotkey isn't found in the hotkey items (with a matching netuid)
    const calculateOtherAlphaBalance = (group: GroupedData) =>
      group.alpha_balances
        .filter(
          (ab) => !group.hotkeys.map((hk) => hk.hotkey.ss58).includes(ab.hotkey)
        )
        .reduce((acc, cur) => acc + Number(cur.balance), 0);

    // Other alpha needs to come from summing balance_as_tao from alpha balances where netuid matches and the hotkey isn't found in the hotkey items (with a matching netuid)
    const calculateOtherAlphaBalanceAsTao = (group: GroupedData) =>
      group.alpha_balances
        .filter(
          (ab) => !group.hotkeys.map((hk) => hk.hotkey.ss58).includes(ab.hotkey)
        )
        .reduce((acc, cur) => acc + Number(cur.balance_as_tao), 0);

    // Total TAO is MinerColdkeyItem.free_balance + mining_alpha * price + other_alpha * price
    // basically free_balance + mining_alpha_as_tao + other_alpha_as_tao, but to normalize alpha as TAO you are using the pool price. Then when USD is selected just multiply the final TAO value by the TAO USD price
    const calculateTotalTao = (group: GroupedData) =>
      group.free_balance +
      calculateMiningAlphaBalanceAsTao(group) +
      calculateOtherAlphaBalanceAsTao(group);

    // Combined performance is HotkeyItem.total_emission_as_tao summed (and the non _as_tao version depending on selected currency)
    const calculateCombinedPerformanceAsAlpha = (group: GroupedData) =>
      group.hotkeys.reduce((a, b) => a + Number(b.total_emission), 0);

    const calculateCombinedPerformanceAsTao = (group: GroupedData) =>
      group.hotkeys.reduce((a, b) => a + Number(b.total_emission_as_tao), 0);

    // Avg per hotkey is combined perf / hotkeys in group
    const calculateAveragePerformanceAsAlpha = (group: GroupedData) =>
      calculateCombinedPerformanceAsAlpha(group) / group.hotkeys.length;

    const calculateAveragePerformanceAsTao = (group: GroupedData) =>
      calculateCombinedPerformanceAsTao(group) / group.hotkeys.length;

    const getValueForSelectedCurrency = (
      taoFunc: () => number,
      alphaFunc?: () => number
    ) => {
      if (currencySelected === 'ALPHA' && alphaFunc) {
        return alphaFunc() / RAO_PER_TAO;
      }

      const taoValue = taoFunc() / RAO_PER_TAO;

      if (currencySelected === 'USD') {
        return getDollarValue(taoValue);
      }

      return taoValue;
    };

    const mappedData: ColdkeyTableColumns[] = Array.from(groupedData.values())
      .map((group) => ({
        coldkey: group.coldkey,
        coldkeyName: walletsData?.wallets.find(
          (w) => w.address === group.coldkey
        )?.name,
        netuid: group.netuid,
        subnetSymbol: getSubnetSymbol(group.netuid),
        miningAlphaBalance: getValueForSelectedCurrency(
          () => calculateMiningAlphaBalanceAsTao(group),
          () => calculateMiningAlphaBalance(group)
        ),
        otherAlphaBalance: getValueForSelectedCurrency(
          () => calculateOtherAlphaBalanceAsTao(group),
          () => calculateOtherAlphaBalance(group)
        ),
        // Free TAO comes from MinerColdkeyItem.free_balance
        freeTaoBalance: getValueForSelectedCurrency(() => group.free_balance),
        totalTaoBalance: getValueForSelectedCurrency(() =>
          calculateTotalTao(group)
        ),
        combinedPerformance: getValueForSelectedCurrency(
          () => calculateCombinedPerformanceAsTao(group),
          () => calculateCombinedPerformanceAsAlpha(group)
        ),
        averagePerformance: getValueForSelectedCurrency(
          () => calculateAveragePerformanceAsTao(group),
          () => calculateAveragePerformanceAsAlpha(group)
        ),
        // Hotkey Stats Total is total hotkeys in group
        hotkeyTotal: group.hotkeys.length,
        // Hotkey Stats Immune is total hotkeys in group where immune = true
        hotkeyImmune: group.hotkeys.filter((h) => h.immune).length,
        // Hotkey Stats Danger is total hotkeys in group where in_danger = true
        hotkeyDanger: group.hotkeys.filter((h) => h.in_danger).length,
        // Hotkey Stats Deregs is total hotkeys in group where deregistered = true
        hotkeyDereg: group.hotkeys.filter((h) => h.deregistered).length,
      }))
      .sort((a, b) => a.netuid - b.netuid)
      .sort((a, b) => a.coldkey.localeCompare(b.coldkey));

    return mappedData;
  }, [
    currencySelected,
    getDollarValue,
    getSubnetSymbol,
    minerColdkeyData,
    walletsData?.wallets,
  ]);

  const totalData = useMemo(() => {
    const reducedData = tableData
      .filter(
        (td) =>
          selectedColdkeys.map((ck) => ck.coldkey).includes(td.coldkey) &&
          selectedColdkeys.map((ck) => ck.netuid).includes(td.netuid)
      )
      .reduce(
        (a, b) => ({
          coldkey: 'Total',
          netuid: b.netuid,
          subnetSymbol: getSubnetSymbol(b.netuid),
          miningAlphaBalance: a.miningAlphaBalance + b.miningAlphaBalance,
          otherAlphaBalance: a.otherAlphaBalance + b.otherAlphaBalance,
          freeTaoBalance: a.freeTaoBalance + b.freeTaoBalance,
          totalTaoBalance: a.totalTaoBalance + b.totalTaoBalance,
          combinedPerformance: a.combinedPerformance + b.combinedPerformance,
          averagePerformance:
            (a.combinedPerformance + b.combinedPerformance) /
            (a.hotkeyTotal + b.hotkeyTotal),
          hotkeyTotal: a.hotkeyTotal + b.hotkeyTotal,
          hotkeyImmune: a.hotkeyImmune + b.hotkeyImmune,
          hotkeyDanger: a.hotkeyDanger + b.hotkeyDanger,
          hotkeyDereg: a.hotkeyDereg + b.hotkeyDereg,
        }),
        {
          coldkey: 'Total',
          netuid: -1,
          subnetSymbol: 'α',
          miningAlphaBalance: 0,
          otherAlphaBalance: 0,
          freeTaoBalance: 0,
          totalTaoBalance: 0,
          combinedPerformance: 0,
          averagePerformance: 0,
          hotkeyTotal: 0,
          hotkeyImmune: 0,
          hotkeyDanger: 0,
          hotkeyDereg: 0,
        }
      );

    return reducedData;
  }, [getSubnetSymbol, selectedColdkeys, tableData]);

  const combinedData = useMemo(() => {
    if (tableData.length === 0) {
      return [...tableData];
    }
    return [totalData, ...tableData];
  }, [tableData, totalData]);

  const table = useReactTable({
    data: combinedData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    //debugTable: true,
  });

  const getRowClassName = (row: Row<ColdkeyTableColumns>) => {
    const rowStyles = ['text-white'];

    if (row.index === 0) {
      rowStyles.push(
        '[&_td]:border-b [&_td]:border-t [&_td]:border-b-white/60 [&_td]:border-t-white/60 text-opacity-60'
      );
      return cn(rowStyles);
    }

    // Grey out rows that do not have a matching netuid
    if (
      selectedColdkeys.length > 0 &&
      selectedColdkeys.some((ck) => ck.netuid !== row.original.netuid)
    ) {
      rowStyles.push('text-opacity-30');
    }

    // Highlight selected coldkeys
    if (
      selectedColdkeys.some(
        (ck) =>
          ck.coldkey === row.original.coldkey &&
          ck.netuid === row.original.netuid
      )
    ) {
      rowStyles.push(
        '!bg-[#00DBBC33] first:[&_td]:border-l-4 first:[&_td]:border-l-[#00DBBC] first:[&_td]:pl-1 first:[&_td]:sm:pl-2'
      );
    } else {
      rowStyles.push('first:[&_td]:pl-2 first:[&_td]:sm:pl-3');
    }

    return cn(rowStyles);
  };

  return (
    <>
      <div className='mt-8 flex flex-col gap-8 md:flex-row md:items-center md:justify-between md:gap-16'>
        <div className='flex flex-row items-center gap-8'>
          <Text as='h2' level='headerMedium' className='text-white'>
            Coldkey Table
          </Text>
        </div>

        <div className='flex flex-1 flex-col items-end gap-8 md:flex-row-reverse md:items-center'>
          <CurrencySelector
            options={['ALPHA', 'TAO', 'USD']}
            selection={currencySelected}
            onSelectionChange={setCurrencySelected}
          />
          <DateRangeSelector />
        </div>
      </div>
      <DataTable
        table={table}
        hasBorder={false}
        rowClassName={getRowClassName}
      />
    </>
  );
};

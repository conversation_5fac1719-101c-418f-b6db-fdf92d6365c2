import { useCallback, useMemo } from 'react';
import { createColumnHelper, type ColumnDef } from '@tanstack/react-table';
import {
  BigSmallValueDisplay,
  Checkbox,
  TaoOrAlphaValueDisplay,
  Text,
} from '@repo/ui/components';
import { miningDateRanges } from '@/app/mining/lib/constants';
import { useMiningContext } from '@/app/mining/lib/context';
import { KeyText } from '@/app/mining/ui/shared/key-text';
import { TAO_CURRENCY } from '@/lib/utils';

export type ColdkeyTableColumns = {
  coldkey: string;
  coldkeyName?: string;
  netuid: number;
  subnetSymbol: string;
  miningAlphaBalance: number;
  otherAlphaBalance: number;
  freeTaoBalance: number;
  totalTaoBalance: number;
  combinedPerformance: number;
  averagePerformance: number;
  hotkeyTotal: number;
  hotkeyImmune: number;
  hotkeyDanger: number;
  hotkeyDereg: number;
};

export const useColumns = () => {
  const columnHelper = createColumnHelper<ColdkeyTableColumns>();
  const {
    dateRangeSelected,
    currencySelected,
    selectedColdkeys,
    setSelectedColdkeys,
  } = useMiningContext();

  const setCheckedChanged = useCallback(
    (isSelected: boolean, coldkey: string, subnet: number) => {
      if (isSelected) {
        setSelectedColdkeys(
          selectedColdkeys.filter(
            (ck) => ck.coldkey !== coldkey || ck.netuid !== subnet
          )
        );
      } else {
        setSelectedColdkeys([...selectedColdkeys, { coldkey, netuid: subnet }]);
      }
    },
    [selectedColdkeys, setSelectedColdkeys]
  );

  const getCurrencySymbol = useCallback(
    (subnetSymbol?: string) => {
      if (currencySelected === 'USD') {
        return '$';
      }

      if (currencySelected === 'ALPHA' && subnetSymbol) {
        return subnetSymbol;
      }

      return TAO_CURRENCY;
    },
    [currencySelected]
  );

  const columns: ColumnDef<ColdkeyTableColumns>[] = useMemo(
    () => [
      columnHelper.group({
        id: 'coldkey-subnet-col-group',
        header: '',
        columns: [
          {
            id: 'select-col',
            accessorKey: 'select',
            header: 'Select',
            cell: ({ row }) => {
              const d = row.original;

              if (row.index === 0) {
                return null;
              }

              const isSelected =
                selectedColdkeys.find(
                  (ck) => ck.coldkey === d.coldkey && ck.netuid === d.netuid
                ) !== undefined;

              const isEnabled =
                isSelected ||
                selectedColdkeys.length === 0 ||
                selectedColdkeys.map((ck) => ck.netuid).includes(d.netuid);

              return (
                <Checkbox
                  checked={isSelected}
                  disabled={!isEnabled}
                  onCheckedChange={() => {
                    setCheckedChanged(isSelected, d.coldkey, d.netuid);
                  }}
                  className={
                    isSelected ? 'border-none !bg-[#00DBBC]' : undefined
                  }
                />
              );
            },
            enableSorting: false,
          },
          {
            id: 'coldkey-col',
            accessorKey: 'coldkey',
            header: 'Coldkey',
            cell: ({ row }) => {
              const d = row.original;
              return row.index === 0 ? (
                <Text level='labelExtraSmall' className='py-2 text-inherit'>
                  {d.coldkey}
                </Text>
              ) : (
                <KeyText
                  hotOrColdKey={d.coldkey}
                  hotOrColdKeyName={d.coldkeyName}
                />
              );
            },
            enableSorting: false,
          },
          {
            id: 'subnet-col',
            accessorKey: 'netuid',
            header: 'Subnet',
            meta: {
              align: 'end',
            },
            cell: ({ row }) => {
              const d = row.original;
              return row.index === 0 ? null : (
                <Text level='sm' className='flex justify-end text-inherit'>
                  {d.netuid}
                </Text>
              );
            },
            enableSorting: false,
          },
        ],
      }),
      columnHelper.display({
        id: 'coldkey-subnet-col-group-separator',
        meta: {
          separator: 'dashed',
        },
      }),
      columnHelper.group({
        id: 'balance-col-group',
        header: () => <Text level='labelSmall'>Coldkey Balance</Text>,
        meta: {
          align: 'center',
        },
        columns: [
          {
            id: 'mining-alpha-balance-col',
            accessorKey: 'miningAlphaBalance',
            header: 'Mining Alpha',
            meta: {
              align: 'end',
            },
            cell: ({ row }) => {
              const d = row.original;
              return (
                <div className='flex justify-end gap-2'>
                  <TaoOrAlphaValueDisplay
                    symbol={getCurrencySymbol(d.subnetSymbol)}
                    value={d.miningAlphaBalance}
                    valueFormattingOptions={{
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    }}
                    valueTextLevel={
                      row.index === 0 ? 'labelExtraSmall' : 'labelLarge'
                    }
                    iconClassName='text-xs'
                    colourClassName='text-inherit'
                  />
                </div>
              );
            },
            enableSorting: false,
          },
          {
            id: 'other-alpha-balance-col',
            accessorKey: 'otherAlphaBalance',
            header: 'Other Alpha',
            meta: {
              align: 'end',
            },
            cell: ({ row }) => {
              const d = row.original;
              return (
                <div className='flex justify-end gap-2'>
                  <TaoOrAlphaValueDisplay
                    symbol={getCurrencySymbol(d.subnetSymbol)}
                    value={d.otherAlphaBalance}
                    valueFormattingOptions={{
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    }}
                    valueTextLevel={
                      row.index === 0 ? 'labelExtraSmall' : 'labelLarge'
                    }
                    iconClassName='text-xs'
                    colourClassName='text-inherit'
                  />
                </div>
              );
            },
            enableSorting: false,
          },
          {
            id: 'free-tao-balance-col',
            accessorKey: 'freeTaoBalance',
            header: 'Free TAO',
            meta: {
              align: 'end',
            },
            cell: ({ row }) => {
              const d = row.original;
              return (
                <div className='flex justify-end gap-2'>
                  <TaoOrAlphaValueDisplay
                    symbol={getCurrencySymbol()}
                    value={d.freeTaoBalance}
                    valueFormattingOptions={{
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    }}
                    valueTextLevel={
                      row.index === 0 ? 'labelExtraSmall' : 'labelLarge'
                    }
                    iconClassName='text-xs'
                    colourClassName='text-inherit'
                  />
                </div>
              );
            },
            enableSorting: false,
          },
          {
            id: 'total-tao-balance-col',
            accessorKey: 'totalTaoBalance',
            header: 'Total TAO',
            meta: {
              align: 'end',
            },
            cell: ({ row }) => {
              const d = row.original;
              return (
                <div className='flex justify-end gap-2'>
                  <TaoOrAlphaValueDisplay
                    symbol={getCurrencySymbol()}
                    value={d.totalTaoBalance}
                    valueFormattingOptions={{
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    }}
                    valueTextLevel={
                      row.index === 0 ? 'labelExtraSmall' : 'labelLarge'
                    }
                    iconClassName='text-xs'
                    colourClassName='text-inherit'
                  />
                </div>
              );
            },
            enableSorting: false,
          },
        ],
      }),
      columnHelper.display({
        id: 'balance-col-group-separator',
        meta: {
          separator: 'dashed',
        },
      }),
      columnHelper.group({
        id: 'performance-col-group',
        header: () => (
          <Text level='labelSmall'>
            Performance/
            {
              miningDateRanges.find((dr) => dr.value === dateRangeSelected)
                ?.label
            }
          </Text>
        ),
        meta: {
          align: 'center',
        },
        columns: [
          {
            id: 'combined-performance-col',
            accessorKey: 'combinedPerformance',
            header: 'Combined',
            meta: {
              align: 'end',
            },
            cell: ({ row }) => {
              const d = row.original;
              return (
                <div className='flex justify-end gap-2'>
                  <TaoOrAlphaValueDisplay
                    symbol={getCurrencySymbol(d.subnetSymbol)}
                    value={d.combinedPerformance}
                    valueFormattingOptions={{
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    }}
                    valueTextLevel={
                      row.index === 0 ? 'labelExtraSmall' : 'labelLarge'
                    }
                    iconClassName='text-xs'
                    colourClassName='text-inherit'
                  />
                </div>
              );
            },
            enableSorting: false,
          },
          {
            id: 'average-performance-col',
            accessorKey: 'averagePerformance',
            header: 'Avg Per Hotkey',
            meta: {
              align: 'end',
            },
            cell: ({ row }) => {
              const d = row.original;
              return (
                <div className='flex justify-end gap-2'>
                  <TaoOrAlphaValueDisplay
                    symbol={getCurrencySymbol(d.subnetSymbol)}
                    value={d.averagePerformance}
                    valueFormattingOptions={{
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    }}
                    valueTextLevel={
                      row.index === 0 ? 'labelExtraSmall' : 'labelLarge'
                    }
                    iconClassName='text-xs'
                    colourClassName='text-inherit'
                  />
                </div>
              );
            },
            enableSorting: false,
          },
        ],
      }),
      columnHelper.display({
        id: 'performance-col-group-separator',
        meta: {
          separator: 'dashed',
        },
      }),
      columnHelper.group({
        id: 'hotkey-stats-col-group',
        header: () => <Text level='labelSmall'>Hotkey Stats</Text>,
        meta: {
          align: 'center',
        },
        columns: [
          {
            id: 'hotkey-total-col',
            accessorKey: 'hotkeyTotal',
            header: 'Total',
            meta: {
              align: 'end',
            },
            cell: ({ row }) => {
              const d = row.original;
              return (
                <div className='flex justify-end gap-2'>
                  <BigSmallValueDisplay
                    textLevel={
                      row.index === 0 ? 'labelExtraSmall' : 'labelLarge'
                    }
                    value={d.hotkeyTotal}
                    valueFormattingOptions={{
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0,
                    }}
                    className='text-inherit'
                  />
                </div>
              );
            },
            enableSorting: false,
          },
          {
            id: 'hotkey-immune-col',
            accessorKey: 'hotkeyImmune',
            header: 'Immune',
            meta: {
              align: 'end',
            },
            cell: ({ row }) => {
              const d = row.original;
              return (
                <div className='flex justify-end gap-2'>
                  <BigSmallValueDisplay
                    textLevel={
                      row.index === 0 ? 'labelExtraSmall' : 'labelLarge'
                    }
                    value={d.hotkeyImmune}
                    valueFormattingOptions={{
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0,
                    }}
                    className='text-inherit'
                  />
                </div>
              );
            },
            enableSorting: false,
          },
          {
            id: 'hotkey-danger-col',
            accessorKey: 'hotkeyDanger',
            header: 'Danger',
            meta: {
              align: 'end',
            },
            cell: ({ row }) => {
              const d = row.original;
              return (
                <div className='flex justify-end gap-2'>
                  <BigSmallValueDisplay
                    textLevel={
                      row.index === 0 ? 'labelExtraSmall' : 'labelLarge'
                    }
                    value={d.hotkeyDanger}
                    valueFormattingOptions={{
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0,
                    }}
                    className='text-inherit'
                  />
                </div>
              );
            },
            enableSorting: false,
          },
          {
            id: 'hotkey-dereg-col',
            accessorKey: 'hotkeyDereg',
            header: 'Dereg',
            meta: {
              align: 'end',
            },
            cell: ({ row }) => {
              const d = row.original;
              return (
                <div className='flex justify-end gap-2'>
                  <BigSmallValueDisplay
                    textLevel={
                      row.index === 0 ? 'labelExtraSmall' : 'labelLarge'
                    }
                    value={d.hotkeyDereg}
                    valueFormattingOptions={{
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0,
                    }}
                    className='text-inherit'
                  />
                </div>
              );
            },
            enableSorting: false,
          },
        ],
      }),
    ],
    [
      columnHelper,
      dateRangeSelected,
      getCurrencySymbol,
      selectedColdkeys,
      setCheckedChanged,
    ]
  );

  return columns;
};

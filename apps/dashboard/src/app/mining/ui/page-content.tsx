'use client';

import { memo } from 'react';
import { notFound } from 'next/navigation';
import { ColdkeyTable } from './coldkey-table/coldkey-table';
import { HotkeyCharts } from './hotkey-charts/hotkey-charts';
import { HotkeyTable } from './hotkey-table/hotkey-table';
import { StatsCardSection } from './stats-cards/stats-cards-section';
import { useAppEnv } from '@/lib/hooks/global-hooks';

export const PageContent = memo(function MiningPageContent() {
  const appEnv = useAppEnv();
  if (appEnv !== 'development' && appEnv !== 'test') {
    return notFound();
  }

  return (
    <>
      <StatsCardSection />
      <ColdkeyTable />
      <HotkeyTable />
      <HotkeyCharts />
    </>
  );
});

import { CopyButton, Text } from '@repo/ui/components';
import { cn } from '@repo/ui/lib';

type KeyTextProps = {
  hotOrColdKey: string;
  hotOrColdKeyName?: string;
  className?: string;
};

export const KeyText = ({
  hotOrColdKey,
  hotOrColdKeyName,
  className,
}: KeyTextProps) => {
  return (
    <span className={cn('flex flex-row gap-1 truncate', className)}>
      <Text
        level='headerExtraSmall'
        className='font-fira font-normal text-inherit'
      >
        {(hotOrColdKeyName ? hotOrColdKeyName : hotOrColdKey).substring(0, 19)}
      </Text>
      <CopyButton size={14} value={hotOrColdKey} className='text-inherit' />
    </span>
  );
};

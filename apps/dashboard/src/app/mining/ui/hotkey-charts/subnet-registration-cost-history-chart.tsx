/* eslint-disable @typescript-eslint/no-unnecessary-condition -- Necessary to handle cases where conditions may appear redundant but are required for type safety */
'use client';

import { useCallback, memo } from 'react';
import { curveStep } from '@visx/curve';
import { LinearGradient } from '@visx/gradient';
import { ParentSize } from '@visx/responsive';
import { defaultStyles as tooltipDefaultStyles } from '@visx/tooltip';
import {
  XYChart,
  Axis,
  AreaSeries,
  Grid,
  Tooltip,
  type TooltipData,
} from '@visx/xychart';
import { Text, Skeleton, Alert } from '@repo/ui/components';
import { truncateString } from '@repo/ui/lib';
import { type SingleAxisData } from './utils';
import { useMiningContext } from '@/app/mining/lib/context';
import { useSubnetRegistrationCostHistoryQuery } from '@/app/mining/lib/query-hooks';
import { RAO_PER_TAO } from '@/contexts/chain-and-wallet/constants';
import { formatNumberToSpecifiedDp } from '@/lib/utils';

const dataKeys = {
  leftValueArea: 'Left Value Area',
  upperLeftThreshold: 'Upper Left Threshold',
  lowerLeftThreshold: 'Lower Left Threshold',
} as const;

const getColour = (index: number, length: number) => {
  const hue = (360 / length) * index;

  const saturation = 40;
  const lightness = 50;
  const colour = `hsl(${hue}deg, ${saturation}%, ${lightness}%)`;

  return { colour };
};

const useTooltipRenderer = () => {
  const handleRenderTooltip = useCallback(
    ({ tooltipData }: { tooltipData?: TooltipData }) => {
      const datums = Object.values(tooltipData?.datumByKey ?? {});

      const date = (datums[0]?.datum as SingleAxisData | undefined)?.date;
      const leftValues = datums
        .map((d) => {
          return {
            key: d.key,
            leftValue: (d.datum as SingleAxisData | undefined)?.leftValue,
            hotkey: (d.datum as SingleAxisData | undefined)?.label,
          };
        })
        .filter((d) => d.leftValue !== undefined);

      if (!date || !leftValues) {
        return null;
      }

      return (
        <div className='min-w-60.75 rounded-lg px-5 py-3'>
          <div className='flex flex-col gap-2'>
            <div>
              <Text level='labelLarge'>
                {date.toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric',
                  year: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit',
                  hour12: false,
                })}
              </Text>
            </div>
            {leftValues.map(({ key, leftValue, hotkey }, index) => (
              <div key={key} className='flex flex-row items-center gap-2'>
                <div
                  className='h-3 w-3 rounded-sm'
                  style={{
                    backgroundColor: getColour(index, leftValues.length).colour,
                  }}
                />
                <Text level='bodySmall' className='text-white/60'>
                  {/* eslint-disable-next-line @typescript-eslint/no-non-null-assertion -- Necessary because 'leftValue' is guaranteed to be defined due to prior filtering */}
                  {formatNumberToSpecifiedDp(leftValue!, 3)}
                  {hotkey ? ` (${truncateString(hotkey)})` : null}
                </Text>
              </div>
            ))}
          </div>
        </div>
      );
    },
    []
  );

  return handleRenderTooltip;
};

export const SubnetRegistrationCostHistoryChart = memo(
  function SubnetRegistrationCostHistoryChart() {
    const { dateRangeSelected } = useMiningContext();
    const { isLoading, isError, isSuccess } =
      useSubnetRegistrationCostHistoryQuery(dateRangeSelected);

    return (
      <div className='h-full w-full overflow-hidden'>
        <Text as='h3' level='headerSmall' className='font-normal'>
          Subnet Registration Cost History
        </Text>
        {isLoading ? (
          <div className='flex w-full items-center justify-center'>
            <Skeleton className='h-full w-full' />
          </div>
        ) : null}

        {isError ? (
          <Alert
            type='error'
            description='An error occurred while getting the earnings data.'
            title='Error'
          />
        ) : null}

        {isSuccess ? (
          <div className='h-full w-full'>
            <ParentSize>
              {({ width, height }) => <Chart width={width} height={height} />}
            </ParentSize>
          </div>
        ) : null}
      </div>
    );
  }
);

const Chart = memo(function Chart({
  width,
  height,
}: {
  width: number;
  height: number;
}) {
  const { dateRangeSelected } = useMiningContext();
  const { data } = useSubnetRegistrationCostHistoryQuery(dateRangeSelected);
  const handleRenderTooltip = useTooltipRenderer();

  const dataSet =
    data
      ?.map((d) => ({
        date: new Date(d.timestamp),
        leftValue: Number(d.registration_cost) / RAO_PER_TAO,
      }))
      .sort((a, b) => a.date.getTime() - b.date.getTime()) ?? [];

  return (
    <div className='h-full w-full'>
      <XYChart
        width={width}
        height={height}
        xScale={{ type: 'time' }}
        yScale={{
          type: 'linear',
        }}
        //margin={defaultMargin}
      >
        <Grid
          columns
          numTicks={5}
          lineStyle={{
            strokeOpacity: 0.1,
            strokeWidth: 0.5,
            stroke: '#FFFFFF',
          }}
        />
        <Axis
          orientation='bottom'
          hideAxisLine={false}
          axisLineClassName='stroke-[#ffffff]/10'
          hideTicks
          numTicks={5}
          tickFormat={(date) =>
            (date as Date).toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric',
            })
          }
        />
        <Axis
          hideAxisLine
          hideTicks
          numTicks={5}
          orientation='left'
          tickLabelProps={{
            fill: '#888',
            fontSize: 11,
          }}
          tickFormat={(value) =>
            (value as number).toLocaleString('en-US', {
              notation: 'compact',
              compactDisplay: 'short',
              maximumFractionDigits: 3,
            })
          }
        />

        <LinearGradient
          id='left-area-gradient'
          from={getColour(0, 1).colour}
          to={getColour(0, 1).colour}
          fromOpacity={0.2}
          toOpacity={0}
        />
        <AreaSeries
          dataKey={dataKeys.leftValueArea}
          data={dataSet}
          xAccessor={(d) => d?.date}
          yAccessor={(d) => d?.leftValue}
          fill='url(#left-area-gradient'
          curve={curveStep}
          lineProps={{
            strokeWidth: 2,
            stroke: getColour(0, 1).colour,
          }}
        />

        <Tooltip
          style={{
            ...tooltipDefaultStyles,
            opacity: 0.8,
            backdropFilter: 'blur(23.8px)',
            backgroundColor: '#1d1d1d',
            borderRadius: '8px',
            padding: '10px',
          }}
          showVerticalCrosshair
          verticalCrosshairStyle={{
            strokeDasharray: '5 3',
          }}
          renderTooltip={handleRenderTooltip}
          showSeriesGlyphs
          renderGlyph={({ color }) => {
            return <circle r={4} fill={color} stroke='white' strokeWidth={2} />;
          }}
        />
      </XYChart>
    </div>
  );
});

import { Card, Text } from '@repo/ui/components';
import { HotkeyChart } from './hotkey-chart';
import { SubnetQuantityImmuneChart } from './subnet-quantity-immune-chart';
import { SubnetRegistrationCostHistoryChart } from './subnet-registration-cost-history-chart';
import { ChartTypeOptions } from './utils';

export const HotkeyChartCard = ({
  chartType,
  title,
  linkedChart,
}: {
  chartType: ChartTypeOptions;
  title: string;
  linkedChart?: ChartTypeOptions;
}) => (
  <Card
    className='h-[260px] w-full border border-[#323232] bg-[#1D1D1D]'
    contentContainerClassName='h-full'
  >
    <HotkeyChart
      chartType={chartType}
      title={title}
      linkedChart={linkedChart}
    />
  </Card>
);

export const HotkeyCharts = () => {
  return (
    <>
      <div className='mt-8 flex flex-row items-center gap-8'>
        <Text as='h2' level='headerMedium' className='text-white'>
          Hotkey Charts
        </Text>
      </div>

      <div className='grid w-full grid-cols-1 gap-8 md:grid-cols-2 xl:grid-cols-3'>
        <HotkeyChartCard
          chartType={ChartTypeOptions.Incentive}
          title='Incentive'
          linkedChart={ChartTypeOptions.Incentive}
        />
        <HotkeyChartCard
          chartType={ChartTypeOptions.Emission}
          title='Emission'
          linkedChart={ChartTypeOptions.Emission}
        />
        <HotkeyChartCard
          chartType={ChartTypeOptions.Weights}
          title='Weights'
          linkedChart={ChartTypeOptions.Weights}
        />
        <HotkeyChartCard
          chartType={ChartTypeOptions.Trust}
          title='Trust'
          linkedChart={ChartTypeOptions.Trust}
        />
        <HotkeyChartCard
          chartType={ChartTypeOptions.Rank}
          title='Rank'
          linkedChart={ChartTypeOptions.Rank}
        />
        <HotkeyChartCard
          chartType={ChartTypeOptions.PruningScore}
          title='Pruning Score'
          linkedChart={ChartTypeOptions.PruningScore}
        />
      </div>
      <div className='grid w-full grid-cols-1 gap-8 md:grid-cols-2'>
        <Card
          className='h-[260px] w-full border border-[#323232] bg-[#1D1D1D]'
          contentContainerClassName='h-full'
        >
          <SubnetQuantityImmuneChart />
        </Card>
        <Card
          className='h-[260px] w-full border border-[#323232] bg-[#1D1D1D]'
          contentContainerClassName='h-full'
        >
          <SubnetRegistrationCostHistoryChart />
        </Card>
      </div>
    </>
  );
};

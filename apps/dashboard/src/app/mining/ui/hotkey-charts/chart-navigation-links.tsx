import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  BiBar<PERSON>hart,
  BiCube,
  BiD<PERSON><PERSON>ll,
  BiPulse,
} from 'react-icons/bi';
import { CgLivePhoto } from 'react-icons/cg';
import { ChartTypeOptions } from '@/app/mining/ui/hotkey-charts/utils';

export type NavigationLink = {
  label: string;
  href: string;
  icon: React.ReactNode;
};

export const sectionNavigationLinks: NavigationLink[] = [
  {
    label: 'Back',
    href: '/mining',
    icon: <BiArrowBack size={24} color='#00DBBC' />,
  },
  {
    label: 'Incentive',
    href: `#${ChartTypeOptions.Incentive}`,
    icon: <BiCube size={24} />,
  },
  {
    label: 'Emission',
    href: `#${ChartTypeOptions.Emission}`,
    icon: <CgLivePhoto size={24} />,
  },
  {
    label: 'Weights',
    href: `#${ChartTypeOptions.Weights}`,
    icon: <BiDumbbell size={24} />,
  },
  {
    label: 'Trust',
    href: `#${ChartTypeOptions.Trust}`,
    icon: <BiBadgeCheck size={24} />,
  },
  {
    label: 'Rank',
    href: `#${ChartTypeOptions.Rank}`,
    icon: <BiBarChart size={24} />,
  },
  {
    label: 'Pruning Score',
    href: `#${ChartTypeOptions.PruningScore}`,
    icon: <BiPulse size={24} />,
  },
];

/* eslint-disable @typescript-eslint/no-unnecessary-condition -- Necessary to handle cases where conditions may appear redundant but are required for type safety */
'use client';

import { useCallback, memo, useMemo } from 'react';
import { curveStep } from '@visx/curve';
import { LinearGradient } from '@visx/gradient';
import { ParentSize } from '@visx/responsive';
import { defaultStyles as tooltipDefaultStyles } from '@visx/tooltip';
import {
  XYChart,
  Axis,
  AreaSeries,
  Grid,
  Tooltip,
  type TooltipData,
} from '@visx/xychart';
import { Text, Skeleton, Alert } from '@repo/ui/components';
import { ChartTypeOptions, useChartData, type SingleAxisData } from './utils';
import { useMiningContext } from '@/app/mining/lib/context';
import { useAggregatedNeuronHistoryDataForSubnet } from '@/app/mining/lib/query-hooks';
import { formatNumberToSpecifiedDp } from '@/lib/utils';

const dataKeys = {
  leftValueArea: 'Left Value Area',
  upperLeftThreshold: 'Upper Left Threshold',
  lowerLeftThreshold: 'Lower Left Threshold',
} as const;

const getColour = (index: number, length: number) => {
  const hue = (360 / length) * index;

  const saturation = 40;
  const lightness = 50;
  const colour = `hsl(${hue}deg, ${saturation}%, ${lightness}%)`;

  return { colour };
};

const useTooltipRenderer = () => {
  const handleRenderTooltip = useCallback(
    ({ tooltipData }: { tooltipData?: TooltipData }) => {
      const datums = Object.values(tooltipData?.datumByKey ?? {});

      const date = (datums[0]?.datum as SingleAxisData | undefined)?.date;
      const leftValues = datums
        .map((d) => {
          return {
            key: d.key,
            leftValue: (d.datum as SingleAxisData | undefined)?.leftValue,
            hotkey: (d.datum as SingleAxisData | undefined)?.label,
          };
        })
        .filter((d) => d.leftValue !== undefined);

      if (!date || !leftValues) {
        return null;
      }

      return (
        <div className='min-w-60.75 rounded-lg px-5 py-3'>
          <div className='flex flex-col gap-2'>
            <div>
              <Text level='labelLarge'>
                {date.toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric',
                  year: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit',
                  hour12: false,
                })}
              </Text>
            </div>
            {leftValues.map(({ key, leftValue }, index) => (
              <div key={key} className='flex flex-row items-center gap-2'>
                <div
                  className='h-3 w-3 rounded-sm'
                  style={{
                    backgroundColor: getColour(index, leftValues.length).colour,
                  }}
                />
                <Text level='bodySmall' className='text-white/60'>
                  {key === undefined
                    ? ''
                    : key.endsWith('-hotkeys-total')
                      ? 'Hotkey(s) Total '
                      : 'Subnet Total '}
                  {/* eslint-disable-next-line @typescript-eslint/no-non-null-assertion -- Necessary because 'leftValue' is guaranteed to be defined due to prior filtering */}
                  {formatNumberToSpecifiedDp(leftValue!, 0)}
                </Text>
              </div>
            ))}
          </div>
        </div>
      );
    },
    []
  );

  return handleRenderTooltip;
};

export const SubnetQuantityImmuneChart = memo(
  function SubnetQuantityImmuneChart() {
    const { selectedSubnet, dateRangeSelected } = useMiningContext();

    const {
      isLoading: isChartDataLoading,
      isError: isChartDataError,
      isSuccess: isChartDataSuccess,
    } = useChartData(ChartTypeOptions.QuantityImmune);

    const {
      isLoading: isTotalImmuneLoading,
      isError: isTotalImmuneError,
      isSuccess: isTotalImmuneSuccess,
    } = useAggregatedNeuronHistoryDataForSubnet(
      selectedSubnet,
      dateRangeSelected
    );

    return (
      <div className='h-full w-full overflow-hidden'>
        <div className='relative top-[-100px] block h-0' />

        <Text as='h3' level='headerSmall' className='font-normal'>
          Subnet Quantity of Immune Miners
        </Text>

        {isChartDataLoading || isTotalImmuneLoading ? (
          <div className='flex w-full items-center justify-center'>
            <Skeleton className='h-full w-full' />
          </div>
        ) : null}

        {isChartDataError || isTotalImmuneError ? (
          <Alert
            type='error'
            description='An error occurred while getting the immunity data.'
            title='Error'
          />
        ) : null}

        {isChartDataSuccess && isTotalImmuneSuccess ? (
          <div className='h-full w-full'>
            <ParentSize>
              {({ width, height }) => <Chart width={width} height={height} />}
            </ParentSize>
          </div>
        ) : null}
      </div>
    );
  }
);

const Chart = memo(function Chart({
  width,
  height,
}: {
  width: number;
  height: number;
}) {
  const { selectedSubnet, dateRangeSelected } = useMiningContext();

  const { data, defaultMargin, showTicks } = useChartData(
    ChartTypeOptions.QuantityImmune
  );

  const { data: aggregatedNeuronHistoryDataForSubnet } =
    useAggregatedNeuronHistoryDataForSubnet(selectedSubnet, dateRangeSelected);

  const subnetTotalImmuneChartData = useMemo(
    () =>
      aggregatedNeuronHistoryDataForSubnet?.map((item) => {
        return {
          date: new Date(item.timestamp),
          leftValue: item.immune_count,
        };
      }) ?? [],
    [aggregatedNeuronHistoryDataForSubnet]
  );

  const handleRenderTooltip = useTooltipRenderer();

  return (
    <div className='h-full w-full'>
      <XYChart
        width={width}
        height={height}
        xScale={{ type: 'time' }}
        yScale={{
          type: 'linear',
          zero: true,
        }}
        margin={defaultMargin}
      >
        <Grid
          columns
          numTicks={5}
          lineStyle={{
            strokeOpacity: 0.1,
            strokeWidth: 0.5,
            stroke: '#FFFFFF',
          }}
        />
        <Axis
          orientation='bottom'
          hideAxisLine={false}
          axisLineClassName='stroke-[#ffffff]/10'
          hideTicks
          numTicks={5}
          tickFormat={(date) =>
            (date as Date).toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric',
            })
          }
        />
        <Axis
          hideAxisLine
          hideTicks
          numTicks={5}
          orientation='left'
          tickLabelProps={{
            fill: '#888',
            fontSize: 11,
          }}
          tickFormat={(value) =>
            showTicks
              ? (value as number).toLocaleString('en-US', {
                  notation: 'compact',
                  compactDisplay: 'short',
                  maximumFractionDigits: 3,
                })
              : ''
          }
        />

        <LinearGradient
          id='left-area-gradient-hotkeys-total'
          from={getColour(0, 2).colour}
          to={getColour(0, 2).colour}
          fromOpacity={0.2}
          toOpacity={0}
        />
        <AreaSeries
          dataKey={`${dataKeys.leftValueArea}-hotkeys-total`}
          data={data.hotkeyChartData[0]}
          xAccessor={(d) => d?.date}
          yAccessor={(d) => d?.leftValue}
          fill='url(#left-area-gradient-hotkeys-total)'
          curve={curveStep}
          lineProps={{
            strokeWidth: 2,
            stroke: getColour(0, 2).colour,
          }}
        />

        <LinearGradient
          id='left-area-gradient-subnet-total'
          from={getColour(1, 2).colour}
          to={getColour(1, 2).colour}
          fromOpacity={0.2}
          toOpacity={0}
        />
        <AreaSeries
          dataKey={`${dataKeys.leftValueArea}-subnet-total`}
          data={subnetTotalImmuneChartData}
          xAccessor={(d) => d?.date}
          yAccessor={(d) => d?.leftValue + 1}
          fill='url(#left-area-gradient-subnet-total)'
          curve={curveStep}
          lineProps={{
            strokeWidth: 2,
            stroke: getColour(1, 2).colour,
          }}
        />

        <Tooltip
          style={{
            ...tooltipDefaultStyles,
            opacity: 0.8,
            backdropFilter: 'blur(23.8px)',
            backgroundColor: '#1d1d1d',
            borderRadius: '8px',
            padding: '10px',
          }}
          showVerticalCrosshair
          verticalCrosshairStyle={{
            strokeDasharray: '5 3',
          }}
          renderTooltip={handleRenderTooltip}
          showSeriesGlyphs
          renderGlyph={({ color }) => {
            return <circle r={4} fill={color} stroke='white' strokeWidth={2} />;
          }}
        />
      </XYChart>
    </div>
  );
});

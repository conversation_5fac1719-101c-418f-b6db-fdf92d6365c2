/* eslint-disable @typescript-eslint/no-unnecessary-condition -- Necessary to handle cases where conditions may appear redundant but are required for type safety */
'use client';

import React, { useCallback, memo, useMemo } from 'react';
import { curveStepAfter } from '@visx/curve';
import { LinearGradient } from '@visx/gradient';
import { ParentSize } from '@visx/responsive';
import { defaultStyles as tooltipDefaultStyles } from '@visx/tooltip';
import {
  XYChart,
  Axis,
  AreaSeries,
  Grid,
  Tooltip,
  type TooltipData,
} from '@visx/xychart';
import { CgArrowRight } from 'react-icons/cg';
import { useSessionStorage } from 'usehooks-ts';
import { Text, Skeleton, Alert, Link, Checkbox } from '@repo/ui/components';
import { truncateString } from '@repo/ui/lib';
import {
  type ChartTypeOptions,
  useChartData,
  type SingleAxisData,
} from './utils';
import { formatNumberToSpecifiedDp } from '@/lib/utils';

interface SupplementaryColour {
  label: string;
  colour: string;
}

const dataKeys = {
  leftValueArea: 'Left Value Area',
  upperLeftThreshold: 'Upper Left Threshold',
  lowerLeftThreshold: 'Lower Left Threshold',
} as const;

const getColour = (index: number, length: number) => {
  const hue = (360 / length) * index;

  const saturation = 40;
  const lightness = 50;
  const colour = `hsl(${hue}deg, ${saturation}%, ${lightness}%)`;

  return { colour };
};

const useTooltipRenderer = () => {
  const handleRenderTooltip = useCallback(
    ({ tooltipData }: { tooltipData?: TooltipData }) => {
      const datums = Object.values(tooltipData?.datumByKey ?? {});

      const date = (datums[0]?.datum as SingleAxisData | undefined)?.date;
      const leftValues = datums
        .map((d) => {
          return {
            key: d.key,
            leftValue: (d.datum as SingleAxisData | undefined)?.leftValue,
            label: (d.datum as SingleAxisData | undefined)?.label,
          };
        })
        .filter((d) => d.leftValue !== undefined);

      if (!date || !leftValues) {
        return null;
      }

      return (
        <div className='min-w-60.75 rounded-lg px-5 py-3'>
          <div className='flex flex-col gap-2'>
            <div>
              <Text level='labelLarge'>
                {date.toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric',
                  year: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit',
                  hour12: false,
                })}
              </Text>
            </div>
            {leftValues.map(({ key, leftValue, label }, index) => (
              <div key={key} className='flex flex-row items-center gap-2'>
                <div
                  className='h-3 w-3 rounded-sm'
                  style={{
                    backgroundColor: getColour(index, leftValues.length).colour,
                  }}
                />
                <Text level='bodySmall' className='text-white/60'>
                  {/* eslint-disable-next-line @typescript-eslint/no-non-null-assertion -- Necessary because 'leftValue' is guaranteed to be defined due to prior filtering */}
                  {formatNumberToSpecifiedDp(leftValue!, 3)}
                  {label ? ` (${truncateString(label)})` : null}
                </Text>
              </div>
            ))}
          </div>
        </div>
      );
    },
    []
  );

  return handleRenderTooltip;
};

export const HotkeyChart = memo(function HotkeyChart({
  chartType,
  title,
  linkedChart,
}: {
  chartType: ChartTypeOptions;
  title: string;
  linkedChart?: ChartTypeOptions;
}) {
  const { isLoading, isError, isSuccess, data } = useChartData(chartType);
  const [hiddenSupplementaryData, setHiddenSupplementaryData] =
    useSessionStorage<string[]>(`mining-chart-supplementary-${chartType}`, []);

  const supplementaryColors = useMemo(
    () =>
      data?.supplementaryChartData
        .filter((d) => d[0]?.label !== undefined)
        .map((dataset, i) => ({
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion -- has been filtered to ensure label is defined
          label: dataset[0].label!,
          colour: getColour(
            i + data.hotkeyChartData.length,
            data.supplementaryChartData.length + data.hotkeyChartData.length
          ).colour,
        })) ?? [],
    [data]
  );

  return (
    <>
      <div className='h-full w-full overflow-hidden'>
        <div className='relative top-[-100px] block h-0' id={chartType} />
        {linkedChart ? (
          <Link
            href={`/mining/charts#${linkedChart}`}
            className='flex w-full flex-row items-center justify-between'
          >
            <Text as='h3' level='headerSmall' className='font-normal'>
              {title}
            </Text>

            <CgArrowRight size={24} className='text-[#00DBBC]' />
          </Link>
        ) : (
          <Text as='h3' level='headerSmall' className='font-normal'>
            {title}
          </Text>
        )}
        {isLoading ? (
          <div className='flex w-full items-center justify-center'>
            <Skeleton className='h-full w-full' />
          </div>
        ) : null}
        {isError ? (
          <Alert
            type='error'
            description='An error occurred while getting the earnings data.'
            title='Error'
          />
        ) : null}
        {isSuccess ? (
          <div className='h-full w-full'>
            <ParentSize>
              {({ width, height }) => (
                <Chart
                  width={width}
                  height={height}
                  chartType={chartType}
                  hiddenSupplementaryData={hiddenSupplementaryData}
                  supplementaryColors={supplementaryColors}
                />
              )}
            </ParentSize>
          </div>
        ) : null}
      </div>
      {isSuccess && data.supplementaryChartData.length > 0 ? (
        <div className='flex flex-row justify-end gap-1 sm:gap-2'>
          {data.supplementaryChartData
            .filter((dataset) => dataset[0]?.label)
            .map((dataset) => {
              const label = dataset[0].label;
              const hidden = label && hiddenSupplementaryData.includes(label);
              return (
                <div
                  key={dataset[0]?.label}
                  className='flex flex-row items-center gap-1'
                >
                  <Checkbox
                    checked={!hidden}
                    onCheckedChange={() => {
                      if (hidden) {
                        setHiddenSupplementaryData((prev) =>
                          prev.filter((item) => item !== label)
                        );
                      } else if (label) {
                        setHiddenSupplementaryData((prev) => [...prev, label]);
                      }
                    }}
                    className={hidden ? undefined : `border-none`}
                    style={
                      hidden
                        ? undefined
                        : {
                            backgroundColor:
                              supplementaryColors.find(
                                (color) => color.label === label
                              )?.colour ?? '#00DBBC',
                          }
                    }
                  />
                  <Text level='bodySmall' className='text-white/60'>
                    {label}
                  </Text>
                </div>
              );
            })}
        </div>
      ) : null}
    </>
  );
});

const Chart = memo(function Chart({
  width,
  height,
  chartType,
  hiddenSupplementaryData,
  supplementaryColors,
}: {
  width: number;
  height: number;
  chartType: ChartTypeOptions;
  hiddenSupplementaryData: string[];
  supplementaryColors: SupplementaryColour[];
}) {
  const { data, chartConfig, defaultMargin, showTicks } =
    useChartData(chartType);
  const handleRenderTooltip = useTooltipRenderer();

  const findSupplementaryColour = (label: string) =>
    supplementaryColors.find((sc) => sc.label === label)?.colour;

  return (
    <div className='h-full w-full'>
      <XYChart
        width={width}
        height={height}
        xScale={{ type: 'time' }}
        yScale={{
          type: 'linear',
          domain: [chartConfig.leftAxisMinScale, chartConfig.leftAxisMaxScale],
          zero: true,
        }}
        margin={defaultMargin}
      >
        <Grid
          columns
          numTicks={5}
          lineStyle={{
            strokeOpacity: 0.1,
            strokeWidth: 0.5,
            stroke: '#FFFFFF',
          }}
        />
        <Axis
          orientation='bottom'
          hideAxisLine={false}
          axisLineClassName='stroke-[#ffffff]/10'
          hideTicks
          numTicks={5}
          tickFormat={(date) =>
            (date as Date).toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric',
            })
          }
        />
        <Axis
          hideAxisLine
          hideTicks
          numTicks={5}
          orientation='left'
          tickLabelProps={{
            fill: '#888',
            fontSize: 11,
          }}
          tickFormat={(value) =>
            showTicks
              ? (value as number).toLocaleString('en-US', {
                  notation: 'compact',
                  compactDisplay: 'short',
                  maximumFractionDigits: 3,
                })
              : ''
          }
        />

        {data.hotkeyChartData.map(
          (dataSet, index) => (
            <React.Fragment key={dataSet[0]?.label}>
              <LinearGradient
                id={`left-area-gradient-${index}`}
                from={getColour(index, data.hotkeyChartData.length).colour}
                to={getColour(index, data.hotkeyChartData.length).colour}
                fromOpacity={0.2}
                toOpacity={0}
              />
              <AreaSeries
                dataKey={`${dataKeys.leftValueArea}-${index}`}
                data={dataSet}
                xAccessor={(d) => d?.date}
                yAccessor={(d) => d?.leftValue}
                fill={`url(#left-area-gradient-${index})`}
                curve={curveStepAfter}
                lineProps={{
                  strokeWidth: 2,
                  stroke: getColour(index, data.hotkeyChartData.length).colour,
                }}
              />
            </React.Fragment>
          ),
          [data]
        )}

        {data.supplementaryChartData
          .filter(
            (d) =>
              d[0]?.label !== undefined &&
              !hiddenSupplementaryData.includes(d[0].label)
          )
          .map(
            (dataSet, index) => {
              const offsetIndex = index + data.hotkeyChartData.length;
              // eslint-disable-next-line @typescript-eslint/no-non-null-assertion --  has been filtered to ensure label is defined
              const label = dataSet[0].label!;

              return (
                <React.Fragment key={dataSet[0]?.label}>
                  <LinearGradient
                    id={`left-area-gradient-${offsetIndex}`}
                    from={findSupplementaryColour(label)}
                    to={findSupplementaryColour(label)}
                    fromOpacity={0.5}
                    toOpacity={0}
                  />
                  <AreaSeries
                    dataKey={`${dataKeys.leftValueArea}-${offsetIndex}`}
                    data={dataSet}
                    xAccessor={(d) => d?.date}
                    yAccessor={(d) => d?.leftValue}
                    fill={`url(#left-area-gradient-${offsetIndex})`}
                    curve={curveStepAfter}
                    lineProps={{
                      strokeWidth: 1,
                      stroke: findSupplementaryColour(label),
                      opacity: 0.5,
                    }}
                  />
                </React.Fragment>
              );
            },
            [data]
          )}

        <Tooltip
          style={{
            ...tooltipDefaultStyles,
            opacity: 0.8,
            backdropFilter: 'blur(23.8px)',
            backgroundColor: '#1d1d1d',
            borderRadius: '8px',
            padding: '10px',
          }}
          showVerticalCrosshair
          verticalCrosshairStyle={{
            strokeDasharray: '5 3',
          }}
          renderTooltip={handleRenderTooltip}
          showSeriesGlyphs
          renderGlyph={({ color }) => {
            return <circle r={4} fill={color} stroke='white' strokeWidth={2} />;
          }}
        />
      </XYChart>
    </div>
  );
});

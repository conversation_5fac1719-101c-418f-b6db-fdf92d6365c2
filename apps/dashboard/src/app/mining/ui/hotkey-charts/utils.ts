import { useMemo } from 'react';
import type { UseQueryResult } from '@tanstack/react-query';
import type {
  NeuronAggregatedHistoryItem,
  NeuronItem,
} from '@repo/types/website-api-types';
import { useMiningContext } from '@/app/mining/lib/context';
import {
  useAggregatedNeuronHistoryDataForSubnet,
  useNeuronHistoryDataForHotkeys,
} from '@/app/mining/lib/query-hooks';
import { RAO_PER_TAO } from '@/contexts/chain-and-wallet/constants';

export interface SingleAxisData {
  date: Date;
  leftValue: number;
  label?: string;
}

export interface ChartAxisData {
  hotkeyChartData: SingleAxisData[][];
  supplementaryChartData: SingleAxisData[][];
}

const upScale = (val: number, scale = 1.1) => (val || 1) * scale;
const downScale = (val: number, scale = 0.9) => val * scale;
const SHOW_TICKS = true;

export enum ChartTypeOptions {
  Incentive = 'incentive',
  Emission = 'emission',
  Weights = 'consensus',
  Trust = 'trust',
  Rank = 'miner_rank',
  PruningScore = 'pruning_score',
  QuantityImmune = 'is_immune',
}

const calculatePositionFactor = (
  axisVal: number,
  axisMin: number,
  axisMax: number
) => {
  // Get the amount of the axis value which is actually on the chart
  // e.g. if the axis starts at 9k an ends at 10k, and the value is 9.5k, the amount on the chart is 0.5k
  const amountOnChart = axisVal - axisMin;

  // Get the range of the axis
  const axisRange = axisMax - axisMin;

  // Get the position factor of the value on the axis
  const positionFactor = amountOnChart / axisRange;

  return positionFactor;
};

export const getDefaultMargin = (showTicks: boolean) =>
  showTicks
    ? { top: 40, right: 0, bottom: 50, left: 40 }
    : { top: 40, right: 0, bottom: 50, left: 0 };

export const useChartConfig = (data: ChartAxisData) => {
  const showTicks = SHOW_TICKS;
  const defaultMargin = getDefaultMargin(showTicks);

  const lVals = useMemo(() => {
    return data.hotkeyChartData
      .flatMap((d) => d.map((x) => x.leftValue))
      .concat(
        data.supplementaryChartData.flatMap((d) => d.map((x) => x.leftValue))
      );
  }, [data]);

  const chartConfig = useMemo(() => {
    const leftAxisMin = Math.min(...lVals);
    const leftAxisMax = Math.max(...lVals);
    const leftAxisMaxScale = upScale(leftAxisMax, 1.0001);
    const leftAxisMinScale = downScale(leftAxisMin, 0.9999);

    const lValMinPositionFactor = calculatePositionFactor(
      leftAxisMin,
      leftAxisMinScale,
      leftAxisMaxScale
    );

    const lValMaxPositionFactor = calculatePositionFactor(
      leftAxisMax,
      leftAxisMinScale,
      leftAxisMaxScale
    );

    return {
      leftAxisMin,
      leftAxisMax,
      leftAxisMaxScale,
      leftAxisMinScale,
      lValMinPositionFactor,
      lValMaxPositionFactor,
    };
  }, [lVals]);

  return {
    chartConfig,
    showTicks,
    defaultMargin,
  };
};

const getQuantityImmuneChartData = (
  neuronHistoryQuery: UseQueryResult<NeuronItem[]>[]
) => {
  const aggregatedData: Record<string, number> = {};

  neuronHistoryQuery.forEach((q) =>
    q.data?.forEach((history) => {
      const timestamp = new Date(history.timestamp).toISOString();
      aggregatedData[timestamp] =
        (aggregatedData[timestamp] || 0) + Number(history.is_immune);
    })
  );

  const result = Object.entries(aggregatedData)
    .map(([timestamp, total]) => ({
      timestamp,
      total,
    }))
    .sort((a, b) => a.timestamp.localeCompare(b.timestamp))
    .map((item) => ({
      date: new Date(item.timestamp),
      leftValue: item.total,
    }));

  return [[...result]] as SingleAxisData[][];
};

const getChartData = (
  neuronHistoryQuery: UseQueryResult<NeuronItem[]>[],
  dataOption: ChartTypeOptions
) => {
  const result = neuronHistoryQuery
    .map((q) =>
      q.data?.map((history) => ({
        date: new Date(Date.parse(history.timestamp)),
        leftValue: Number(history[dataOption]),
        label: history.hotkey.ss58,
      }))
    )
    .filter((q) => q !== undefined);

  return result as SingleAxisData[][];
};

const getImmunesupplementaryChartData = (
  aggregatedNeuronHistoryQuery: UseQueryResult<NeuronAggregatedHistoryItem[]>
) => {
  const topImmune: SingleAxisData[] =
    aggregatedNeuronHistoryQuery.data?.map((history) => ({
      date: new Date(Date.parse(history.timestamp)),
      leftValue: Number(history.max_immune_incentive),
      label: 'Max Immune',
    })) ?? [];
  const deregIncentive: SingleAxisData[] =
    aggregatedNeuronHistoryQuery.data?.map((history) => ({
      date: new Date(Date.parse(history.timestamp)),
      leftValue: Number(history.last_dereg_incentive),
      label: 'Last Dereg',
    })) ?? [];
  const danger: SingleAxisData[] =
    aggregatedNeuronHistoryQuery.data?.map((history) => ({
      date: new Date(Date.parse(history.timestamp)),
      leftValue: Number(history.max_danger_incentive),
      label: 'Max Danger',
    })) ?? [];
  const highestIncentive: SingleAxisData[] =
    aggregatedNeuronHistoryQuery.data?.map((history) => ({
      date: new Date(Date.parse(history.timestamp)),
      leftValue: Number(history.max_incentive),
      label: 'Max Incentive',
    })) ?? [];

  return [topImmune, deregIncentive, danger, highestIncentive];
};

const getPruningsupplementaryChartData = (
  aggregatedNeuronHistoryQuery: UseQueryResult<NeuronAggregatedHistoryItem[]>
) => {
  const topImmune: SingleAxisData[] =
    aggregatedNeuronHistoryQuery.data?.map((history) => ({
      date: new Date(Date.parse(history.timestamp)),
      leftValue: Number(history.max_immune_pruning_score),
      label: 'Max Immune',
    })) ?? [];
  const deregIncentive: SingleAxisData[] =
    aggregatedNeuronHistoryQuery.data?.map((history) => ({
      date: new Date(Date.parse(history.timestamp)),
      leftValue: Number(history.last_dereg_pruning_score),
      label: 'Last Dereg',
    })) ?? [];
  const danger: SingleAxisData[] =
    aggregatedNeuronHistoryQuery.data?.map((history) => ({
      date: new Date(Date.parse(history.timestamp)),
      leftValue: Number(history.max_danger_pruning_score),
      label: 'Max Danger',
    })) ?? [];
  const highestIncentive: SingleAxisData[] =
    aggregatedNeuronHistoryQuery.data?.map((history) => ({
      date: new Date(Date.parse(history.timestamp)),
      leftValue: Number(history.max_pruning_score),
      label: 'Max Pruning',
    })) ?? [];

  return [topImmune, deregIncentive, danger, highestIncentive];
};

export const useChartData = (dataOption: ChartTypeOptions) => {
  const { selectedHotkeys, dateRangeSelected } = useMiningContext();
  const neuronHistoryQuery = useNeuronHistoryDataForHotkeys(
    selectedHotkeys,
    dateRangeSelected
  );
  const aggregatedNeuronHistoryQuery = useAggregatedNeuronHistoryDataForSubnet(
    selectedHotkeys[0]?.netuid,
    dateRangeSelected
  );

  const data: ChartAxisData = useMemo(() => {
    switch (dataOption) {
      case ChartTypeOptions.QuantityImmune: {
        const hotkeyChartData = getQuantityImmuneChartData(neuronHistoryQuery);
        return { hotkeyChartData, supplementaryChartData: [] };
      }
      case ChartTypeOptions.Emission: {
        const hotkeyChartData = getChartData(
          neuronHistoryQuery,
          dataOption
        ).map((cd) =>
          cd.map((item) => {
            return {
              ...item,
              leftValue: Number(item.leftValue) / RAO_PER_TAO,
            };
          })
        );

        return { hotkeyChartData, supplementaryChartData: [] };
      }
      case ChartTypeOptions.Incentive: {
        const hotkeyChartData = getChartData(neuronHistoryQuery, dataOption);
        const supplementaryChartData = getImmunesupplementaryChartData(
          aggregatedNeuronHistoryQuery
        );
        return {
          hotkeyChartData,
          supplementaryChartData,
        };
      }
      case ChartTypeOptions.PruningScore: {
        const hotkeyChartData = getChartData(neuronHistoryQuery, dataOption);
        const supplementaryChartData = getPruningsupplementaryChartData(
          aggregatedNeuronHistoryQuery
        );
        return {
          hotkeyChartData,
          supplementaryChartData,
        };
      }
      default: {
        const hotkeyChartData = getChartData(neuronHistoryQuery, dataOption);
        return { hotkeyChartData, supplementaryChartData: [] };
      }
    }
  }, [aggregatedNeuronHistoryQuery, dataOption, neuronHistoryQuery]);

  const isLoading = neuronHistoryQuery.some((q) => q.isLoading);

  const isError = neuronHistoryQuery.some((q) => q.isError);

  const isSuccess = !neuronHistoryQuery.some((q) => !q.isSuccess);

  const { chartConfig, defaultMargin, showTicks } = useChartConfig(data);

  return {
    data,
    isLoading,
    isError,
    isSuccess,
    chartConfig,
    defaultMargin,
    showTicks,
  };
};

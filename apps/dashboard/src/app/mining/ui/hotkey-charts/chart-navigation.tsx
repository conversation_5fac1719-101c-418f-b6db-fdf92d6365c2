'use client';

import { memo, useState } from 'react';
import { LuPanelLeftOpen } from 'react-icons/lu';
import { Button, Link, Text } from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import type { NavigationLink } from '@/app/mining/ui/hotkey-charts/chart-navigation-links';

export const ChartNavigation = memo(function MiningPageContent({
  navigationLinks,
}: {
  navigationLinks: NavigationLink[];
}) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className='sticky top-28 z-[100] flex flex-row justify-between rounded-2xl border border-[#1F1F1F] bg-[#0D0D0D] p-2 sm:mb-auto sm:flex-col sm:gap-2'>
      <div className='ml-auto hidden flex-row sm:flex'>
        <Button
          variant='secondary'
          className='h-10 rounded-md border border-[#323232]  bg-[#1D1D1D] p-1 hover:bg-[#1D1D1D]/20 sm:p-2'
          onClick={() => {
            setIsOpen(!isOpen);
          }}
        >
          <LuPanelLeftOpen
            size={24}
            className={cn('opacity-60', isOpen && 'rotate-180')}
          />
        </Button>
      </div>
      {navigationLinks.map((link) => (
        <div key={link.href}>
          <Link href={link.href}>
            <Button
              variant='secondary'
              className='flex h-10 w-full items-center gap-2 rounded p-1 hover:bg-[#1D1D1D]/20 sm:rounded-l-none sm:p-2'
            >
              {link.icon}
              {isOpen ? (
                <Text level='sm' className='hidden min-w-[7.5rem] sm:flex'>
                  {link.label}
                </Text>
              ) : null}
            </Button>
          </Link>
        </div>
      ))}
    </div>
  );
});

'use client';

import { memo } from 'react';
import { miningDateRanges } from '@/app/mining/lib/constants';
import { useMiningContext } from '@/app/mining/lib/context';

export const DateRangeSelector = memo(function DateRangeSelector() {
  const { dateRangeSelected, setDateRangeSelected } = useMiningContext();
  return (
    <div className='flex items-center gap-4'>
      {miningDateRanges.map((range) => (
        <button
          type='button'
          key={range.value}
          className='data-[active=true]:border-accent-1 data-[active=true]:bg-accent-1/10 h-7 w-10 rounded-lg border border-[#323232] bg-[#1D1D1D] text-sm hover:bg-emerald-500/20 hover:text-white data-[active=true]:text-white'
          data-active={range.value === dateRangeSelected}
          onClick={() => {
            setDateRangeSelected(range.value);
          }}
        >
          {range.label}
        </button>
      ))}
    </div>
  );
});

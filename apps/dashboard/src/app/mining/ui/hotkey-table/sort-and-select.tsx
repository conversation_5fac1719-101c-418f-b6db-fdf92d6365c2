'use client';

import { memo, useState } from 'react';
import { BiChevronDown } from 'react-icons/bi';
import {
  Button,
  CopyButton,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Text,
} from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import type { SortAndSelectOptionValues } from '@/app/mining/lib/types';
import { truncateString } from '@/lib/utils';

const sortAndSelectOptions: {
  label: string;
  value: SortAndSelectOptionValues;
}[] = [
  {
    label: '5 newest non-immune miners',
    value: 'NewestNonImmune',
  },
  {
    label: '5 oldest immune miners',
    value: 'OldestImmune',
  },
  {
    label: '5 recently de-registered miners',
    value: 'RecentlyDeregistered',
  },
  {
    label: '5 bottom miners',
    value: 'Bottom',
  },
  {
    label: '5 top miners',
    value: 'Top',
  },
  {
    label: '5 newest miners',
    value: 'Newest',
  },
  {
    label: '5 oldest miners',
    value: 'Oldest',
  },
  {
    label: 'all danger zone miners',
    value: 'DangerZone',
  },
];

type SortAndSelectProps = {
  selectedOption: SortAndSelectOptionValues;
  onChange: (value: SortAndSelectOptionValues) => void;
};

export const SortAndSelect = memo(function SortAndSelect({
  selectedOption,
  onChange,
}: SortAndSelectProps) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const handleOptionClick = (option: SortAndSelectOptionValues) => {
    setIsDropdownOpen(false);
    onChange(option);
  };

  return (
    <Popover open={isDropdownOpen}>
      <>
        <PopoverTrigger asChild disabled>
          <div
            className={cn(
              'bg-input-primary w-full rounded-full py-5 pl-6 pr-4 ',
              selectedOption === '' ? undefined : 'border-ocean border'
            )}
          >
            <button
              type='button'
              onClick={() => {
                setIsDropdownOpen(!isDropdownOpen);
              }}
              className='flex w-full flex-row justify-between'
            >
              <Text level='labelSmall'>
                {selectedOption === ''
                  ? 'Sort & Select Hotkeys'
                  : sortAndSelectOptions.find(
                      (opt) => opt.value === selectedOption
                    )?.label}
              </Text>
              <BiChevronDown
                aria-hidden='true'
                className={cn(
                  'text-grayish pointer-events-none col-start-1 row-start-1 mr-3 size-5 self-center justify-self-end transition-transform sm:size-4',
                  isDropdownOpen && 'rotate-180'
                )}
              />
            </button>
          </div>
        </PopoverTrigger>
        <PopoverContent
          align='start'
          className={cn(
            'z-10 mt-1 w-[var(--radix-popover-trigger-width)] rounded-2xl border border-[#323232] bg-[#1D1D1DCC] p-6 shadow-lg backdrop-blur-sm'
          )}
        >
          <div className='overflow-auto py-1'>
            <Text level='labelLarge' className='px-2 py-1 opacity-60'>
              Sort & Select
            </Text>
            {sortAndSelectOptions.map((opt) => (
              <div
                key={opt.value}
                className={cn(
                  'hover:border-accent-1 flex flex-row items-center justify-between gap-4 rounded-lg border border-transparent bg-transparent px-2 py-1 transition-[border-color] duration-300',
                  opt.value === selectedOption && 'border-accent-1'
                )}
              >
                <Button
                  className={cn(
                    'border-1 flex-1 justify-start overflow-hidden truncate border-transparent bg-transparent px-1 shadow-none hover:bg-transparent'
                  )}
                  onClick={() => {
                    handleOptionClick(opt.value);
                  }}
                >
                  <Text
                    level='buttonSmall'
                    className={
                      opt.value === selectedOption ? undefined : 'opacity-60'
                    }
                  >
                    {opt.label}
                  </Text>
                </Button>
              </div>
            ))}
          </div>
        </PopoverContent>
      </>
    </Popover>
  );
});

type ColdkeyTextProps = {
  address: string;
  truncateAddress?: boolean;
  allowCopy?: boolean;
};

export const ColdkeyText = ({
  address,
  truncateAddress = false,
  allowCopy = false,
}: ColdkeyTextProps) => {
  return (
    <span className='flex flex-row items-center gap-1 truncate'>
      <span className='font-mono'>
        {truncateAddress ? truncateString(address, 6) : address}
      </span>
      {allowCopy ? (
        <CopyButton size={14} value={address} className='text-white' />
      ) : null}
    </span>
  );
};

import { useEffect, useMemo, useState } from 'react';
import type {
  ColumnFiltersState,
  OnChangeFn,
  Row,
  SortingState,
} from '@tanstack/react-table';
import {
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import type { HotkeyItem } from '@repo/types/website-api-types';
import {
  CurrencySelector,
  DataTable,
  SubnetNameDisplay,
  Text,
} from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { SearchInput } from './search-input';
import { SortAndSelect } from './sort-and-select';
import type { HotkeyStatus, HotkeyTableColumns } from './utils';
import {
  keyCustomFilterFn,
  rankCustomSortingFn,
  statusCustomFilterFn,
  statusCustomSortingFn,
  useColumns,
} from './utils';
import { useMiningContext } from '@/app/mining/lib/context';
import { DateRangeSelector } from '@/app/mining/ui/date-range-selector';
import { RAO_PER_TAO } from '@/contexts/chain-and-wallet/constants';
import { useSubnetLookup } from '@/lib/hooks/global-hooks';
import { useTaoToDollarConverter } from '@/lib/hooks/price-hooks';
import useDebounce from '@/lib/hooks/use-debounce';

export const HotkeyTable = () => {
  const columns = useColumns();
  const {
    hotkeyData,
    blockData,
    selectedColdkeys,
    selectedHotkeys,
    setSelectedHotkeys,
    walletsData,
    currencySelected,
    setCurrencySelected,
    hotkeyTableSortAndSelect,
    setHotkeyTableSortAndSelect,
    hotkeyTableColumnFilters,
    setHotkeyTableColumnFilters,
    hotkeyTableSorting,
    setHotkeyTableSorting,
  } = useMiningContext();
  const { getDollarValue } = useTaoToDollarConverter();
  const { getSubnetSymbol, getSubnetName } = useSubnetLookup();
  const subnetSymbol = useMemo(
    () =>
      selectedColdkeys[0] ? getSubnetSymbol(selectedColdkeys[0].netuid) : '',
    [getSubnetSymbol, selectedColdkeys]
  );
  const subnetName = useMemo(
    () =>
      selectedColdkeys[0] ? getSubnetName(selectedColdkeys[0].netuid) : '',
    [getSubnetName, selectedColdkeys]
  );

  const tableData = useMemo(() => {
    const status: (hotkeyItem: HotkeyItem) => HotkeyStatus = (hotkeyItem) =>
      hotkeyItem.deregistered
        ? 'Deregistered'
        : hotkeyItem.in_danger
          ? 'InDanger'
          : hotkeyItem.immune
            ? 'Immune'
            : 'Active';

    const getBlockTimestamp = (blockNumber: number) => {
      if (blockNumber === 0) {
        return '';
      }

      const block = blockData.find((bd) => bd.block_number === blockNumber);

      return block?.timestamp ?? '';
    };

    const getValueForSelectedCurrency = (
      taoValue: string,
      alphaValue: string
    ) => {
      if (currencySelected === 'ALPHA') {
        return Number(alphaValue) / RAO_PER_TAO;
      }

      const normalisedTaoValue = Number(taoValue) / RAO_PER_TAO;

      if (currencySelected === 'USD') {
        return getDollarValue(normalisedTaoValue);
      }

      return normalisedTaoValue;
    };

    const result: HotkeyTableColumns[] = hotkeyData.map((hk) => {
      return {
        status: status(hk),
        uid: hk.uid,
        rank: hk.miner_rank ?? 0,
        alphaPer: getValueForSelectedCurrency(
          hk.total_emission_as_tao,
          hk.total_emission
        ),
        balance: getValueForSelectedCurrency(
          hk.alpha_balance_as_tao,
          hk.alpha_balance
        ),
        trust: Number(hk.trust),
        consensus: Number(hk.consensus),
        incentive: Number(hk.incentive),
        emission: Number(hk.emission) / RAO_PER_TAO,
        axon: hk.axon,
        age: getBlockTimestamp(hk.registration_block),
        hotkey: hk.hotkey.ss58,
        coldkey: hk.coldkey.ss58,
        coldkeyName: walletsData?.wallets.find(
          (w) => w.address === hk.coldkey.ss58
        )?.name,
        deregTimestamp: hk.deregistration_timestamp,
        netuid: hk.netuid,
        subnetSymbol,
      };
    });

    return result;
  }, [
    blockData,
    currencySelected,
    getDollarValue,
    subnetSymbol,
    hotkeyData,
    walletsData?.wallets,
  ]);

  const [hotkeyTableKeyFilter, setHotkeyTableKeyFilter] = useState<string>(
    (hotkeyTableColumnFilters.find((f) => f.id === 'hotkey-col')?.value ??
      '') as string
  );
  const hotkeyTableFilterDebounced = useDebounce(hotkeyTableKeyFilter, 300);

  useEffect(() => {
    const keycol = (hotkeyTableColumnFilters.find((f) => f.id === 'hotkey-col')
      ?.value ?? '') as string;

    if (keycol !== hotkeyTableKeyFilter) {
      setHotkeyTableKeyFilter(keycol);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps -- only trigger on columnFilter change otherwise would cause loop
  }, [hotkeyTableColumnFilters]);

  const onSortingChange: OnChangeFn<SortingState> = (updaterOrValue) => {
    const val = updaterOrValue as SortingState;
    setHotkeyTableSorting(val);
  };

  const onColumnFiltersChange: OnChangeFn<ColumnFiltersState> = (
    updaterOrValue
  ) => {
    const val = updaterOrValue as ColumnFiltersState;
    setHotkeyTableColumnFilters(val);
  };

  const table = useReactTable({
    data: tableData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onSortingChange,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange,
    filterFns: {
      keyCustomFilterFn,
      statusCustomFilterFn,
    },
    sortingFns: {
      statusCustomSortingFn,
      rankCustomSortingFn,
    },
    state: {
      columnFilters: hotkeyTableColumnFilters,
      sorting: hotkeyTableSorting,
    },
    //debugTable: true,
  });

  useEffect(() => {
    switch (hotkeyTableSortAndSelect) {
      case 'NewestNonImmune': {
        setHotkeyTableColumnFilters([
          { id: 'status-col', value: ['Active', 'InDanger'] },
        ]);
        setHotkeyTableSorting([{ id: 'age-col', desc: true }]);
        const preselectedHotkeys = tableData
          .filter((row) => ['Active', 'InDanger'].includes(row.status))
          .sort((a, b) => b.age.localeCompare(a.age))
          .slice(0, 5)
          .map((row) => ({
            coldkey: row.coldkey,
            hotkey: row.hotkey,
            uid: row.uid,
            netuid: row.netuid,
          }));
        setSelectedHotkeys(preselectedHotkeys);
        break;
      }
      case 'OldestImmune': {
        setHotkeyTableColumnFilters([{ id: 'status-col', value: ['Immune'] }]);
        setHotkeyTableSorting([{ id: 'age-col', desc: false }]);
        const preselectedHotkeys = tableData
          .filter((row) => row.status === 'Immune')
          .sort((a, b) => a.age.localeCompare(b.age))
          .slice(0, 5)
          .map((row) => ({
            coldkey: row.coldkey,
            hotkey: row.hotkey,
            uid: row.uid,
            netuid: row.netuid,
          }));
        setSelectedHotkeys(preselectedHotkeys);
        break;
      }
      case 'RecentlyDeregistered': {
        setHotkeyTableColumnFilters([
          { id: 'status-col', value: ['Deregistered'] },
        ]);
        setHotkeyTableSorting([{ id: 'status-col', desc: true }]);
        const preselectedHotkeys = tableData
          .filter((row) => row.status === 'Deregistered')
          .sort((a, b) => b.deregTimestamp.localeCompare(a.deregTimestamp))
          .slice(0, 5)
          .map((row) => ({
            coldkey: row.coldkey,
            hotkey: row.hotkey,
            uid: row.uid,
            netuid: row.netuid,
          }));
        setSelectedHotkeys(preselectedHotkeys);
        break;
      }
      case 'Bottom': {
        setHotkeyTableColumnFilters([]);
        setHotkeyTableSorting([{ id: 'alpha-per-col', desc: false }]);
        const preselectedHotkeys = tableData
          .sort((a, b) => a.alphaPer - b.alphaPer)
          .slice(0, 5)
          .map((row) => ({
            coldkey: row.coldkey,
            hotkey: row.hotkey,
            uid: row.uid,
            netuid: row.netuid,
          }));
        setSelectedHotkeys(preselectedHotkeys);
        break;
      }
      case 'Top': {
        setHotkeyTableColumnFilters([]);
        setHotkeyTableSorting([{ id: 'alpha-per-col', desc: true }]);
        const preselectedHotkeys = tableData
          .sort((a, b) => b.alphaPer - a.alphaPer)
          .slice(0, 5)
          .map((row) => ({
            coldkey: row.coldkey,
            hotkey: row.hotkey,
            uid: row.uid,
            netuid: row.netuid,
          }));
        setSelectedHotkeys(preselectedHotkeys);
        break;
      }
      case 'Newest': {
        setHotkeyTableColumnFilters([]);
        setHotkeyTableSorting([{ id: 'age-col', desc: true }]);
        const preselectedHotkeys = tableData
          .sort((a, b) => b.age.localeCompare(a.age))
          .slice(0, 5)
          .map((row) => ({
            coldkey: row.coldkey,
            hotkey: row.hotkey,
            uid: row.uid,
            netuid: row.netuid,
          }));
        setSelectedHotkeys(preselectedHotkeys);
        break;
      }
      case 'Oldest': {
        setHotkeyTableColumnFilters([]);
        setHotkeyTableSorting([{ id: 'age-col', desc: false }]);
        const preselectedHotkeys = tableData
          .sort((a, b) => a.age.localeCompare(b.age))
          .slice(0, 5)
          .map((row) => ({
            coldkey: row.coldkey,
            hotkey: row.hotkey,
            uid: row.uid,
            netuid: row.netuid,
          }));
        setSelectedHotkeys(preselectedHotkeys);
        break;
      }
      case 'DangerZone': {
        setHotkeyTableColumnFilters([
          { id: 'status-col', value: ['InDanger'] },
        ]);
        setHotkeyTableSorting([]);
        const preselectedHotkeys = tableData
          .filter((row) => row.status === 'InDanger')
          .map((row) => ({
            coldkey: row.coldkey,
            hotkey: row.hotkey,
            uid: row.uid,
            netuid: row.netuid,
          }));
        setSelectedHotkeys(preselectedHotkeys);
        break;
      }
      case '': {
        break;
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps -- only trigger on sortAndSelectFilter change
  }, [hotkeyTableSortAndSelect]);

  useEffect(() => {
    setHotkeyTableKeyFilter(hotkeyTableFilterDebounced);
    const filters = hotkeyTableColumnFilters.filter(
      (f) => f.id !== 'hotkey-col'
    );
    setHotkeyTableColumnFilters([
      ...filters,
      { id: 'hotkey-col', value: hotkeyTableKeyFilter },
    ]);
    // eslint-disable-next-line react-hooks/exhaustive-deps -- trigger on columnFilter change would cause loop
  }, [hotkeyTableFilterDebounced]);

  const getRowClassName = (row: Row<HotkeyTableColumns>) => {
    const rowStyles = ['text-white'];

    if (
      selectedHotkeys.find(
        (ck) =>
          ck.coldkey === row.original.coldkey &&
          ck.hotkey === row.original.hotkey &&
          ck.uid === row.original.uid &&
          ck.netuid === row.original.netuid
      ) !== undefined
    ) {
      rowStyles.push(
        '!bg-[#00DBBC33] first:[&_td]:border-l-4 first:[&_td]:border-l-[#00DBBC] first:[&_td]:pl-1 first:[&_td]:sm:pl-2'
      );
    } else {
      rowStyles.push('first:[&_td]:pl-2 first:[&_td]:sm:pl-3');
      if (row.original.status === 'InDanger') {
        rowStyles.push('bg-[#FF000011]');
      }
    }

    return cn(rowStyles);
  };

  return (
    <>
      <div className='mt-8 flex flex-col gap-8 2xl:flex-row 2xl:items-center 2xl:justify-between 2xl:gap-16'>
        <div className='flex flex-row items-center justify-between gap-8'>
          <div className='flex flex-row items-center gap-8'>
            <Text as='h2' level='headerMedium' className='text-white'>
              Hotkey Table
            </Text>
            {selectedColdkeys.length === 0 ? null : (
              <SubnetNameDisplay
                subnetName={subnetName}
                netuid={selectedColdkeys[0].netuid}
                isClickable
                inline
              />
            )}
          </div>
          <div className='hidden lg:max-2xl:flex'>
            <DateRangeSelector />
          </div>
        </div>

        <div className='flex flex-1 flex-col items-end gap-8 lg:flex-row-reverse lg:items-center'>
          <CurrencySelector
            options={['ALPHA', 'TAO', 'USD']}
            selection={currencySelected}
            onSelectionChange={setCurrencySelected}
          />
          <SortAndSelect
            selectedOption={hotkeyTableSortAndSelect}
            onChange={(value) => {
              setHotkeyTableSortAndSelect(value);
            }}
          />
          <SearchInput
            onChange={(value) => {
              setHotkeyTableKeyFilter(value);
            }}
            keyInput={hotkeyTableKeyFilter}
          />
          <div className='lg:max-2xl:hidden'>
            <DateRangeSelector />
          </div>
        </div>
      </div>
      <DataTable
        table={table}
        hasBorder={false}
        rowClassName={getRowClassName}
      />
    </>
  );
};

import { useMemo, useState } from 'react';
import { BiSearch } from 'react-icons/bi';
import { useWindowSize } from 'usehooks-ts';
import { Input } from '@repo/ui/components';
import { truncateString } from '@/lib/utils';

type HotkeyTableFilterProps = {
  onChange: (value: string) => void;
  keyInput: string;
};

export const SearchInput = ({ keyInput, onChange }: HotkeyTableFilterProps) => {
  const [isBeingEdited, setIsBeingEdited] = useState(false);

  const windowSize = useWindowSize();

  const isSmallScreen = useMemo(
    () => windowSize.width < 640,
    [windowSize.width]
  );
  return (
    <Input
      IconLeft={BiSearch}
      placeholder='Filter Hotkey/Coldkey'
      inputId='hotkey-table-key-filter'
      onChange={(e) => {
        onChange(e.target.value);
      }}
      value={
        isSmallScreen
          ? isBeingEdited
            ? keyInput
            : truncateString(keyInput, 6)
          : keyInput
      }
      inputClassName='rounded-full py-4 pr-4'
      className='w-full'
      onFocus={() => {
        if (isSmallScreen) {
          setIsBeingEdited(true);
        }
      }}
      onBlur={() => {
        if (isSmallScreen) {
          setIsBeingEdited(false);
        }
      }}
    />
  );
};

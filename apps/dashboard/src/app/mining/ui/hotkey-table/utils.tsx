import { useCallback, useMemo } from 'react';
import type {
  FilterFn,
  Row,
  ColumnDef,
  SortingFn,
} from '@tanstack/react-table';
import { Pickaxe } from 'lucide-react';
import { BiXCircle } from 'react-icons/bi';
import {
  BigSmallValueDisplay,
  Checkbox,
  StaticTableDateFormatter,
  TaoOrAlphaValueDisplay,
  Text,
} from '@repo/ui/components';
import { useMiningContext } from '@/app/mining/lib/context';
import { KeyText } from '@/app/mining/ui/shared/key-text';
import { TAO_CURRENCY } from '@/lib/utils';

export type HotkeyStatus = 'Deregistered' | 'InDanger' | 'Immune' | 'Active';

export type HotkeyTableColumns = {
  status: HotkeyStatus;
  uid: number;
  rank: number;
  alphaPer: number;
  balance: number;
  trust: number;
  consensus: number;
  incentive: number;
  emission: number;
  axon: string;
  age: string;
  hotkey: string;
  coldkey: string;
  coldkeyName?: string;
  deregTimestamp: string;
  netuid: number;
  subnetSymbol: string;
};

export const keyCustomFilterFn: FilterFn<HotkeyTableColumns> = (
  row: Row<HotkeyTableColumns>,
  _columnId: string,
  filterValue: string
) => {
  const normalisedFilterValue = filterValue.trim().toLocaleLowerCase();

  return (
    row.original.hotkey.toLocaleLowerCase().includes(normalisedFilterValue) ||
    row.original.coldkey.toLocaleLowerCase().includes(normalisedFilterValue) ||
    (row.original.coldkeyName !== undefined &&
      row.original.coldkeyName
        .toLocaleLowerCase()
        .includes(normalisedFilterValue))
  );
};

export const statusCustomFilterFn: FilterFn<HotkeyTableColumns> = (
  row: Row<HotkeyTableColumns>,
  _columnId: string,
  filterValue: HotkeyStatus[]
) => filterValue.length === 0 || filterValue.includes(row.original.status);

export const rankCustomSortingFn: SortingFn<HotkeyTableColumns> = (
  rowA: Row<HotkeyTableColumns>,
  rowB: Row<HotkeyTableColumns>,
  _columnId: string
) => {
  if (rowA.original.rank === rowB.original.rank) {
    return 0;
  }

  // Zero (n/a) is the worst rank, so it should be at the end
  if (rowA.original.rank === 0) {
    return -1;
  }

  if (rowB.original.rank === 0) {
    return 1;
  }

  // Sorting remainder from best to worst (lower number is better)
  return rowA.original.rank < rowB.original.rank ? 1 : -1;
};

export const statusCustomSortingFn: SortingFn<HotkeyTableColumns> = (
  rowA: Row<HotkeyTableColumns>,
  rowB: Row<HotkeyTableColumns>,
  _columnId: string
) => {
  const statuses: HotkeyStatus[] = [
    'Active',
    'InDanger',
    'Immune',
    'Deregistered',
  ];

  // First sort by status
  const statusResult =
    statuses.indexOf(rowA.original.status) -
    statuses.indexOf(rowB.original.status);

  // If they do not match, return the result
  if (statusResult !== 0) {
    return statusResult;
  }

  // If they match and are deregistered, sort by deregistration timestamp
  if (rowA.original.status === 'Deregistered') {
    return rowA.original.deregTimestamp === rowB.original.deregTimestamp
      ? 0
      : rowA.original.deregTimestamp < rowB.original.deregTimestamp
        ? -1
        : 1;
  }

  // Otherwise, sort by age
  return rowA.original.age === rowB.original.age
    ? 0
    : rowA.original.age < rowB.original.age
      ? -1
      : 1;
};

export const useColumns = () => {
  const {
    dateRangeSelected,
    selectedHotkeys,
    setSelectedHotkeys,
    currencySelected,
  } = useMiningContext();

  const setCheckedChanged = useCallback(
    (
      isSelected: boolean,
      coldkey: string,
      hotkey: string,
      uid: number,
      netuid: number
    ) => {
      if (isSelected) {
        setSelectedHotkeys(
          selectedHotkeys.filter(
            (ck) =>
              ck.coldkey !== coldkey || ck.hotkey !== hotkey || ck.uid !== uid
          )
        );
      } else {
        setSelectedHotkeys([
          ...selectedHotkeys,
          { hotkey, coldkey, uid, netuid },
        ]);
      }
    },
    [selectedHotkeys, setSelectedHotkeys]
  );

  const getCurrencySymbol = useCallback(
    (subnetSymbol?: string) => {
      if (currencySelected === 'USD') {
        return '$';
      }

      if (currencySelected === 'ALPHA' && subnetSymbol) {
        return subnetSymbol;
      }

      return TAO_CURRENCY;
    },
    [currencySelected]
  );

  const columns: ColumnDef<HotkeyTableColumns>[] = useMemo(
    () => [
      {
        id: 'select-col',
        accessorKey: 'select',
        header: ({ table }) => {
          const rowModel = table.getRowModel();
          const status =
            selectedHotkeys.length === 0
              ? 'unchecked'
              : selectedHotkeys.length === rowModel.rows.length
                ? 'checked'
                : 'indeterminate';
          return (
            <div className='flex flex-row items-center gap-1 pl-1'>
              <Checkbox
                checked={status !== 'unchecked'}
                indeterminate={status === 'indeterminate'}
                onCheckedChange={() => {
                  if (status === 'unchecked' || status === 'indeterminate') {
                    setSelectedHotkeys(
                      rowModel.rows.map((row) => ({
                        hotkey: row.original.hotkey,
                        coldkey: row.original.coldkey,
                        uid: row.original.uid,
                        netuid: row.original.netuid,
                      }))
                    );
                  } else {
                    setSelectedHotkeys([]);
                  }
                }}
                className={
                  status === 'unchecked'
                    ? undefined
                    : 'border-none !bg-[#00DBBC]'
                }
              />
              Select
            </div>
          );
        },
        cell: ({ row }) => {
          const d = row.original;

          const isSelected =
            selectedHotkeys.find(
              (ck) =>
                ck.coldkey === d.coldkey &&
                ck.hotkey === d.hotkey &&
                ck.uid === d.uid &&
                ck.netuid === d.netuid
            ) !== undefined;

          return (
            <Checkbox
              checked={isSelected}
              onCheckedChange={() => {
                setCheckedChanged(
                  isSelected,
                  d.coldkey,
                  d.hotkey,
                  d.uid,
                  d.netuid
                );
              }}
              className={isSelected ? 'border-none !bg-[#00DBBC]' : undefined}
            />
          );
        },
        enableSorting: false,
      },
      {
        id: 'status-col',
        accessorKey: 'status',
        filterFn: statusCustomFilterFn,
        sortingFn: statusCustomSortingFn,
        header: 'Type',
        meta: {
          align: 'end',
        },
        cell: ({ row }) => {
          const d = row.original;
          const icon = () => {
            switch (d.status) {
              case 'Deregistered':
                return (
                  <span title='Deregistered'>
                    <BiXCircle color='#EB5347' size={16} />
                  </span>
                );
              case 'InDanger':
                return (
                  <span title='In Danger'>
                    <Pickaxe color='#EBC247' size={16} />
                  </span>
                );
              case 'Immune':
                return (
                  <span title='Immune'>
                    <Pickaxe color='#FF8B25' size={16} />
                  </span>
                );
              default:
                return (
                  <span title='Active'>
                    <Pickaxe color='#00DBBC' size={16} />
                  </span>
                );
            }
          };

          return <div className='flex justify-end pr-4'>{icon()}</div>;
        },
        enableSorting: true,
      },
      {
        id: 'uid-col',
        accessorKey: 'uid',
        header: 'UID',
        meta: {
          align: 'end',
        },
        cell: ({ row }) => {
          const d = row.original;
          return (
            <Text
              level='labelLarge'
              className='text-ocean flex justify-end pr-4'
            >
              {d.uid}
            </Text>
          );
        },
        enableSorting: true,
      },
      {
        id: 'rank-col',
        accessorKey: 'rank',
        sortingFn: rankCustomSortingFn,
        header: 'Rank',
        meta: {
          align: 'end',
        },
        cell: ({ row }) => {
          const d = row.original;
          return (
            <Text level='labelLarge' className='flex justify-end pr-4'>
              {d.rank > 0 ? d.rank : ''}
            </Text>
          );
        },
        enableSorting: true,
      },
      {
        id: 'alpha-per-col',
        accessorKey: 'alphaPer',
        header: () => `Alpha per ${dateRangeSelected.toLocaleUpperCase()}`,
        meta: {
          align: 'end',
        },
        cell: ({ row }) => {
          const d = row.original;
          return (
            <div className='flex justify-end gap-2 pr-4'>
              <TaoOrAlphaValueDisplay
                symbol={getCurrencySymbol(d.subnetSymbol)}
                value={d.alphaPer}
                valueFormattingOptions={{
                  minimumFractionDigits: currencySelected === 'USD' ? 2 : 3,
                  maximumFractionDigits: currencySelected === 'USD' ? 2 : 3,
                }}
                valueTextLevel='labelLarge'
                iconClassName='text-xs'
              />
            </div>
          );
        },
        enableSorting: true,
      },
      {
        id: 'balance-col',
        accessorKey: 'balance',
        header: 'Balance',
        meta: {
          align: 'end',
        },
        cell: ({ row }) => {
          const d = row.original;
          return (
            <div className='flex justify-end gap-2 pr-4'>
              <TaoOrAlphaValueDisplay
                symbol={getCurrencySymbol(d.subnetSymbol)}
                value={d.balance}
                valueFormattingOptions={{
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }}
                valueTextLevel='labelLarge'
                iconClassName='text-xs'
              />
            </div>
          );
        },
        enableSorting: true,
      },
      {
        id: 'trust-col',
        accessorKey: 'trust',
        header: 'Trust',
        meta: {
          align: 'end',
        },
        cell: ({ row }) => {
          const d = row.original;
          return (
            <div className='flex justify-end gap-2 pr-4'>
              <BigSmallValueDisplay
                textLevel='labelLarge'
                value={d.trust}
                valueFormattingOptions={{
                  minimumFractionDigits: 5,
                  maximumFractionDigits: 5,
                }}
                areDecimalsSmall={false}
              />
            </div>
          );
        },
        enableSorting: true,
      },
      {
        id: 'consensus-col',
        accessorKey: 'consensus',
        header: 'Consensus',
        meta: {
          align: 'end',
        },
        cell: ({ row }) => {
          const d = row.original;
          return (
            <div className='flex justify-end gap-2 pr-4'>
              <BigSmallValueDisplay
                textLevel='labelLarge'
                value={d.consensus}
                valueFormattingOptions={{
                  minimumFractionDigits: 5,
                  maximumFractionDigits: 5,
                }}
                areDecimalsSmall={false}
              />
            </div>
          );
        },
        enableSorting: true,
      },
      {
        id: 'incentive-col',
        accessorKey: 'incentive',
        header: 'Incentive',
        meta: {
          align: 'end',
        },
        cell: ({ row }) => {
          const d = row.original;
          return (
            <div className='flex justify-end gap-2 pr-4'>
              <BigSmallValueDisplay
                textLevel='labelLarge'
                value={d.incentive}
                valueFormattingOptions={{
                  minimumFractionDigits: 5,
                  maximumFractionDigits: 5,
                }}
                areDecimalsSmall={false}
              />
            </div>
          );
        },
        enableSorting: true,
      },
      {
        id: 'emission-col',
        accessorKey: 'emission',
        header: 'Emission',
        meta: {
          align: 'end',
        },
        cell: ({ row }) => {
          const d = row.original;
          return (
            <div className='flex justify-end gap-2 pr-4'>
              <BigSmallValueDisplay
                textLevel='labelLarge'
                value={d.emission}
                valueFormattingOptions={{
                  minimumFractionDigits: 5,
                  maximumFractionDigits: 5,
                }}
                areDecimalsSmall={false}
              />
            </div>
          );
        },
        enableSorting: true,
      },
      {
        id: 'axon-col',
        accessorKey: 'axon',
        header: 'Axon',
        meta: {
          align: 'end',
        },
        cell: ({ row }) => {
          const d = row.original;
          return (
            <Text level='labelLarge' className='flex justify-end pr-4'>
              {d.axon}
            </Text>
          );
        },
        enableSorting: true,
      },
      {
        id: 'age-col',
        accessorKey: 'age',
        header: 'Age',
        meta: {
          align: 'end',
        },
        cell: ({ row }) => {
          const d = row.original;
          return (
            <div className='flex justify-end gap-2 pr-4'>
              <StaticTableDateFormatter
                timestamp={d.age}
                className='text-inerit !leading-18 !text-[15px] font-normal opacity-100'
                timeClassName='font-normal !text-[15px] !leading-18 opacity-100'
              />
            </div>
          );
        },
        enableSorting: true,
      },
      {
        id: 'hotkey-col',
        accessorKey: 'hotkey',
        filterFn: keyCustomFilterFn,
        header: 'Hotkey',
        meta: {
          align: 'end',
        },
        cell: ({ row }) => {
          const d = row.original;
          return <KeyText hotOrColdKey={d.hotkey} className='justify-end' />;
        },
        enableSorting: false,
      },
      {
        id: 'coldkey-col',
        accessorKey: 'coldkey',
        header: 'Coldkey',
        meta: {
          align: 'end',
        },
        cell: ({ row }) => {
          const d = row.original;
          return (
            <KeyText
              hotOrColdKey={d.coldkey}
              hotOrColdKeyName={d.coldkeyName}
              className='justify-end'
            />
          );
        },
        enableSorting: false,
      },
    ],
    [
      currencySelected,
      dateRangeSelected,
      getCurrencySymbol,
      selectedHotkeys,
      setCheckedChanged,
      setSelectedHotkeys,
    ]
  );

  return columns;
};

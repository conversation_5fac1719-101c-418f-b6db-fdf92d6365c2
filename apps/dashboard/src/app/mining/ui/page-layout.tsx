import type { ReactNode } from 'react';
import { ChartNavigation } from './hotkey-charts/chart-navigation';
import { PageHeader } from './page-header';
import { sectionNavigationLinks } from '@/app/mining/ui/hotkey-charts/chart-navigation-links';

export const PageLayout = ({
  children,
  title,
  headerRight,
  showNavigation,
  snap = 'lg',
}: {
  children: ReactNode;
  title: string;
  headerRight?: ReactNode;
  showNavigation?: boolean;
  snap?: 'sm' | 'lg';
}) => {
  return (
    <div className='flex flex-col gap-8 p-4 sm:flex-row'>
      {showNavigation ? (
        <ChartNavigation navigationLinks={sectionNavigationLinks} />
      ) : null}

      <div className='flex min-w-0 grow flex-col gap-8'>
        <div
          className={`flex w-full flex-col justify-between gap-8 ${snap}:flex-row`}
        >
          <PageHeader title={title} />
          {headerRight ? headerRight : null}
        </div>
        <div className='flex grow flex-col gap-8'>{children}</div>
      </div>
    </div>
  );
};

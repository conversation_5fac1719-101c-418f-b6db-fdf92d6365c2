import { memo } from 'react';
import { MiningBalanceCard } from './mining-balance-card';
import { useMiningContext } from '@/app/mining/lib/context';
import { RAO_PER_TAO } from '@/contexts/chain-and-wallet/constants';
import { useTaoToDollarConverter } from '@/lib/hooks/price-hooks';

export const TotalBalanceCard = memo(function TotalBalanceCard() {
  const { accountData, accountLatestQuery } = useMiningContext();
  const { getDollarValue } = useTaoToDollarConverter();

  const totalBalance = Number(accountData?.balance_total ?? 0) / RAO_PER_TAO;

  return (
    <MiningBalanceCard
      isLoading={accountLatestQuery.isFetching}
      title='Total Balance'
      value={totalBalance}
      valueSub={[{ symbol: '$', value: getDollarValue(totalBalance) }]}
    />
  );
});

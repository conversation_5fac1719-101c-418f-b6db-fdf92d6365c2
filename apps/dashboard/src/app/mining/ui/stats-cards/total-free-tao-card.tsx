import { memo } from 'react';
import { MiningBalanceCard } from './mining-balance-card';
import { useMiningContext } from '@/app/mining/lib/context';
import { RAO_PER_TAO } from '@/contexts/chain-and-wallet/constants';
import { useTaoToDollarConverter } from '@/lib/hooks/price-hooks';

export const TotalFreeTaoCard = memo(function TotalFreeTaoCard() {
  const { accountData, accountLatestQuery } = useMiningContext();
  const { getDollarValue } = useTaoToDollarConverter();

  const freeTao = Number(accountData?.balance_free ?? 0) / RAO_PER_TAO;

  return (
    <MiningBalanceCard
      isLoading={accountLatestQuery.isFetching}
      title='Total Free Tao'
      value={freeTao}
      valueSub={[{ symbol: '$', value: getDollarValue(freeTao) }]}
    />
  );
});

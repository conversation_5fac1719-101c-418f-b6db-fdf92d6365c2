import type { ReactNode } from 'react';
import { memo } from 'react';
import { Card, Skeleton, Text } from '@repo/ui/components';

export const MiningStatsCard = memo(function MiningStatsCard({
  title,
  children,
  isLoading,
}: {
  title: string;
  children: ReactNode;
  isLoading: boolean;
}) {
  return (
    <Card className='flex-1' contentContainerClassName='gap-3 xl:gap-6'>
      <Text as='h3' level='labelSmall' className='text-sm text-white/60'>
        {title}
      </Text>
      {isLoading ? <Skeleton className='h-12 w-full' /> : <div>{children}</div>}
    </Card>
  );
});

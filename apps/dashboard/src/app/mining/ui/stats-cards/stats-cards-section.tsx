import { memo } from 'react';
import { CombinedPerformanceCard } from './combined-performance-card';
import { HotkeyAveragePerformanceCard } from './hotkey-average-performance-card';
import { HotkeyStatsCard } from './hotkey-stats-card';
import { MiningAlphaCard } from './mining-alpha-card';
import { SubnetsCard } from './subnets-card';
import { TotalBalanceCard } from './total-balance-card';
import { TotalFreeTaoCard } from './total-free-tao-card';
import { TotalOtherAlphaCard } from './total-other-alpha-card';

export const StatsCardSection = memo(function StatsCardSection() {
  return (
    <>
      <div className='flex w-full flex-col gap-8 xl:flex-row'>
        <div className='flex flex-row gap-8 xl:w-1/5'>
          <MiningAlphaCard />
        </div>
        <div className='flex flex-row gap-8 xl:w-2/5'>
          <TotalOtherAlphaCard />
          <TotalFreeTaoCard />
        </div>
        <div className='flex flex-row gap-8 xl:w-2/5'>
          <TotalBalanceCard />
          <SubnetsCard />
        </div>
      </div>
      <div className='flex flex-col gap-8 xl:flex-row'>
        <div className='flex flex-row gap-8 xl:w-2/3'>
          <CombinedPerformanceCard />
          <HotkeyAveragePerformanceCard />
        </div>
        <div className='flex flex-row gap-8 xl:w-1/3'>
          <HotkeyStatsCard />
        </div>
      </div>
    </>
  );
});

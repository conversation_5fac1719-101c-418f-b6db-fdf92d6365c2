import { memo, useMemo } from 'react';
import { MiningBalanceCard } from './mining-balance-card';
import { useMiningContext } from '@/app/mining/lib/context';
import { RAO_PER_TAO } from '@/contexts/chain-and-wallet/constants';
import { useTaoToDollarConverter } from '@/lib/hooks/price-hooks';

export const TotalOtherAlphaCard = memo(function TotalOtherAlphaCard() {
  const { activeMinerColdkeyData, minerColdkeyQuery } = useMiningContext();
  const { getDollarValue } = useTaoToDollarConverter();

  // Other alpha needs to come from summing balance_as_tao from alpha balances where netuid matches and the hotkey isn't found in the hotkey items (with a matching netuid)
  const otherAlphaBalances = useMemo(() => {
    const allAlphaBalances = activeMinerColdkeyData?.alpha_balances ?? [];
    const allHotkeys = activeMinerColdkeyData?.hotkeys ?? [];

    return allAlphaBalances.filter(
      (ab) =>
        !allHotkeys.find(
          (hk) => hk.netuid === ab.netuid && hk.hotkey.ss58 !== ab.hotkey
        )
    );
  }, [activeMinerColdkeyData]);

  const otherAlphaBalance =
    otherAlphaBalances.reduce((acc, cur) => acc + Number(cur.balance), 0) /
    RAO_PER_TAO;

  const otherAlphaBalanceAsTao =
    otherAlphaBalances.reduce(
      (acc, cur) => acc + Number(cur.balance_as_tao),
      0
    ) / RAO_PER_TAO;

  const netuids = useMemo(
    () => Array.from(new Set(otherAlphaBalances.map((ab) => ab.netuid))),
    [otherAlphaBalances]
  );

  if (netuids.length > 1) {
    // Can only sum alpha for one subnet
    return (
      <MiningBalanceCard
        isLoading={minerColdkeyQuery.some((mcq) => mcq.isFetching)}
        title='Total Other Alpha'
        value={otherAlphaBalanceAsTao}
        valueSub={[
          { symbol: '$', value: getDollarValue(otherAlphaBalanceAsTao) },
        ]}
      />
    );
  }

  return (
    <MiningBalanceCard
      isLoading={minerColdkeyQuery.some((mcq) => mcq.isFetching)}
      title='Total Other Alpha'
      value={otherAlphaBalance}
      valueSymbol='α'
      valueSub={[
        { symbol: '$', value: getDollarValue(otherAlphaBalanceAsTao) },
        { value: otherAlphaBalanceAsTao },
      ]}
    />
  );
});

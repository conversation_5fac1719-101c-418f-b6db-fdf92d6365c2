import { memo } from 'react';
import { Text } from '@repo/ui/components';
import { useWindowSize } from '@repo/ui/lib';
import { MiningStatsCard } from './mining-stats-card';
import { useMiningContext } from '@/app/mining/lib/context';
import { getDateRangeLabel } from '@/app/mining/lib/utils';

const StatusBadge = ({ badgeColour }: { badgeColour: string }) => (
  <span className={`h-2 w-2 rounded-full bg-[${badgeColour}]`} />
);

const StatusRow = ({
  label,
  value,
  periodValue,
  periodSelected,
  badgeColour,
}: {
  label: string;
  value?: number;
  periodValue?: number;
  periodSelected?: string;
  badgeColour: string;
}) => (
  <div className='flex flex-row justify-between gap-8'>
    <div className='flex flex-row items-center gap-2'>
      <StatusBadge badgeColour={badgeColour} />
      <Text level='labelSmall' className='text-white'>
        {label}
      </Text>
    </div>
    {value === undefined ? null : (
      <Text level='labelSmall' className='text-right'>
        {value === -1 ? <span className='opacity-60'>NA</span> : value}
        {periodValue === undefined ? null : (
          <>
            <span className='opacity-60'>{` / ${periodSelected}:`}</span>
            <span className={value === -1 ? 'opacity-80' : 'opacity-60'}>
              {periodValue}
            </span>
          </>
        )}
      </Text>
    )}
  </div>
);

export const HotkeyStatsCard = memo(function HotkeyStatsCard() {
  const { activeMinerColdkeyData, minerColdkeyQuery, dateRangeSelected } =
    useMiningContext();

  const { isMobile } = useWindowSize();

  const periodSelected = getDateRangeLabel(dateRangeSelected);

  return (
    <MiningStatsCard
      title='Hotkey Stats'
      isLoading={minerColdkeyQuery.some((mcq) => mcq.isFetching)}
    >
      <div className='flex flex-row justify-between'>
        <div className='flex flex-col justify-between'>
          <StatusRow label='Total' badgeColour='#00DBBC' />
          <Text
            level={isMobile ? 'headerMedium' : 'displaySmall'}
            className='font-normal'
          >
            {activeMinerColdkeyData?.hotkeys.length ?? 0}
          </Text>
        </div>
        <div className='flex flex-col justify-between'>
          <StatusRow
            label='Immune'
            value={Number(activeMinerColdkeyData?.total_immune_hotkeys ?? 0)}
            periodValue={Number(
              activeMinerColdkeyData?.total_immune_hotkeys_during_period ?? 0
            )}
            periodSelected={periodSelected}
            badgeColour='#FF8B25'
          />
          <StatusRow
            label='Danger'
            value={Number(activeMinerColdkeyData?.total_hotkeys_in_danger ?? 0)}
            periodValue={Number(
              activeMinerColdkeyData?.total_hotkeys_in_danger_during_period ?? 0
            )}
            periodSelected={periodSelected}
            badgeColour='#EBC247'
          />
          <StatusRow
            label='Dereg'
            value={-1}
            periodValue={Number(
              activeMinerColdkeyData?.total_deregistered_hotkeys ?? 0
            )}
            periodSelected={periodSelected}
            badgeColour='#EB5347'
          />
        </div>
      </div>
    </MiningStatsCard>
  );
});

import { memo, useMemo } from 'react';
import { MiningBalanceCard } from './mining-balance-card';
import { useMiningContext } from '@/app/mining/lib/context';
import { getDateRangeLabel } from '@/app/mining/lib/utils';
import { RAO_PER_TAO } from '@/contexts/chain-and-wallet/constants';
import { useTaoToDollarConverter } from '@/lib/hooks/price-hooks';

export const CombinedPerformanceCard = memo(function CombinedPerformanceCard() {
  const { activeMinerColdkeyData, minerColdkeyQuery, dateRangeSelected } =
    useMiningContext();
  const { getDollarValue } = useTaoToDollarConverter();

  const combinedPerformanceAsTao = useMemo(() => {
    return (
      (activeMinerColdkeyData?.hotkeys
        .map((hk) => hk.total_emission_as_tao)
        .reduce((acc, cur) => acc + Number(cur), 0) ?? 0) / RAO_PER_TAO
    );
  }, [activeMinerColdkeyData]);

  return (
    <MiningBalanceCard
      isLoading={minerColdkeyQuery.some((mcq) => mcq.isFetching)}
      title={`Combined Performance - ${getDateRangeLabel(dateRangeSelected)}`}
      value={combinedPerformanceAsTao}
      valueSub={[
        { symbol: 'USD', value: getDollarValue(combinedPerformanceAsTao) },
      ]}
    />
  );
});

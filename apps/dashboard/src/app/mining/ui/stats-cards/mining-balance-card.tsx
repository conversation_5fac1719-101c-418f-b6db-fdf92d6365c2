import React, { memo } from 'react';
import { TaoOrAlphaValueDisplay, Text } from '@repo/ui/components';
import { useWindowSize } from '@repo/ui/lib';
import { MiningStatsCard } from './mining-stats-card';

export const MiningBalanceCard = memo(function MiningBalanceCard({
  title,
  value,
  valueSymbol,
  valueSub,
  isLoading,
}: {
  title: string;
  value: number;
  valueSymbol?: string;
  valueSub?: { symbol?: string; value: number }[];
  isLoading: boolean;
}) {
  const { isMobile } = useWindowSize();

  return (
    <MiningStatsCard title={title} isLoading={isLoading}>
      <div>
        <div className='flex items-center gap-4'>
          <TaoOrAlphaValueDisplay
            value={value}
            symbol={valueSymbol}
            valueTextLevel={isMobile ? 'headerMedium' : 'displaySmall'}
            valueFormattingOptions={{
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }}
            valueClassName='font-normal'
            areDecimalsSmall
          />
        </div>
        {valueSub && valueSub.length > 0 ? (
          <div className='flex flex-wrap items-center gap-2'>
            {valueSub.map((item, index) => (
              <React.Fragment key={`${item.symbol ?? 'TAO'}${item.value}`}>
                <TaoOrAlphaValueDisplay
                  key={item.symbol ?? 'TAO'}
                  value={item.value}
                  valueTextLevel='labelSmall'
                  symbol={item.symbol}
                  iconClassName='h-2 w-2'
                  valueFormattingOptions={{
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  }}
                  valueClassName='font-normal'
                  colourClassName='text-white/60'
                />
                {index < valueSub.length - 1 ? (
                  <Text
                    level='labelSmall'
                    className='font-normal text-white/60'
                  >
                    /
                  </Text>
                ) : null}
              </React.Fragment>
            ))}
          </div>
        ) : null}
      </div>
    </MiningStatsCard>
  );
});

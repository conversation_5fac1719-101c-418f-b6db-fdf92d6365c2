import { memo, useMemo } from 'react';
import { MiningBalanceCard } from './mining-balance-card';
import { useMiningContext } from '@/app/mining/lib/context';
import { getDateRangeLabel } from '@/app/mining/lib/utils';
import { RAO_PER_TAO } from '@/contexts/chain-and-wallet/constants';
import { useTaoToDollarConverter } from '@/lib/hooks/price-hooks';

export const HotkeyAveragePerformanceCard = memo(
  function HotkeyAveragePerformanceCard() {
    const { activeMinerColdkeyData, minerColdkeyQuery, dateRangeSelected } =
      useMiningContext();
    const { getDollarValue } = useTaoToDollarConverter();

    const averageHotkeyPerformance = useMemo(() => {
      const allHotkeyPerformances =
        activeMinerColdkeyData?.hotkeys.map((hk) => hk.total_emission_as_tao) ??
        [];
      const average =
        allHotkeyPerformances.reduce((acc, cur) => acc + Number(cur), 0) /
        (allHotkeyPerformances.length || 1);

      return average / RAO_PER_TAO;
    }, [activeMinerColdkeyData]);

    return (
      <MiningBalanceCard
        isLoading={minerColdkeyQuery.some((mcq) => mcq.isFetching)}
        title={`Avg per Hotkey Performance - ${getDateRangeLabel(dateRangeSelected)}`}
        value={averageHotkeyPerformance}
        valueSub={[
          { symbol: '$', value: getDollarValue(averageHotkeyPerformance) },
        ]}
      />
    );
  }
);

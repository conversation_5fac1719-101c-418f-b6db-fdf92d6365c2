import { memo } from 'react';
import { Text } from '@repo/ui/components';
import { useWindowSize } from '@repo/ui/lib';
import { MiningStatsCard } from './mining-stats-card';
import { useMiningContext } from '@/app/mining/lib/context';

export const SubnetsCard = memo(function SubnetsCard() {
  const { activeMinerColdkeyData, minerColdkeyQuery } = useMiningContext();

  const { isMobile } = useWindowSize();

  return (
    <MiningStatsCard
      title='Subnets'
      isLoading={minerColdkeyQuery.some((mcq) => mcq.isFetching)}
    >
      <Text
        level={isMobile ? 'headerMedium' : 'displaySmall'}
        className='font-normal'
      >
        {activeMinerColdkeyData?.active_subnets ?? 0}
      </Text>
    </MiningStatsCard>
  );
});

import { memo, useMemo } from 'react';
import { MiningBalanceCard } from './mining-balance-card';
import { useMiningContext } from '@/app/mining/lib/context';
import { RAO_PER_TAO } from '@/contexts/chain-and-wallet/constants';
import { useTaoToDollarConverter } from '@/lib/hooks/price-hooks';

export const MiningAlphaCard = memo(function MiningAlphaCard() {
  const { activeMinerColdkeyData, minerColdkeyQuery } = useMiningContext();
  const { getDollarValue } = useTaoToDollarConverter();

  const allHotkeys = useMemo(
    () => activeMinerColdkeyData?.hotkeys ?? [],
    [activeMinerColdkeyData]
  );

  const miningAlpha = useMemo(() => {
    const uniqueNetuids = Array.from(
      new Set(allHotkeys.map((hotkey) => hotkey.netuid))
    );

    if (uniqueNetuids.length > 1) {
      // Summing alpha across different subnet tokens does not make sense
      return -1;
    }

    return (
      allHotkeys.reduce((acc, cur) => acc + Number(cur.alpha_balance), 0) /
      RAO_PER_TAO
    );
  }, [allHotkeys]);

  const miningAlphaAsTao = useMemo(() => {
    return (
      allHotkeys.reduce(
        (acc, cur) => acc + Number(cur.alpha_balance_as_tao),
        0
      ) / RAO_PER_TAO
    );
  }, [allHotkeys]);

  const valueSub = useMemo(() => {
    const vs = [{ symbol: '$', value: getDollarValue(miningAlphaAsTao) }];
    if (miningAlpha > -1) {
      vs.push({ symbol: 'α', value: miningAlpha });
    }
    return vs;
  }, [getDollarValue, miningAlpha, miningAlphaAsTao]);

  return (
    <MiningBalanceCard
      isLoading={minerColdkeyQuery.some((mcq) => mcq.isFetching)}
      title='Mining Alpha'
      value={miningAlphaAsTao}
      valueSub={valueSub}
    />
  );
});

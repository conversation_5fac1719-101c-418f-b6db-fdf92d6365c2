'use client';

import { Skeleton, AlertError } from '@repo/ui/components';
import { Profile } from './ui/profile';
import { useUserAccount } from '@/lib/hooks/user-hooks';

export default function ProfilePage() {
  const { isLoading, isError } = useUserAccount();

  if (isLoading) {
    return (
      <div className='flex flex-col gap-4'>
        <Skeleton className='h-[100px] w-full' />
        <Skeleton className='h-[100px] w-full' />
      </div>
    );
  }

  if (isError) {
    return (
      <AlertError title='Error' description='Error fetching user account' />
    );
  }

  return <Profile />;
}

import { Hono } from 'hono';
import { csrf } from 'hono/csrf';
import { handle } from 'hono/vercel';
import { adminAccountsApi } from './(admin)/accounts';
import { adminStatsApi } from './(admin)/stats';
import { accountApi } from './account';
import { apiKeysApi } from './api-keys';
import { blocksApi } from './blocks';
import { dtaoApi } from './dtao';
import { getErrorMessage } from './lib/utils';
import { mentatApi } from './mentat';
import { minerApi } from './miner';
import { neuronApi } from './neuron';
import { priceApi } from './price';
import { statsApi } from './stats';
import { stripeApi } from './stripe';
import { subnetApi } from './subnet';
import { userAccountApi } from './user-account';
import { validatorApi } from './validator';
import { APP_ENV } from '@/lib/config';

export const runtime = 'nodejs';

const app = new Hono().basePath('/api');

const csrfOriginMap = {
  development: 'http://localhost:3000',
  test: 'https://test-dash.taostats.io',
  beta: 'https://app2.taostats.io',
  production: 'https://dash.taostats.io',
};

app.use(
  csrf({
    origin: [csrfOriginMap[APP_ENV]],
  })
);

const routes = app
  .route('/account', accountApi)
  .route('/admin/accounts', adminAccountsApi)
  .route('/admin/stats', adminStatsApi)
  .route('/apiKeys', apiKeysApi)
  .route('/blocks', blocksApi)
  .route('/dtao', dtaoApi)
  .route('/mentat', mentatApi)
  .route('/miner', minerApi)
  .route('/neuron', neuronApi)
  .route('/price', priceApi)
  .route('/stats', statsApi)
  .route('/stripe', stripeApi)
  .route('/subnet', subnetApi)
  .route('/validator', validatorApi)
  .route('/userAccount', userAccountApi)
  .onError((err, c) => {
    return c.json(
      {
        message: getErrorMessage(err),
      },
      500
    );
  });

export type ApiTypes = typeof routes;
export const GET = handle(app);
export const POST = handle(app);
export const PUT = handle(app);
export const DELETE = handle(app);

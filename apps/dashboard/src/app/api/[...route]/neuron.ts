import { <PERSON>o } from 'hono';
import { z } from 'zod';
import { zValidator } from './lib/zod-validator';
import {
  getNeuronAggregatedistory,
  getNeuronHistory,
} from '@/api-proxy/website-api/neuron';

const app = new Hono()
  .get(
    '/history',
    zValidator(
      'query',
      z.object({
        netuid: z.string(),
        uid: z.string().optional(),
        hotkey: z.string().optional(),
        coldkey: z.string().optional(),
        timestampStart: z.string(),
        page: z.string().optional(),
      })
    ),
    async (c) => {
      const {
        netuid,
        uid,
        hotkey,
        coldkey,
        timestampStart,
        page = '1',
      } = c.req.valid('query');

      const response = await getNeuronHistory({
        netuid: Number(netuid),
        uid: uid ? Number(uid) : undefined,
        hotkey,
        coldkey,
        // is_immune: isImmune,
        // in_danger: isInDanger,
        // has_dividends: hasDividends,
        // has_incentive: hasIncentive,
        // block_start: blockStart,
        // block_end: blockEnd,
        timestamp_start: Number(timestampStart),
        // timestamp_end: timestampEnd,
        limit: 200,
        page: Number(page),
        order: 'timestamp_asc',
      });

      return c.json(response);
    }
  )
  .get(
    '/aggregatedHistory',
    zValidator(
      'query',
      z.object({
        netuid: z.string(),
        uid: z.string().optional(),
        hotkey: z.string().optional(),
        coldkey: z.string().optional(),
        timestampStart: z.string(),
        page: z.string().optional(),
      })
    ),
    async (c) => {
      const { netuid, timestampStart, page = '1' } = c.req.valid('query');

      const response = await getNeuronAggregatedistory({
        netuid: Number(netuid),
        // block_start: blockStart,
        // block_end: blockEnd,
        timestamp_start: Number(timestampStart),
        // timestamp_end: timestampEnd,
        limit: 200,
        page: Number(page),
        order: 'timestamp_asc',
      });

      return c.json(response);
    }
  );

export const neuronApi = app;

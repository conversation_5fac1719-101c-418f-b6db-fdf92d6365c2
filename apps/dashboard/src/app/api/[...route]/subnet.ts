import { Hono } from 'hono';
import { fetchDtaoSubnet } from '@/api-proxy/website-api/dtao';
import {
  getSubnets,
  getSubnetIdentities,
  getSubnetRegistrationCostHistory,
} from '@/api-proxy/website-api/subnet';

const app = new Hono()
  .get('/', async (c) => {
    const response = await getSubnets({});
    return c.json(response);
  })
  .get('/identity', async (c) => {
    const response = await getSubnetIdentities({ ...c.req.query() });
    return c.json(response);
  })
  .get('/registration_cost/history', async (c) => {
    const response = await getSubnetRegistrationCostHistory({
      ...c.req.query(),
    });
    return c.json(response);
  })
  .get('/dtaoSubnet', async (c) => {
    const response = await fetchDtaoSubnet();
    return c.json(response);
  });

export const subnetApi = app;

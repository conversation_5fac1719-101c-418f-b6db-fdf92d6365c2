import { Hono } from 'hono';
import { z } from 'zod';
import { zValidator } from './lib/zod-validator';
import { getMinerColdkey } from '@/api-proxy/website-api/miner';

const app = new Hono().get(
  '/:coldkey/:days',
  zValidator(
    'param',
    z.object({
      coldkey: z.string(),
      days: z.string(),
    })
  ),
  async (c) => {
    const { coldkey, days } = c.req.valid('param');
    const response = await getMinerColdkey({
      coldkey,
      days: Number(days),
    });
    return c.json(response);
  }
);

export const minerApi = app;

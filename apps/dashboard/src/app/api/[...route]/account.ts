import { Hono } from 'hono';
import { z } from 'zod';
import { zValidator } from './lib/zod-validator';
import {
  addPortfolioWallet,
  deletePortfolioWallet,
  getPortfolioWallets,
} from '@/api-proxy/dashboard-api/account';
import { getAccountLatest } from '@/api-proxy/website-api/account';
import { getColdkeyReport } from '@/api-proxy/website-api/accounting';
import {
  getDelegations,
  getStakeBalanceLatest,
} from '@/api-proxy/website-api/stake';

const app = new Hono()
  .get(
    '/:address/latest',
    zValidator('param', z.object({ address: z.string() })),
    async (c) => {
      return c.json(
        await getAccountLatest({
          ...c.req.query(),
          address: c.req.valid('param').address,
        })
      );
    }
  )
  .get(
    '/:address/stake/balance',
    zValidator('param', z.object({ address: z.string() })),
    zValidator(
      'query',
      z.object({ days: z.coerce.number().int().nonnegative() })
    ),
    async (c) => {
      return c.json(
        await getStakeBalanceLatest({
          ...c.req.query(),
          coldkey: c.req.valid('param').address,
          days: c.req.valid('query').days,
        })
      );
    }
  )
  .get(
    '/:address/stake/detailed/:page',
    zValidator(
      'param',
      z.object({
        address: z.string(),
        page: z.coerce
          .number()
          .int()
          .nonnegative({ message: 'Page index must be 0 or greater' }),
      })
    ),
    zValidator(
      'query',
      z.object({
        amountMin: z.coerce.number().int().nonnegative().optional(),
        subnetId: z.coerce.number().int().nonnegative().optional(),
        timestampStart: z.coerce.number().int().nonnegative().optional(),
        timestampEnd: z.coerce.number().int().nonnegative().optional(),
      })
    ),
    async (c) => {
      const { address, page } = c.req.valid('param');
      return c.json(
        await getDelegations({
          nominator: address,
          page,
          amount_min: c.req.valid('query').amountMin?.toString(),
          netuid: c.req.valid('query').subnetId,
          timestamp_start: c.req.valid('query').timestampStart,
          timestamp_end: c.req.valid('query').timestampEnd,
        })
      );
    }
  )
  .get(
    '/:address/stake/detailed/:subnetId/:page',
    zValidator(
      'param',
      z.object({
        address: z.string(),
        subnetId: z.coerce.number().int().nonnegative(),
        page: z.coerce
          .number()
          .int()
          .nonnegative({ message: 'Page index must be 0 or greater' }),
      })
    ),
    async (c) => {
      const { address, subnetId, page } = c.req.valid('param');
      return c.json(
        await getDelegations({
          nominator: address,
          netuid: subnetId,
          page,
        })
      );
    }
  )
  .get(
    '/:address/coldkeyReport/:fromDate/:toDate',
    zValidator(
      'param',
      z.object({
        address: z.string(),
        fromDate: z.string(),
        toDate: z.string(),
      })
    ),
    async (c) => {
      const { address, fromDate, toDate } = c.req.valid('param');
      return c.json(
        await getColdkeyReport({
          coldkey: address,
          date_start: fromDate,
          date_end: toDate,
        })
      );
    }
  )
  .get('/portfolio/wallets', async (c) => {
    return c.json(await getPortfolioWallets());
  })
  .post(
    '/portfolio/wallet',
    zValidator(
      'json',
      z.object({
        name: z.string(),
        address: z.string(),
      })
    ),
    async (c) => {
      return c.json(await addPortfolioWallet(c.req.valid('json')));
    }
  )
  .delete(
    '/portfolio/wallet/:walletId',
    zValidator(
      'param',
      z.object({
        walletId: z.string(),
      })
    ),
    async (c) => {
      const { walletId } = c.req.valid('param');
      return c.json(await deletePortfolioWallet(walletId));
    }
  );

export const accountApi = app;

import { Hono } from 'hono';
import { fetchBlocks } from '@/api-proxy/website-api/blocks';

const app = new Hono()
  .get('/limit', async (c) => {
    const limit = c.req.query('limit');

    const response = await fetchBlocks({
      limit: limit ? parseInt(limit) : 1,
    });
    return c.json(response);
  })
  .get('/:block_number', async (c) => {
    const blockNumber = c.req.param('block_number');

    const response = await fetchBlocks({
      block_number: blockNumber ? parseInt(blockNumber) : 1,
    });
    return c.json(response);
  });

export const blocksApi = app;

import { Hono } from 'hono';
import { z } from 'zod';

const mentatStrategySchema = z.object({
  origin: z.string(),
  name: z.string(),
  proxyAddress: z.string(),
  hotkeyAddress: z.string(),
  walletAddress: z.string(),
});

const mentatStrategiesSchema = z.array(mentatStrategySchema);

const mentatApyResponseSchema = z.object({
  weightedAPY: z.number(),
  days: z.number(),
});

const app = new Hono()
  .get('/strategies', async (c) => {
    const response = await fetch('https://mentatminds.com/api/strategies');
    const data = (await response.json()) as unknown;
    const validatedData = mentatStrategiesSchema.parse(data);
    return c.json(validatedData);
  })
  .get('/wallet/:walletAddress/apy', async (c) => {
    const walletAddress = c.req.param('walletAddress');
    const response = await fetch(
      `https://mentatminds.com/api/apy/${walletAddress}/15`
    );
    const data = (await response.json()) as unknown;
    const validatedData = mentatApyResponseSchema.parse(data);
    return c.json(validatedData);
  });

export const mentatApi = app;

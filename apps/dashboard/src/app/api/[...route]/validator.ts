import { Hono } from 'hono';
import { getValidatorMetadata } from '@/api-proxy/validator-metadata';
import {
  getTopValidatorsPerSubnet,
  getValidators,
  getValidatorsIdentity,
} from '@/api-proxy/website-api/validator';

const app = new Hono()
  .get('/', async (c) => {
    const response = await getValidators({ ...c.req.query() });
    return c.json(response);
  })
  .get('/topValidatorsPerSubnet', async (c) => {
    const response = await getTopValidatorsPerSubnet();
    return c.json(response);
  })
  .get('/validatorMetadata', async (c) => {
    const response = await getValidatorMetadata();
    return c.json(response);
  })
  .get('/validatorIdentity', async (c) => {
    const response = await getValidatorsIdentity();
    return c.json(response);
  });

export const validatorApi = app;

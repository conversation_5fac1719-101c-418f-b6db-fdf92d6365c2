import { AxiosError } from 'axios';
import type { HTTPResponseError } from 'hono/types';

export const getErrorMessage = (error: Error | HTTPResponseError) => {
  // Check if the error is an AxiosError and has a response with data and a message
  // If so, return the message
  // Otherwise, return the standard error message
  if (
    error instanceof AxiosError &&
    error.response?.data &&
    typeof error.response.data === 'object' &&
    'message' in error.response.data
  ) {
    return (error.response.data as { message: string }).message;
  }

  return error.message;
};

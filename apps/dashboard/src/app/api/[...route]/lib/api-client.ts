import type { ClientResponse } from 'hono/client';
import { hc } from 'hono/client';
import type { ApiTypes } from '@/app/api/[...route]/route';

export const apiClient = hc<ApiTypes>('').api;

export async function handleResponse<T>(
  response: ClientResponse<T>
): Promise<T> {
  if (!response.ok) {
    const errorData = (await response.json()) as { message: string };
    throw new Error(errorData.message || 'API Error');
  }
  const responseJson = (await response.json()) as Promise<T>;
  return responseJson;
}

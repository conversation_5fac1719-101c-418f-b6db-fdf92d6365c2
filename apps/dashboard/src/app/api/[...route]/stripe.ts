import { Hono } from 'hono';
import Stripe from 'stripe';
import { z } from 'zod';
import { createStripeCustomerSession } from '@/api-proxy/dashboard-api/billing';
import { zValidator } from '@/app/api/[...route]/lib/zod-validator';

const getStripeClient = () => {
  const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
  if (!stripeSecretKey) {
    throw new Error('STRIPE_SECRET_KEY is not set');
  }

  try {
    return new Stripe(stripeSecretKey, {
      apiVersion: '2024-12-18.acacia',
    });
  } catch (error) {
    console.error('Failed to create Stripe client', error);
    throw error;
  }
};

const app = new Hono()
  .post('/getClientSecret', async (c) => {
    const response = await createStripeCustomerSession();
    return c.json(response);
  })
  .post(
    '/createCheckoutSession',
    zValidator(
      'json',
      z.object({
        priceId: z.string(),
        customerId: z.string(),
        mode: z.enum(['subscription', 'payment']),
        invoiceCreation: z.boolean().optional(),
      })
    ),
    async (c) => {
      const origin = c.req.header('origin');
      const { customerId, mode, priceId, invoiceCreation } =
        c.req.valid('json');

      const session = await getStripeClient().checkout.sessions.create({
        customer: customerId,
        payment_method_types: ['card'],
        mode,
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        success_url: `${origin}/api-keys?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: `${origin}/billing`,
        ...(invoiceCreation && {
          invoice_creation: { enabled: true },
        }),
      });
      return c.json({ sessionId: session.id });
    }
  )
  .post(
    '/createPortalSession',
    zValidator(
      'json',
      z.object({
        customerId: z.string(),
      })
    ),
    async (c) => {
      const origin = c.req.header('origin');
      const session = await getStripeClient().billingPortal.sessions.create({
        customer: c.req.valid('json').customerId,
        return_url: `${origin}/billing`,
      });
      return c.json({ session });
    }
  );

export const stripeApi = app;

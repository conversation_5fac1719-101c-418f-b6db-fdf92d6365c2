import { Hono } from 'hono';
import { z } from 'zod';
import { getTopUsage } from '@/api-proxy/dashboard-api/admin';
import { zValidator } from '@/app/api/[...route]/lib/zod-validator';

const app = new Hono().get(
  '/:startDate/:endDate/topUsage',
  zValidator(
    'param',
    z.object({
      startDate: z
        .string({
          required_error: 'Start date is required',
          invalid_type_error:
            'Start date must be a string in YYYY-MM-DD format',
        })
        .regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format'),
      endDate: z
        .string({
          required_error: 'End date is required',
          invalid_type_error: 'End date must be a string in YYYY-MM-DD format',
        })
        .regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format'),
    })
  ),
  async (c) => {
    const { startDate, endDate } = c.req.valid('param');
    const response = await getTopUsage({
      startDate,
      endDate,
      limit: 50,
    });
    return c.json(response);
  }
);

export const adminStatsApi = app;

import { Hono } from 'hono';
import { z } from 'zod';
import {
  updateAccount,
  getAccounts,
  getAccount,
  searchAccounts,
} from '@/api-proxy/dashboard-api/admin';
import { zValidator } from '@/app/api/[...route]/lib/zod-validator';

const app = new Hono()
  .get(
    '/page/:pageIndex',
    zValidator(
      'param',
      z.object({
        pageIndex: z.coerce
          .number()
          .int()
          .nonnegative({ message: 'Page index must be 0 or greater' }),
      })
    ),
    async (c) => {
      const { pageIndex } = c.req.valid('param');
      const response = await getAccounts(pageIndex);
      return c.json(response);
    }
  )
  .get(
    '/search/:searchTerm',
    zValidator(
      'param',
      z.object({
        searchTerm: z.string().min(1, 'Search term must not be empty'),
      })
    ),
    async (c) => {
      const { searchTerm } = c.req.valid('param');
      const response = await searchAccounts(searchTerm);
      return c.json(response);
    }
  )
  .post(
    '/:accountId',
    zValidator(
      'param',
      z.object({
        accountId: z.string().uuid({ message: 'Invalid account ID format' }),
      })
    ),
    zValidator(
      'json',
      z.object({
        rateLimit: z.number(),
      })
    ),
    async (c) => {
      const { accountId } = c.req.valid('param');
      const body = c.req.valid('json');
      await updateAccount({ accountId, ...body });
      return c.json({});
    }
  )
  .get(
    '/:accountId',
    zValidator(
      'param',
      z.object({
        accountId: z.string().uuid('Invalid account ID format'),
      })
    ),
    async (c) => {
      const { accountId } = c.req.valid('param');
      const response = await getAccount(accountId);
      return c.json(response);
    }
  );

export const adminAccountsApi = app;

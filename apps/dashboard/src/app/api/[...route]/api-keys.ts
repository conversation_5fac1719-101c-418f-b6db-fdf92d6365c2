import { Hono } from 'hono';
import { z } from 'zod';
import {
  createApi<PERSON>ey,
  deleteApi<PERSON>ey,
  getApi<PERSON>eys,
  getApiKeyUsage,
} from '@/api-proxy/dashboard-api/api-keys';
import { zValidator } from '@/app/api/[...route]/lib/zod-validator';

const app = new Hono()
  .get('/', async (c) => {
    const response = await getApiKeys();
    return c.json(response);
  })
  .get('/:apiKeyId/usage', async (c) => {
    const { apiKeyId } = c.req.param();
    const response = await getApiKeyUsage(apiKeyId);
    return c.json(response);
  })
  .post(
    '/',
    zValidator(
      'json',
      z.object({
        name: z.string(),
        description: z.string(),
      })
    ),
    async (c) => {
      const { name, description } = c.req.valid('json');
      const response = await createApi<PERSON><PERSON>({ name, description });
      return c.json(response);
    }
  )
  .delete(
    '/',
    zValidator(
      'json',
      z.object({
        apiKeyId: z.string(),
      })
    ),
    async (c) => {
      const { apiKeyId } = c.req.valid('json');
      await deleteApiKey(apiKeyId);
      return c.json({});
    }
  );

export const apiKeysApi = app;

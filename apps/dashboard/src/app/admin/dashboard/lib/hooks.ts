import { keepPreviousData, useQuery } from '@tanstack/react-query';
import type { DateRange } from './constants';
import { apiClient, handleResponse } from '@/app/api/[...route]/lib/api-client';
import { convertDateRangeToDates } from '@/lib/utils';

const adminStatsApiClient = apiClient.admin.stats;

export const useUsageQuery = (dateRange: DateRange) => {
  return useQuery({
    queryKey: ['usage', dateRange],
    queryFn: async () => {
      const { from, to } = convertDateRangeToDates(dateRange);
      return handleResponse(
        await adminStatsApiClient[':startDate'][':endDate'].topUsage.$get({
          param: {
            startDate: from,
            endDate: to,
          },
        })
      );
    },
    placeholderData: keepPreviousData,
    staleTime: 1000 * 60,
  });
};

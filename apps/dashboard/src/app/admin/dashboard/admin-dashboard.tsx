'use client';

import { useMemo, useState } from 'react';
import NumberFlow from '@number-flow/react';
import type { ColumnDef } from '@tanstack/react-table';
import { getCoreRowModel, useReactTable } from '@tanstack/react-table';
import type { GetTopUsageResponse } from '@repo/types/dashboard-api-types';
import {
  AlertError,
  Badge,
  DataTable,
  Skeleton,
  Spinner,
} from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { adminDashboardTimeRanges, type DateRange } from './lib/constants';
import { useUsageQuery } from './lib/hooks';
import { useAccounts } from '@/app/admin/(lib)/accounts-hooks';
import { getErrorMessage } from '@/app/api/[...route]/lib/utils';
import { PageLayout } from '@/components/page-layout';
import { useSessionContext } from '@/lib/providers/session-context-provider';
import { formatNumber, titleCase } from '@/lib/utils';

export const AdminDashboard = () => {
  const [dateRangeSelected, setDateRangeSelected] = useState<DateRange>('1d');

  // Get range as a from and to date
  const usageQuery = useUsageQuery(dateRangeSelected);

  // We want to get the total accounts, just pass in a pageIndex of 0 and a pageSize of 1
  const accountsQuery = useAccounts({ pageIndex: 0, pageSize: 1 });

  const isLoading = usageQuery.isLoading || accountsQuery.isLoading;
  const isSuccess = usageQuery.isSuccess && accountsQuery.isSuccess;
  const error = usageQuery.error || accountsQuery.error;

  return (
    <PageLayout title='Admin Dashboard'>
      {isLoading ? (
        <>
          <Skeleton className='h-10 w-full' />
          <Skeleton className='h-10 w-full' />
          <Skeleton className='h-10 w-full' />
          <Skeleton className='h-10 w-full' />
        </>
      ) : null}

      {isSuccess ? (
        <AdminDashboardContent
          isFetching={usageQuery.isFetching}
          data={usageQuery.data}
          userCount={accountsQuery.data.total || 0}
          dateRangeSelected={dateRangeSelected}
          setDateRangeSelected={setDateRangeSelected}
        />
      ) : null}

      {error ? (
        <AlertError title='Error' description={getErrorMessage(error)} />
      ) : null}
    </PageLayout>
  );
};

type TopUsageDisplayData = {
  email: string;
  key: string;
  apiCalls: number;
};

type TopUsageStat = {
  name: string;
  value: string | number;
  showSpinner: boolean;
};

const columns: ColumnDef<TopUsageDisplayData>[] = [
  { header: 'User', accessorKey: 'email' },
  { header: 'Key', accessorKey: 'key' },
  {
    header: 'API Calls',
    accessorKey: 'apiCalls',
    cell: ({ row }) => {
      return formatNumber(row.original.apiCalls);
    },
  },
];

const taoStatsApiKeys = ['tao-ff5f37fe-0ec7-4503-94c4-e49c84c88cfe:ab232c60'];

const AdminDashboardContent = ({
  data,
  userCount,
  dateRangeSelected,
  setDateRangeSelected,
  isFetching,
}: {
  data: GetTopUsageResponse;
  userCount: number;
  dateRangeSelected: DateRange;
  setDateRangeSelected: (dateRange: DateRange) => void;
  isFetching: boolean;
}) => {
  const { appEnv } = useSessionContext();

  const listData = useMemo(
    () =>
      data.apiKeys
        .filter((apiKey) => !taoStatsApiKeys.includes(apiKey.apiKeyId))
        .map<TopUsageDisplayData>((apiKey) => ({
          email: apiKey.email,
          key: apiKey.apiKeyId,
          apiCalls: apiKey.apiCalls,
        })),
    [data]
  );

  const topUser = useMemo(
    () =>
      listData.length
        ? listData.reduce((prev, curr) =>
            prev.apiCalls > curr.apiCalls ? prev : curr
          )
        : null,
    [listData]
  );

  const stats = useMemo(
    () =>
      [
        {
          name: `Total API Calls`,
          value: listData.reduce((acc, curr) => acc + curr.apiCalls, 0),
          showSpinner: isFetching,
        },
        topUser
          ? {
              name: `Top User`,
              value: topUser.email,
              showSpinner: isFetching,
            }
          : null,
        topUser
          ? {
              name: `Top User Calls`,
              value: topUser.apiCalls,
              showSpinner: isFetching,
            }
          : null,
        {
          name: 'Total Registered (all time)',
          value: userCount,
          showSpinner: false,
        },
      ].filter(Boolean) as TopUsageStat[],
    [listData, isFetching, topUser, userCount]
  );

  const table = useReactTable({
    data: listData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    enableSorting: false,
  });

  return (
    <>
      <header>
        <div className='flex flex-col items-start justify-between gap-x-8 gap-y-4 bg-gray-700/10 px-4 py-4 sm:flex-row sm:items-center sm:px-6 lg:px-8'>
          <div>
            <div className='flex items-center gap-x-3'>
              <div className='flex-none rounded-full bg-green-400/10 p-1 text-green-400'>
                <div className='size-2 rounded-full bg-current' />
              </div>
              <h1 className='flex gap-x-3 text-base/7'>
                <span className='font-semibold text-white'>Taostats API</span>
              </h1>
            </div>
          </div>

          <Badge variant='green1' className='order-first sm:order-none'>
            {titleCase(appEnv)}
          </Badge>
        </div>

        <div className='flex w-full items-center gap-4 bg-gray-700/10 '>
          <div className='flex items-center gap-2 p-4 sm:w-auto sm:flex-shrink-0'>
            {adminDashboardTimeRanges.map((range) => (
              <button
                type='button'
                key={range.value}
                className='data-[active=true]:border-accent-1 data-[active=true]:bg-accent-1/10 flex-1 rounded-lg border border-transparent px-3 py-1.5 text-sm hover:bg-emerald-500/20 hover:text-white data-[active=true]:text-white sm:flex-initial'
                data-active={range.value === dateRangeSelected}
                onClick={() => {
                  setDateRangeSelected(range.value);
                }}
              >
                {range.label}
              </button>
            ))}
          </div>
          {isFetching ? <Spinner /> : null}
        </div>

        <div className='grid grid-cols-1 bg-gray-700/10 sm:grid-cols-2 lg:grid-cols-4'>
          {stats.map((stat, statIdx) => (
            <div
              key={stat.name}
              className={cn(
                statIdx % 2 === 1
                  ? 'sm:border-l'
                  : statIdx === 2
                    ? 'lg:border-l'
                    : '',
                'border-t border-white/5 px-4 py-6 sm:px-6 lg:px-8'
              )}
            >
              <p className='text-sm/6 font-medium text-gray-400'>{stat.name}</p>
              <p className='mt-2 flex items-baseline gap-x-2 overflow-hidden text-ellipsis'>
                <span
                  className={`tracking-tight text-white ${typeof stat.value === 'string' ? 'text-lg' : 'text-2xl'}`}
                >
                  {stat.showSpinner ? (
                    <Spinner />
                  ) : (
                    <ValueDisplay value={stat.value} />
                  )}
                </span>
              </p>
            </div>
          ))}
        </div>
      </header>

      <DataTable table={table} />
    </>
  );
};

const ValueDisplay = ({ value }: { value: number | string }) => {
  return typeof value === 'number' ? <NumberFlow value={value} /> : value;
};

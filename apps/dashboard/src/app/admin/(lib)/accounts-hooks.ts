import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import type { PaginationState } from '@tanstack/react-table';
import { apiClient, handleResponse } from '@/app/api/[...route]/lib/api-client';

const adminApiClient = apiClient.admin;

const queryKeys = {
  accounts: 'accounts',
} as const;

export const useAccounts = (pagination: PaginationState) => {
  return useQuery({
    queryKey: [queryKeys.accounts, pagination],
    queryFn: async () =>
      handleResponse(
        await adminApiClient.accounts.page[':pageIndex'].$get({
          param: { pageIndex: pagination.pageIndex.toString() },
        })
      ),
    placeholderData: keepPreviousData,
  });
};

export const useSearchAccounts = (searchTerm: string) => {
  return useQuery({
    queryKey: [queryKeys.accounts, searchTerm],
    queryFn: async () =>
      handleResponse(
        await adminApiClient.accounts.search[':searchTerm'].$get({
          param: { searchTerm },
        })
      ),
    enabled: false, // enabled is false because we want to run the query on a user action e.g. pressing enter or search button
  });
};

type UpdateAccountParams = Parameters<
  (typeof adminApiClient.accounts)[':accountId']['$post']
>[0]['json'] & { accountId: string };

export const useUpdateAccount = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ accountId, rateLimit }: UpdateAccountParams) => {
      return handleResponse(
        await adminApiClient.accounts[':accountId'].$post({
          json: { rateLimit },
          param: { accountId },
        })
      );
    },
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: [queryKeys.accounts] });
      void queryClient.invalidateQueries({ queryKey: ['userAccount'] });
    },
  });
};

export const useAccount = (accountId: string) => {
  return useQuery({
    queryKey: [queryKeys.accounts, accountId],
    queryFn: async () =>
      handleResponse(
        await adminApiClient.accounts[':accountId'].$get({
          param: { accountId },
        })
      ),
    enabled: Boolean(accountId),
  });
};

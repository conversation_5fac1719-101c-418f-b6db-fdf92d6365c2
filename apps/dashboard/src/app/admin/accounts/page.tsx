'use client';

import { useState } from 'react';
import type { PaginationState } from '@tanstack/react-table';
import { Alert, AlertError, Skeleton } from '@repo/ui/components';
import { AccountList } from './ui/account-list';
import { useAccounts } from '@/app/admin/(lib)/accounts-hooks';
import { PageLayout } from '@/components/page-layout';

export default function Accounts() {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 100,
  });

  const query = useAccounts(pagination);
  const accounts = query.data?.accounts || [];

  return (
    <PageLayout title='Accounts'>
      {query.isLoading ? (
        <div className='flex flex-col gap-4 '>
          <Skeleton className='h-16 w-full' />
          <Skeleton className='h-16 w-full' />
          <Skeleton className='h-16 w-full' />
        </div>
      ) : null}

      <NoRecordsUi
        shouldHide={
          query.isLoading ||
          query.isError ||
          (query.isSuccess && accounts.length > 0)
        }
      />

      {query.isSuccess && accounts.length > 0 ? (
        <AccountList
          accounts={accounts}
          total={query.data.total}
          pagination={pagination}
          setPagination={setPagination}
        />
      ) : null}

      {query.isError ? (
        <AlertError
          className='lg:w-[575px]'
          description={query.error.message}
          title='Error fetching Users'
        />
      ) : null}
    </PageLayout>
  );
}

const NoRecordsUi = ({ shouldHide }: { shouldHide: boolean }) => {
  if (shouldHide) {
    return null;
  }
  return (
    <Alert
      type='info'
      title='No records'
      description='No records found'
      className='lg:w-[575px]'
    />
  );
};

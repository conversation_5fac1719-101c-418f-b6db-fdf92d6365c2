'use client';

import { useParams } from 'next/navigation';
import { AlertError, Spinner } from '@repo/ui/components';
import { useAccount } from '@/app/admin/(lib)/accounts-hooks';
import { PageLayout } from '@/components/page-layout';

export default function AccountDetailsPage() {
  const params = useParams();
  const accountId = params.accountId as string;
  const query = useAccount(accountId);

  return (
    <PageLayout title='Account Details'>
      {query.isLoading ? (
        <div className='flex justify-center py-4'>
          <Spinner />
        </div>
      ) : null}

      {query.isError ? (
        <AlertError
          className='lg:w-[575px]'
          description={query.error.message}
          title='Error fetching account'
        />
      ) : null}

      {query.isSuccess ? (
        <div>
          {/* Add your account details UI here */}
          <pre>{JSON.stringify(query.data, null, 2)}</pre>
        </div>
      ) : null}
    </PageLayout>
  );
}

import { useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import type { SubmitHandler } from 'react-hook-form';
import { Controller, useForm } from 'react-hook-form';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, BiEnvelopeOpen } from 'react-icons/bi';
import { SlSpeedometer } from 'react-icons/sl';
import { z } from 'zod';
import type { Account } from '@repo/types/dashboard-api-types';
import {
  AlertError,
  Spinner,
  Button,
  Input,
  Text,
  Dialog,
} from '@repo/ui/components';
import { notify } from '@repo/ui/lib';
import { useUpdateAccount } from '@/app/admin/(lib)/accounts-hooks';

type ChangeRateLimitDialogProps = {
  closeFn: () => void;
  account: Account | null;
};

export const ChangeRateLimitDialog = ({
  closeFn,
  account,
}: ChangeRateLimitDialogProps) => {
  return (
    <Dialog
      HeaderIcon={SlSpeedometer}
      closeFn={closeFn}
      isOpen={Boolean(account)}
      title='Change Rate Limit'
    >
      {account ? (
        <Form account={account} closeFn={closeFn} />
      ) : (
        <div className='flex justify-center py-4'>
          <Spinner />
        </div>
      )}
    </Dialog>
  );
};

const formId = 'change-rate-limit-form';

const rateLimitSchema = z.object({
  rateLimit: z.coerce
    .number({ invalid_type_error: 'Rate limit must be a valid number' })
    .min(1)
    .max(3000),
});

type RateLimitSchema = z.infer<typeof rateLimitSchema>;

type ChangeRateLimitDialogFormProps = ChangeRateLimitDialogProps & {
  account: Account; // Not nullable within the form
};

const Form = ({ account, closeFn }: ChangeRateLimitDialogFormProps) => {
  const form = useForm<RateLimitSchema>({
    defaultValues: { rateLimit: account.rateLimit || 0 },
    resolver: zodResolver(rateLimitSchema),
  });

  useEffect(() => {
    setTimeout(() => {
      form.setFocus('rateLimit');
    }, 100);
  }, [form]);

  const changeRateLimit = useUpdateAccount();
  const handleChangeRateLimit: SubmitHandler<RateLimitSchema> = async (
    data
  ) => {
    const isValid = await form.trigger();
    if (!isValid) {
      return;
    }

    const { rateLimit } = data;
    changeRateLimit.mutate(
      { accountId: account.accountId, rateLimit },
      {
        onSuccess: () => {
          notify.success('Rate limit updated');
          closeFn();
        },
      }
    );
  };
  return (
    <>
      <div className='flex flex-col gap-6'>
        <Text as='p' level='bodyMedium'>
          Enter the new rate limit for the account. This is the maximum number
          of requests per minute.
        </Text>
        <form
          id={formId}
          onSubmit={(e) => {
            void form.handleSubmit(handleChangeRateLimit)(e);
          }}
        >
          <Controller
            control={form.control}
            name='rateLimit'
            render={({ field }) => (
              <Input
                IconLeft={BiEnvelopeOpen}
                IconRight={field.value ? BiCheck : undefined}
                error={form.formState.errors.rateLimit?.message}
                forwardRef={field.ref}
                inputId='rateLimit'
                placeholder='Rate Limit (requests per minute)'
                onChange={(e) => {
                  field.onChange(e.target.value);
                }}
                value={field.value.toString()}
              />
            )}
          />
        </form>
      </div>

      <div className='flex flex-col gap-4'>
        <Button
          className='flex w-full items-center justify-center gap-2'
          disabled={changeRateLimit.isPending}
          form={formId}
          size='lg'
          type='submit'
          variant='default'
        >
          {changeRateLimit.isPending ? <Spinner /> : <BiCheck size={20} />}
          Change Rate Limit
        </Button>
        {changeRateLimit.isError ? (
          <AlertError
            description={changeRateLimit.error.message}
            title='Error'
          />
        ) : null}
      </div>
    </>
  );
};

import type { Dispatch, SetStateAction } from 'react';
import { useMemo, useState } from 'react';
import type { PaginationState } from '@tanstack/react-table';
import {
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { BiSearch } from 'react-icons/bi';
import type { Account } from '@repo/types/dashboard-api-types';
import {
  Alert,
  Button,
  DataTable,
  Input,
  Spinner,
  TablePagination,
} from '@repo/ui/components';
import { notify } from '@repo/ui/lib';
import { ChangeRateLimitDialog } from './change-rate-limit-dialog';
import { useColumns } from './columns';
import { useSearchAccounts } from '@/app/admin/(lib)/accounts-hooks';

type AccountListProps = {
  accounts: Account[];
  total: number;
  pagination: PaginationState;
  setPagination: Dispatch<SetStateAction<PaginationState>>;
};

export const AccountList = ({
  accounts,
  pagination,
  setPagination,
  total,
}: AccountListProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  const searchAccountsResult = useSearchAccounts(searchTerm);

  const triggerSearch = () => {
    if (searchTerm) {
      void searchAccountsResult.refetch();
    }
  };

  const orderedAccounts = useMemo(() => {
    const searchResults = searchAccountsResult.data?.accounts || [];
    return searchResults.sort((a, b) => {
      if (!a.email) return 1;
      if (!b.email) return -1;
      return a.email.localeCompare(b.email);
    });
  }, [searchAccountsResult.data?.accounts]);

  const currentAccounts = searchTerm ? orderedAccounts : accounts;

  const useTotal = useMemo(() => {
    return (searchTerm ? searchAccountsResult.data?.total : total) ?? 0;
  }, [searchTerm, searchAccountsResult.data?.total, total]);

  const [changeRateLimitForAccount, setChangeRateLimitForAccount] =
    useState<Account | null>(null);

  const clearChangeRateLimitForAccount = () => {
    setChangeRateLimitForAccount(null);
    triggerSearch();
  };

  const handleChangeRateLimitClick = (accountId: string) => {
    const account = currentAccounts.find((a) => a.accountId === accountId);
    if (!account) {
      notify.error('Could not find account');
      return;
    }
    setChangeRateLimitForAccount(account);
  };

  const columns = useColumns(handleChangeRateLimitClick);

  const table = useReactTable({
    data: currentAccounts,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onPaginationChange: setPagination,
    manualPagination: true,
    rowCount: useTotal,
    state: {
      pagination,
    },
    debugTable: true,
  });

  return (
    <div className='container flex flex-col gap-4'>
      <Alert
        type='info'
        description={`There are ${total} accounts in total. Please search by email to find a specific account.`}
      />
      <div className='flex items-center gap-2'>
        <Input
          onChange={(e) => {
            setSearchTerm(e.target.value);
          }}
          onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
            if (e.key === 'Enter') {
              triggerSearch();
            }
          }}
          value={searchTerm}
          inputId='search'
          placeholder='Search by email'
        />
        <Button
          variant='cta1'
          disabled={searchTerm === '' || searchAccountsResult.isFetching}
          onClick={() => {
            triggerSearch();
          }}
          className='flex items-center gap-2'
        >
          {searchAccountsResult.isFetching ? <Spinner /> : <BiSearch />}
          Search
        </Button>
      </div>
      <TablePagination table={table} total={useTotal} />
      <DataTable table={table} />

      <ChangeRateLimitDialog
        closeFn={clearChangeRateLimitForAccount}
        account={changeRateLimitForAccount}
      />
    </div>
  );
};

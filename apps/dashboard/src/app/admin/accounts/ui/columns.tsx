'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { useRouter } from 'next/navigation';
import { BiDotsHorizontalRounded } from 'react-icons/bi';
import type { Account } from '@repo/types/dashboard-api-types';
import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@repo/ui/components';
import { notify } from '@repo/ui/lib';

export const useColumns = (
  handleChangeRateLimitClick: (accountId: string) => void
) => {
  const router = useRouter();
  const columns: ColumnDef<Account>[] = [
    {
      accessorKey: 'type',
      header: 'Type',
    },
    {
      accessorKey: 'email',
      header: 'Email',
    },
    {
      accessorKey: 'rateLimit',
      header: 'Rate Limit',
    },
    {
      accessorKey: 'keyCount',
      header: 'Key Count',
    },
    {
      id: 'actions',
      cell: ({ row }) => {
        const account = row.original;

        return (
          <div className='flex justify-end'>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant='ghost' className='h-8 w-8 p-0'>
                  <span className='sr-only'>Open menu</span>
                  <BiDotsHorizontalRounded className='h-4 w-4' />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align='end'>
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuItem
                  onClick={() => {
                    void navigator.clipboard.writeText(account.accountId);
                    notify.success('Account ID copied to clipboard');
                  }}
                >
                  Copy Account ID
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => {
                    router.push(`/admin/accounts/${account.accountId}`);
                  }}
                >
                  View Account
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    handleChangeRateLimitClick(account.accountId);
                  }}
                >
                  Change Rate Limit
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    },
  ];

  return columns;
};

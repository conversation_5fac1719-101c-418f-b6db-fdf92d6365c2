'use client';

import type { ReactNode } from 'react';
import {
  createContext,
  useCallback,
  useContext,
  useMemo,
  useState,
} from 'react';
import { BigNumber } from 'bignumber.js';
import { notify } from '@repo/ui/lib';
import { getSelectedSubnetId } from '@/app/stake/lib/helpers';
import type {
  SelectedSubnetType,
  AddSubnetParam,
  StakingTypes,
} from '@/app/stake/lib/types';
import { useAntiMevSlippage, useMaxSlippage } from '@/lib/hooks/staking-hooks';

type SliderState = {
  sliderValue: number; // % value between 0 and 100
  isLocked: boolean;
};

interface StakeContextProviderProps {
  totalTao: number; // This is the total amount of tao in the wallet, delegated and un-delegated
  availableBalance: number; // This is the amount of tao available to delegate
  initialSubnets?: SelectedSubnetType[]; // This is the list of subnets that are already delegated to, used for initial state
  stakingType: StakingTypes; // This is the type of staking that is being used
  children: ReactNode;
}

type StakeContextValue = Pick<StakeContextProviderProps, 'availableBalance'> & {
  totalTao: number;
  initialStakedAmount: number;
  maxSlippage: number;
  updateMaxSlippage: (maxSlippage: number) => void;
  selectedSubnets: SelectedSubnetType[];
  addSubnet: (selectedSubnet: AddSubnetParam) => boolean;
  removeSubnet: (selectedSubnetId: string) => void;
  sliderState: Record<string, SliderState>;
  updateSliderValue: (selectedSubnetId: string, sliderValue: number) => void;
  updateAmountToDelegateToSubnet: (
    selectedSubnetId: string,
    amount: number
  ) => void;
  updateSliderLocked: (selectedSubnetId: string, isLocked: boolean) => void;
  availableToDelegatePercentage: number;
  updateAvailableToDelegatePercentage: (percentage: number) => void;
  updateAvailableToDelegateValue: (value: number) => void;
  amountToDelegateTao: number;
  getInitialAmountForSubnet: (selectedSubnetId: string) => number;
  stakingType: StakingTypes;
  isMentat: boolean;
  isSimple: boolean;
  antiMevSlippageValue: number | null;
  setAntiMevSlippageValue: (value: number | null) => void;
};

const StakeContext = createContext<StakeContextValue | undefined>(undefined);

const calculateInitialSliderState = (
  initialStakedAmount: number,
  initialSubnets: SelectedSubnetType[]
) => {
  return initialSubnets.reduce((acc, subnet) => {
    // The slider value is a percentage of the total delegated amount
    const sliderValue = BigNumber(subnet.initialAmount)
      .dividedBy(initialStakedAmount)
      .multipliedBy(100)
      .toNumber();
    return {
      ...acc,
      [subnet.selectedSubnetId]: {
        sliderValue,
        isLocked: false,
      },
    };
  }, {});
};

const getInitialState = (
  totalTao: number,
  availableBalance: number,
  initialSubnets: SelectedSubnetType[]
) => {
  const initialStakedAmount = totalTao - availableBalance;
  const initialAvailableToStakePercentage =
    totalTao !== 0 ? (initialStakedAmount / totalTao) * 100 : 0;
  const initialSliderState = calculateInitialSliderState(
    initialStakedAmount,
    initialSubnets
  );
  return {
    initialStakedAmount,
    initialAvailableToStakePercentage,
    initialSliderState,
  };
};

export const StakeContextProvider = ({
  children,
  totalTao,
  availableBalance,
  initialSubnets = [],
  stakingType,
}: StakeContextProviderProps) => {
  const {
    initialStakedAmount,
    initialAvailableToStakePercentage,
    initialSliderState,
  } = getInitialState(totalTao, availableBalance, initialSubnets);

  const { maxSlippage, updateMaxSlippage } = useMaxSlippage();
  const { antiMevSlippageValue, setAntiMevSlippageValue } =
    useAntiMevSlippage();

  const [selectedSubnets, setSelectedSubnets] =
    useState<SelectedSubnetType[]>(initialSubnets);

  const [sliderState, setSliderState] =
    useState<Record<string, SliderState>>(initialSliderState);

  const [availableToDelegatePercentage, setAvailableToDelegatePercentage] =
    useState(initialAvailableToStakePercentage);

  const amountToDelegateTao = totalTao * (availableToDelegatePercentage / 100);

  const updateAvailableToDelegatePercentage = useCallback(
    (percentage: number) => {
      const clampedPercentage = Math.min(Math.max(percentage, 0), 100);
      setAvailableToDelegatePercentage(clampedPercentage);
    },
    []
  );

  const updateAvailableToDelegateValue = useCallback(
    (value: number) => {
      const clampedValue = Math.min(Math.max(value, 0), totalTao);
      const percentage =
        clampedValue === 0 ? 0 : (clampedValue / totalTao) * 100;
      setAvailableToDelegatePercentage(percentage);
    },
    [totalTao]
  );

  const updateSliderValueAutoBalance = useCallback(
    (selectedSubnetId: string, sliderValue: number) => {
      // Get all unlocked sliders except current one
      const unlockedSliders = Object.entries(sliderState).filter(
        ([id, value]) => id !== selectedSubnetId && !value.isLocked
      );

      // Calculate total of locked sliders
      const lockedTotal = Object.values(sliderState).reduce(
        (acc, value) => acc + (value.isLocked ? value.sliderValue : 0),
        0
      );

      // Calculate max allowed value for this slider
      const maxAllowed = 100 - lockedTotal;
      const clampedValue = Math.min(Math.max(sliderValue, 0), maxAllowed);

      // If no other unlocked sliders, just update this one
      if (unlockedSliders.length === 0) {
        setSliderState((prev) => ({
          ...prev,
          [selectedSubnetId]: {
            ...prev[selectedSubnetId],
            sliderValue: clampedValue,
          },
        }));
        return;
      }

      // Calculate current proportions of other unlocked sliders
      const currentUnlockedTotal = unlockedSliders.reduce(
        (acc, [_, value]) => acc + value.sliderValue,
        0
      );

      const proportions = unlockedSliders.map(([id, value]) => ({
        id,
        proportion:
          currentUnlockedTotal > 0
            ? value.sliderValue / currentUnlockedTotal
            : 1 / unlockedSliders.length,
      }));

      // Calculate remaining percentage to distribute
      const remainingPercentage = 100 - lockedTotal - clampedValue;

      // Update all sliders
      setSliderState((prev) => {
        const newState = { ...prev };
        // Update the changed slider
        newState[selectedSubnetId] = {
          ...prev[selectedSubnetId],
          sliderValue: clampedValue,
        };

        // Distribute remaining percentage among other unlocked sliders
        proportions.forEach(({ id, proportion }) => {
          newState[id] = {
            ...prev[id],
            sliderValue: Math.max(0, remainingPercentage * proportion),
          };
        });

        return newState;
      });
    },
    [sliderState]
  );

  const updateSliderLocked = useCallback(
    (selectedSubnetId: string, isLocked: boolean) => {
      setSliderState((prev) => {
        return {
          ...prev,
          [selectedSubnetId]: { ...prev[selectedSubnetId], isLocked },
        };
      });
    },
    []
  );

  const updateSliderValue = useCallback(
    (selectedSubnetId: string, sliderValue: number) => {
      if (stakingType === 'manual') {
        setSliderState((prev) => ({
          ...prev,
          [selectedSubnetId]: {
            ...prev[selectedSubnetId],
            sliderValue,
            isLocked: false, // Remove lock if value changes because it could have been locked by the auto balance
          },
        }));
      } else {
        updateSliderValueAutoBalance(selectedSubnetId, sliderValue);
      }
    },
    [stakingType, updateSliderValueAutoBalance]
  );

  const addSubnet = useCallback(
    (subnetToAdd: AddSubnetParam) => {
      // Generate id
      const selectedSubnetId = getSelectedSubnetId(subnetToAdd);

      // Check if subnet already exists
      if (
        selectedSubnets.find((s) => s.selectedSubnetId === selectedSubnetId)
      ) {
        notify.error('Subnet/validator combination already selected');
        return false;
      }

      // Get initial value for subnet/validator being added
      const subnetInitialValue =
        initialSubnets.find((s) => s.selectedSubnetId === selectedSubnetId)
          ?.initialAmount ?? 0;

      // Add subnet
      const selectedSubnet = {
        selectedSubnetId: getSelectedSubnetId(subnetToAdd),
        initialAmount: subnetInitialValue,
        ...subnetToAdd,
      };
      setSelectedSubnets((prev) => [...prev, selectedSubnet]);

      updateSliderValue(selectedSubnet.selectedSubnetId, subnetInitialValue);

      updateSliderLocked(selectedSubnet.selectedSubnetId, false);

      return true;
    },
    [initialSubnets, selectedSubnets, updateSliderValue, updateSliderLocked]
  );

  const removeSubnet = useCallback((selectedSubnetId: string) => {
    setSelectedSubnets((prev) =>
      prev.filter((s) => s.selectedSubnetId !== selectedSubnetId)
    );
    setSliderState((prev) => {
      const { [selectedSubnetId]: _, ...newState } = prev;
      return newState;
    });
  }, []);

  const updateAmountToDelegateToSubnet = useCallback(
    (selectedSubnetId: string, amount: number) => {
      const clampedAmount = Math.min(Math.max(amount, 0), amountToDelegateTao);
      const percentage = (clampedAmount / amountToDelegateTao) * 100;
      updateSliderValue(selectedSubnetId, percentage);
    },
    [amountToDelegateTao, updateSliderValue]
  );

  const getInitialAmountForSubnet = useCallback(
    (selectedSubnetId: string) => {
      return (
        selectedSubnets.find((s) => s.selectedSubnetId === selectedSubnetId)
          ?.initialAmount ?? 0
      );
    },
    [selectedSubnets]
  );

  const value = useMemo<StakeContextValue>(() => {
    return {
      totalTao,
      availableBalance,
      initialStakedAmount,
      maxSlippage,
      updateMaxSlippage,
      selectedSubnets,
      addSubnet,
      removeSubnet,
      sliderState,
      updateSliderValue,
      updateAmountToDelegateToSubnet,
      updateSliderLocked,
      availableToDelegatePercentage,
      updateAvailableToDelegatePercentage,
      updateAvailableToDelegateValue,
      amountToDelegateTao,
      getInitialAmountForSubnet,
      stakingType,
      isMentat: stakingType === 'mentat',
      isSimple: stakingType === 'simple',
      antiMevSlippageValue,
      setAntiMevSlippageValue,
    };
  }, [
    totalTao,
    availableBalance,
    initialStakedAmount,
    maxSlippage,
    updateMaxSlippage,
    selectedSubnets,
    addSubnet,
    removeSubnet,
    sliderState,
    updateSliderValue,
    updateAmountToDelegateToSubnet,
    updateSliderLocked,
    availableToDelegatePercentage,
    updateAvailableToDelegatePercentage,
    updateAvailableToDelegateValue,
    amountToDelegateTao,
    getInitialAmountForSubnet,
    stakingType,
    antiMevSlippageValue,
    setAntiMevSlippageValue,
  ]);

  return (
    <StakeContext.Provider value={value}>{children}</StakeContext.Provider>
  );
};

export const useStakeContext = () => {
  const context = useContext(StakeContext);
  if (context === undefined) {
    throw new Error(
      'useStakeContext must be used within a StakeContextProvider'
    );
  }
  return context;
};

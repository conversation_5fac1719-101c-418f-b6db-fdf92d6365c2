import { z } from 'zod';

export type SelectedSubnetType = {
  selectedSubnetId: string; // Composite of netuid and validatorSs58
  netuid: number;
  validatorSs58: string;
  initialAmount: number;
};

export type AddSubnetParam = Pick<
  SelectedSubnetType,
  'netuid' | 'validatorSs58'
>;

export const stakingTypeSchema = z.enum([
  'simple',
  'manual',
  'auto-balance',
  'mentat',
]);

export type StakingTypes = z.infer<typeof stakingTypeSchema>;

export type CurrentPositionItem = {
  netuid: number;
  subnetName: string;
  subnetSymbol: string;
  validatorHotkey: string;
  validatorName: string;
  balanceAsTao: number;
  balanceAsAlpha: number;
};

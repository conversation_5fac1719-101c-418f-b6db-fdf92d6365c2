import { useCallback } from 'react';
import { BigNumber } from 'bignumber.js';
import { notify } from '@repo/ui/lib';
import { useStakeContext } from '@/app/stake/lib/contexts/stake-context';
import { getIdsFromSelectedSubnetId } from '@/app/stake/lib/helpers';
import { useAccountContext } from '@/contexts/chain-and-wallet/account-context';
import { useChainAndWalletContext } from '@/contexts/chain-and-wallet/chain-and-wallet-context';
import { MIN_STAKING_AMOUNT_TAO } from '@/contexts/chain-and-wallet/constants';
import {
  useSlippageCalculator,
  useTaoToAlphaConverterWithChainData,
} from '@/contexts/chain-and-wallet/hooks';
import type {
  HandleSubmitTransactionParams,
  StakeTransactionSummary,
} from '@/contexts/chain-and-wallet/types';
import { useStakeFeeCalculator } from '@/contexts/chain-and-wallet/use-stake-calculator';
import { useTransactionSubmitter } from '@/contexts/chain-and-wallet/use-transaction-submitter';
import {
  useDtaoSubnetPools,
  useSubnetLookup,
  useSubnets,
  useValidatorLookup,
  useValidators,
} from '@/lib/hooks/global-hooks';
import { usePriceLimitCalculator } from '@/lib/hooks/staking-hooks';
import { log } from '@/lib/utils';
import { useChainAmountConverter } from '@/lib/utils/conversion/conversion-utils';
import { roundDown } from '@/lib/utils/number-utils';

export const useSubnetPickerOptions = () => {
  const poolQuery = useDtaoSubnetPools();
  const subnetQuery = useSubnets();
  const validatorQuery = useValidators();
  const isLoading =
    poolQuery.isLoading || subnetQuery.isLoading || validatorQuery.isLoading;
  const isSuccess =
    poolQuery.isSuccess && subnetQuery.isSuccess && validatorQuery.isSuccess;

  return {
    poolQuery,
    subnetQuery,
    validatorQuery,
    isLoading,
    isSuccess,
  };
};

export const useSubnetSummary = () => {
  const { sliderState, amountToDelegateTao, getInitialAmountForSubnet } =
    useStakeContext();
  const { getSubnetName, getSubnetSymbol } = useSubnetLookup();
  const { getValidatorName } = useValidatorLookup();
  const { convertTaoToAlpha } = useTaoToAlphaConverterWithChainData();
  const { slippageCalculator } = useSlippageCalculator();
  const { getAddStakePriceLimit, getRemoveStakePriceLimit } =
    usePriceLimitCalculator();

  const getSubnetSummary = (selectedSubnetId: string) => {
    const { netuid, validatorSs58 } =
      getIdsFromSelectedSubnetId(selectedSubnetId);
    const subnetAlphaSymbol = getSubnetSymbol(netuid);

    const { sliderValue, isLocked } = sliderState[selectedSubnetId];
    const subnetAmountToDelegateTao = amountToDelegateTao * (sliderValue / 100);

    const subnetAmountToDelegateAlpha = convertTaoToAlpha(
      netuid,
      subnetAmountToDelegateTao
    ).shiftedBy(-9);

    const initialAmount = getInitialAmountForSubnet(selectedSubnetId);
    const amountToDelegateDifference =
      subnetAmountToDelegateTao - initialAmount;

    const amountToDelegateDifferenceAlpha = convertTaoToAlpha(
      netuid,
      amountToDelegateDifference,
      { inputFormat: 'human', outputFormat: 'human' }
    ).toNumber();

    const slippageCalcResult = slippageCalculator(
      netuid,
      amountToDelegateDifference
    );
    const slippage = BigNumber(slippageCalcResult?.slippage ?? 0)
      .abs()
      .toNumber();

    const subnetName = getSubnetName(netuid);
    const validatorName = getValidatorName(validatorSs58);

    return {
      selectedSubnetId,
      netuid,
      validatorSs58,
      subnetName,
      subnetAlphaSymbol,
      validatorName,
      sliderValue,
      isLocked,
      amountToDelegateDifference,
      amountToDelegateDifferenceAlpha,
      subnetAmountToDelegateTao,
      subnetAmountToDelegateAlpha,
      slippage,
      getAddStakePriceLimit,
      getRemoveStakePriceLimit,
    };
  };

  return {
    getSubnetSummary,
  };
};

export type SubnetSummary = ReturnType<
  ReturnType<typeof useSubnetSummary>['getSubnetSummary']
>;

export const useSubnetSummaries = () => {
  const { selectedSubnets } = useStakeContext();
  const { getSubnetSummary } = useSubnetSummary();

  const subnetSummaries = selectedSubnets.map((subnet) =>
    getSubnetSummary(subnet.selectedSubnetId)
  );

  const subnetSummariesWithValidDifference = subnetSummaries.filter((subnet) =>
    BigNumber(subnet.amountToDelegateDifference)
      .abs()
      .isGreaterThan(MIN_STAKING_AMOUNT_TAO)
  );

  return {
    subnetSummaries,
    subnetSummariesWithValidDifference,
  };
};

export const useSlippageChecker = () => {
  const { maxSlippage } = useStakeContext();
  const { subnetSummariesWithValidDifference } = useSubnetSummaries();

  const subnetsWithSlippageErrors = subnetSummariesWithValidDifference.filter(
    (subnet) => subnet.slippage > maxSlippage
  );

  return {
    hasSlippageErrors: subnetsWithSlippageErrors.length > 0,
    slippageErrors: subnetsWithSlippageErrors,
  };
};

export const useTransactionBuilder = () => {
  const {
    state: { api },
  } = useChainAndWalletContext();

  const buildTransaction = (
    stakeTransactionSummaries: StakeTransactionSummary[]
  ) => {
    if (!api) {
      notify.error('Could not find connection');
      return null;
    }

    log('DEBUG', { stakeTransactionSummaries });
    const txs = stakeTransactionSummaries
      .map((stakeTransactionSummary) => {
        switch (stakeTransactionSummary.type) {
          case 'add_stake':
            return api.tx.subtensorModule.addStakeLimit(
              stakeTransactionSummary.destinationHotkey,
              stakeTransactionSummary.destinationNetuid,
              stakeTransactionSummary.taoAmount,
              stakeTransactionSummary.priceLimit,
              false
            );

          case 'remove_stake':
            return api.tx.subtensorModule.removeStakeLimit(
              stakeTransactionSummary.removeFromHotkey,
              stakeTransactionSummary.removeFromNetuid,
              stakeTransactionSummary.alphaAmount,
              stakeTransactionSummary.priceLimit,
              false
            );

          default:
            return null;
        }
      })
      .filter((tx) => tx !== null);

    log('DEBUG', { txsLength: txs.length });

    if (txs.length === 0) {
      notify.error('No transactions to submit');
      return null;
    }

    // If there is only one transaction, then don't batch it
    // Otherwise batch all the transactions
    const tx = txs.length > 1 ? api.tx.utility.batchAll(txs) : txs[0];

    return { tx };
  };

  return {
    buildTransaction,
  };
};

type HandleStakeConfirmClickParams = {
  stakeTransactionSummaries: StakeTransactionSummary[];
} & HandleSubmitTransactionParams;

export const useStakingLogic = () => {
  const { buildTransaction } = useTransactionBuilder();
  const {
    state: { api },
  } = useChainAndWalletContext();
  const { submitTx, isPending, txStatus } = useTransactionSubmitter();

  const handleStakeConfirmClick = async ({
    stakeTransactionSummaries,
    ...params
  }: HandleStakeConfirmClickParams) => {
    if (!api) {
      return;
    }

    const buildTransactionResult = buildTransaction(stakeTransactionSummaries);
    if (!buildTransactionResult) {
      notify.error('Could not build transaction');
      return;
    }

    // Submit the transaction
    await submitTx(buildTransactionResult.tx, params);
  };

  return {
    handleStakeConfirmClick,
    isPending,
    txStatus,
  };
};

const emptyTransactionFee = {
  totalFee: 0,
  transactionFee: 0,
  stakingFeesTotal: 0,
  stakingFees: [],
};

export const useTransactionFeeCalculator = () => {
  const { buildTransaction } = useTransactionBuilder();
  const { calculateTransactionFee } = useTransactionSubmitter();
  const { calculateStakeFee } = useStakeFeeCalculator();

  const getFees = async (
    stakeTransactionSummaries: StakeTransactionSummary[]
  ) => {
    const buildTransactionResult = buildTransaction(stakeTransactionSummaries);
    if (!buildTransactionResult?.tx) {
      notify.error('Could not build transaction');
      return emptyTransactionFee;
    }

    const tx = buildTransactionResult.tx;
    const getTransactionFee = await calculateTransactionFee(tx);

    if (getTransactionFee === null) {
      notify.error('Could not get estimated fee from the chain.');
    }

    const transactionFee = getTransactionFee ?? 0;
    const { stakingFeesTotal, stakingFees } = await calculateStakeFee(
      stakeTransactionSummaries
    );

    log('DEBUG', {
      transactionFee,
      stakingFeesTotal,
    });

    const totalFee = transactionFee + stakingFeesTotal;
    return {
      totalFee,
      transactionFee,
      stakingFeesTotal,
      stakingFees,
    };
  };

  return {
    getFees,
  };
};

export const useTransactionTotals = () => {
  const { totalTao, availableBalance, initialStakedAmount } = useStakeContext();
  const initialStakedAmountRoundedDown = roundDown(initialStakedAmount, 5);
  const { getHumanFromChainAmount } = useChainAmountConverter();

  const getTransactionTotals = useCallback(
    (
      transactionFee: number,
      stakingFees: number[],
      transactionSummaries: StakeTransactionSummary[]
    ) => {
      const netChangeInTao = transactionSummaries
        .map((transactionSummary) => {
          return {
            stakeAmountTao:
              transactionSummary.type === 'add_stake'
                ? roundDown(
                    getHumanFromChainAmount(transactionSummary.taoAmount),
                    5
                  )
                : transactionSummary.type === 'remove_stake'
                  ? roundDown(
                      getHumanFromChainAmount(transactionSummary.taoAmount),
                      5
                    ) * -1
                  : 0,
          };
        })
        .reduce((acc, curr) => acc + curr.stakeAmountTao, 0);

      // The amount required to cover the amount being staked and the transaction fee
      const amountRequired =
        initialStakedAmountRoundedDown + netChangeInTao + transactionFee;

      const stakingFeesTotal = stakingFees.reduce((acc, fee) => acc + fee, 0);
      const totalFee = transactionFee + stakingFeesTotal;
      const resultingTotalStakedMinusFees = amountRequired - totalFee;
      const isTotalStakedTooMuch = amountRequired > totalTao;
      const hasEnoughBalanceToCoverTransactionFee =
        availableBalance >= transactionFee;

      return {
        hasEnoughBalanceToCoverTransactionFee,
        initialStakedAmount: initialStakedAmountRoundedDown,
        totalTao: roundDown(totalTao, 5),
        amountRequired,
        netChangeInTao,
        stakingFeesTotal,
        totalFee,
        resultingTotalStakedMinusFees: roundDown(
          resultingTotalStakedMinusFees,
          5
        ),
        isTotalStakedTooMuch,
      };
    },
    [
      availableBalance,
      getHumanFromChainAmount,
      initialStakedAmountRoundedDown,
      totalTao,
    ]
  );

  return {
    getTransactionTotals,
  };
};

const useUserStakeOnChain = () => {
  const {
    state: { delegateBalances },
  } = useAccountContext();

  const getUserStakeOnChain = (subnetId: number, validatorHotkey: string) => {
    const getStakeOnChain = delegateBalances.find(
      (balance) =>
        balance.netuid === subnetId && balance.hotkey === validatorHotkey
    );
    return getStakeOnChain;
  };

  return {
    getUserStakeOnChain,
  };
};

interface CalculateAlphaToUnstakeParam {
  netuid: number;
  validatorSs58: string;
}

export const useAmountToUnstakeCalculator = () => {
  const { getUserStakeOnChain } = useUserStakeOnChain();
  const { convertTaoToAlpha } = useTaoToAlphaConverterWithChainData();
  const { convertAmount } = useChainAmountConverter();

  const calculateAlphaToUnstake = (
    param: CalculateAlphaToUnstakeParam,
    amountInTao: number
  ) => {
    const amountToRemoveInTaoHumanFormat = BigNumber(amountInTao)
      .abs()
      .toNumber();

    let alphaToUnstakeInChainFormat = convertTaoToAlpha(
      param.netuid,
      amountToRemoveInTaoHumanFormat
    ).toNumber();

    // The UI is based on changing Tao values
    // The unstake function is based on alpha
    // So we work out the alpha to unstake based on the tao value from the UI times the price
    // The trouble is the price is potentially changing every 12 seconds
    // This can lead to the alpha amount trying to be unstaked for this calculation being more than the user has staked
    // We have the amount the user has staked from the initial page load or from the last time the user pressed Reset
    // So check the calculated amount to unstake is not greater than the amount the user has staked
    // If it is greater, then limit it to the amount the user has staked
    // Otherwise proceed with the calculated amount

    const userStakeOnChain = getUserStakeOnChain(
      param.netuid,
      param.validatorSs58
    );
    if (!userStakeOnChain) {
      const errorMessage = `Could not find user stake on chain for subnet: ${param.netuid} validator: ${param.validatorSs58}`;
      console.error(errorMessage);
      notify.error(errorMessage);
      return null;
    }

    const alphaStakedOnChain = convertAmount(
      userStakeOnChain.alphaAmount
    ).toNumber();

    if (alphaToUnstakeInChainFormat > alphaStakedOnChain) {
      log(
        '👀 Trying to remove more stake than user has. Limiting to user stake.',
        {
          netuid: param.netuid,
          alphaToUnstakeInChainFormat,
          alphaStakedOnChain,
        }
      );
      alphaToUnstakeInChainFormat = alphaStakedOnChain;
    }

    return alphaToUnstakeInChainFormat;
  };

  return {
    calculateAlphaToUnstake,
  };
};

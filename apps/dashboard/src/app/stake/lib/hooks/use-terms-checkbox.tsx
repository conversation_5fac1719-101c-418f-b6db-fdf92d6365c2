import { useState } from 'react';
import { Checkbox, Text } from '@repo/ui/components';

export const useTermsCheckbox = () => {
  const [termsAccepted, setTermsAccepted] = useState(false);

  const renderTermsCheckbox = (wasSuccessful: boolean) => {
    if (wasSuccessful) return null;

    return (
      <div className='mb-6 mt-4 flex flex-col gap-4'>
        <div className='flex items-start gap-3'>
          <Checkbox
            id='terms'
            checked={termsAccepted}
            onCheckedChange={() => {
              setTermsAccepted(!termsAccepted);
            }}
          />
          <div className='grid gap-1.5 leading-none'>
            <label htmlFor='terms' className='cursor-pointer'>
              <Text level='buttonSmall' className='text-white/60'>
                I understand and agree to the terms and conditions.
              </Text>
            </label>
          </div>
        </div>
      </div>
    );
  };

  return {
    termsAccepted,
    setTermsAccepted,
    renderTermsCheckbox,
  };
};

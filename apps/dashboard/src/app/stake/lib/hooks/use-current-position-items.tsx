import { useCallback } from 'react';
import { useSubnetSummary } from './hooks';
import { useStakeContext } from '@/app/stake/lib/contexts/stake-context';
import { getSelectedSubnetId } from '@/app/stake/lib/helpers';
import type { CurrentPositionItem } from '@/app/stake/lib/types';
import { roundDown } from '@/lib/utils/number-utils';

export const useCurrentPositionItems = ({
  shouldRoundAmounts = true,
}: {
  shouldRoundAmounts?: boolean;
} = {}) => {
  const { selectedSubnets } = useStakeContext();
  const { getSubnetSummary } = useSubnetSummary();

  const getCurrentPositionItems = useCallback(() => {
    const normalisedData: CurrentPositionItem[] = selectedSubnets.map((s) => {
      const subnetSummary = getSubnetSummary(getSelectedSubnetId(s));
      return {
        netuid: s.netuid,
        subnetName: subnetSummary.subnetName,
        subnetSymbol: subnetSummary.subnetAlphaSymbol,
        validatorHotkey: s.validatorSs58,
        validatorName: subnetSummary.validatorName,
        balanceAsTao: shouldRoundAmounts
          ? roundDown(subnetSummary.subnetAmountToDelegateTao, 5)
          : subnetSummary.subnetAmountToDelegateTao,
        balanceAsAlpha: shouldRoundAmounts
          ? roundDown(subnetSummary.subnetAmountToDelegateAlpha.toNumber(), 5)
          : subnetSummary.subnetAmountToDelegateAlpha.toNumber(),
      };
    });

    return normalisedData;
  }, [getSubnetSummary, shouldRoundAmounts, selectedSubnets]);

  return { getCurrentPositionItems };
};

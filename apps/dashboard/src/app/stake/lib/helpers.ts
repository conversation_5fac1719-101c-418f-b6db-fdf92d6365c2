import type { AddSubnetParam } from './types';

const SELECTED_SUBNET_ID_SEPARATOR = '---';
export const getSelectedSubnetId = (subnet: AddSubnetParam) => {
  return `${subnet.netuid}${SELECTED_SUBNET_ID_SEPARATOR}${subnet.validatorSs58}`;
};

export const getIdsFromSelectedSubnetId = (selectedSubnetId: string) => {
  const [netuid, validatorSs58] = selectedSubnetId.split(
    SELECTED_SUBNET_ID_SEPARATOR
  );
  return { netuid: Number(netuid), validatorSs58 };
};

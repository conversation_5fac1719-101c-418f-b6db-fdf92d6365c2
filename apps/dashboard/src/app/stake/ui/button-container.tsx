import { BiReset } from 'react-icons/bi';
import { Button } from '@repo/ui/components';
import { StakeButton } from './stake/stake-button';
import { useAccountContext } from '@/contexts/chain-and-wallet/account-context';

export const PrimaryActionButtonsContainer = () => {
  const { fetchInfo } = useAccountContext();
  return (
    <div className='mb-6 flex flex-row justify-between'>
      <Button
        size='lg'
        onClick={() => {
          void fetchInfo();
        }}
      >
        Reset
        <BiReset className='ml-2 h-5 w-5' />
      </Button>

      <StakeButton />
    </div>
  );
};

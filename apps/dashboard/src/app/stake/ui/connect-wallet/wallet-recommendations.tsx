import Image from 'next/image';
import { HiOutlineExternalLink } from 'react-icons/hi';
import { Card, Text } from '@repo/ui/components';

export const WalletRecommendations = () => {
  return (
    <div className='flex flex-col gap-6 self-start rounded-xl bg-[#181818] p-6'>
      <div>
        <Text level='bodyLarge' className='text-white/60'>
          Don&apos;t have a wallet to connect?
        </Text>
        <Text level='bodyLarge' className='text-white/60'>
          We recommend using any of these.
        </Text>
      </div>
      <div className='flex flex-col gap-4'>
        {wallets.map((wallet) => (
          <Card
            key={wallet.name}
            className='border border-[#323232] bg-[#171717] p-2 shadow-[0px_5px_23.8px_0px_rgba(0,0,0,0.25)]'
          >
            <div className='flex flex-row items-center justify-between p-4'>
              <div className='flex h-[64px] w-[64px] overflow-hidden rounded-full'>
                <Image
                  src={wallet.icon}
                  alt={wallet.name}
                  width={64}
                  height={64}
                />
              </div>

              <Text level='headerMedium'>{wallet.name}</Text>
              <a
                className='rounded-xl border border-[#323232] bg-[#1D1D1D] p-4 shadow-[0px_5px_23.8px_0px_rgba(0,0,0,0.25)]'
                href={wallet.url}
                target='_blank'
                rel='noopener noreferrer'
              >
                <HiOutlineExternalLink className='size-5' />
              </a>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};

const wallets = [
  {
    name: 'Talisman',
    icon: '/images/icons/talisman-icon.png',
    url: 'https://www.talisman.xyz/',
  },
  {
    name: 'Polkadot',
    icon: '/images/icons/polkadot-icon.svg',
    url: 'https://polkadot.js.org/extension/',
  },
  {
    name: 'SubWallet',
    icon: '/images/icons/subwallet-icon.svg',
    url: 'https://subwallet.app/',
  },
];

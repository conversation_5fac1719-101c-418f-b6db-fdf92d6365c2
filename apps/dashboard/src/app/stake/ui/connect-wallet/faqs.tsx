import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Text,
} from '@repo/ui/components';

const faqs = [
  {
    question: 'What is delegation on the Bittensor Network?',
    answer:
      'Delegation allows you to stake your TAO with validators to earn rewards without running a validator yourself.',
  },
  {
    question: 'How do I delegate my TAO?',
    answer:
      'Connect your wallet, choose a validator, and specify the amount you want to delegate.',
  },
  {
    question: 'Are there restrictions to delegation?',
    answer: 'Yes, there is a minimum delegation amount and a lock-up period.',
  },
  {
    question: 'Is delegation safe?',
    answer: 'Yes, delegation is secure as your TAO remains in your control.',
  },
  {
    question: 'Who should I delegate my tao with?',
    answer:
      'Research validators based on their performance, uptime, and commission rates.',
  },
  {
    question: 'I have another question:',
    answer:
      'Visit our documentation or join our Discord community for more information.',
  },
];

export const Faqs = () => {
  return (
    <div className='flex flex-col gap-6'>
      <Text level='headerMedium'>Staking FAQs</Text>
      <Accordion type='single' collapsible className='w-full'>
        {faqs.map((faq) => (
          <FaqItem key={faq.question} q={faq.question} a={faq.answer} />
        ))}
      </Accordion>
    </div>
  );
};

const FaqItem = ({ q, a }: { q: string; a: string }) => {
  return (
    <AccordionItem value={q}>
      <AccordionTrigger className='hover:no-underline'>
        <Text
          level='headerExtraSmall'
          className='sm:leading-24 text-left font-normal text-white/60 sm:text-[20px]'
        >
          {q}
        </Text>
      </AccordionTrigger>
      <AccordionContent>{a}</AccordionContent>
    </AccordionItem>
  );
};

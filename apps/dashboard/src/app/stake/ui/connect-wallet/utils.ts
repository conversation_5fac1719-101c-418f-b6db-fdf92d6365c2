import { useEffect } from 'react';
import { useChainAndWalletContext } from '@/contexts/chain-and-wallet/chain-and-wallet-context';
import { LOCAL_STORAGE_CURRENT_ACCOUNT } from '@/contexts/chain-and-wallet/constants';

export const useAutoReconnectWallets = () => {
  const {
    connectWallet,
    setCurrentAccount,
    state: { accounts, currentAccount },
  } = useChainAndWalletContext();

  useEffect(() => {
    if (
      !currentAccount &&
      localStorage.getItem(LOCAL_STORAGE_CURRENT_ACCOUNT)
    ) {
      connectWallet();
    }
  }, [connectWallet, currentAccount]);

  useEffect(() => {
    if (!currentAccount && accounts.length) {
      const current = localStorage.getItem(LOCAL_STORAGE_CURRENT_ACCOUNT);
      const account = current
        ? accounts.find((acc) => acc.address === current) ?? accounts[0]
        : accounts[0];
      setCurrentAccount(account);
    }
  }, [currentAccount, accounts, setCurrentAccount]);
};

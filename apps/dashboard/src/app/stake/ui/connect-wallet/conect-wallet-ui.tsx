import { Faqs } from './faqs';
import { WalletRecommendations } from './wallet-recommendations';
import { PageHeader } from '@/components/page-header';
import { ConnectWalletCard } from '@/components/wallet/connect-wallet-card';

export const ConnectWalletUi = () => {
  return (
    <div className='grid grid-cols-1 gap-12 lg:grid-cols-[minmax(0,1000px),400px]'>
      <div className='flex flex-col gap-12'>
        <div>
          <PageHeader title='Stake' />
          <div className='flex flex-col gap-8 py-0 pt-8 sm:py-8'>
            <div className='flex flex-col gap-12'>
              <ConnectWalletCard />
            </div>
          </div>
        </div>
        <div className='lg:hidden'>
          <WalletRecommendations />
        </div>
        <Faqs />
      </div>
      <div className='hidden lg:block'>
        <WalletRecommendations />
      </div>
    </div>
  );
};

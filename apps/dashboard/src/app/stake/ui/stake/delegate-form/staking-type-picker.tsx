'use client';

import type { ReactNode } from 'react';
import { memo, useMemo, useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  BiChevronDown,
  BiGitMerge,
  BiInfoCircle,
  BiListPlus,
} from 'react-icons/bi';
import {
  Text,
  TooltipContent,
  Tooltip,
  TooltipProvider,
  TooltipTrigger,
  Flex,
  DropdownMenu,
  Button,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  Link,
} from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { useStakeContext } from '@/app/stake/lib/contexts/stake-context';
import type { StakingTypes } from '@/app/stake/lib/types';
import { useAppEnv } from '@/lib/hooks/global-hooks';

const stakingTypeOptions: {
  type: StakingTypes;
  label: string;
  icon: ReactNode;
}[] = [
  {
    type: 'simple',
    label: 'Simple',
    icon: <BiListPlus className='-ml-0.5 text-lg' />,
  },
  {
    type: 'manual',
    label: 'Manual',
    icon: <BiListPlus className='-ml-0.5 text-lg' />,
  },
  {
    type: 'auto-balance',
    label: 'Balance',
    icon: <BiGitMerge className='-ml-0.5 text-lg' />,
  },
  {
    type: 'mentat',
    label: 'Automate',
    icon: <BiGitMerge className='-ml-0.5 text-lg' />,
  },
];

export const StakingTypePicker = memo(function StakingTypeSelector() {
  const { stakingType } = useStakeContext();
  const [isOpen, setIsOpen] = useState(false);
  const router = useRouter();

  const handleSelectionChange = (selection: StakingTypes) => {
    router.push(`/stake/${selection}`);
    setIsOpen(false);
  };

  const appEnv = useAppEnv();
  const stakingTypeOptionsToUse = useMemo(() => {
    if (appEnv !== 'production') {
      return stakingTypeOptions;
    }
    return stakingTypeOptions.filter((opt) => opt.type !== 'mentat');
  }, [appEnv]);

  return (
    <>
      <Flex className='w-full justify-between sm:hidden'>
        <Flex className='items-center gap-2'>
          <Text level='bodyMedium'>Staking Type</Text>
          <StakingTypeTooltip />
        </Flex>
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild>
            <Button
              size='sm'
              variant='cta3'
              className={cn('h-10 text-white/60')}
              onClick={() => {
                setIsOpen(!isOpen);
              }}
            >
              <Text level='labelMedium' className='text-label-secondary'>
                {
                  stakingTypeOptionsToUse.find(
                    (option) => option.type === stakingType
                  )?.label
                }
              </Text>
              <BiChevronDown
                aria-hidden='true'
                className={cn(
                  'pointer-events-none col-start-1 row-start-1 size-6 self-center justify-self-end transition-transform sm:size-4',
                  isOpen && 'rotate-180'
                )}
              />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end' className='h-96 overflow-y-scroll'>
            {stakingTypeOptionsToUse.map((option) => (
              <DropdownMenuItem
                key={option.type}
                onClick={() => {
                  handleSelectionChange(option.type);
                }}
              >
                <span>{option.label}</span>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </Flex>

      <Flex
        col
        className='hidden items-center gap-2 rounded-2xl border border-[#323232] bg-[#1D1D1D] p-1 sm:flex sm:h-fit sm:w-fit sm:flex-row'
      >
        {stakingTypeOptionsToUse.map((option) => (
          <StakingTypePickerButton
            key={option.type}
            selectedType={stakingType}
            onSelectionChange={handleSelectionChange}
            option={option}
          />
        ))}
        <div className='hidden items-center sm:flex'>
          <StakingTypeTooltip />
        </div>
      </Flex>
    </>
  );
});

const StakingTypeTooltip = () => {
  const appEnv = useAppEnv();
  const [open, setOpen] = useState(false);
  return (
    <TooltipProvider delayDuration={0}>
      <Tooltip open={open}>
        <TooltipTrigger asChild>
          <button
            type='button'
            className='cursor-pointer'
            onMouseEnter={() => {
              setOpen(true);
            }}
            onMouseLeave={() => {
              setOpen(false);
            }}
            onTouchStart={(e) => {
              e.stopPropagation();
              setOpen(!open);
            }}
          >
            <BiInfoCircle size={20} className='text-white/60' />
          </button>
        </TooltipTrigger>

        <TooltipContent side='bottom' align='center'>
          <div className='flex max-w-xs flex-col gap-2'>
            <Text level='bodyMedium'>
              <span className='text-accent-1'>Simple</span>
              <br />
              Create a simple list of stake/unstake transactions.
            </Text>
            <Text level='bodyMedium'>
              <span className='text-accent-1'>Manual</span>
              <br />
              Control the amount you stake to each subnet manually via the
              slider interface.
            </Text>
            <Text level='bodyMedium'>
              <span className='text-accent-1'>Balance</span>
              <br />
              When you change the amount you stake to one subnet, your other
              stakes are balanced automatically based on the proportion staked
              to those subnets.
            </Text>
            {appEnv !== 'production' && (
              <Text level='bodyMedium'>
                <span className='text-accent-1'>Automate</span>
                <br />
                Pick your strategy and let{' '}
                <Link
                  href='https://mentatminds.com'
                  target='_blank'
                  rel='noopener noreferrer'
                  className='text-accent-1'
                >
                  Mentat Minds
                </Link>{' '}
                do the work for you.
              </Text>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

const StakingTypePickerButton = ({
  selectedType,
  onSelectionChange,
  option,
}: {
  selectedType: StakingTypes;
  onSelectionChange: (selection: StakingTypes) => void;
  option: (typeof stakingTypeOptions)[number];
}) => {
  const { type, label, icon } = option;
  return (
    <Text
      level='buttonSmall'
      className={cn(
        'flex w-full cursor-pointer flex-row items-center justify-start gap-2 whitespace-nowrap rounded-xl border px-2 py-1 duration-700 sm:w-fit sm:flex-1 sm:justify-center',
        selectedType === type
          ? ' border-[#00DBBC] !bg-[#00DBBC1A] text-white'
          : 'border-transparent text-[#777777]'
      )}
      onClick={() => {
        onSelectionChange(type);
      }}
    >
      <span
        className={cn(
          'flex h-6 w-6 items-center justify-center',
          selectedType === type ? 'text-white' : 'text-[#777777]'
        )}
      >
        {icon}
      </span>
      {label}
    </Text>
  );
};

import { useState } from 'react';
import { z } from 'zod';
import { notify } from '@repo/ui/lib';
import { useStakeContext } from '@/app/stake/lib/contexts/stake-context';
import { TaoBadge } from '@/app/stake/ui/stake/components';
import { formatNumber2dp } from '@/lib/utils';
import { roundDown } from '@/lib/utils/number-utils';

const inputId = 'available-to-delegate-input';

export const AvailableToDelegate = () => {
  const { totalTao, amountToDelegateTao, updateAvailableToDelegateValue } =
    useStakeContext();

  const [isEditing, setIsEditing] = useState(false);
  const [inputValue, setInputValue] = useState('');

  const formattedAmountToDelegateTao = roundDown(amountToDelegateTao, 5);

  return isEditing ? (
    <TaoBadge>
      <input
        id={inputId}
        type='text'
        value={inputValue}
        onBlur={() => {
          setIsEditing(false);
        }}
        onChange={(e) => {
          setInputValue(e.target.value);
        }}
        onKeyDown={(e) => {
          if (e.key === 'Enter') {
            try {
              const schema = z.number().min(0);
              const value = schema.parse(Number(inputValue));
              updateAvailableToDelegateValue(value);
              setIsEditing(false);
            } catch (err) {
              notify.error(
                `Please enter a number between 0 and ${formatNumber2dp(totalTao)}`
              );
            }
          } else if (e.key === 'Escape') {
            setIsEditing(false);
          }
        }}
        className='w-[1ch] max-w-[100px] bg-transparent text-right focus:outline-none'
        style={{ width: `${Math.max(inputValue.length, 1)}ch` }}
        autoComplete='off'
      />
    </TaoBadge>
  ) : (
    <TaoBadge
      onClick={() => {
        setIsEditing(true);
        setInputValue(formattedAmountToDelegateTao.toString());
        setTimeout(() => {
          const input = document.getElementById(inputId);
          if (input) {
            input.focus();
          }
        }, 250);
      }}
    >
      {formattedAmountToDelegateTao}τ
    </TaoBadge>
  );
};

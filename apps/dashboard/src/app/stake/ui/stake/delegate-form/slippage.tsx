import { useState } from 'react';
import { BiInfoCircle, Bi<PERSON>heck, BiX } from 'react-icons/bi';
import { z } from 'zod';
import {
  Text,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  Button,
  Flex,
} from '@repo/ui/components';
import { cn, notify } from '@repo/ui/lib';
import { SlippageAntiMevDropdown } from './slippage-anti-mev-dropdown';
import { useStakeContext } from '@/app/stake/lib/contexts/stake-context';
import { useSlippageHelper } from '@/lib/hooks/staking-hooks';

export const SlippageConfig = () => {
  const { maxSlippage } = useStakeContext();
  const { getSlippageTextClassName, slippageBoundaries } = useSlippageHelper();
  return (
    <TooltipProvider>
      <Flex col className='w-full justify-end gap-4 sm:flex-row'>
        <Flex className='items-center justify-between gap-2 sm:justify-normal'>
          <Flex col className='gap-1'>
            <div className='flex items-center gap-2 text-white/60'>
              <Text level='labelMedium' className='text-white/60'>
                Max Slippage
              </Text>

              <Tooltip>
                <TooltipTrigger>
                  <BiInfoCircle size={20} />
                </TooltipTrigger>
                <TooltipContent>
                  <Text level='bodyMedium' className='max-w-[300px]'>
                    Maximum allowed slippage for your delegation transaction
                  </Text>
                </TooltipContent>
              </Tooltip>
            </div>

            {maxSlippage > slippageBoundaries.medium ? (
              <div>
                <Text
                  level='labelExtraSmall'
                  className={cn(
                    'text-[12px]',
                    getSlippageTextClassName(maxSlippage)
                  )}
                >
                  High Slippage
                </Text>
              </div>
            ) : null}
          </Flex>

          <div>
            <MaxSlippage />
          </div>
        </Flex>

        <SlippageAntiMevDropdown />
      </Flex>
    </TooltipProvider>
  );
};

const inputId = 'max-slippage-input';

export const MaxSlippage = () => {
  const { maxSlippage, updateMaxSlippage } = useStakeContext();
  const [isEditing, setIsEditing] = useState(false);
  const [inputValue, setInputValue] = useState('');

  const handleInputSubmit = () => {
    try {
      const schema = z.number().min(0).max(100);
      const value = schema.parse(Number(inputValue));
      updateMaxSlippage(value);
      setIsEditing(false);
    } catch (err) {
      notify.error('Please enter a number between 0 and 100');
    }
  };

  return isEditing ? (
    <div className='flex flex-row items-center gap-2'>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <input
              id={inputId}
              type='text'
              value={inputValue}
              enterKeyHint='done'
              onChange={(e) => {
                setInputValue(e.target.value);
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.code === 'Enter') {
                  handleInputSubmit();
                } else if (e.key === 'Escape') {
                  setIsEditing(false);
                }
              }}
              className='bg-input-primary text-label-secondary w-[50px] cursor-pointer rounded-lg p-3 text-center text-sm leading-4 focus:outline-none'
              autoComplete='off'
              autoCorrect='off'
              autoCapitalize='off'
              spellCheck='false'
            />
          </TooltipTrigger>
          <TooltipContent>
            <Text level='bodySmall'>Press enter to save</Text>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <Button
        variant='cta1'
        size='sm'
        onClick={() => {
          handleInputSubmit();
        }}
      >
        <BiCheck size={16} />
      </Button>
      <Button
        variant='cta3'
        size='sm'
        onClick={() => {
          setIsEditing(false);
        }}
      >
        <BiX size={16} />
      </Button>
    </div>
  ) : (
    <button
      type='button'
      className='bg-input-primary w-[90px] cursor-pointer rounded-lg px-3 py-3'
      onClick={() => {
        setIsEditing(true);
        setInputValue(maxSlippage.toString());
        setTimeout(() => {
          const input = document.getElementById(inputId);
          if (input) {
            input.focus();
          }
        }, 250);
      }}
    >
      <Text level='labelMedium' className='text-label-secondary'>
        {maxSlippage.toFixed(2)}%
      </Text>
    </button>
  );
};

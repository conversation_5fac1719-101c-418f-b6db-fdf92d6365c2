import { Slider, Text } from '@repo/ui/components';
import { AvailableToDelegate } from './available-to-delegate';
import { SlippageConfig } from './slippage';
import { StakingTypePicker } from './staking-type-picker';
import { useStakeContext } from '@/app/stake/lib/contexts/stake-context';
import { ProxiedToMentatMessage } from '@/app/stake/ui/stake/mentat-ui/proxied-to-mentat-message';
import { TaoBalanceSummary } from '@/app/stake/ui/stake/mentat-ui/tao-balance-summary';
import { SubnetPicker } from '@/app/stake/ui/stake/subnet-picker';
import { useTaoToDollarConverter } from '@/lib/hooks/price-hooks';
import { formatNumber2dp } from '@/lib/utils';
import { roundDown } from '@/lib/utils/number-utils';

export const StakingConfig = () => {
  const { isMentat, isSimple } = useStakeContext();

  return (
    <div className='sm:bg-hero-primary flex flex-col gap-10 rounded-3xl p-2 sm:px-8 sm:py-10'>
      <div className='flex flex-col justify-between gap-4 sm:flex-row sm:gap-6'>
        <div className='flex w-full flex-shrink-0 flex-col items-center justify-between gap-4 sm:flex-row sm:gap-2'>
          <StakingTypePicker />

          {!isMentat ? <SlippageConfig /> : <TaoBalanceSummary />}
        </div>
      </div>

      {!isMentat && !isSimple ? <SubnetPicker /> : null}

      {!isMentat ? <ProxiedToMentatMessage /> : null}

      {!isMentat && !isSimple ? <TaoSummaryAndGlobalSlider /> : null}
    </div>
  );
};

const TaoSummaryAndGlobalSlider = () => {
  const { totalTao, amountToDelegateTao } = useStakeContext();
  const { getDollarValue } = useTaoToDollarConverter();
  const formattedTotalTao = roundDown(totalTao, 5);
  const formattedTotalTaoUsd = formatNumber2dp(getDollarValue(totalTao));
  const formattedAmountToDelegateUsd = formatNumber2dp(
    getDollarValue(amountToDelegateTao)
  );

  return (
    <div className='flex flex-col gap-0 sm:gap-4'>
      <div className='flex flex-row items-center justify-between gap-6'>
        <div className='flex flex-shrink-0 flex-col gap-2'>
          <Text level='labelSmall' className='text-white/60'>
            TAO Balance
          </Text>
          <div className='flex items-baseline gap-2'>
            <Text level='headerMedium'>{formattedTotalTao}</Text>
            <Text level='headerExtraSmall'>τ</Text>
          </div>
          <Text level='labelLarge' className='text-white/60'>
            ${formattedTotalTaoUsd}
          </Text>
        </div>

        <div className='hidden w-full sm:flex'>
          <div className='flex-1'>
            <AvailableToDelegateSlider />
          </div>
        </div>

        <div className='flex flex-shrink-0 flex-col gap-2 text-right'>
          <Text level='labelSmall' className='text-right text-white/60'>
            Available to Delegate
          </Text>
          <div className='flex items-baseline justify-end gap-2'>
            <div className='text-right'>
              <div className='relative'>
                <AvailableToDelegate />
              </div>
            </div>
          </div>
          <Text level='labelLarge' className='text-right text-white/60'>
            ${formattedAmountToDelegateUsd}
          </Text>
        </div>
      </div>
      <div className='flex w-full sm:hidden'>
        <div className='flex-1'>
          <AvailableToDelegateSlider />
        </div>
      </div>
    </div>
  );
};

const AvailableToDelegateSlider = () => {
  const {
    availableBalance,
    availableToDelegatePercentage,
    updateAvailableToDelegatePercentage,
  } = useStakeContext();

  const formattedAvailableToDelegatePercentage = formatNumber2dp(
    availableToDelegatePercentage
  );

  return (
    <div className='flex flex-col gap-2'>
      <div className='flex flex-row items-center justify-center'>
        <Text level='labelSmall' className='text-white/60'>
          {formattedAvailableToDelegatePercentage}%
        </Text>
      </div>
      <Slider
        disabled={availableBalance <= 0}
        value={[availableToDelegatePercentage]}
        onValueChange={(values) => {
          updateAvailableToDelegatePercentage(values[0]);
        }}
        max={100}
        step={0.05}
        className='w-full py-4'
      />
    </div>
  );
};

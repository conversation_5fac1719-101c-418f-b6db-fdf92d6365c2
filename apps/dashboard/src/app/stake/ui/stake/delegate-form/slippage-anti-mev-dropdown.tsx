import { useState } from 'react';
import { BiInfoCircle } from 'react-icons/bi';
import {
  TooltipTrigger,
  Tooltip,
  TooltipContent,
  Text,
  Flex,
} from '@repo/ui/components';
import { AntiMevCombobox } from '@/components/anti-mev-combobox';

export const SlippageAntiMevDropdown = () => {
  return (
    <Flex className='w-full justify-between gap-1 sm:w-fit sm:justify-normal'>
      <div className='flex items-center gap-2 text-white/60'>
        <Text level='labelMedium' className='text-white/60'>
          Anti MEV
        </Text>
        <AntiMevTooltip />
      </div>

      <AntiMevCombobox />
    </Flex>
  );
};

const AntiMevTooltip = () => {
  const [open, setOpen] = useState(false);
  return (
    <Tooltip open={open}>
      <TooltipTrigger asChild>
        <button
          type='button'
          className='cursor-pointer'
          onMouseEnter={() => {
            setOpen(true);
          }}
          onMouseLeave={() => {
            setOpen(false);
          }}
          onTouchStart={(e) => {
            e.stopPropagation();
            setOpen(!open);
          }}
        >
          <BiInfoCircle size={20} />
        </button>
      </TooltipTrigger>
      <TooltipContent>
        <Text level='bodyMedium' className='max-w-[300px]'>
          Limit slippage amount to transaction slippage amount plus selected
          value. E.g. if your transaction slippage amount is 0.25%, and you
          select a tolerance of 0.05%, the maximum slippage will be set to 0.3%
          for your transaction.
        </Text>
      </TooltipContent>
    </Tooltip>
  );
};

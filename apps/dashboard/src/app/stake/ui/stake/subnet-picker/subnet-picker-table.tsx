import { memo, useMemo, useState } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
} from '@tanstack/react-table';
import type { SortingState } from '@tanstack/react-table';
import type { EnrichedSubnet } from '@repo/types/website-api-types';
import { DataTable } from '@repo/ui/components';
import { notify } from '@repo/ui/lib';
import { AddManualValidatorDialog } from './add-manual-validator-dialog';
import { useColumns } from './columns';
import { useStakeContext } from '@/app/stake/lib/contexts/stake-context';
import { useSubnetPickerOptions } from '@/app/stake/lib/hooks';
import { useValidatorLookup } from '@/lib/hooks/global-hooks';

export const SubnetPickerTable = memo(function SubnetPickerTable({
  subnetData,
}: {
  subnetData: EnrichedSubnet[];
}) {
  const { addSubnet } = useStakeContext();
  const { poolQuery } = useSubnetPickerOptions();
  const poolData = poolQuery.data?.data ?? [];

  const [sorting, setSorting] = useState<SortingState>([
    { id: 'netuid', desc: false },
  ]);

  const handleAddSubnetClick = (subnetUid: number, validatorSs58: string) => {
    const result = addSubnet({
      netuid: subnetUid,
      validatorSs58,
    });
    if (result) {
      notify.success('Subnet added');
    }
  };

  const { getTaostatsValidator } = useValidatorLookup();

  const defaultSubnetData = useMemo(() => {
    const taostatsValidator = getTaostatsValidator();
    return subnetData.reduce((acc, subnet) => {
      return {
        ...acc,
        [subnet.netuid]: taostatsValidator?.hotkey.ss58 || '',
      };
    }, {});
  }, [getTaostatsValidator, subnetData]);

  const [selectedValidators, setSelectedValidators] =
    useState<Record<number, string>>(defaultSubnetData);

  const [manualValidatorForSubnetId, setManualValidatorForSubnetId] = useState<
    number | null
  >(null);
  const [manualValidatorModalOpen, setManualValidatorModalOpen] =
    useState(false);

  const columns = useColumns(
    handleAddSubnetClick,
    subnetData,
    poolData,
    setManualValidatorModalOpen,
    setManualValidatorForSubnetId,
    selectedValidators,
    setSelectedValidators
  );

  const table = useReactTable({
    data: subnetData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    state: {
      sorting,
    },
    debugTable: true,
  });

  const successCallback = (hotkey: string) => {
    if (manualValidatorForSubnetId === null) {
      notify.error('Could not get subnet id');
      return;
    }

    setSelectedValidators({
      ...selectedValidators,
      [manualValidatorForSubnetId]: hotkey,
    });

    setManualValidatorForSubnetId(null);
  };

  return (
    <div>
      <DataTable table={table} />
      <AddManualValidatorDialog
        isOpen={manualValidatorModalOpen}
        setOpen={setManualValidatorModalOpen}
        successCallback={successCallback}
      />
    </div>
  );
});

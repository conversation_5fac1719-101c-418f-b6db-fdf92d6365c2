import { useMemo, useState } from 'react';
import { CgChevronDown, CgClose, CgExtension } from 'react-icons/cg';
import {
  Button,
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Skeleton,
  Text,
} from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { SubnetPickerTable } from './subnet-picker-table';
import { useStakeContext } from '@/app/stake/lib/contexts/stake-context';
import { useSubnetPickerOptions } from '@/app/stake/lib/hooks';
import { SubnetAndValidator } from '@/app/stake/ui/stake/components';
import { useValidatorLookup, useSubnetLookup } from '@/lib/hooks/global-hooks';

export const SubnetPicker = () => {
  const [subnetFilter, setSubnetFilter] = useState('');
  const [isPickerOpen, setIsPickerOpen] = useState(false);
  const { selectedSubnets, removeSubnet } = useStakeContext();
  const { getSubnetName } = useSubnetLookup();
  const { getValidatorName } = useValidatorLookup();
  const handlePickerOpen = () => {
    setIsPickerOpen(true);
  };

  return (
    <div className='w-full sm:relative sm:flex-grow'>
      <Popover open={isPickerOpen} onOpenChange={setIsPickerOpen}>
        <>
          <PopoverTrigger asChild className='w-full'>
            <div>
              <Input
                IconLeft={CgExtension}
                placeholder='Add Subnet, search netuid'
                inputId='subnet-picker-input'
                onChange={(e) => {
                  setSubnetFilter(e.target.value);
                }}
                onClick={(e) => {
                  // stop propagation to prevent the event bubbling up to the popover
                  e.stopPropagation();

                  // manually open the popover
                  handlePickerOpen();

                  setTimeout(() => {
                    // manually give focus to the input because the popover steals it when it opens
                    const input = document.getElementById(
                      'subnet-picker-input'
                    );
                    input?.focus();
                  }, 200);
                }}
                value={subnetFilter}
                inputClassName='rounded-full'
                buttonRightProps={{
                  variant: 'cta3',
                  size: 'default',
                  className:
                    'bg-transparent hover:bg-transparent shadow-none border-none disabled:text-gray-400 mr-0',
                  onClick: handlePickerOpen,
                  children: (
                    <CgChevronDown
                      className={cn(isPickerOpen && 'rotate-180')}
                    />
                  ),
                }}
              />
            </div>
          </PopoverTrigger>
          <PopoverContent
            align='start'
            avoidCollisions={false}
            className='absolute z-10 -ml-8 mt-1 w-[calc(var(--radix-popper-available-width)_+_32px)] max-w-[80rem] rounded-2xl border border-[#323232] bg-[#1D1D1DCC] p-6 shadow-lg backdrop-blur-xl sm:w-[calc(var(--radix-popper-available-width)_-_32px)]'
          >
            <button
              className='absolute right-4 top-4 rounded-md bg-transparent text-[#909090] hover:text-gray-400 focus:outline-none focus:ring-offset-2'
              onClick={() => {
                setIsPickerOpen(false);
              }}
              type='button'
            >
              <span className='absolute -inset-2.5' />
              <span className='sr-only'>Close picker</span>
              <CgClose aria-hidden='true' className='size-6' />
            </button>
            <div>
              <div className='flex flex-col gap-4'>
                <div className='flex min-h-9 flex-row items-center gap-4'>
                  <div className='flex-shrink-0'>
                    <Text level='headerExtraSmall'>Selected Subnets</Text>
                  </div>

                  {selectedSubnets.length > 0 ? (
                    <div className='hidden flex-row flex-wrap items-center gap-2 sm:flex'>
                      {selectedSubnets.map((subnet) => {
                        return (
                          <Button
                            key={subnet.selectedSubnetId}
                            variant='cta3'
                            size='sm'
                            className='animate-in fade-in flex items-center rounded-full text-sm font-medium text-white/60 duration-200'
                            onClick={() => {
                              removeSubnet(subnet.selectedSubnetId);
                            }}
                          >
                            <SubnetAndValidator
                              subnetName={getSubnetName(subnet.netuid)}
                              validatorName={getValidatorName(
                                subnet.validatorSs58
                              )}
                            />
                            <CgClose size={16} className='ml-2' />
                          </Button>
                        );
                      })}
                    </div>
                  ) : null}
                </div>
                <SubnetList subnetFilter={subnetFilter} />
              </div>
            </div>
          </PopoverContent>
        </>
      </Popover>
    </div>
  );
};

const SubnetList = ({ subnetFilter }: { subnetFilter: string }) => {
  const { subnetQuery, isLoading } = useSubnetPickerOptions();

  const filteredSubnetData = useMemo(() => {
    return (
      subnetQuery.data?.data.filter((subnet) => {
        return (
          subnet.metadata?.name
            .toLowerCase()
            .includes(subnetFilter.toLowerCase()) ||
          subnet.netuid.toString().includes(subnetFilter.toLowerCase())
        );
      }) ?? []
    );
  }, [subnetFilter, subnetQuery.data?.data]);

  return (
    <div className='scrollbar-thin scrollbar-track-rounded-full scrollbar-track-white/20 scrollbar-thumb-white scrollbar-thumb-rounded-full hover:scrollbar-thumb-white/80 max-h-[600px] overflow-y-auto'>
      {isLoading ? (
        <Skeleton className='h-[300px]' />
      ) : (
        <SubnetPickerTable subnetData={filteredSubnetData} />
      )}
    </div>
  );
};

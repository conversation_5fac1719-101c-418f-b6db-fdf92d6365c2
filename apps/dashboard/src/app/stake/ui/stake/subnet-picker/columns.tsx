'use client';

import { useCallback, useMemo } from 'react';
import type { ColumnDef } from '@tanstack/react-table';
import type { DtaoSubnet, EnrichedSubnet } from '@repo/types/website-api-types';
import { Button, Combobox } from '@repo/ui/components';
import { notify } from '@repo/ui/lib';
import {
  useSubnetLookup,
  useValidatorComboboxOptions,
} from '@/lib/hooks/global-hooks';
import { formatNumber2dp, TAO_CURRENCY } from '@/lib/utils';
import {
  chainConvertFormat,
  useChainAmountConverter,
} from '@/lib/utils/conversion/conversion-utils';

export const useColumns = (
  handleAddSubnetClick: (netuid: number, selectedValidatorSs58: string) => void,
  subnetData: EnrichedSubnet[],
  poolData: DtaoSubnet[],
  setManualValidatorModalOpen: (open: boolean) => void,
  setManualValidatorForSubnetId: (subnetId: number) => void,
  selectedValidators: Record<number, string>,
  setSelectedValidators: (validators: Record<number, string>) => void
) => {
  const { getSubnetName } = useSubnetLookup();
  const { getValidatorOptions } = useValidatorComboboxOptions();

  const getPool = useCallback(
    (netuid: number) => {
      return poolData.find((p) => p.netuid === netuid);
    },
    [poolData]
  );

  const { convertAmount } = useChainAmountConverter();

  const getTaoDisplayValue = useCallback(
    (value: string) => {
      return (
        formatNumber2dp(convertAmount(Number(value), chainConvertFormat.cToH)) +
        TAO_CURRENCY
      );
    },
    [convertAmount]
  );

  const columns: ColumnDef<EnrichedSubnet>[] = useMemo(
    () => [
      {
        accessorFn: (row) => getSubnetName(row.netuid),
        id: 'name',
        header: 'Subnet',
        cell: ({ row }) => {
          const subnet = row.original;
          return <div>{getSubnetName(subnet.netuid)}</div>;
        },
      },
      {
        accessorKey: 'netuid',
        header: 'Netuid',
      },
      {
        id: 'marketCap',
        accessorFn: (row) => getPool(row.netuid)?.market_cap || '0',
        header: 'MC',
        cell: ({ row }) => {
          return getTaoDisplayValue(
            getPool(row.original.netuid)?.market_cap || '0'
          );
        },
      },
      {
        id: 'liquidity',
        accessorFn: (row) => getPool(row.netuid)?.liquidity || '0',
        header: 'LIQ',
        cell: ({ row }) => {
          return getTaoDisplayValue(
            getPool(row.original.netuid)?.liquidity || '0'
          );
        },
      },
      {
        id: 'volumePer24hr',
        accessorFn: (row) => getPool(row.netuid)?.tao_volume_24_hr || '0',
        header: 'VOL/24HR',
        cell: ({ row }) => {
          return getTaoDisplayValue(
            getPool(row.original.netuid)?.tao_volume_24_hr || '0'
          );
        },
      },
      {
        id: 'validator',
        header: 'Validator',
        cell: ({ row }) => {
          const subnet = row.original;

          const validatorOptions = getValidatorOptions(subnet.netuid);
          validatorOptions.push({
            value: 'manual',
            label: 'Add Manual Hotkey',
            hasName: false,
          });

          return (
            <Combobox
              className='w-[250px]'
              value={selectedValidators[subnet.netuid]}
              onChange={(value) => {
                if (value === 'manual') {
                  // Open the manual validator input modal
                  setManualValidatorModalOpen(true);
                  setManualValidatorForSubnetId(subnet.netuid);
                } else {
                  setSelectedValidators({
                    ...selectedValidators,
                    [subnet.netuid]: value,
                  });
                }
              }}
              options={validatorOptions}
            />
          );
        },
      },
      {
        id: 'actions',
        header: 'Delegation',
        cell: ({ row }) => {
          const subnet = row.original;

          return (
            <Button
              variant='cta2'
              size='sm'
              className='flex flex-1 flex-row gap-2'
              onClick={() => {
                const selectedValidatorSs58 = selectedValidators[subnet.netuid];
                if (!selectedValidatorSs58) {
                  notify.error('Please select a validator');
                  return;
                }
                handleAddSubnetClick(subnet.netuid, selectedValidatorSs58);
              }}
            >
              Add
            </Button>
          );
        },
      },
    ],
    [
      getPool,
      getSubnetName,
      getTaoDisplayValue,
      getValidatorOptions,
      handleAddSubnetClick,
      selectedValidators,
      setManualValidatorForSubnetId,
      setManualValidatorModalOpen,
      setSelectedValidators,
    ]
  );

  return columns;
};

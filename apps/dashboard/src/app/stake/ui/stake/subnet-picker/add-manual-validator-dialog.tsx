'use client';

import { useEffect, useRef, useState } from 'react';
import { BiKey } from 'react-icons/bi';
import { Button, Dialog, Input } from '@repo/ui/components';
import { useLocalManualValidators } from '@/lib/hooks/global-hooks';

const defaultData = {
  hotkey: '',
};

export function AddManualValidatorDialog({
  isOpen,
  setOpen,
  successCallback,
}: {
  isOpen: boolean;
  setOpen: (open: boolean) => void;
  successCallback: (hotkey: string) => void;
}) {
  const [data, setData] = useState(defaultData);
  const hotkeyInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isOpen) {
      setTimeout(() => {
        hotkeyInputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setData({ ...data, [e.target.name]: e.target.value });
  };

  const { manualValidators, setManualValidators } = useLocalManualValidators();

  const handleAddValidator = () => {
    setManualValidators([...manualValidators, data.hotkey]);
    successCallback(data.hotkey);
    setOpen(false);
  };

  return (
    <Dialog
      closeFn={() => {
        setOpen(false);
        setTimeout(() => {
          setData(defaultData);
        }, 200);
      }}
      isOpen={isOpen}
      title='Add Validator Hotkey'
      HeaderIcon={BiKey}
    >
      <div className='flex flex-col gap-6 text-left'>
        <Input
          inputId='hotkey'
          label='Hotkey'
          onChange={handleChange}
          value={data.hotkey}
          forwardRef={hotkeyInputRef}
        />

        <AddButton
          data={data}
          handleAddValidator={() => {
            handleAddValidator();
          }}
        />
      </div>
    </Dialog>
  );
}

const AddButton = ({
  data,
  handleAddValidator,
  title = 'Add Manual Validator',
}: {
  data: typeof defaultData;
  handleAddValidator: () => void;
  title?: string;
}) => {
  return (
    <div>
      <Button
        className='flex w-full items-center justify-center gap-2'
        disabled={!data.hotkey}
        onClick={handleAddValidator}
        size='lg'
        type='button'
        variant='cta2'
      >
        {title}
      </Button>
    </div>
  );
};

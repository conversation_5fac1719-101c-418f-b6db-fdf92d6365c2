import { Text, TooltipProvider } from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { AmountSummaryRow } from './amount-summary-row';
import type { useTransactionTotals } from '@/app/stake/lib/hooks';

type FinancialSummarySectionProps = ReturnType<
  ReturnType<typeof useTransactionTotals>['getTransactionTotals']
>;

export const FinancialSummarySection = ({
  transactionTotalsResult,
}: {
  transactionTotalsResult: FinancialSummarySectionProps;
}) => {
  const {
    totalTao,
    totalFee,
    netChangeInTao,
    initialStakedAmount,
    resultingTotalStakedMinusFees,
    isTotalStakedTooMuch,
  } = transactionTotalsResult;

  return (
    <TooltipProvider>
      <div className='flex flex-col gap-2 pr-4'>
        <Text level='headerExtraSmall'>Financial Summary</Text>
        <AmountSummaryRow
          label='Current Total Balance'
          amount={totalTao}
          tooltip='The overall total you currently have, both staked and unstaked.'
        />
        <AmountSummaryRow
          label='Current Staked Amount'
          amount={initialStakedAmount}
          tooltip='The total you currently have staked.'
        />
        <AmountSummaryRow
          label='Staking Amount Change'
          amount={netChangeInTao}
          tooltip='The total change to your overall staked amount within this transaction.'
          showPlusOrMinus
        />
        <AmountSummaryRow
          label='Fees'
          amount={totalFee}
          tooltip='This is the total of the transaction fee (taken from your unstaked balance) and the staking fees (taken from the amount you are staking or unstaking).'
        />
        <AmountSummaryRow
          label='Resulting Total Staked'
          amount={
            resultingTotalStakedMinusFees < 0
              ? 0
              : resultingTotalStakedMinusFees
          }
          tooltip='The total you will have staked after this transaction, after fees have been taken.'
          valueClassName={cn(
            isTotalStakedTooMuch ? 'text-accent-2' : 'text-accent-1'
          )}
        />
        {!isTotalStakedTooMuch ? (
          <AmountSummaryRow
            label='Resulting Total Balance'
            amount={totalTao - totalFee}
            tooltip='The overall total you will have, both staked and unstaked, after this transaction and after fees have been taken.'
          />
        ) : null}
      </div>
    </TooltipProvider>
  );
};

import { useState } from 'react';
import { BiInfoCircle } from 'react-icons/bi';
import {
  Text,
  TooltipTrigger,
  TooltipContent,
  TooltipProvider,
  Tooltip,
} from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { TAO_CURRENCY } from '@/lib/utils';
import { round } from '@/lib/utils/number-utils';

export const AmountSummaryRow = ({
  label,
  amount,
  className,
  valueClassName,
  tooltip,
  showPlusOrMinus,
}: {
  label: string;
  amount: number;
  className?: string;
  valueClassName?: string;
  tooltip?: string;
  showPlusOrMinus?: boolean;
}) => {
  const [open, setOpen] = useState(false);
  const roundedAmount = round(amount, 9);
  const formattedAmount = showPlusOrMinus
    ? roundedAmount > 0
      ? `+${roundedAmount}`
      : roundedAmount
    : roundedAmount;
  return (
    <div className={cn('flex flex-row justify-between', className)}>
      <div className='flex flex-row items-center gap-1'>
        <Text level='labelLarge' className='text-white/60'>
          {label}
        </Text>
        {tooltip ? (
          <TooltipProvider>
            <Tooltip open={open}>
              <TooltipTrigger asChild>
                <button
                  type='button'
                  className='cursor-pointer'
                  onMouseEnter={() => {
                    setOpen(true);
                  }}
                  onMouseLeave={() => {
                    setOpen(false);
                  }}
                  onTouchStart={(e) => {
                    e.stopPropagation();
                    setOpen(!open);
                  }}
                >
                  <BiInfoCircle className='text-white/60' fontSize={16} />
                </button>
              </TooltipTrigger>
              <TooltipContent
                className='max-w-[300px]'
                side='top'
                align='center'
              >
                {tooltip}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ) : null}
      </div>
      <Text level='labelLarge' className={valueClassName}>
        {formattedAmount}
        {TAO_CURRENCY}
      </Text>
    </div>
  );
};

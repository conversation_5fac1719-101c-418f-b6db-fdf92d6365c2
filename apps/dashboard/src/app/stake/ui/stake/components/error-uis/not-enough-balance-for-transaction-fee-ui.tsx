import { Text } from '@repo/ui/components';
import { ErrorSection } from './error-section';
import { useSubnetSummaries } from '@/app/stake/lib/hooks';
import { AmountSummaryRow } from '@/app/stake/ui/stake/components/amount-summary-row';

type NotEnoughBalanceForTransactionFeeUiProps = {
  availableBalance: number;
  transactionFee: number;
};

export const NotEnoughBalanceForTransactionFeeUi = ({
  availableBalance,
  transactionFee,
}: NotEnoughBalanceForTransactionFeeUiProps) => {
  const { subnetSummariesWithValidDifference } = useSubnetSummaries();
  return (
    <ErrorSection title='Transaction Fee Problem'>
      <Text level='labelLarge'>
        You don&apos;t have enough available balance to cover the transaction
        fee. Transaction fees are charged before transaction execution, so even
        if you&apos;re unstaking, you still must have sufficient free balance to
        cover the fee at the start of the transaction.
      </Text>
      <div className='flex flex-col gap-1'>
        <AmountSummaryRow label='Available Balance' amount={availableBalance} />
        <AmountSummaryRow label='Transaction Fee' amount={transactionFee} />
        <AmountSummaryRow
          label='Additional Required'
          amount={transactionFee - availableBalance}
          valueClassName='text-accent-2'
        />
      </div>
      {subnetSummariesWithValidDifference.length > 1 ? (
        <Text level='labelLarge'>
          You currently have {subnetSummariesWithValidDifference.length}{' '}
          transactions queued up. The more transactions you attempt, the higher
          the fee. Similarly, if you have only one transaction, it typically
          comes with a very small or zero fee. You can remove some transactions
          above which will adjust the fee, then try again.
        </Text>
      ) : null}
    </ErrorSection>
  );
};

import { Text } from '@repo/ui/components';
import { AmountSummaryRow } from './amount-summary-row';
import { TAO_CURRENCY } from '@/lib/utils';
import { roundDown } from '@/lib/utils/number-utils';

export const FeeSummarySection = ({
  availableBalance,
  transactionFee,
  stakingFees,
}: {
  availableBalance: number;
  transactionFee: number;
  stakingFees: number[];
}) => {
  const stakingFeesTotal = stakingFees.reduce((acc, fee) => acc + fee, 0);
  const totalFee = transactionFee + stakingFeesTotal;
  return (
    <div className='flex flex-col gap-2 pr-4'>
      <Text level='headerExtraSmall'>Fees</Text>
      <AmountSummaryRow
        label='Available Balance'
        amount={
          availableBalance > 1
            ? roundDown(availableBalance, 5)
            : availableBalance
        }
      />
      <AmountSummaryRow
        label='Transaction Fee'
        amount={transactionFee}
        tooltip='Transaction fees are charged before transaction execution, and therefore you must have this amount available as free balance at the start of the transaction.'
      />
      <AmountSummaryRow
        label='Staking Fees'
        amount={stakingFeesTotal}
        className='border-b border-white/20 pb-2'
        tooltip={`The total staking fees. These fees are taken from the amount you're staking or unstaking. E.g. If you're staking 1${TAO_CURRENCY}, and the staking fee is 0.00005${TAO_CURRENCY}, then the amount which is actually staked will be 0.99995${TAO_CURRENCY} after the fees are taken.`}
      />
      <AmountSummaryRow label='Total Fee' amount={totalFee} />
      <Text level='labelLarge' className='text-cta1 text-xs italic'>
        Please note: all fees are network fees, Taostats does not charge any
        fees.
      </Text>
    </div>
  );
};

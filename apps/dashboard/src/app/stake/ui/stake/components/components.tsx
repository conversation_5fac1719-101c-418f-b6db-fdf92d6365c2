import type { ReactNode } from 'react';
import { BiError, BiSortAlt2 } from 'react-icons/bi';
import { Button, Text } from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { useStakeContext } from '@/app/stake/lib/contexts/stake-context';
import { useSlippageHelper } from '@/lib/hooks/staking-hooks';
import { formatNumber2dp } from '@/lib/utils';
import { roundDown } from '@/lib/utils/number-utils';

export const TaoBadge = ({
  children,
  onClick,
}: {
  children: ReactNode;
  onClick?: () => void;
}) => {
  return (
    // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions -- TaoBadge is interactive
    <div
      className={cn(
        'leading-29 rounded-lg bg-[#00DBBC29] px-1 py-0.5 text-[20px] font-normal text-[#00DBBC]',
        onClick && 'cursor-pointer'
      )}
      onClick={onClick}
    >
      {children}
    </div>
  );
};

export const DelegationAmountDifference = ({
  amountDifference,
  symbol,
}: {
  amountDifference: number;
  symbol: string;
}) => {
  const numToUse = roundDown(amountDifference, 5);
  return (
    <Text level='labelLarge' className='text-white/60'>
      {numToUse > 0 ? '+' : ''}
      {numToUse === 0 ? '0.00' : numToUse}
      {symbol}
    </Text>
  );
};

export const SlippageMessage = ({
  slippage,
  size = 'lg',
}: {
  slippage: number;
  size?: 'sm' | 'lg';
}) => {
  const { getSlippageTextClassName } = useSlippageHelper();
  const slippageTextClassName = getSlippageTextClassName(slippage);
  return (
    <div className='flex flex-row items-center gap-1'>
      <BiError
        size={size === 'sm' ? 16 : 20}
        className={cn(slippageTextClassName, 'transition-colors duration-1000')}
      />
      <Text
        level={size === 'sm' ? 'labelExtraSmall' : 'labelLarge'}
        className={cn(slippageTextClassName, 'transition-colors duration-1000')}
      >
        {formatNumber2dp(slippage)}% slippage
      </Text>
    </div>
  );
};

export const SubnetAndValidator = ({
  subnetName,
  validatorName,
  className,
}: {
  subnetName: string;
  validatorName: string;
  className?: string;
}) => {
  return (
    <div className='flex min-w-0 items-baseline'>
      <Text
        as='span'
        level='buttonSmall'
        className={cn('max-w-[150px] truncate', className)}
      >
        {subnetName}
      </Text>
      <Text
        as='span'
        level='buttonSmall'
        className='text-accent-1 mx-1 flex-shrink-0 text-xs font-normal'
      >
        {' // '}
      </Text>
      <Text
        as='span'
        level='buttonSmall'
        className={cn('max-w-[150px] truncate font-normal', className)}
      >
        {validatorName}
      </Text>
    </div>
  );
};

export const QuickActionButtons = () => {
  const { updateSliderValue, selectedSubnets, stakingType } = useStakeContext();

  const handleSetAllToZero = () => {
    selectedSubnets.forEach((subnet) => {
      updateSliderValue(subnet.selectedSubnetId, 0);
    });
  };

  if (selectedSubnets.length === 0 || stakingType === 'auto-balance') {
    return null;
  }

  return (
    <div className='flex flex-row justify-end gap-2'>
      <Button size='sm' variant='cta3' onClick={handleSetAllToZero}>
        <BiSortAlt2 size={15} className='mr-1' />
        Set all to 0
      </Button>
    </div>
  );
};

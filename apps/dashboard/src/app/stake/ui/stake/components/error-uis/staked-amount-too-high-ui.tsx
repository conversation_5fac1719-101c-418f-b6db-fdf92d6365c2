import { Text } from '@repo/ui/components';
import { ErrorSection } from './error-section';
import { AmountSummaryRow } from '@/app/stake/ui/stake/components/amount-summary-row';

export const StakedAmountTooHighUi = ({
  amountRequired,
  totalTao,
}: {
  amountRequired: number;
  totalTao: number;
}) => {
  return (
    <ErrorSection title='Staked Amount Too High'>
      <Text level='labelLarge'>
        The amount you are trying to stake is too high. You are trying to stake
        more than your total balance.
      </Text>
      <div className='flex flex-col gap-1'>
        <AmountSummaryRow
          label='Amount Required'
          amount={amountRequired}
          tooltip='The amount required to cover the amount being staked and the transaction fee'
        />
        <AmountSummaryRow
          label='Total Available'
          amount={totalTao}
          tooltip='The overall total you currently have, both staked and unstaked.'
        />
        <AmountSummaryRow
          label='Over By'
          amount={amountRequired - totalTao}
          valueClassName='text-accent-2'
          tooltip='The amount you are over by i.e. the amount you need to reduce your stake by to allow the transaction to proceed.'
        />
      </div>
    </ErrorSection>
  );
};

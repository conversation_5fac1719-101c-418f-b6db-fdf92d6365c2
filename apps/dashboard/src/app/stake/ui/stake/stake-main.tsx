import { QuickActionButtons } from './components';
import { StakingConfig } from './delegate-form';
import { MentatUi } from './mentat-ui';
import { SelectedSubnets } from './selected-subnets';
import { SimpleUi } from './simple-ui';
import {
  StakeContextProvider,
  useStakeContext,
} from '@/app/stake/lib/contexts/stake-context';
import type { SelectedSubnetType, StakingTypes } from '@/app/stake/lib/types';
import { PrimaryActionButtonsContainer } from '@/app/stake/ui/button-container';
import { PageHeader } from '@/components/page-header';
import { WalletPicker } from '@/components/wallet/wallet-picker';

export const ConnectedStakeMain = ({
  availableBalance,
  initialSubnets = [],
  stakingType,
}: {
  availableBalance: number;
  initialSubnets?: SelectedSubnetType[];
  stakingType: StakingTypes;
}) => {
  const totalTao =
    availableBalance +
    initialSubnets.reduce((acc, subnet) => acc + subnet.initialAmount, 0);

  return (
    <StakeContextProvider
      availableBalance={availableBalance}
      initialSubnets={initialSubnets}
      totalTao={totalTao}
      stakingType={stakingType}
    >
      <div className='flex flex-col gap-6'>
        <div className='flex flex-col items-center gap-6 sm:flex-row sm:justify-between sm:gap-0 sm:py-4'>
          <PageHeader title='Stake' />
          <WalletPicker />
        </div>
        <StakingConfig />
        <StakingUi />
      </div>
    </StakeContextProvider>
  );
};

const StakingUi = () => {
  const { isMentat, isSimple } = useStakeContext();

  if (isMentat) {
    return <MentatUi />;
  }

  if (isSimple) {
    return <SimpleUi />;
  }

  return <StakingSliderUi />;
};

const StakingSliderUi = () => {
  return (
    <>
      <QuickActionButtons />
      <SelectedSubnets />
      <PrimaryActionButtonsContainer />
    </>
  );
};

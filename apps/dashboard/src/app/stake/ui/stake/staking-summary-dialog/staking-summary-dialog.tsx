import { useState } from 'react';
import { BiListPlus } from 'react-icons/bi';
import { AlertError, <PERSON><PERSON>, Di<PERSON>, Spinner, Text } from '@repo/ui/components';
import { notify } from '@repo/ui/lib';
import { useTransactionSummaryBuilder } from './lib';
import { SubnetSummary } from './subnet-summary';
import { useStakeContext } from '@/app/stake/lib/contexts/stake-context';
import {
  useTransactionTotals,
  useSlippageChecker,
  useStakingLogic,
  useSubnetSummaries,
  useTransactionFeeCalculator,
  useTermsCheckbox,
} from '@/app/stake/lib/hooks';
import {
  NotEnoughBalanceForTransactionFeeUi,
  StakedAmountTooHighUi,
} from '@/app/stake/ui/stake/components/error-uis';
import { FeeSummarySection } from '@/app/stake/ui/stake/components/fee-summary-section';
import { FinancialSummarySection } from '@/app/stake/ui/stake/components/financial-summary-section';
import { SlippageError } from '@/app/stake/ui/stake/components/slippage-error';
import { useTransactionSuccessLogic } from '@/contexts/chain-and-wallet/use-transaction-success-logic';

export const StakingSummaryDialog = ({
  closeFn,
  isOpen,
  transactionFee = 0,
  stakingFees = [],
}: {
  closeFn: () => void;
  isOpen: boolean;
  transactionFee?: number;
  stakingFees?: number[];
}) => {
  const { buildStakeTransactionSummaries } = useTransactionSummaryBuilder();
  const { getFees } = useTransactionFeeCalculator();
  const [isBuildingTransaction, setIsBuildingTransaction] = useState(false);
  const [transactionFeeLocal, setTransactionFeeLocal] =
    useState<number>(transactionFee);
  const [stakingFeesLocal, setStakingFeesLocal] =
    useState<number[]>(stakingFees);
  const { availableBalance, removeSubnet } = useStakeContext();
  const { handleStakeConfirmClick, isPending, txStatus } = useStakingLogic();
  const { termsAccepted, renderTermsCheckbox } = useTermsCheckbox();
  const { hasSlippageErrors } = useSlippageChecker();
  const {
    fetchInfo,
    wasSuccessful,
    setWasSuccessful,
    renderMessageIfSuccessful,
  } = useTransactionSuccessLogic();

  const title = txStatus?.status ? txStatus.status : 'Staking Summary';

  const handleGetFees = async (filterSubnetIds: string[] = []) => {
    try {
      setIsBuildingTransaction(true);
      const stakeTransactionSummaries =
        buildStakeTransactionSummaries(filterSubnetIds);
      const { transactionFee: transactionFeeNew, stakingFees: stakingFeesNew } =
        await getFees(stakeTransactionSummaries);
      setTransactionFeeLocal(transactionFeeNew);
      setStakingFeesLocal(stakingFeesNew);
    } catch (error) {
      notify.error('There was an error when building the transaction');
      console.error(error);
    } finally {
      setIsBuildingTransaction(false);
    }
  };

  const handleRemoveSubnet = (selectedSubnetId: string) => {
    removeSubnet(selectedSubnetId);
    void handleGetFees([selectedSubnetId]);
  };

  const stakeTransactionSummaries = buildStakeTransactionSummaries();
  const { getTransactionTotals } = useTransactionTotals();
  const getTransactionTotalsResult = getTransactionTotals(
    transactionFeeLocal,
    stakingFeesLocal,
    stakeTransactionSummaries
  );

  const isButtonDisabled =
    isPending ||
    hasSlippageErrors ||
    !termsAccepted ||
    !getTransactionTotalsResult.hasEnoughBalanceToCoverTransactionFee ||
    getTransactionTotalsResult.isTotalStakedTooMuch;

  const errorUiToShow = getTransactionTotalsResult.isTotalStakedTooMuch ? (
    <StakedAmountTooHighUi
      amountRequired={getTransactionTotalsResult.amountRequired}
      totalTao={getTransactionTotalsResult.totalTao}
    />
  ) : !getTransactionTotalsResult.hasEnoughBalanceToCoverTransactionFee ? (
    <NotEnoughBalanceForTransactionFeeUi
      availableBalance={availableBalance}
      transactionFee={transactionFeeLocal}
    />
  ) : null;

  return (
    <Dialog
      HeaderIcon={isPending ? undefined : BiListPlus}
      HeaderNode={
        isPending ? (
          <div className='flex h-12 items-center justify-center'>
            <Spinner className='h-8 w-8' />
          </div>
        ) : null
      }
      closeFn={wasSuccessful ? fetchInfo : closeFn}
      isOpen={isOpen}
      title={title}
      className='sm:w-[600px]'
    >
      {!isOpen ? (
        <div className='flex justify-center py-4'>
          <Spinner />
        </div>
      ) : (
        <>
          <SlippageErrorOrNull />

          {renderMessageIfSuccessful()}

          {isBuildingTransaction ? (
            <div className='flex flex-row items-center justify-center gap-2 py-4'>
              <Spinner />
              <Text level='labelLarge'>Re-calculating fees...</Text>
            </div>
          ) : null}

          <StakingSummary
            stakingFees={stakingFeesLocal}
            handleRemoveSubnet={handleRemoveSubnet}
            wasSuccessful={wasSuccessful}
          />

          {errorUiToShow}

          <FeeSummarySection
            availableBalance={availableBalance}
            transactionFee={transactionFeeLocal}
            stakingFees={stakingFeesLocal}
          />

          <FinancialSummarySection
            transactionTotalsResult={getTransactionTotalsResult}
          />

          {renderTermsCheckbox(wasSuccessful)}

          {txStatus?.message ? (
            <AlertError description={txStatus.message} />
          ) : null}
        </>
      )}

      {wasSuccessful ? null : (
        <div className='flex flex-col gap-4'>
          <Button
            className='w-full'
            disabled={isButtonDisabled}
            onClick={() => {
              void handleStakeConfirmClick({
                stakeTransactionSummaries,
                onSuccessCallback: () => {
                  setWasSuccessful(true);
                },
                doFullRefetchOnSuccess: false,
              });
            }}
            size='lg'
            type='button'
            variant='cta2'
          >
            {isPending ? 'Confirming...' : 'Confirm'}
            <BiListPlus className='ml-2' size={20} />
          </Button>
        </div>
      )}
    </Dialog>
  );
};

const SlippageErrorOrNull = () => {
  const { hasSlippageErrors } = useSlippageChecker();
  if (hasSlippageErrors) {
    return <SlippageError />;
  }
  return null;
};

const StakingSummary = ({
  stakingFees,
  handleRemoveSubnet,
  wasSuccessful,
}: {
  stakingFees?: number[];
  handleRemoveSubnet: (selectedSubnetId: string) => void;
  wasSuccessful: boolean;
}) => {
  const { subnetSummariesWithValidDifference } = useSubnetSummaries();
  return (
    <div className='scrollbar-thin scrollbar-track-rounded-full scrollbar-track-white/20 scrollbar-thumb-white scrollbar-thumb-rounded-full hover:scrollbar-thumb-white/80 max-h-[30vh] overflow-y-auto pr-4'>
      {subnetSummariesWithValidDifference.map((subnet, index) => (
        <SubnetSummary
          key={subnet.selectedSubnetId}
          selectedSubnetId={subnet.selectedSubnetId}
          stakingFee={stakingFees?.[index]}
          removeSubnet={handleRemoveSubnet}
          wasSuccessful={wasSuccessful}
        />
      ))}
    </div>
  );
};

import { BigNumber } from 'bignumber.js';
import { notify } from '@repo/ui/lib';
import {
  useAmountToUnstakeCalculator,
  useSubnetSummaries,
} from '@/app/stake/lib/hooks';
import { useChainAndWalletContext } from '@/contexts/chain-and-wallet/chain-and-wallet-context';
import type { StakeTransactionSummary } from '@/contexts/chain-and-wallet/types';
import { log } from '@/lib/utils';
import { useChainAmountConverter } from '@/lib/utils/conversion/conversion-utils';
import { roundDown } from '@/lib/utils/number-utils';

export const useTransactionSummaryBuilder = () => {
  const {
    state: { currentAccount },
  } = useChainAndWalletContext();

  const { subnetSummariesWithValidDifference } = useSubnetSummaries();
  const { calculateAlphaToUnstake } = useAmountToUnstakeCalculator();
  const { convertAmount } = useChainAmountConverter();

  const buildStakeTransactionSummaries = (
    filterSubnetIds: string[] = []
  ): StakeTransactionSummary[] => {
    if (!currentAccount?.address) {
      notify.error(
        'Could not find current account. Can not build transactions.'
      );
      return [];
    }

    log('DEBUG', { subnetSummariesWithValidDifference });

    const sortedSummaries = [...subnetSummariesWithValidDifference]
      .filter((subnet) => !filterSubnetIds.includes(subnet.selectedSubnetId))
      .sort((a, b) => {
        const aDiff = BigNumber(a.amountToDelegateDifference);
        const bDiff = BigNumber(b.amountToDelegateDifference);
        return aDiff.minus(bDiff).toNumber();
      });

    log('DEBUG', { sortedSummaries });

    const txs = sortedSummaries
      .map((subnet): StakeTransactionSummary | null => {
        // stakeChangeTao is in human readable format e.g. 5.2. Chain format is 5.2 * 1_000_000_000
        const stakeChangeTao = roundDown(subnet.amountToDelegateDifference, 5);

        const hotkey = subnet.validatorSs58;

        if (stakeChangeTao > 0) {
          // The amount to delegate is greater than the initial amount
          // So we need to add stake
          // The amountToDelegateDifferenceInTao is in human readable format e.g. 5.2
          // We need to convert this to chain format as that's what is expected by the addStake function
          // We also need to calculate the limit value to pass into addStakeLimit

          // Get the amount to delegate in chain format
          const stakeChangeChainFormat =
            convertAmount(stakeChangeTao).toNumber();

          // Calculate the limit value to pass into addStakeLimit, in chain format
          const priceLimit = subnet
            .getAddStakePriceLimit(subnet.netuid, subnet.slippage)
            .toNumber();

          log('DEBUG', {
            stakeChangeChainFormat,
            stakeChangeTao,
            priceLimit,
            hotkey,
          });

          return {
            type: 'add_stake',
            destinationHotkey: hotkey,
            destinationNetuid: subnet.netuid,
            originColdkey: currentAccount.address,
            taoAmount: stakeChangeChainFormat,
            priceLimit,
          };
        }

        if (stakeChangeTao < 0) {
          // This will be a negative number, which means we need to remove some stake
          // We need it as a positive number as it's being passed into the removeStakeLimit function, which expects a positive alpha chain format number
          // So get the absolute value of the amount to remove in tao
          // Then convert that to alpha in chain format

          const alphaToUnstake = calculateAlphaToUnstake(
            subnet,
            stakeChangeTao
          );

          if (!alphaToUnstake) {
            notify.error(
              `Skipping transaction. Could not calculate alpha to unstake for subnet ${subnet.netuid} validator ${subnet.validatorSs58}`
            );
            return null;
          }

          // Calculate the limit value to pass into removeStakeLimit, in chain format
          const priceLimit = subnet
            .getRemoveStakePriceLimit(subnet.netuid, subnet.slippage)
            .toNumber();

          log('DEBUG', { alphaToUnstake, stakeChangeTao, priceLimit, hotkey });

          return {
            type: 'remove_stake',
            originColdkey: currentAccount.address,
            removeFromHotkey: hotkey,
            removeFromNetuid: subnet.netuid,
            alphaAmount: alphaToUnstake,
            taoAmount: convertAmount(stakeChangeTao).abs().toNumber(),
            priceLimit,
          };
        }

        return null;
      })
      .filter((tx): tx is StakeTransactionSummary => tx !== null);

    return txs;
  };

  return {
    buildStakeTransactionSummaries,
  };
};

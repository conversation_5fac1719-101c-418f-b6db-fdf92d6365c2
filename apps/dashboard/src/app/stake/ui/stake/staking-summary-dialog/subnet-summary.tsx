import { BiTrash } from 'react-icons/bi';
import { Button, Text } from '@repo/ui/components';
import { useSubnetSummary } from '@/app/stake/lib/hooks';
import {
  DelegationAmountDifference,
  SlippageMessage,
  SubnetAndValidator,
} from '@/app/stake/ui/stake/components';
import { TAO_CURRENCY } from '@/lib/utils';
import { roundDown } from '@/lib/utils/number-utils';

export const SubnetSummary = ({
  selectedSubnetId,
  stakingFee,
  removeSubnet,
  wasSuccessful,
}: {
  selectedSubnetId: string;
  stakingFee?: number;
  removeSubnet: (selectedSubnetId: string) => void;
  wasSuccessful: boolean;
}) => {
  const { getSubnetSummary } = useSubnetSummary();
  const subnetSummary = getSubnetSummary(selectedSubnetId);
  return (
    <div className='flex flex-row items-center justify-between gap-4 border-b border-white/20 py-4'>
      {/* First column */}
      <div className='w-[60%] min-w-0'>
        <div className='flex flex-row items-center gap-1'>
          {wasSuccessful ? null : (
            <Button
              variant='ghost'
              size='sm'
              className='px-0 pr-1 text-white/20 hover:text-red-400'
              onClick={() => {
                removeSubnet(selectedSubnetId);
              }}
            >
              <BiTrash size={20} />
            </Button>
          )}

          <div className='truncate'>
            <div className='flex flex-row items-baseline gap-1 text-white/60'>
              <Text level='labelLarge' className='flex-shrink-0'>
                {roundDown(subnetSummary.subnetAmountToDelegateTao, 5)}
                {TAO_CURRENCY}
              </Text>
              to{' '}
              <div className='min-w-0 flex-shrink flex-grow'>
                <SubnetAndValidator
                  subnetName={subnetSummary.subnetName}
                  validatorName={subnetSummary.validatorName}
                  className='text-white/60'
                />
              </div>
            </div>

            {stakingFee ? (
              <Text level='labelLarge' className='italic text-white/40'>
                {stakingFee}
                {TAO_CURRENCY} staking fee
              </Text>
            ) : null}
          </div>
        </div>
      </div>

      {/* Second column */}
      <div className='flex w-[40%] flex-row items-center justify-between gap-2 text-white/60'>
        <div>
          <DelegationAmountDifference
            amountDifference={subnetSummary.amountToDelegateDifference}
            symbol={TAO_CURRENCY}
          />
          <DelegationAmountDifference
            amountDifference={subnetSummary.amountToDelegateDifferenceAlpha}
            symbol={subnetSummary.subnetAlphaSymbol}
          />
        </div>
        <div>
          <SlippageMessage slippage={subnetSummary.slippage} size='sm' />
        </div>
      </div>
    </div>
  );
};

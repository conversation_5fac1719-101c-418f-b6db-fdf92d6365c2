import { BiListPlus } from 'react-icons/bi';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>lex,
  Spinner,
  Text,
} from '@repo/ui/components';
import { useRemoveMentatProxyLogic } from './lib/hooks';
import { useTransactionSuccessLogic } from '@/contexts/chain-and-wallet/use-transaction-success-logic';

export const MentatRemoveProxyDialog = ({
  closeFn,
  isOpen,
  proxyAddress,
}: {
  closeFn: () => void;
  isOpen: boolean;
  proxyAddress: string;
}) => {
  const { handleRemoveMentatProxyClick, isPending, txStatus } =
    useRemoveMentatProxyLogic();
  const {
    fetchInfo,
    wasSuccessful,
    setWasSuccessful,
    renderMessageIfSuccessful,
  } = useTransactionSuccessLogic();

  const title =
    !txStatus?.status || txStatus.status === 'Success'
      ? 'Revoke Mentat Proxy'
      : txStatus.status;

  const isButtonDisabled = isPending;

  return (
    <Dialog
      HeaderIcon={isPending ? undefined : BiListPlus}
      HeaderNode={
        isPending ? (
          <div className='flex h-12 items-center justify-center'>
            <Spinner className='h-8 w-8' />
          </div>
        ) : null
      }
      closeFn={wasSuccessful ? fetchInfo : closeFn}
      isOpen={isOpen}
      title={title}
      className='sm:w-[600px]'
    >
      {!isOpen ? (
        <div className='flex justify-center py-4'>
          <Spinner />
        </div>
      ) : (
        <>
          {renderMessageIfSuccessful()}

          {wasSuccessful ? null : (
            <Card>
              <Flex col className='gap-2'>
                <Text level='bodyMedium' className='text-accent-1'>
                  What will happen now?
                </Text>
                <Text level='bodyMedium'>
                  The Mentat proxy will be removed from your wallet and Mentat
                  will no longer be able to manage your Tao&apos;s delegation.
                </Text>
              </Flex>
            </Card>
          )}

          {txStatus?.message ? (
            <AlertError description={txStatus.message} />
          ) : null}
        </>
      )}

      {wasSuccessful ? null : (
        <div className='flex flex-col gap-4'>
          <Button
            className='w-full'
            disabled={isButtonDisabled}
            onClick={() => {
              void handleRemoveMentatProxyClick(proxyAddress, {
                onSuccessCallback: () => {
                  setWasSuccessful(true);
                },
                doFullRefetchOnSuccess: false,
              });
            }}
            size='lg'
            type='button'
            variant='cta2'
          >
            {isPending ? 'Confirming...' : 'Confirm'}
            <BiListPlus className='ml-2' size={20} />
          </Button>
        </div>
      )}
    </Dialog>
  );
};

import { BigNumber } from 'bignumber.js';
import { TaoOrAlphaValueDisplay, Text, Flex } from '@repo/ui/components';
import { useStakeContext } from '@/app/stake/lib/contexts/stake-context';
import { useAccountContext } from '@/contexts/chain-and-wallet/account-context';
import { roundDown } from '@/lib/utils/number-utils';

export const TaoBalanceSummary = () => {
  const {
    state: { availableBalance },
  } = useAccountContext();
  const { totalTao } = useStakeContext();
  const availableTao = roundDown(BigNumber(availableBalance).toNumber(), 5);
  return (
    <Flex className='justify-center gap-6 sm:justify-end'>
      <Flex col className='gap-2'>
        <Text level='labelSmall' className='text-center text-white/60'>
          Total
        </Text>
        <TaoOrAlphaValueDisplay
          value={roundDown(totalTao, 5)}
          valueFormattingOptions={{
            minimumFractionDigits: 2,
            maximumFractionDigits: 5,
          }}
          valueTextLevel='labelLarge'
          iconClassName='text-xs'
          areDecimalsSmall
        />
      </Flex>
      <Flex col className='gap-2'>
        <Text level='labelSmall' className='text-center text-white/60'>
          Available
        </Text>
        <TaoOrAlphaValueDisplay
          value={availableTao}
          valueFormattingOptions={{
            minimumFractionDigits: 2,
            maximumFractionDigits: 5,
          }}
          valueTextLevel='labelLarge'
          iconClassName='text-xs'
          areDecimalsSmall
        />
      </Flex>
    </Flex>
  );
};

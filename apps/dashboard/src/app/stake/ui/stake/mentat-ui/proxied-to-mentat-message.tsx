import { Al<PERSON>, Flex, <PERSON> } from '@repo/ui/components';
import { useMentatStrategyCurrentPosition } from './lib/hooks';
import { useChainAndWalletContext } from '@/contexts/chain-and-wallet/chain-and-wallet-context';

export const ProxiedToMentatMessage = ({
  showUpdateMsg = true,
}: {
  showUpdateMsg?: boolean;
}) => {
  const {
    state: { currentAccount },
  } = useChainAndWalletContext();
  const { isProxiedToMentat, config, strategyName } =
    useMentatStrategyCurrentPosition();

  if (!isProxiedToMentat) {
    return null;
  }

  if (!config) {
    return (
      <Alert
        type='error'
        description={`You are proxied to Mentat but we could not find the strategy config using key: ${strategyName}`}
      />
    );
  }

  return (
    <Alert
      type='success'
      description={
        <Flex
          col
          className='w-full items-center justify-center gap-4 sm:flex-row sm:justify-between'
        >
          <div>
            You are proxied to <PERSON><PERSON>. Current strategy: {config.title}. <br />
            {showUpdateMsg ? (
              <span className='text-xs'>
                Note, if you make any changes to your positions, they will be
                automatically updated inline with your chosen strategy.
              </span>
            ) : null}
          </div>
          {currentAccount?.address ? (
            <Link
              href={`/portfolio/${currentAccount.address}`}
              className='text-white'
            >
              View Performance &raquo;
            </Link>
          ) : null}
        </Flex>
      }
    />
  );
};

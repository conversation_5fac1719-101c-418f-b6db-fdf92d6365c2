import { BigNumber } from 'bignumber.js';
import { BiListPlus } from 'react-icons/bi';
import {
  AlertE<PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Dialog,
  <PERSON>lex,
  <PERSON>,
  Spinner,
  Text,
} from '@repo/ui/components';
import { useAddMentatProxyLogic } from './lib/hooks';
import { ErrorSection } from '@/app/stake/ui/stake/components';
import { useAccountContext } from '@/contexts/chain-and-wallet/account-context';
import { useTransactionSuccessLogic } from '@/contexts/chain-and-wallet/use-transaction-success-logic';
import { TAO_CURRENCY } from '@/lib/utils';
import { roundDown } from '@/lib/utils/number-utils';

export const MentatAddProxyDialog = ({
  closeFn,
  isOpen,
  proxyAddress,
  hotkeyAddress,
}: {
  closeFn: () => void;
  isOpen: boolean;
  proxyAddress: string;
  hotkeyAddress: string;
}) => {
  const {
    state: { availableBalance },
  } = useAccountContext();
  const { handleAddMentatProxyClick, isPending, txStatus } =
    useAddMentatProxyLogic();
  const {
    fetchInfo,
    wasSuccessful,
    setWasSuccessful,
    renderMessageIfSuccessful,
  } = useTransactionSuccessLogic();

  const title =
    !txStatus?.status || txStatus.status === 'Success'
      ? 'Delegate via Mentat'
      : txStatus.status;

  const availableTao = roundDown(BigNumber(availableBalance).toNumber(), 5);
  const hasEnoughBalance = availableTao >= 0.1;
  const isButtonDisabled = isPending || !hasEnoughBalance;

  return (
    <Dialog
      HeaderIcon={isPending ? undefined : BiListPlus}
      HeaderNode={
        isPending ? (
          <div className='flex h-12 items-center justify-center'>
            <Spinner className='h-8 w-8' />
          </div>
        ) : null
      }
      closeFn={wasSuccessful ? fetchInfo : closeFn}
      isOpen={isOpen}
      title={title}
      className='sm:w-[600px]'
    >
      {!isOpen ? (
        <div className='flex justify-center py-4'>
          <Spinner />
        </div>
      ) : (
        <>
          {!hasEnoughBalance ? (
            <ErrorSection title='Transaction Fee Problem'>
              <Text level='labelLarge'>
                You need at least 0.1{TAO_CURRENCY} available balance to
                delegate via Mentat.{' '}
                <Link href='/stake/simple' className='text-accent-1'>
                  Unstake 0.1{TAO_CURRENCY}
                </Link>{' '}
                and try again. Please note, you may need to unstake a little
                more than 0.1{TAO_CURRENCY} in order to cover the staking fee.
              </Text>
            </ErrorSection>
          ) : null}

          {renderMessageIfSuccessful()}

          {wasSuccessful ? null : (
            <Card>
              <Flex col className='gap-2'>
                <Text level='bodyMedium' className='text-accent-1'>
                  What will happen now?
                </Text>
                <Text level='bodyMedium'>{`If you proceed, you are approving Mentat Minds to handle your Tao's delegation. This is called adding a proxy.`}</Text>
                <Text level='bodyMedium'>
                  Approving the Mentat proxy will give them the right to buy
                  subnet tokens and delegate to a validator with your Tao.
                </Text>
              </Flex>

              <Flex col className='gap-2'>
                <Text level='bodyMedium' className='text-accent-1'>
                  Does my Tao leave my wallet?
                </Text>

                <Text level='bodyMedium'>
                  {`No. Your funds stay on your wallet at all times, and you can revoke Mentat's proxy access at any time.`}
                </Text>

                <Text level='bodyMedium'>
                  All of your Tao will be staked to Root, ready for distribution
                  by Mentat based on your chosen strategy.
                </Text>
              </Flex>
              <Flex col className='gap-2'>
                <Text level='bodyMedium' className='text-accent-1'>
                  Can I delegate a portion of my stake?
                </Text>
                <Text level='bodyMedium'>
                  If you only want to allow Mentat to manage a portion of your
                  stake, create a new wallet, transfer the amount you want to
                  stake to that wallet, and then grant Mentat access to that
                  wallet.
                </Text>
              </Flex>

              <Text level='bodyMedium'>
                {`Please note, the chain requires 0.093 ${TAO_CURRENCY} to be locked to set an approval. You will get that back when you revoke Mentat access.`}
              </Text>
            </Card>
          )}

          {txStatus?.message ? (
            <AlertError description={txStatus.message} />
          ) : null}
        </>
      )}

      {wasSuccessful ? null : (
        <div className='flex flex-col gap-4'>
          <Button
            className='w-full'
            disabled={isButtonDisabled}
            onClick={() => {
              void handleAddMentatProxyClick(
                {
                  proxyAddress,
                  hotkeyAddress,
                },
                {
                  onSuccessCallback: () => {
                    setWasSuccessful(true);
                  },
                  doFullRefetchOnSuccess: false,
                }
              );
            }}
            size='lg'
            type='button'
            variant='cta2'
          >
            {isPending ? 'Confirming...' : 'Confirm'}
            <BiListPlus className='ml-2' size={20} />
          </Button>
        </div>
      )}
    </Dialog>
  );
};

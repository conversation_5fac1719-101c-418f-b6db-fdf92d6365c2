import { useState } from 'react';
import { CgArrowRight } from 'react-icons/cg';
import {
  <PERSON><PERSON>,
  AlertError,
  Flex,
  Link,
  Text,
  TooltipProvider,
} from '@repo/ui/components';
import {
  useMentatConfigMap,
  useMentatStrategies,
  useMentatStrategyCurrentPosition,
} from './lib/hooks';
import { MentatAddProxyDialog } from './mentat-add-proxy-dialog';
import { MentatRemoveProxyDialog } from './mentat-remove-proxy-dialog';
import { MentatUiCard } from './mentat-ui-card';
import { ProxiedToMentatMessage } from './proxied-to-mentat-message';

export const MentatUi = () => {
  const { isProxiedToMentat, strategyProxyAddress } =
    useMentatStrategyCurrentPosition();
  const { getConfigByName } = useMentatConfigMap();
  const strategiesQuery = useMentatStrategies();
  const [addProxyAddresses, setAddProxyAddresses] = useState<{
    proxyAddress: string;
    hotkeyAddress: string;
  } | null>(null);
  const [removeProxyAddress, setRemoveProxyAddress] = useState<string | null>(
    null
  );

  const handleDelegateClick = (
    proxyAddressClicked: string,
    hotkeyAddressClicked: string
  ) => {
    setAddProxyAddresses({
      proxyAddress: proxyAddressClicked,
      hotkeyAddress: hotkeyAddressClicked,
    });
  };

  const handleRemoveProxyClick = (proxyAddress: string) => {
    setRemoveProxyAddress(proxyAddress);
  };

  if (strategiesQuery.isLoading) {
    return (
      <Alert
        type='success'
        showSpinner
        description='Getting strategies from Mentat, please wait a moment'
        alignItems='center'
      />
    );
  }

  if (strategiesQuery.isError) {
    return (
      <AlertError
        description={`Failed to fetch strategies. ${strategiesQuery.error.message}`}
        alignItems='center'
      />
    );
  }

  const data = strategiesQuery.data ?? [];

  return (
    <Flex col className='gap-4'>
      <MentatHeroSection />

      <ProxiedToMentatMessage showUpdateMsg={false} />

      <div className='grid grid-cols-1 gap-4 sm:grid-cols-2'>
        <TooltipProvider delayDuration={0}>
          {data.map((strategy) => {
            const config = getConfigByName(strategy.name);
            if (!config) {
              return null;
            }

            return (
              <MentatUiCard
                key={
                  strategy.name + strategy.walletAddress + strategy.proxyAddress
                }
                strategy={strategy}
                config={config}
                isProxiedToMentat={isProxiedToMentat}
                strategyProxyAddress={strategyProxyAddress ?? null}
                handleDelegateClick={handleDelegateClick}
                handleRemoveProxyClick={handleRemoveProxyClick}
              />
            );
          })}
          {addProxyAddresses ? (
            <MentatAddProxyDialog
              isOpen={Boolean(addProxyAddresses)}
              proxyAddress={addProxyAddresses.proxyAddress}
              hotkeyAddress={addProxyAddresses.hotkeyAddress}
              closeFn={() => {
                setAddProxyAddresses(null);
              }}
            />
          ) : null}
          {removeProxyAddress ? (
            <MentatRemoveProxyDialog
              isOpen={Boolean(removeProxyAddress)}
              proxyAddress={removeProxyAddress}
              closeFn={() => {
                setRemoveProxyAddress(null);
              }}
            />
          ) : null}
        </TooltipProvider>
      </div>
    </Flex>
  );
};

const MentatHeroSection = () => {
  const { isProxiedToMentat } = useMentatStrategyCurrentPosition();

  if (isProxiedToMentat) {
    return null;
  }

  return (
    <Flex className='grid grid-cols-1 gap-4 sm:grid-cols-3'>
      <MentatHeaderCard
        title='Taostats ❤️ Mentat Minds'
        description='Mentat Minds analyse subnets and validators for you, then delegate your tokens on your behalf. You pick your strategy and let Mentat do the rest.'
        buttonText='FAQ'
        buttonHref='https://mentatminds.com/faq/'
      />
      <MentatHeaderCard
        title='Is my Tao safe?'
        description='Your funds stay in your wallet, you simply give Mentat the ability to manage your stake. You can revoke access at any time.'
        buttonText='Security'
        buttonHref='https://mentatminds.com/security/'
      />
      <MentatHeaderCard
        title='Lower risk, higher returns'
        description='Mentat constantly monitor the network, which allows them to detect the best opportunities. They delegate your tokens on your behalf to give you peace of mind, the best returns, and less risk.'
        buttonText='Learn more'
        buttonHref='https://mentat-minds.notion.site/Mentat-Minds-Resources-eac6279216ec4ae09132e7063a43c68f'
      />
    </Flex>
  );
};

const MentatHeaderCard = ({
  title,
  description,
  buttonText,
  buttonHref,
}: {
  title: string;
  description: string;
  buttonText: string;
  buttonHref: string;
}) => {
  return (
    <Link
      href={buttonHref}
      className='block'
      target='_blank'
      rel='noopener noreferrer'
    >
      <div className='bg-hero-primary group flex h-full min-h-[290px] flex-col justify-between gap-9 rounded-3xl p-10 transition-all duration-500 ease-in-out hover:scale-[1.03]'>
        <div className='flex flex-col gap-5'>
          <Text as='h3' level='headerMedium' className=''>
            {title}
          </Text>
          <Text
            as='p'
            level='bodyMedium'
            className='font-normal text-[#A2A2A2]'
          >
            {description}
          </Text>
        </div>
        <div>
          <span className='flex flex-row items-center'>
            <Text level='buttonMedium'>{buttonText}</Text>
            <CgArrowRight
              size={24}
              className='text-accent-1 ml-2 transition-transform duration-500 group-hover:translate-x-6'
            />
          </span>
        </div>
      </div>
    </Link>
  );
};

export const mentatConfigMap = [
  {
    name: 'root',
    title: 'Optimised Root',
    blurb: 'Earn optimized TAO returns without risking your capital.',
    points: ['TAO only', 'Optimized APY', 'No subnet tokens', 'No price risk'],
  },
  {
    name: 'protectedalpha',
    title: 'Protected Alpha',
    blurb:
      'Gradually buy subnet tokens by reinvesting your dividends into the top 15 subnets.',
    points: [
      'Initial capital stays in TAO',
      'Dividends reinvested weekly',
      'Top 15 subnets',
      'Optimize risk/return',
    ],
  },
  {
    name: 'alpha',
    title: 'Alpha 5',
    blurb:
      'Higher risks, higher rewards: invest your TAO in the top 5 subnets weighted by market capitalization.',
    points: [
      'Full subnet exposure',
      'Top 5 subnets',
      'Active rebalancing',
      'Risk of loss',
    ],
  },
  {
    name: 'macrocosmos',
    title: 'Macrocosmos',
    blurb:
      'Invest your TAO across the unique constellation of Macrocosmos subnets.',
    points: [
      'Macrocosmos subnets: 1, 9, 13, 25, 37',
      'Weighted by market cap',
      'Active rebalancing',
      'Risk of loss',
    ],
  },
  {
    name: 'rayonLabs',
    title: 'Rayon Labs',
    blurb:
      'Invest your TAO in Rayon Labs subnets, the future of decentralized AI.',
    points: [
      'Rayon Labs subnets: 64, 56, 19',
      'Weighted by market cap',
      'Active rebalancing',
      'Risk of loss',
    ],
  },
  {
    name: 'prediction',
    title: 'Prediction',
    blurb: 'Invest your TAO in the top 3 prediction subnets by market cap.',
    points: [
      'Top 3 prediction subnets by market cap',
      'Weighted by market cap',
      'Active rebalancing',
      'Risk of loss',
    ],
  },
  {
    name: 'inference',
    title: 'Inference',
    blurb: 'Invest your TAO in the top 3 inference subnets by market cap.',
    points: [
      'Top 3 inference subnets by market cap',
      'Weighted by market cap',
      'Active rebalancing',
      'Risk of loss',
    ],
  },
];

import type { ReactNode } from 'react';
import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, BiRefresh } from 'react-icons/bi';
import {
  <PERSON><PERSON>,
  <PERSON>,
  Flex,
  Spinner,
  TaoOrAlphaValueDisplay,
  Text,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import type { mentatConfigMap } from './lib/config';
import { useMentatStrategyApy, type useMentatStrategies } from './lib/hooks';
import { useStakeContext } from '@/app/stake/lib/contexts/stake-context';
import { roundDown } from '@/lib/utils/number-utils';

type Strategy = NonNullable<
  ReturnType<typeof useMentatStrategies>['data']
>[number];
type MentatConfig = (typeof mentatConfigMap)[number];

export const MentatUiCard = ({
  strategy,
  config,
  isProxiedToMentat,
  strategyProxyAddress,
  handleDelegateClick,
  handleRemoveProxyClick,
}: {
  strategy: Strategy;
  config: MentatConfig;
  isProxiedToMentat: boolean;
  strategyProxyAddress: string | null;
  handleDelegateClick: (proxyAddress: string, hotkeyAddress: string) => void;
  handleRemoveProxyClick: (proxyAddress: string) => void;
}) => {
  const { totalTao } = useStakeContext();
  const isProxiedToThisStrategy =
    strategyProxyAddress === strategy.proxyAddress;

  return (
    <Card
      title={config.title}
      headerRight={
        <HeaderRight
          walletAddress={strategy.walletAddress}
          isProxiedToThisStrategy={isProxiedToThisStrategy}
        />
      }
      className={cn({
        'opacity-50': isProxiedToMentat && !isProxiedToThisStrategy,
        'border-accent-1 border': isProxiedToThisStrategy,
      })}
    >
      <Flex col className='gap-10'>
        <ul className='ml-6 grid list-disc grid-cols-2 gap-x-8 gap-y-1 text-sm'>
          {config.points.map((point) => (
            <li key={point}>{point}</li>
          ))}
        </ul>
        <Text level='bodyMedium' className='text-accent-1'>
          {config.blurb}
        </Text>

        {isProxiedToThisStrategy ? (
          <div className='border-accent-1 flex items-center gap-2 rounded-lg border p-2'>
            <Text as='span' level='bodyLarge'>
              Balance:
            </Text>
            <TaoOrAlphaValueDisplay
              valueTextLevel='bodyLarge'
              areDecimalsSmall
              value={totalTao}
              valueFormattingOptions={{
                minimumFractionDigits: 2,
                maximumFractionDigits: 5,
              }}
            />
          </div>
        ) : null}

        <div>
          {!isProxiedToThisStrategy ? (
            <Button
              disabled={isProxiedToMentat}
              variant='cta2'
              onClick={() => {
                handleDelegateClick(
                  strategy.proxyAddress,
                  strategy.hotkeyAddress
                );
              }}
            >
              Delegate
              <BiChart className='ml-2' />
            </Button>
          ) : (
            <Button
              variant='cta2'
              onClick={() => {
                handleRemoveProxyClick(strategy.proxyAddress);
              }}
            >
              Revoke
              <BiRefresh className='ml-2' />
            </Button>
          )}
        </div>
      </Flex>
    </Card>
  );
};

const HeaderRight = ({
  walletAddress,
  isProxiedToThisStrategy,
}: {
  walletAddress: string;
  isProxiedToThisStrategy: boolean;
}) => {
  const apyQuery = useMentatStrategyApy({
    walletAddress,
  });

  if (apyQuery.isLoading) {
    return <Spinner />;
  }

  if (apyQuery.isSuccess) {
    if (isProxiedToThisStrategy) {
      return (
        <ApyTooltip>
          <Apy apy={apyQuery.data.weightedAPY} />
        </ApyTooltip>
      );
    }

    return <Apy apy={apyQuery.data.weightedAPY} />;
  }

  return null;
};

const Apy = ({ apy }: { apy: number }) => {
  return (
    <Text level='bodyMedium' className='text-accent-1'>
      {roundDown(apy, 2)}% APY
    </Text>
  );
};

const ApyTooltip = ({ children }: { children: ReactNode }) => {
  const [open, setOpen] = useState(false);
  return (
    <Tooltip open={open}>
      <TooltipTrigger asChild>
        <button
          type='button'
          className='cursor-pointer'
          onMouseEnter={() => {
            setOpen(true);
          }}
          onMouseLeave={() => {
            setOpen(false);
          }}
          onTouchStart={(e) => {
            e.stopPropagation();
            setOpen(!open);
          }}
        >
          {children}
        </button>
      </TooltipTrigger>

      <TooltipContent side='bottom' align='center'>
        <div className='flex max-w-xs flex-col gap-2'>
          <Text level='bodyMedium'>
            <span className='text-accent-1'>
              Estimation of the quantity of tokens earned over a year when using
              this strategy.
            </span>
            <br />
            <br />
            Based on the last 15 days of data, weighted according to the
            specific strategy allocation across subnets.
            <br />
            <br />
            Past performance is not indicative of future results.
          </Text>
        </div>
      </TooltipContent>
    </Tooltip>
  );
};

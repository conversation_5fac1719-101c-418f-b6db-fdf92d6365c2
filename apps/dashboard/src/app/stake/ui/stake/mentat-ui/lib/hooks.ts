import { useQuery } from '@tanstack/react-query';
import { BigNumber } from 'bignumber.js';
import { notify } from '@repo/ui/lib';
import { mentatConfigMap } from './config';
import { apiClient, handleResponse } from '@/app/api/[...route]/lib/api-client';
import { useCurrentPositionItems } from '@/app/stake/lib/hooks';
import { useAccountContext } from '@/contexts/chain-and-wallet/account-context';
import { useChainAndWalletContext } from '@/contexts/chain-and-wallet/chain-and-wallet-context';
import type { HandleSubmitTransactionParams } from '@/contexts/chain-and-wallet/types';
import { useTransactionSubmitter } from '@/contexts/chain-and-wallet/use-transaction-submitter';
import { usePriceLimitCalculator } from '@/lib/hooks/staking-hooks';
import {
  chainConvertFormat,
  useChainAmountConverter,
} from '@/lib/utils/conversion/conversion-utils';
import { roundDown } from '@/lib/utils/number-utils';

const mentatApiClient = apiClient.mentat;

export const useMentatStrategies = () => {
  const query = useQuery({
    queryKey: ['mentat-strategies'],
    queryFn: async () => {
      return handleResponse(await mentatApiClient.strategies.$get());
    },
    select: (data) => {
      // We only want the strategies that are mentatDynamic
      return data.filter((strategy) =>
        strategy.origin.includes('mentatDynamic')
      );
    },
  });

  return query;
};

export const useMentatStrategyApy = ({
  walletAddress,
}: {
  walletAddress: string;
}) => {
  const query = useQuery({
    queryKey: ['mentat-strategy-apy', walletAddress],
    queryFn: async () => {
      return handleResponse(
        await mentatApiClient.wallet[':walletAddress'].apy.$get({
          param: { walletAddress },
        })
      );
    },
  });

  return query;
};

// Hold back a buffer when staking to Root to pay future fees
const stakingToRootBuffer = 0.01;
// Hold back some dust when unstaking in case of rounding differences/small price changes
const unstakingBufferInRao = 100;

const useRemoveStakeTransactionsBuilder = () => {
  const {
    state: { api },
  } = useChainAndWalletContext();
  const { convertAmount } = useChainAmountConverter();
  const { getRemoveStakePriceLimit } = usePriceLimitCalculator();
  const { getCurrentPositionItems } = useCurrentPositionItems({
    shouldRoundAmounts: false,
  });

  const getRemoveStakeTransactions = () => {
    if (!api) {
      notify.error('Could not find connection');
      return {
        removeStakeTransactions: [],
        unstakedTaoTotal: 0,
      };
    }

    const normalisedData = getCurrentPositionItems();
    const removeStakeTransactions = normalisedData.map((item) => {
      return api.tx.subtensorModule.removeStakeLimit(
        item.validatorHotkey,
        item.netuid,
        convertAmount(
          roundDown(item.balanceAsAlpha, 5),
          chainConvertFormat.hToC
        ).toNumber() - unstakingBufferInRao,
        getRemoveStakePriceLimit(item.netuid).toNumber(),
        false
      );
    });

    const unstakedTaoTotal = normalisedData.reduce(
      (acc, item) => acc + roundDown(item.balanceAsTao - 0.00005, 5),
      0
    );

    return { removeStakeTransactions, unstakedTaoTotal };
  };

  return {
    getRemoveStakeTransactions,
  };
};

const useAddMentatProxyTransactionBuilder = () => {
  const {
    state: { api },
  } = useChainAndWalletContext();
  const {
    state: { availableBalance },
  } = useAccountContext();
  const { convertAmount } = useChainAmountConverter();
  const { getRemoveStakeTransactions } = useRemoveStakeTransactionsBuilder();

  const buildTransaction = (
    proxyAddress: string,
    hotkeyAddress: string,
    transactionFee: number | null
  ) => {
    if (!api) {
      notify.error('Could not find connection');
      return null;
    }

    const { removeStakeTransactions: txs, unstakedTaoTotal } =
      getRemoveStakeTransactions();

    const reservationFee = 0.093;
    const availableBalanceTao = roundDown(BigNumber(availableBalance), 5);
    const totalToStake =
      unstakedTaoTotal +
      availableBalanceTao -
      reservationFee -
      (transactionFee ?? 0) -
      stakingToRootBuffer;

    const totalToStakeChain = convertAmount(
      totalToStake,
      chainConvertFormat.hToC
    ).toNumber();

    txs.push(
      api.tx.subtensorModule.addStake(hotkeyAddress, 0, totalToStakeChain)
    );

    // Prepend proxy tx
    // For method info, see https://polkadot.js.org/apps/?rpc=wss://entrypoint-finney.opentensor.ai:443#/extrinsics
    txs.unshift(
      api.tx.proxy.addProxy(
        proxyAddress,
        'Staking', // <-- Proxy Type
        0 // <-- Delay
      )
    );

    return { tx: api.tx.utility.batchAll(txs) };
  };

  return {
    buildTransaction,
  };
};

export const useAddMentatProxyLogic = () => {
  const { buildTransaction } = useAddMentatProxyTransactionBuilder();
  const {
    state: { api },
  } = useChainAndWalletContext();
  const { calculateTransactionFee, submitTx, isPending, txStatus } =
    useTransactionSubmitter();

  const handleAddMentatProxyClick = async (
    {
      proxyAddress,
      hotkeyAddress,
    }: { proxyAddress: string; hotkeyAddress: string },
    params: HandleSubmitTransactionParams
  ) => {
    if (!api) {
      return;
    }

    const buildTransactionResult = buildTransaction(
      proxyAddress,
      hotkeyAddress,
      null
    );
    if (!buildTransactionResult) {
      notify.error('Could not build transaction');
      return;
    }

    let tx = buildTransactionResult.tx;
    const transactionFee = await calculateTransactionFee(tx);

    if (transactionFee) {
      const buildTransactionResultWithFees = buildTransaction(
        proxyAddress,
        hotkeyAddress,
        transactionFee
      );
      if (!buildTransactionResultWithFees) {
        notify.error('Could not build transaction with fees');
        return;
      }

      tx = buildTransactionResultWithFees.tx;
    }

    // Submit the transaction
    await submitTx(tx, params);
  };

  return {
    handleAddMentatProxyClick,
    isPending,
    txStatus,
  };
};

const useRemoveMentatProxyTransactionBuilder = () => {
  const {
    state: { api },
  } = useChainAndWalletContext();
  const { getRemoveStakeTransactions } = useRemoveStakeTransactionsBuilder();

  const buildRemoveMentatProxyTransaction = (proxyAddress: string) => {
    if (!api) {
      notify.error('Could not find connection');
      return null;
    }

    const { removeStakeTransactions: txs } = getRemoveStakeTransactions();

    // Append remove proxy tx
    // For method info, see https://polkadot.js.org/apps/?rpc=wss://entrypoint-finney.opentensor.ai:443#/extrinsics
    txs.push(
      api.tx.proxy.removeProxy(
        proxyAddress,
        'Staking', // <-- Proxy Type
        0 // <-- Delay
      )
    );

    return { tx: api.tx.utility.batchAll(txs) };
  };

  return {
    buildRemoveMentatProxyTransaction,
  };
};

export const useRemoveMentatProxyLogic = () => {
  const { buildRemoveMentatProxyTransaction } =
    useRemoveMentatProxyTransactionBuilder();
  const {
    state: { api },
  } = useChainAndWalletContext();
  const { submitTx, isPending, txStatus } = useTransactionSubmitter();

  const handleRemoveMentatProxyClick = async (
    proxyAddress: string,
    params: HandleSubmitTransactionParams
  ) => {
    if (!api) {
      notify.error('Could not find connection');
      return;
    }

    const buildTransactionResult =
      buildRemoveMentatProxyTransaction(proxyAddress);
    if (!buildTransactionResult) {
      notify.error('Could not build transaction');
      return;
    }

    // Submit the transaction
    await submitTx(buildTransactionResult.tx, params);
  };

  return {
    handleRemoveMentatProxyClick,
    isPending,
    txStatus,
  };
};

export const useMentatConfigMap = () => {
  const getConfigByName = (name: string) => {
    return mentatConfigMap.find((c) => c.name === name);
  };

  return {
    configMap: mentatConfigMap,
    getConfigByName,
  };
};

export const useMentatStrategyCurrentPosition = () => {
  const {
    state: { proxies },
  } = useAccountContext();
  const strategiesQuery = useMentatStrategies();
  const { getConfigByName } = useMentatConfigMap();

  if (proxies.length === 0) {
    return {
      strategyName: null,
      isProxiedToMentat: false,
      config: null,
    };
  }

  // `if any proxies match any strategies, then the account is proxied to Mentat
  const proxyAddresses = proxies.map((proxy) => proxy.proxyAddress);
  const strategies = strategiesQuery.data;
  const matchingStrategies =
    strategies?.filter((strategy) =>
      proxyAddresses.includes(strategy.proxyAddress)
    ) ?? [];
  const isProxiedToMentat = matchingStrategies.length > 0;

  // If the account is proxied to Mentat, then get the strategy name and config
  const strategyProxyAddress = isProxiedToMentat
    ? matchingStrategies[0]?.proxyAddress
    : null;
  const strategyName = isProxiedToMentat ? matchingStrategies[0]?.name : null;
  const config =
    isProxiedToMentat && strategyName ? getConfigByName(strategyName) : null;

  return {
    strategyName,
    strategyProxyAddress,
    isProxiedToMentat,
    config,
  };
};

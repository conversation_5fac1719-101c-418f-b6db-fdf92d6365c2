import { useWindowSize } from 'usehooks-ts';
import { SelectedSubnetCard } from './selected-subnet-card';
import { SelectedSubnetTableRow } from './selected-subnet-table-row';
import { useStakeContext } from '@/app/stake/lib/contexts/stake-context';

export const SelectedSubnets = () => {
  const windowSize = useWindowSize();

  const { selectedSubnets } = useStakeContext();

  if (selectedSubnets.length === 0) {
    return null;
  }

  if (windowSize.width < 640) {
    return (
      <>
        {selectedSubnets.map(({ selectedSubnetId }) => (
          <SelectedSubnetCard
            key={selectedSubnetId}
            selectedSubnetId={selectedSubnetId}
          />
        ))}
      </>
    );
  }

  return (
    <div>
      <table className='w-full [&_td:first-child]:pl-0 [&_td:last-child]:pr-0 [&_td]:px-4 [&_td]:py-0'>
        <tbody>
          {selectedSubnets.map(({ selectedSubnetId }) => (
            <SelectedSubnetTableRow
              key={selectedSubnetId}
              selectedSubnetId={selectedSubnetId}
            />
          ))}
        </tbody>
      </table>
      {/* This is a temporary hack to get the slippage warning colour to be included by tw */}
      <div className='text-slippage-warning hidden' />
    </div>
  );
};

import { BiTrash, BiLockAlt } from 'react-icons/bi';
import {
  Button,
  Combobox,
  Text,
  TooltipProvider,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@repo/ui/components';
import { cn, notify } from '@repo/ui/lib';
import { AmountToDelegate } from './amount-to-delegate';
import { SubnetSlider } from './subnet-slider';
import { useStakeContext } from '@/app/stake/lib/contexts/stake-context';
import { useSubnetSummary } from '@/app/stake/lib/hooks';
import {
  DelegationAmountDifference,
  SlippageMessage,
} from '@/app/stake/ui/stake/components';
import { useValidatorComboboxOptions } from '@/lib/hooks/global-hooks';
import { formatNumber2dp, TAO_CURRENCY } from '@/lib/utils';

export const SelectedSubnetTableRow = ({
  selectedSubnetId,
}: {
  selectedSubnetId: string;
}) => {
  const {
    addSubnet,
    removeSubnet,
    updateSliderLocked,
    stakingType,
    getInitialAmountForSubnet,
    updateAmountToDelegateToSubnet,
  } = useStakeContext();
  const { getSubnetSummary } = useSubnetSummary();
  const subnetSummary = getSubnetSummary(selectedSubnetId);
  const { getValidatorOptions } = useValidatorComboboxOptions();

  return (
    <tr key={selectedSubnetId}>
      <td className='w-fit whitespace-nowrap'>
        <div className='flex flex-row gap-2'>
          <Text level='metadata' className='text-white/60'>
            {subnetSummary.netuid}:
          </Text>

          <div className='flex flex-col items-start gap-2'>
            <Text level='buttonMedium' className='text-white'>
              {subnetSummary.subnetName}
            </Text>
            {subnetSummary.validatorName === 'Unknown' ? (
              <Text level='buttonMedium' className='text-white/60'>
                {subnetSummary.validatorName}
              </Text>
            ) : (
              <div>
                <Combobox
                  className='-ml-4 w-[250px] border-transparent text-white/60'
                  value={subnetSummary.validatorSs58}
                  onChange={(value) => {
                    if (
                      addSubnet({
                        netuid: subnetSummary.netuid,
                        validatorSs58: value,
                      })
                    ) {
                      notify.success('Subnet/validator combination added');
                    }
                  }}
                  options={getValidatorOptions(
                    subnetSummary.netuid,
                    subnetSummary.validatorSs58
                  )}
                />
              </div>
            )}
          </div>
        </div>
      </td>
      <td className='w-full'>
        <div className='mr-8 flex flex-col gap-4'>
          <div className='flex items-center justify-center gap-2'>
            <DelegationAmountDifference
              amountDifference={subnetSummary.amountToDelegateDifference}
              symbol={TAO_CURRENCY}
            />

            <SlippageMessage slippage={subnetSummary.slippage} />
          </div>
          <div className='mb-8 flex items-center gap-2'>
            <SubnetSlider
              stakingType={stakingType}
              subnetSummary={subnetSummary}
            />
            {stakingType === 'auto-balance' ? (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <BiLockAlt
                      className={cn(
                        'cursor-pointer text-white/60',
                        subnetSummary.isLocked && 'text-accent-1'
                      )}
                      size={25}
                      onClick={() => {
                        updateSliderLocked(
                          selectedSubnetId,
                          !subnetSummary.isLocked
                        );
                      }}
                    />
                  </TooltipTrigger>
                  <TooltipContent side='bottom' align='center'>
                    <div className='flex max-w-xs flex-col gap-2'>
                      <Text level='bodyMedium'>
                        <span className='text-accent-1'>Lock Proportion</span>
                        <br />
                        This will lock the proportion of your stake to this
                        subnet at the current value. If you move the other
                        sliders, the auto balancing process will not update
                        locked subnets.
                      </Text>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ) : (
              <div className='h-[25px] w-[25px]' />
            )}
          </div>
        </div>
      </td>
      <td>
        <div className='flex w-[100px] flex-col items-start gap-1'>
          <AmountToDelegate
            selectedSubnetId={selectedSubnetId}
            isLocked={subnetSummary.isLocked}
            amount={subnetSummary.subnetAmountToDelegateTao}
          />
          <Text level='bodySmall' className='text-sm text-white/60'>
            {formatNumber2dp(subnetSummary.sliderValue)}%
          </Text>
        </div>
      </td>
      <td className='text-right'>
        <div className='mb-4 flex items-start justify-end gap-2'>
          <div className='flex w-[100px] flex-col gap-1'>
            <Text level='headerSmall' className='text-white/60'>
              {formatNumber2dp(subnetSummary.subnetAmountToDelegateAlpha)}
            </Text>
          </div>
          {subnetSummary.subnetAlphaSymbol ? (
            <span className='flex h-6 w-6 items-center justify-center rounded-full bg-white text-base text-black'>
              {subnetSummary.subnetAlphaSymbol}
            </span>
          ) : null}
        </div>
      </td>
      <td>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant='ghost'
                size='sm'
                className='text-white/20 hover:text-red-400'
                onClick={() => {
                  const initialAmount =
                    getInitialAmountForSubnet(selectedSubnetId);
                  if (initialAmount > 0) {
                    updateAmountToDelegateToSubnet(selectedSubnetId, 0);
                  } else {
                    removeSubnet(selectedSubnetId);
                  }
                }}
              >
                <BiTrash size={25} />
              </Button>
            </TooltipTrigger>
            <TooltipContent side='bottom' align='center'>
              <div className='flex max-w-xs flex-col gap-2'>
                <Text level='bodyMedium'>
                  <span className='text-accent-1'>Remove</span>
                  <br />
                  If you have just added this subnet, you can remove it. If you
                  are staked to this subnet, this will set your staked amount
                  for this subnet to 0.
                  <br />
                  <span className='text-[0.95rem]'>
                    <span className='text-accent-1'>Note</span>
                    <br />
                    If you want to remove this item from your chain transaction
                    completely, you can do so during the confirmation step.
                  </span>
                </Text>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </td>
    </tr>
  );
};

import { memo } from 'react';
import { Slide<PERSON> } from '@repo/ui/components';
import { useStakeContext } from '@/app/stake/lib/contexts/stake-context';
import type { SubnetSummary } from '@/app/stake/lib/hooks';
import type { StakingTypes } from '@/app/stake/lib/types';

export const SubnetSlider = memo(function SubnetSlider({
  subnetSummary,
  stakingType,
}: {
  subnetSummary: SubnetSummary;
  stakingType: StakingTypes;
}) {
  const { updateSliderValue } = useStakeContext();
  return (
    <Slider
      disabled={Boolean(
        subnetSummary.isLocked && stakingType === 'auto-balance'
      )}
      value={[subnetSummary.sliderValue]}
      onValueChange={(values) => {
        updateSliderValue(subnetSummary.selectedSubnetId, values[0]);
      }}
      max={100}
      step={0.05}
      className='w-full'
    />
  );
});

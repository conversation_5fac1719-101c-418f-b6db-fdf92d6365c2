import { BiTrash, BiLockAlt } from 'react-icons/bi';
import { Combobox, Text } from '@repo/ui/components';
import { cn, notify } from '@repo/ui/lib';
import { AmountToDelegate } from './amount-to-delegate';
import { SubnetSlider } from './subnet-slider';
import { useStakeContext } from '@/app/stake/lib/contexts/stake-context';
import { useSubnetSummary } from '@/app/stake/lib/hooks';
import {
  DelegationAmountDifference,
  SlippageMessage,
} from '@/app/stake/ui/stake/components';
import { useValidatorComboboxOptions } from '@/lib/hooks/global-hooks';
import { formatNumber2dp, TAO_CURRENCY } from '@/lib/utils';

export const SelectedSubnetCard = ({
  selectedSubnetId,
}: {
  selectedSubnetId: string;
}) => {
  const { addSubnet, removeSubnet, updateSliderLocked, stakingType } =
    useStakeContext();
  const { getSubnetSummary } = useSubnetSummary();
  const subnetSummary = getSubnetSummary(selectedSubnetId);
  const { getValidatorOptions } = useValidatorComboboxOptions();

  return (
    <div
      key={selectedSubnetId}
      className='flex flex-col gap-4 rounded-xl bg-[#1d1d1d] p-5'
    >
      <div className='flex flex-row gap-1'>
        <Text level='metadata' className='text-white/60'>
          {subnetSummary.netuid}:
        </Text>
        <div className='flex flex-col items-start gap-0'>
          <Text level='buttonMedium' className='text-white'>
            {subnetSummary.subnetName}
          </Text>
          {subnetSummary.validatorName === 'Unknown' ? (
            <Text level='buttonMedium' className='text-white/60'>
              {subnetSummary.validatorName}
            </Text>
          ) : (
            <div>
              <Combobox
                className='-ml-4 w-[250px] border-transparent bg-transparent text-white/60'
                value={subnetSummary.validatorSs58}
                onChange={(value) => {
                  if (
                    addSubnet({
                      netuid: subnetSummary.netuid,
                      validatorSs58: value,
                    })
                  ) {
                    notify.success('Subnet/validator combination added');
                  }
                }}
                options={getValidatorOptions(
                  subnetSummary.netuid,
                  subnetSummary.validatorSs58
                )}
              />
            </div>
          )}
        </div>
      </div>

      <div className='flex flex-row items-center justify-between'>
        <div className='flex flex-row items-baseline justify-start gap-1'>
          <AmountToDelegate
            selectedSubnetId={selectedSubnetId}
            isLocked={subnetSummary.isLocked}
            amount={subnetSummary.subnetAmountToDelegateTao}
          />
          <Text level='bodySmall' className='text-sm text-white/60'>
            {formatNumber2dp(subnetSummary.sliderValue)}%
          </Text>
        </div>

        <div className='flex items-baseline justify-end gap-2'>
          <Text level='headerSmall' className='text-white/60'>
            {formatNumber2dp(subnetSummary.subnetAmountToDelegateAlpha)}
          </Text>
          {subnetSummary.subnetAlphaSymbol ? (
            <span className='flex h-[30px] w-[30px] items-center justify-center rounded-full bg-white text-base text-black'>
              {subnetSummary.subnetAlphaSymbol}
            </span>
          ) : null}
        </div>
      </div>

      <div className='w-full'>
        <div className='flex flex-col gap-4'>
          <div className='flex items-center justify-start gap-2'>
            <DelegationAmountDifference
              amountDifference={subnetSummary.amountToDelegateDifference}
              symbol={TAO_CURRENCY}
            />

            <SlippageMessage slippage={subnetSummary.slippage} />
          </div>
          <div className='flex items-center justify-between gap-8'>
            <SubnetSlider
              stakingType={stakingType}
              subnetSummary={subnetSummary}
            />
            <div className='flex items-center justify-end gap-2'>
              <BiLockAlt
                className={cn(
                  'cursor-pointer text-white/20',
                  subnetSummary.isLocked && 'text-accent-1'
                )}
                size={25}
                onClick={() => {
                  updateSliderLocked(selectedSubnetId, !subnetSummary.isLocked);
                }}
              />
              <BiTrash
                className={cn('cursor-pointer text-white/20')}
                size={25}
                onClick={() => {
                  removeSubnet(selectedSubnetId);
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

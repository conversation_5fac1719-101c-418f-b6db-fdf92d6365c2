import { useState } from 'react';
import { BiLockAlt } from 'react-icons/bi';
import { z } from 'zod';
import { notify } from '@repo/ui/lib';
import { useStakeContext } from '@/app/stake/lib/contexts/stake-context';
import { TaoBadge } from '@/app/stake/ui/stake/components';
import { formatNumber2dp } from '@/lib/utils';
import { roundDown } from '@/lib/utils/number-utils';

const inputId = 'amount-to-delegate-input';

export const AmountToDelegate = ({
  selectedSubnetId,
  isLocked,
  amount,
}: {
  selectedSubnetId: string;
  isLocked: boolean;
  amount: number;
}) => {
  const { amountToDelegateTao, updateAmountToDelegateToSubnet } =
    useStakeContext();

  const [isEditing, setIsEditing] = useState(false);
  const [inputValue, setInputValue] = useState('');

  const roundedAmount = roundDown(amount, 5);

  return isEditing ? (
    <TaoBadge>
      <input
        id={inputId}
        type='text'
        value={inputValue}
        onBlur={() => {
          setIsEditing(false);
        }}
        onChange={(e) => {
          setInputValue(e.target.value);
        }}
        onKeyDown={(e) => {
          if (e.key === 'Enter') {
            if (isLocked) {
              setIsEditing(false);
              return;
            }
            try {
              const schema = z.number().min(0);
              const value = schema.parse(Number(inputValue));
              updateAmountToDelegateToSubnet(selectedSubnetId, value);
              setIsEditing(false);
            } catch (err) {
              notify.error(
                `Please enter a number between 0 and ${formatNumber2dp(amountToDelegateTao)}`
              );
            }
          } else if (e.key === 'Escape') {
            setIsEditing(false);
          }
        }}
        className='w-[1ch] max-w-[100px] bg-transparent focus:outline-none'
        style={{ width: `${Math.max(inputValue.length, 1)}ch` }}
        autoComplete='off'
      />
    </TaoBadge>
  ) : (
    <TaoBadge
      onClick={() => {
        if (isLocked) {
          notify.icon(
            'Locked. Unlock to edit.',
            <BiLockAlt className='text-accent-1' />
          );
          return;
        }
        setIsEditing(true);
        setInputValue(roundedAmount.toString());
        setTimeout(() => {
          const input = document.getElementById(inputId);
          if (input) {
            input.focus();
          }
        }, 250);
      }}
    >
      {/* {formatNumber2dp(roundDown(amount, 4))}τ */}
      {roundedAmount}τ
    </TaoBadge>
  );
};

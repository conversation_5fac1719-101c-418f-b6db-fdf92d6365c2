import { memo, useState } from 'react';
import type { SortingState } from '@tanstack/react-table';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
} from '@tanstack/react-table';
import { DataTable } from '@repo/ui/components';
import type { TransactionType } from './lib/types';
import { useColumns } from './utils';
import type { CurrentPositionItem } from '@/app/stake/lib/types';

export const CurrentHoldingsTable = memo(function CurrentHoldingsTable({
  data,
  handleAddAllTransactions,
}: {
  data: CurrentPositionItem[];
  handleAddAllTransactions: (transactionType: TransactionType) => void;
}) {
  const [sorting, setSorting] = useState<SortingState>([
    { id: 'holdings-col', desc: false },
  ]);
  const columns = useColumns(handleAddAllTransactions);
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    state: {
      sorting,
    },
    debugTable: true,
  });

  return <DataTable table={table} hasBorder={false} />;
});

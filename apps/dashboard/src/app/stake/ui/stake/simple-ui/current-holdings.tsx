import { useCallback, useMemo } from 'react';
import { useWindowSize } from 'usehooks-ts';
import { CurrentHoldingsCards } from './current-holdings-cards';
import { CurrentHoldingsTable } from './current-holdings-table';
import { useSimpleUiContext } from './lib/context';
import type { TransactionType } from './lib/types';
import { useCurrentPositionItems } from '@/app/stake/lib/hooks';

export const CurrentHoldings = () => {
  const windowSize = useWindowSize();
  const { addTransaction } = useSimpleUiContext();
  const { getCurrentPositionItems: getNormalisedCurrentPositionItems } =
    useCurrentPositionItems();
  const normalisedData = useMemo(
    () => getNormalisedCurrentPositionItems(),
    [getNormalisedCurrentPositionItems]
  );

  const handleAddAllTransactions = useCallback(
    (transactionType: TransactionType) => {
      normalisedData.forEach((item) => {
        addTransaction(transactionType, {
          netuid: item.netuid,
          validatorHotkey: item.validatorHotkey,
          inputState: {
            value:
              transactionType === 'buy' ? '0' : item.balanceAsAlpha.toString(),
            activeField: transactionType === 'buy' ? 'TAO' : 'ALPHA',
          },
        });
      });
    },
    [addTransaction, normalisedData]
  );

  if (windowSize.width < 640) {
    return (
      <CurrentHoldingsCards
        data={normalisedData}
        handleAddAllTransactions={handleAddAllTransactions}
      />
    );
  }

  return (
    <CurrentHoldingsTable
      data={normalisedData}
      handleAddAllTransactions={handleAddAllTransactions}
    />
  );
};

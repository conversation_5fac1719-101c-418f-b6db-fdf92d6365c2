import { BigNumber } from 'bignumber.js';
import { BiCoinStack, BiTrash, BiWallet } from 'react-icons/bi';
import { CgCreditCard } from 'react-icons/cg';
import {
  AlertError,
  Button,
  Card,
  Flex,
  Input,
  Text,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { useSimpleUiContext } from './lib/context';
import type { TokenType } from './lib/types';
import { SubnetPicker } from '@/components/subnet-picker';
import { ValidatorPicker } from '@/components/validator-picker';
import { useChainAndWalletContext } from '@/contexts/chain-and-wallet/chain-and-wallet-context';
import { useSubnetLookup } from '@/lib/hooks/global-hooks';
import { useMaxSlippage, useSlippageHelper } from '@/lib/hooks/staking-hooks';
import { formatNumber, TAO_CURRENCY } from '@/lib/utils';

export const BuySellItem = ({ index }: { index: number }) => {
  const {
    updateValidatorHotkey,
    getFieldValues,
    getTransaction,
    updateSubnetId,
    removeTransaction,
    errors,
  } = useSimpleUiContext();
  const { maxSlippage } = useMaxSlippage();
  const transaction = getTransaction(index);
  const { slippage } = getFieldValues(transaction.id);
  const isSlippageExceeded = BigNumber(slippage).gt(maxSlippage);
  const isBuy = transaction.type === 'buy';

  const errorsForThisTransaction = errors.filter(
    (error) => error.transactionId === transaction.id
  );
  const errorQty = errorsForThisTransaction.length;

  return (
    <Card className='w-full'>
      <Flex className='justify-between gap-2'>
        <Text
          level='bodyLarge'
          className={cn(
            'flex flex-row items-center gap-2 font-medium opacity-60',
            isBuy ? 'text-accent-1' : 'text-accent-2'
          )}
        >
          {isBuy ? 'Buy' : 'Sell'}
          {isBuy ? (
            <BiWallet className='ml-1' />
          ) : (
            <BiCoinStack className='ml-1' />
          )}
        </Text>

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              size='sm'
              variant='cta3'
              className='items-center justify-center px-2 text-base'
              onClick={() => {
                removeTransaction(transaction.id);
              }}
            >
              <BiTrash />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <Text as='p' level='bodyMedium' className='text-[#A2A2A2]'>
              Remove Transaction
            </Text>
          </TooltipContent>
        </Tooltip>
      </Flex>
      <div className='flex flex-col gap-8'>
        <div className='flex flex-col gap-4'>
          <div className='grid grid-cols-1 gap-4'>
            <Flex col className='gap-2'>
              <Text
                as='label'
                level='labelSmall'
                className='font-medium opacity-60'
              >
                Subnet
              </Text>
              <SubnetPicker
                subnetId={transaction.netuid}
                onSelect={(subnetId) => {
                  updateSubnetId(index, subnetId);
                }}
                className='bg-input-primary hover:bg-input-primary h-12 w-full justify-between rounded-lg border-transparent text-sm text-white/60'
              />
            </Flex>
            <Flex col className='gap-2'>
              <ValidatorPicker
                subnetId={transaction.netuid ?? 0}
                value={transaction.validatorHotkey ?? ''}
                onChange={(value) => {
                  updateValidatorHotkey(index, value);
                }}
                labelLevel='labelSmall'
                labelClassName='font-medium opacity-60'
                comboboxClassName='bg-input-primary hover:bg-input-primary h-12 w-full border-transparent text-sm text-white/60'
              />
            </Flex>
          </div>

          <InputFields index={index} />
          <SlippageAndAvailableSection index={index} />

          {errorQty > 0 ? (
            <AlertError
              className='animate-in fade-in-10 duration-300'
              alignItems='center'
              description={
                <div className='flex flex-row items-center gap-2'>
                  <Text level='labelSmall' className='font-medium opacity-60'>
                    {errorQty > 1 ? 'Some' : 'An'}{' '}
                    {errorQty > 1 ? 'errors' : 'error'} occurred:
                    <br />
                    {errorsForThisTransaction
                      .map((error) => error.message)
                      .join(', ')}
                  </Text>
                </div>
              }
            />
          ) : null}

          {isSlippageExceeded ? (
            <AlertError
              className='animate-in fade-in-10 duration-300'
              alignItems='center'
              description={
                <div className='flex flex-row items-center gap-2'>
                  <Text level='labelSmall' className='font-medium opacity-60'>
                    Slippage is too high. Your max is currently set to{' '}
                    {maxSlippage}%.
                  </Text>
                </div>
              }
            />
          ) : null}
        </div>
      </div>
    </Card>
  );
};

const FieldSuffix = ({
  type,
  subnetId,
}: {
  type: TokenType;
  subnetId: number;
}) => {
  const { getSubnetSymbol } = useSubnetLookup();

  return (
    <div className='opacity-60'>
      {type === 'TAO' ? (
        <span className='px-2 text-white'>{TAO_CURRENCY}</span>
      ) : (
        <span className='px-2 text-white'>{getSubnetSymbol(subnetId)}</span>
      )}
    </div>
  );
};

const InputFields = ({
  index,
  disabled = false,
}: {
  index: number;
  disabled?: boolean;
}) => {
  const { isConnectedToChain } = useChainAndWalletContext();
  const { handleInputChange, getFieldValues, getTransaction } =
    useSimpleUiContext();

  const transaction = getTransaction(index);
  const inputState = transaction.inputState;
  const { taoValue, alphaValue } = getFieldValues(transaction.id);
  return (
    <>
      <Input
        disabled={disabled}
        inputId='tao-input'
        IconLeft={CgCreditCard}
        placeholder='0.00000'
        className={cn(
          'rounded-lg border bg-[#2E2E2E]',
          !isConnectedToChain && 'opacity-50',
          inputState.activeField === 'TAO'
            ? 'border-[#00DBBC]'
            : 'border-[#262626]'
        )}
        inputClassName='text-sm leading-4 font-normal rounded-lg'
        rightComponent={
          transaction.netuid !== undefined ? (
            <FieldSuffix type='TAO' subnetId={transaction.netuid} />
          ) : null
        }
        value={taoValue}
        onChange={(e) => {
          handleInputChange(index, e.target.value, 'TAO');
        }}
      />
      <Input
        disabled={disabled}
        inputId='alpha-input'
        IconLeft={CgCreditCard}
        placeholder='0.00000'
        className={cn(
          'rounded-lg border bg-[#2E2E2E]',
          !isConnectedToChain && 'opacity-50',
          inputState.activeField === 'ALPHA'
            ? 'border-[#00DBBC]'
            : 'border-[#262626]'
        )}
        inputClassName='text-sm leading-4 font-normal rounded-lg'
        rightComponent={
          transaction.netuid !== undefined ? (
            <FieldSuffix type='ALPHA' subnetId={transaction.netuid} />
          ) : null
        }
        value={alphaValue}
        onChange={(e) => {
          handleInputChange(index, e.target.value, 'ALPHA');
        }}
      />
    </>
  );
};

const useMaxAmount = (index: number) => {
  const {
    availableTao,
    getDelegatedAlphaForHotkey,
    handleInputChange,
    getTransaction,
  } = useSimpleUiContext();
  const transaction = getTransaction(index);

  const handleMaxClick = () => {
    const isBuy = transaction.type === 'buy';
    const amount = isBuy
      ? availableTao
      : transaction.netuid !== undefined &&
          transaction.validatorHotkey !== undefined
        ? getDelegatedAlphaForHotkey(
            transaction.netuid,
            transaction.validatorHotkey
          )
        : 0;
    handleInputChange(index, amount.toString(), isBuy ? 'TAO' : 'ALPHA');
  };

  return { handleMaxClick };
};

const SlippageAndAvailableSection = ({ index }: { index: number }) => {
  const { handleMaxClick } = useMaxAmount(index);
  const { getTransaction, getFieldValues } = useSimpleUiContext();
  const { getSlippageTextClassName } = useSlippageHelper();

  const transaction = getTransaction(index);
  const { slippage } = getFieldValues(transaction.id);

  return (
    <div className='flex flex-col gap-2 max-sm:items-center max-sm:justify-center max-sm:gap-3 sm:flex-row sm:items-center sm:justify-between'>
      <div className='flex flex-row items-center gap-1 max-sm:justify-center'>
        <div className='flex flex-row items-center gap-1'>
          <Text
            level='labelSmall'
            className={cn(
              getSlippageTextClassName(BigNumber(slippage).toNumber()),
              'font-medium transition-colors duration-1000'
            )}
          >
            Slippage {slippage}%
          </Text>
        </div>
      </div>

      <div className='flex flex-row items-center gap-1 max-sm:justify-center'>
        <Button
          size='sm'
          variant='outline'
          className='h-6 px-2 py-0.5'
          onClick={() => {
            handleMaxClick();
          }}
        >
          Max
        </Button>
        <AvailableSection index={index} />
      </div>
    </div>
  );
};

const AvailableSection = ({ index }: { index: number }) => {
  const { availableTao, getDelegatedAlphaForHotkey, getTransaction } =
    useSimpleUiContext();
  const transaction = getTransaction(index);
  const { handleMaxClick } = useMaxAmount(index);
  const { getSubnetSymbol } = useSubnetLookup();

  const isBuy = transaction.type === 'buy';

  // For the buy section, we want to show the amount of tao we can use to buy alpha
  // For the sell section, we want to show the amount of alpha we currently have delegated to the selected validator which we can sell for tao
  const amount = isBuy
    ? availableTao
    : transaction.netuid !== undefined &&
        transaction.validatorHotkey !== undefined
      ? getDelegatedAlphaForHotkey(
          transaction.netuid,
          transaction.validatorHotkey
        )
      : 0;

  return (
    // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions -- Allow this div to be clickable
    <div
      className='flex cursor-pointer flex-row items-center gap-1'
      onClick={handleMaxClick}
    >
      <Text
        level='labelSmall'
        className='font-medium opacity-60 hover:opacity-100'
      >
        {isBuy ? 'Available' : 'Delegated'}{' '}
        {isBuy
          ? 'τ'
          : transaction.netuid !== undefined
            ? getSubnetSymbol(transaction.netuid)
            : null}{' '}
        {formatNumber(amount, {
          minimumFractionDigits: 2,
          maximumFractionDigits: 5,
        })}
      </Text>
    </div>
  );
};

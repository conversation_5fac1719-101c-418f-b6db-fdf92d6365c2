import { useMemo } from 'react';
import type { ColumnDef } from '@tanstack/react-table';
import Image from 'next/image';
import { BiCoinStack, BiWallet } from 'react-icons/bi';
import {
  Button,
  SubnetNameDisplay,
  TaoOrAlphaValueDisplay,
} from '@repo/ui/components';
import { notify } from '@repo/ui/lib';
import { useSimpleUiContext } from './lib/context';
import type { TransactionType } from './lib/types';
import type { CurrentPositionItem } from '@/app/stake/lib/types';
import { useChainAndWalletContext } from '@/contexts/chain-and-wallet/chain-and-wallet-context';
import { TAO_CURRENCY, truncateString } from '@/lib/utils';
import { getIconUrl } from '@/lib/utils/validator-utils';

export const isWalletConnected = (
  currentAccount: ReturnType<
    typeof useChainAndWalletContext
  >['state']['currentAccount']
) => {
  if (!currentAccount) {
    notify.error('No wallet connected. Please connect a wallet to continue.');
    return false;
  }

  return true;
};

export const useColumns = (
  handleAddAllTransactions: (transactionType: TransactionType) => void
) => {
  const {
    state: { currentAccount },
  } = useChainAndWalletContext();
  const { addTransaction } = useSimpleUiContext();

  const columns: ColumnDef<CurrentPositionItem>[] = useMemo(
    () => [
      {
        id: 'holdings-col',
        accessorKey: 'netuid',
        header: 'Subnet',
        cell: ({ row }) => {
          const d = row.original;
          return (
            <div className='max-w-44'>
              <SubnetNameDisplay
                subnetName={d.subnetName}
                netuid={d.netuid}
                isClickable
              />
            </div>
          );
        },
      },
      {
        id: 'validator-col',
        accessorKey: 'validatorName',
        header: 'Validator',
        cell: ({ row }) => {
          const d = row.original;
          return (
            <div className='flex flex-row items-center gap-2'>
              <Image
                height={32}
                width={32}
                sizes='32px'
                src={getIconUrl(d.validatorHotkey)}
                className='rounded-full'
                quality={100}
                alt={`${d.validatorName || truncateString(d.validatorHotkey)} icon`}
              />

              <span>
                <a
                  href={`https://taostats.io/validators/${d.validatorHotkey}`}
                  target='_blank'
                  rel='noopener noreferrer'
                  className='hover:text-accent-1 transition-colors duration-500'
                >
                  {d.validatorName || truncateString(d.validatorHotkey)}
                </a>
              </span>
            </div>
          );
        },
      },

      {
        id: 'alpha-balance-col',
        accessorKey: 'balanceAsAlpha',
        header: 'α Balance',
        meta: {
          align: 'end',
        },
        cell: ({ row }) => {
          const d = row.original;
          return (
            <div className='flex justify-end gap-2'>
              <TaoOrAlphaValueDisplay
                symbol={d.subnetSymbol}
                value={d.balanceAsAlpha}
                valueFormattingOptions={{
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 5,
                }}
                valueTextLevel='labelLarge'
                iconClassName='text-xs'
                areDecimalsSmall
              />
            </div>
          );
        },
      },
      {
        id: 'tao-balance-col',
        accessorKey: 'balanceAsTao',
        header: () => `${TAO_CURRENCY} Balance`,
        meta: {
          align: 'end',
        },
        cell: ({ row }) => {
          const d = row.original;
          return (
            <div className='flex justify-end gap-2'>
              <TaoOrAlphaValueDisplay
                value={d.balanceAsTao}
                valueFormattingOptions={{
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 5,
                }}
                valueTextLevel='labelLarge'
                iconClassName='text-xs'
                areDecimalsSmall
              />
            </div>
          );
        },
      },
      {
        id: 'actions-col',
        header: () => {
          return (
            <div className='flex w-full flex-row justify-end gap-2'>
              <Button
                className={`${currentAccount ? 'border-accent-1' : 'border-accent-1/50 text-white/50'} hover:bg-accent-1/20 text-sm`}
                onClick={() => {
                  if (isWalletConnected(currentAccount)) {
                    handleAddAllTransactions('buy');
                  }
                }}
              >
                Buy All <BiWallet className='ml-1' />
              </Button>
              <Button
                className={`${currentAccount ? 'border-accent-2' : 'border-accent-2/50 text-white/50'} hover:bg-accent-2/20 text-sm`}
                onClick={() => {
                  if (isWalletConnected(currentAccount)) {
                    handleAddAllTransactions('sell');
                  }
                }}
              >
                Sell All <BiCoinStack className='ml-1' />
              </Button>
            </div>
          );
        },
        cell: ({ row }) => {
          const d = row.original;
          return (
            <div className='flex flex-row justify-end gap-2'>
              <Button
                className={`${currentAccount ? 'border-accent-1' : 'border-accent-1/50 text-white/50'} hover:bg-accent-1/20`}
                onClick={() => {
                  if (isWalletConnected(currentAccount)) {
                    addTransaction('buy', {
                      netuid: d.netuid,
                      validatorHotkey: d.validatorHotkey,
                    });
                  }
                }}
              >
                Buy <BiWallet className='ml-1' />
              </Button>
              <Button
                className={`${currentAccount ? 'border-accent-2' : 'border-accent-2/50 text-white/50'} hover:bg-accent-2/20`}
                onClick={() => {
                  if (isWalletConnected(currentAccount)) {
                    addTransaction('sell', {
                      netuid: d.netuid,
                      validatorHotkey: d.validatorHotkey,
                    });
                  }
                }}
              >
                Sell <BiCoinStack className='ml-1' />
              </Button>
            </div>
          );
        },
      },
    ],
    [addTransaction, currentAccount, handleAddAllTransactions]
  );

  return columns;
};

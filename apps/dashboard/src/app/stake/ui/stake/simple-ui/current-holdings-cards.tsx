import { memo } from 'react';
import Image from 'next/image';
import { BiCoinStack, BiWallet } from 'react-icons/bi';
import {
  Button,
  Card,
  SubnetNameDisplay,
  TaoOrAlphaValueDisplay,
  Text,
} from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { useSimpleUiContext } from './lib/context';
import type { TransactionType } from './lib/types';
import { isWalletConnected } from './utils';
import type { CurrentPositionItem } from '@/app/stake/lib/types';
import { useChainAndWalletContext } from '@/contexts/chain-and-wallet/chain-and-wallet-context';
import { TAO_CURRENCY, truncateString } from '@/lib/utils';
import { getIconUrl } from '@/lib/utils/validator-utils';

export const CurrentHoldingsCards = memo(function CurrentHoldingsCards({
  data,
  handleAddAllTransactions,
}: {
  data: CurrentPositionItem[];
  handleAddAllTransactions: (transactionType: TransactionType) => void;
}) {
  const {
    state: { currentAccount },
  } = useChainAndWalletContext();
  const { addTransaction } = useSimpleUiContext();
  return (
    <div className='flex flex-col gap-6'>
      <Card>
        <div className='flex w-full flex-row justify-center gap-2'>
          <Button
            className={`${currentAccount ? 'border-accent-1' : 'border-accent-1/50 text-white/50'} hover:bg-accent-1/20 text-sm`}
            onClick={() => {
              if (isWalletConnected(currentAccount)) {
                handleAddAllTransactions('buy');
              }
            }}
          >
            Buy All <BiWallet className='ml-1' />
          </Button>
          <Button
            className={`${currentAccount ? 'border-accent-2' : 'border-accent-2/50 text-white/50'} hover:bg-accent-2/20 text-sm`}
            onClick={() => {
              if (isWalletConnected(currentAccount)) {
                handleAddAllTransactions('sell');
              }
            }}
          >
            Sell All <BiCoinStack className='ml-1' />
          </Button>
        </div>
      </Card>
      {data.map((d) => {
        return (
          <Card key={`${d.netuid}-${d.validatorHotkey}`}>
            <div className='flex flex-col gap-6'>
              <SubnetNameDisplay
                subnetName={d.subnetName}
                netuid={d.netuid}
                isClickable
              />
              <div className='flex flex-row items-center gap-4'>
                <Image
                  height={32}
                  width={32}
                  sizes='32px'
                  src={getIconUrl(d.validatorHotkey)}
                  className='rounded-full'
                  quality={100}
                  alt={`${d.validatorName || truncateString(d.validatorHotkey)} icon`}
                />

                <span>
                  <a
                    href={`https://taostats.io/validators/${d.validatorHotkey}`}
                    target='_blank'
                    rel='noopener noreferrer'
                    className='hover:text-accent-1 underline transition-colors duration-1000 hover:no-underline'
                  >
                    {d.validatorName || truncateString(d.validatorHotkey)}
                  </a>
                </span>
              </div>
              <div className='grid grid-cols-3 gap-x-4 gap-y-6'>
                <CardDataDisplay
                  label='α Balance'
                  value={
                    <TaoOrAlphaValueDisplay
                      symbol={d.subnetSymbol}
                      value={d.balanceAsAlpha}
                      valueFormattingOptions={{
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      }}
                      valueTextLevel='labelLarge'
                      iconClassName='text-xs'
                      areDecimalsSmall
                    />
                  }
                />
                <CardDataDisplay
                  label={`${TAO_CURRENCY} Balance`}
                  value={
                    <TaoOrAlphaValueDisplay
                      value={d.balanceAsTao}
                      valueFormattingOptions={{
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 5,
                      }}
                      valueTextLevel='labelLarge'
                      iconClassName='text-xs'
                      areDecimalsSmall
                    />
                  }
                />
              </div>

              <div className='flex flex-row flex-wrap gap-4'>
                <Button
                  className={cn(
                    'flex-1',
                    currentAccount
                      ? 'border-accent-1'
                      : 'border-accent-1/50 text-white/50',
                    'hover:bg-accent-1/20'
                  )}
                  onClick={() => {
                    if (isWalletConnected(currentAccount)) {
                      addTransaction('buy', {
                        netuid: d.netuid,
                        validatorHotkey: d.validatorHotkey,
                      });
                    }
                  }}
                >
                  Buy <BiWallet className='ml-1' />
                </Button>
                <Button
                  className={cn(
                    'flex-1',
                    currentAccount
                      ? 'border-accent-2'
                      : 'border-accent-2/50 text-white/50',
                    'hover:bg-accent-2/20'
                  )}
                  onClick={() => {
                    if (isWalletConnected(currentAccount)) {
                      addTransaction('sell', {
                        netuid: d.netuid,
                        validatorHotkey: d.validatorHotkey,
                      });
                    }
                  }}
                >
                  Sell <BiCoinStack className='ml-1' />
                </Button>
              </div>
            </div>
          </Card>
        );
      })}
    </div>
  );
});

const CardDataDisplay = ({
  label,
  value,
}: {
  label: string;
  value: React.ReactNode;
}) => {
  return (
    <div className='flex flex-col gap-2'>
      <Text level='labelExtraSmall' className='text-white/60'>
        {label}
      </Text>
      {value}
    </div>
  );
};

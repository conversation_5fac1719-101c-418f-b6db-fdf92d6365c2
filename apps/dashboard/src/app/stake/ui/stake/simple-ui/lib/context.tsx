import type { ReactNode } from 'react';
import {
  useCallback,
  useContext,
  useMemo,
  useState,
  createContext,
} from 'react';
import { BigNumber } from 'bignumber.js';
import { z } from 'zod';
import { notify } from '@repo/ui/lib';
import type {
  InputState,
  TokenType,
  Transaction,
  TransactionType,
} from './types';
import { useAccountContext } from '@/contexts/chain-and-wallet/account-context';
import {
  useSlippageCalculator,
  useTaoToAlphaConverterWithChainData,
} from '@/contexts/chain-and-wallet/hooks';
import { roundDown } from '@/lib/utils/number-utils';

type FieldValuesResult = {
  taoValue: string;
  alphaValue: string;
  slippage: string;
};

type TransactionOptions = {
  netuid: number;
  validatorHotkey: string;
  inputState?: InputState;
};

type Error = {
  transactionId: string;
  message: string;
};

type SimpleUiContextValue = {
  transactions: Transaction[];
  getTransaction: (index: number) => Transaction;
  addTransaction: (
    type: TransactionType,
    transactionOptions?: TransactionOptions
  ) => void;
  removeTransaction: (id: string) => void;
  removeAllTransactions: () => void;
  updateValidatorHotkey: (index: number, validatorHotkey: string) => void;
  updateSubnetId: (index: number, subnetId: number) => void;
  getFieldValues: (id: string) => FieldValuesResult;
  handleInputChange: (
    index: number,
    inputValue: string,
    field: TokenType
  ) => void;
  getInputState: (index: number) => InputState;
  availableTao: number;
  getAvailableAlpha: (subnetId: number) => number;
  getDelegatedAlphaForHotkey: (subnetId: number, hotkey: string) => number;
  errors: Error[];
  setErrors: (errors: Error[]) => void;
  removeErrors: () => void;
};

const SimpleUiContext = createContext<SimpleUiContextValue | undefined>(
  undefined
);

const numberSchema = z.string().refine((val) => {
  if (val === '') return true;
  return !Number.isNaN(Number(val));
}, 'Must be a number');

export const SimpleUiContextProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  const [errors, setErrors] = useState<Error[]>([]);

  const removeErrors = useCallback(() => {
    setErrors([]);
  }, []);

  const {
    state: { subnetAmountsMap, availableBalance, delegateBalances },
  } = useAccountContext();
  const { slippageCalculator } = useSlippageCalculator();
  const { convertTaoToAlpha, chainConvertFormat } =
    useTaoToAlphaConverterWithChainData();

  const [transactions, setTransactions] = useState<Transaction[]>([]);

  const getTransaction = useCallback(
    (id: number) => {
      return transactions[id];
    },
    [transactions]
  );

  const addTransaction = useCallback(
    (type: TransactionType, transactionOptions?: TransactionOptions) => {
      setTransactions((prev) => [
        ...prev,
        {
          id: `tx-${Date.now()}-${prev.length}`,
          type,
          inputState: { value: '', activeField: null },
          ...transactionOptions,
        },
      ]);
      notify.success(`${type === 'buy' ? 'Buy' : 'Sell'} transaction added ↓`);
    },
    []
  );

  const removeTransaction = useCallback(
    (id: string) => {
      // Remove the transaction with the given id
      setTransactions(transactions.filter((tx) => tx.id !== id));
    },
    [transactions]
  );

  const removeAllTransactions = useCallback(() => {
    setTransactions([]);
  }, []);

  const updateValidatorHotkey = useCallback(
    (index: number, validatorHotkey: string) => {
      setTransactions(
        transactions.map((transaction, i) =>
          i === index ? { ...transaction, validatorHotkey } : transaction
        )
      );
    },
    [transactions]
  );

  const updateSubnetId = useCallback(
    (index: number, subnetId: number) => {
      setTransactions(
        transactions.map((transaction, i) =>
          i === index ? { ...transaction, netuid: subnetId } : transaction
        )
      );
    },
    [transactions]
  );

  const getFieldValues = useCallback(
    (id: string): FieldValuesResult => {
      const defaultValues = {
        taoValue: '',
        alphaValue: '',
        slippage: '0',
      };
      const transaction = transactions.find((tx) => tx.id === id);
      if (!transaction) {
        return defaultValues;
      }

      const inputState = transaction.inputState;
      if (
        transaction.netuid === undefined ||
        !inputState.activeField ||
        !inputState.value
      ) {
        return defaultValues;
      }

      // For the active field, return the raw input value
      const result = numberSchema.safeParse(inputState.value);
      if (!result.success) {
        return {
          taoValue: inputState.activeField === 'TAO' ? inputState.value : '0',
          alphaValue:
            inputState.activeField === 'ALPHA' ? inputState.value : '0',
          slippage: '0',
        };
      }

      const price = subnetAmountsMap[transaction.netuid].price;
      const activeFieldValue = BigNumber(inputState.value);
      const convertedValue =
        inputState.activeField === 'TAO'
          ? price > 0
            ? BigNumber(roundDown(activeFieldValue.div(price), 5))
            : BigNumber(0) // TAO to ALPHA
          : price > 0
            ? BigNumber(roundDown(activeFieldValue.times(price), 5))
            : BigNumber(0); // ALPHA to TAO

      const taoValue =
        inputState.activeField === 'TAO' ? activeFieldValue : convertedValue;
      const slippageCalcResult = slippageCalculator(
        transaction.netuid,
        taoValue.toNumber()
      );

      return {
        taoValue:
          inputState.activeField === 'TAO'
            ? inputState.value
            : convertedValue.toString(),
        alphaValue:
          inputState.activeField === 'ALPHA'
            ? inputState.value
            : convertedValue.toString(),
        slippage: slippageCalcResult?.slippage ?? '0',
      };
    },
    [transactions, subnetAmountsMap, slippageCalculator]
  );

  const handleInputChange = useCallback(
    (index: number, inputValue: string, field: TokenType) => {
      setTransactions(
        transactions.map((transaction, i) =>
          i === index
            ? {
                ...transaction,
                inputState: { value: inputValue, activeField: field },
              }
            : transaction
        )
      );
    },
    [transactions]
  );

  const getInputState = useCallback(
    (index: number) => {
      return transactions[index].inputState;
    },
    [transactions]
  );

  const availableTao = BigNumber(availableBalance).toNumber();
  const getAvailableAlpha = useCallback(
    (subnetId: number) =>
      convertTaoToAlpha(
        Number(subnetId),
        availableBalance,
        chainConvertFormat.hToH
      ).toNumber(),
    [convertTaoToAlpha, availableBalance, chainConvertFormat]
  );
  const getDelegatedAlphaForHotkey = useCallback(
    (subnetId: number, hotkey: string) => {
      return (
        delegateBalances.find(
          (d) => d.netuid === subnetId && d.hotkey === hotkey
        )?.alphaAmount ?? 0
      );
    },
    [delegateBalances]
  );

  const value = useMemo<SimpleUiContextValue>(() => {
    return {
      transactions,
      getTransaction,
      addTransaction,
      removeTransaction,
      removeAllTransactions,
      updateValidatorHotkey,
      getFieldValues,
      handleInputChange,
      getInputState,
      availableTao,
      getAvailableAlpha,
      getDelegatedAlphaForHotkey,
      updateSubnetId,
      errors,
      setErrors,
      removeErrors,
    };
  }, [
    addTransaction,
    availableTao,
    errors,
    getAvailableAlpha,
    getDelegatedAlphaForHotkey,
    getFieldValues,
    getInputState,
    getTransaction,
    handleInputChange,
    removeErrors,
    removeTransaction,
    transactions,
    updateSubnetId,
    updateValidatorHotkey,
    removeAllTransactions,
  ]);

  return (
    <SimpleUiContext.Provider value={value}>
      {children}
    </SimpleUiContext.Provider>
  );
};

export const useSimpleUiContext = () => {
  const context = useContext(SimpleUiContext);
  if (!context) {
    throw new Error(
      'useSimpleUiContext must be used within a SimpleUiContextProvider'
    );
  }
  return context;
};

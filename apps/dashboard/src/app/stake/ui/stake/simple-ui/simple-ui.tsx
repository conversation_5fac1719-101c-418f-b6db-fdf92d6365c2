import { useState } from 'react';
import { BiCoinStack, BiListPlus, BiReset, BiWallet } from 'react-icons/bi';
import {
  <PERSON><PERSON>,
  Card,
  Flex,
  Spinner,
  TaoOrAlphaValueDisplay,
  Text,
  TooltipProvider,
} from '@repo/ui/components';
import { notify } from '@repo/ui/lib';
import { BuySellItem } from './buy-sell-item';
import { CurrentHoldings } from './current-holdings';
import { useSimpleUiTransactionSummaryBuilder } from './lib';
import { SimpleUiContextProvider, useSimpleUiContext } from './lib/context';
import { SimpleUiSummaryDialog } from './simple-ui-summary-dialog';
import { useStakeContext } from '@/app/stake/lib/contexts/stake-context';
import {
  useAmountToUnstakeCalculator,
  useTransactionBuilder,
  useTransactionFeeCalculator,
} from '@/app/stake/lib/hooks';
import { useAccountContext } from '@/contexts/chain-and-wallet/account-context';
import { roundDown } from '@/lib/utils/number-utils';

export const SimpleUi = () => {
  return (
    <SimpleUiContextProvider>
      <SimpleUiMain />
    </SimpleUiContextProvider>
  );
};

const SimpleUiMain = () => {
  const { availableTao, transactions } = useSimpleUiContext();
  const { totalTao, selectedSubnets } = useStakeContext();
  const hasCurrentHoldings = selectedSubnets.length > 0;

  return (
    <Flex col className='mt-2 gap-6 sm:mt-0'>
      <Flex
        col
        className='gap-6 sm:flex-row sm:items-center sm:justify-between sm:gap-2'
      >
        <Flex col className='hidden items-center gap-2 sm:flex sm:items-start'>
          <Text level='headerSmall'>Add transactions</Text>
          {hasCurrentHoldings ? (
            <Text
              level='bodyMedium'
              className='text-center text-white/60 sm:text-left'
            >
              Or pre-populate transactions from your current holdings below.
            </Text>
          ) : null}
        </Flex>

        <Flex col className='gap-6 sm:flex-row sm:items-center'>
          <Flex className='justify-center gap-6'>
            <Flex col className='gap-2'>
              <Text level='labelSmall' className='text-center text-white/60'>
                Total
              </Text>
              <TaoOrAlphaValueDisplay
                value={totalTao}
                valueFormattingOptions={{
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 5,
                }}
                valueTextLevel='labelLarge'
                iconClassName='text-xs'
                areDecimalsSmall
              />
            </Flex>
            <Flex col className='gap-2'>
              <Text level='labelSmall' className='text-center text-white/60'>
                Available
              </Text>
              <TaoOrAlphaValueDisplay
                value={availableTao}
                valueFormattingOptions={{
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 5,
                }}
                valueTextLevel='labelLarge'
                iconClassName='text-xs'
                areDecimalsSmall
              />
            </Flex>
          </Flex>
          <Flex className='justify-center sm:justify-start'>
            <Buttons />
          </Flex>
        </Flex>
      </Flex>

      {hasCurrentHoldings ? (
        <Card className='bg-hero-primary p2 rounded-3xl sm:px-8 sm:py-0'>
          <CurrentHoldings />
        </Card>
      ) : null}

      {transactions.length > 0 ? (
        <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3'>
          <TooltipProvider>
            {transactions.map((transaction, index) => (
              <BuySellItem key={transaction.id} index={index} />
            ))}
            <Card className='w-full'>
              <Flex col className='items-center gap-2 sm:items-start'>
                <Text level='bodyMedium'>Actions:</Text>
                <Flex className='gap-2'>
                  <Buttons />
                </Flex>
              </Flex>
              <Flex col className='items-center gap-2 sm:items-start'>
                <Text level='bodyMedium'>Or calculate fees and proceed:</Text>
                <StakeButton />
              </Flex>
            </Card>
          </TooltipProvider>
        </div>
      ) : null}
    </Flex>
  );
};

const Buttons = () => {
  const { fetchInfo } = useAccountContext();
  const { addTransaction } = useSimpleUiContext();
  return (
    <Flex className='gap-2'>
      <Button
        className='border-accent-1 hover:bg-accent-1/20'
        onClick={() => {
          addTransaction('buy');
        }}
      >
        Buy <BiWallet className='ml-1' />
      </Button>
      <Button
        className='border-accent-2 hover:bg-accent-2/20'
        onClick={() => {
          addTransaction('sell');
        }}
      >
        Sell <BiCoinStack className='ml-1' />
      </Button>
      <Button
        onClick={() => {
          void fetchInfo();
        }}
        className='border-gray-500 hover:bg-gray-500/20'
      >
        Reset
        <BiReset className='ml-2' />
      </Button>
    </Flex>
  );
};

const StakeButton = () => {
  const { transactions, setErrors, removeErrors, getFieldValues } =
    useSimpleUiContext();
  const { buildTransactionSummaries } = useSimpleUiTransactionSummaryBuilder();
  const { buildTransaction } = useTransactionBuilder();
  const { getFees } = useTransactionFeeCalculator();
  const { calculateAlphaToUnstake } = useAmountToUnstakeCalculator();
  const [isBuildingTransaction, setIsBuildingTransaction] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [transactionFeeLocal, setTransactionFeeLocal] = useState<
    number | undefined
  >();
  const [stakingFeesLocal, setStakingFeesLocal] = useState<number[]>([]);

  const validateTransactions = () => {
    removeErrors();
    const errors = [];
    for (const tx of transactions) {
      if (tx.netuid === undefined) {
        errors.push({
          transactionId: tx.id,
          message: 'Select a subnet',
        });
      }
      if (tx.validatorHotkey === undefined) {
        errors.push({
          transactionId: tx.id,
          message: 'Select a validator',
        });
      }
      const { taoValue } = getFieldValues(tx.id);
      if (taoValue === '') {
        errors.push({
          transactionId: tx.id,
          message: 'Enter an amount',
        });
      } else if (tx.type === 'sell') {
        const taoValueRoundedHumanFormat = roundDown(taoValue, 5);

        if (tx.netuid !== undefined && tx.validatorHotkey !== undefined) {
          const alphaToUnstakeChainFormat = calculateAlphaToUnstake(
            {
              netuid: tx.netuid,
              validatorSs58: tx.validatorHotkey,
            },
            taoValueRoundedHumanFormat
          );

          if (!alphaToUnstakeChainFormat) {
            notify.error(
              `Skipping transaction. Could not calculate alpha to unstake for subnet ${tx.netuid} validator ${tx.validatorHotkey}`
            );
            errors.push({
              transactionId: tx.id,
              message:
                'Could not calculate alpha to unstake. Are you sure you have the correct subnet and validator selected?',
            });
          }
        }
      }
    }
    return errors;
  };

  const handleNextClick = async () => {
    try {
      const errors = validateTransactions();
      if (errors.length > 0) {
        notify.error(
          'Please fix the errors within the transactions and try again.'
        );
        setErrors(errors);
        return;
      }
      setIsBuildingTransaction(true);
      const stakeTransactionSummaries = buildTransactionSummaries();
      const buildTransactionResult = buildTransaction(
        stakeTransactionSummaries
      );
      if (!buildTransactionResult?.tx) {
        notify.error('Could not build transaction');
        return;
      }
      const { transactionFee, stakingFees } = await getFees(
        stakeTransactionSummaries
      );
      setTransactionFeeLocal(transactionFee);
      setStakingFeesLocal(stakingFees);
      setIsOpen(true);
    } catch (e) {
      notify.error('There was an error when building the transaction');
      console.error(e);
    } finally {
      setIsBuildingTransaction(false);
    }
  };
  const closeDialog = () => {
    setIsOpen(false);
  };

  const isDisabled = isBuildingTransaction || transactions.length <= 0;

  return (
    <>
      <Button
        size='lg'
        variant='cta2'
        onClick={() => {
          void handleNextClick();
        }}
        disabled={isDisabled}
      >
        {isBuildingTransaction ? 'Calculating Fee...' : 'Next'}
        {isBuildingTransaction ? (
          <Spinner className='ml-2 h-4 w-4' />
        ) : (
          <BiListPlus size={24} className='ml-2' />
        )}
      </Button>
      {isOpen ? (
        <SimpleUiSummaryDialog
          isOpen={isOpen}
          closeFn={closeDialog}
          transactionFee={transactionFeeLocal}
          stakingFees={stakingFeesLocal}
        />
      ) : null}
    </>
  );
};

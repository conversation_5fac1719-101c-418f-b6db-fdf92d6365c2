import { notify } from '@repo/ui/lib';
import { useSimpleUiContext } from './context';
import { useAmountToUnstakeCalculator } from '@/app/stake/lib/hooks';
import { useChainAndWalletContext } from '@/contexts/chain-and-wallet/chain-and-wallet-context';
import type { StakeTransactionSummary } from '@/contexts/chain-and-wallet/types';
import { usePriceLimitCalculator } from '@/lib/hooks/staking-hooks';
import { log } from '@/lib/utils';
import { useChainAmountConverter } from '@/lib/utils/conversion/conversion-utils';
import { roundDown } from '@/lib/utils/number-utils';

export const useSimpleUiTransactionSummaryBuilder = () => {
  const {
    state: { currentAccount },
  } = useChainAndWalletContext();
  const { getAddStakePriceLimit, getRemoveStakePriceLimit } =
    usePriceLimitCalculator();
  const { transactions, getFieldValues } = useSimpleUiContext();
  const { calculateAlphaToUnstake } = useAmountToUnstakeCalculator();
  const { convertAmount } = useChainAmountConverter();

  const buildTransactionSummaries = (
    filterTransactionIds: string[] = []
  ): StakeTransactionSummary[] => {
    if (!currentAccount?.address) {
      notify.error(
        'Could not find current account. Can not build transactions.'
      );
      return [];
    }

    const sortedTransactions = [...transactions]
      .filter((tx) => !filterTransactionIds.includes(tx.id))
      .sort((a, b) => {
        // sort by sell transactions first
        if (a.type === 'sell' && b.type === 'buy') {
          return -1;
        }
        if (a.type === 'buy' && b.type === 'sell') {
          return 1;
        }

        return 0;
      });

    log('DEBUG', { sortedTransactions });

    const summaries = sortedTransactions
      .map((tx): StakeTransactionSummary | null => {
        if (tx.netuid === undefined) {
          return null;
        }

        if (tx.validatorHotkey === undefined) {
          return null;
        }

        const { taoValue, slippage } = getFieldValues(tx.id);
        if (taoValue === '') {
          return null;
        }

        // taoValue is in human format e.g. 5.2. Chain format is 5.2 * 1_000_000_000
        const taoValueRoundedHumanFormat = roundDown(taoValue, 5);

        const hotkey = tx.validatorHotkey;

        switch (tx.type) {
          case 'buy': {
            // This is a buy transaction, so we need to add stake
            // The amount is in human readable format e.g. 5.2
            // We need to convert this to chain format as that's what is expected by the addStake function
            // We also need to calculate the limit value to pass into addStakeLimit

            // Get the tao amount in chain format
            const taoInChainFormat = convertAmount(
              taoValueRoundedHumanFormat
            ).toNumber();

            // Calculate the limit value to pass into addStakeLimit, in chain format
            const priceLimit = getAddStakePriceLimit(
              tx.netuid,
              Number(slippage)
            ).toNumber();

            log('DEBUG', {
              taoInChainFormat,
              taoValueRounded: taoValueRoundedHumanFormat,
              priceLimit,
              hotkey,
            });

            return {
              type: 'add_stake',
              destinationHotkey: hotkey,
              destinationNetuid: tx.netuid,
              originColdkey: currentAccount.address,
              taoAmount: taoInChainFormat,
              priceLimit,
            };
          }

          case 'sell': {
            // This is a sell which means we need to remove some stake
            // We need it as a positive number as it's being passed into the removeStakeLimit function, which expects a positive alpha chain format number
            // So get the absolute value of the amount to remove in tao
            // Then convert that to alpha in chain format

            const alphaToUnstakeChainFormat = calculateAlphaToUnstake(
              {
                netuid: tx.netuid,
                validatorSs58: tx.validatorHotkey,
              },
              taoValueRoundedHumanFormat
            );

            if (!alphaToUnstakeChainFormat) {
              notify.error(
                `Skipping transaction. Could not calculate alpha to unstake for subnet ${tx.netuid} validator ${tx.validatorHotkey}`
              );
              return null;
            }

            // Calculate the limit value to pass into removeStakeLimit, in chain format
            const priceLimit = getRemoveStakePriceLimit(
              tx.netuid,
              Number(slippage)
            ).toNumber();

            log('DEBUG', {
              alphaToUnstakeChainFormat,
              taoValueRoundedHumanFormat,
              priceLimit,
              hotkey,
            });

            // Get the tao amount in chain format
            const taoInChainFormat = convertAmount(
              taoValueRoundedHumanFormat
            ).toNumber();

            return {
              type: 'remove_stake',
              originColdkey: currentAccount.address,
              removeFromHotkey: hotkey,
              removeFromNetuid: tx.netuid,
              alphaAmount: alphaToUnstakeChainFormat,
              taoAmount: taoInChainFormat,
              priceLimit,
            };
          }

          default: {
            return null;
          }
        }
      })
      .filter((tx): tx is StakeTransactionSummary => tx !== null);

    return summaries;
  };

  return {
    buildTransactionSummaries,
  };
};

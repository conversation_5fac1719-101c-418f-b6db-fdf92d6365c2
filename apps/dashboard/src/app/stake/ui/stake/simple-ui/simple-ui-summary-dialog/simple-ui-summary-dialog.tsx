import { useState } from 'react';
import { BigNumber } from 'bignumber.js';
import { BiListPlus, BiTrash } from 'react-icons/bi';
import {
  AlertError,
  Button,
  Dialog,
  Flex,
  Spinner,
  TaoOrAlphaValueDisplay,
  Text,
} from '@repo/ui/components';
import { cn, notify } from '@repo/ui/lib';
import { useStakeContext } from '@/app/stake/lib/contexts/stake-context';
import {
  useSlippage<PERSON>hecker,
  useStakingLogic,
  useTermsCheckbox,
  useTransactionFeeCalculator,
  useTransactionTotals,
} from '@/app/stake/lib/hooks';
import {
  SlippageMessage,
  SubnetAndValidator,
} from '@/app/stake/ui/stake/components';
import {
  NotEnoughBalanceForTransactionFeeUi,
  StakedAmountTooHighUi,
} from '@/app/stake/ui/stake/components/error-uis';
import { FeeSummarySection } from '@/app/stake/ui/stake/components/fee-summary-section';
import { FinancialSummarySection } from '@/app/stake/ui/stake/components/financial-summary-section';
import { SlippageError } from '@/app/stake/ui/stake/components/slippage-error';
import { useSimpleUiTransactionSummaryBuilder } from '@/app/stake/ui/stake/simple-ui/lib';
import { useSimpleUiContext } from '@/app/stake/ui/stake/simple-ui/lib/context';
import type { Transaction } from '@/app/stake/ui/stake/simple-ui/lib/types';
import { useTransactionSuccessLogic } from '@/contexts/chain-and-wallet/use-transaction-success-logic';
import { useSubnetLookup, useValidatorLookup } from '@/lib/hooks/global-hooks';

export const SimpleUiSummaryDialog = ({
  closeFn,
  isOpen,
  transactionFee = 0,
  stakingFees = [],
}: {
  closeFn: () => void;
  isOpen: boolean;
  transactionFee?: number;
  stakingFees?: number[];
}) => {
  const { buildTransactionSummaries } = useSimpleUiTransactionSummaryBuilder();
  const { getFees } = useTransactionFeeCalculator();
  const [isBuildingTransaction, setIsBuildingTransaction] = useState(false);
  const [transactionFeeLocal, setTransactionFeeLocal] =
    useState<number>(transactionFee);
  const [stakingFeesLocal, setStakingFeesLocal] =
    useState<number[]>(stakingFees);

  const { availableBalance } = useStakeContext();
  const { removeTransaction } = useSimpleUiContext();
  const { handleStakeConfirmClick, isPending, txStatus } = useStakingLogic();
  const { termsAccepted, renderTermsCheckbox } = useTermsCheckbox();
  const { hasSlippageErrors } = useSlippageChecker();
  const {
    fetchInfo,
    wasSuccessful,
    setWasSuccessful,
    renderMessageIfSuccessful,
  } = useTransactionSuccessLogic();

  const title = txStatus?.status ? txStatus.status : 'Staking Summary';

  const handleGetFees = async (filterTransactionIds: string[] = []) => {
    try {
      setIsBuildingTransaction(true);
      const transactionSummaries =
        buildTransactionSummaries(filterTransactionIds);
      const { transactionFee: transactionFeeNew, stakingFees: stakingFeesNew } =
        await getFees(transactionSummaries);
      setTransactionFeeLocal(transactionFeeNew);
      setStakingFeesLocal(stakingFeesNew);
    } catch (error) {
      notify.error('There was an error when building the transaction');
      console.error(error);
    } finally {
      setIsBuildingTransaction(false);
    }
  };

  const handleRemoveTransaction = (transactionId: string) => {
    removeTransaction(transactionId);
    void handleGetFees([transactionId]);
  };

  const stakeTransactionSummaries = buildTransactionSummaries();
  const { getTransactionTotals } = useTransactionTotals();
  const getTransactionTotalsResult = getTransactionTotals(
    transactionFeeLocal,
    stakingFeesLocal,
    stakeTransactionSummaries
  );

  const isButtonDisabled =
    isPending ||
    hasSlippageErrors ||
    !termsAccepted ||
    !getTransactionTotalsResult.hasEnoughBalanceToCoverTransactionFee ||
    getTransactionTotalsResult.isTotalStakedTooMuch;

  const errorUiToShow = getTransactionTotalsResult.isTotalStakedTooMuch ? (
    <StakedAmountTooHighUi
      amountRequired={getTransactionTotalsResult.amountRequired}
      totalTao={getTransactionTotalsResult.totalTao}
    />
  ) : !getTransactionTotalsResult.hasEnoughBalanceToCoverTransactionFee ? (
    <NotEnoughBalanceForTransactionFeeUi
      availableBalance={availableBalance}
      transactionFee={transactionFeeLocal}
    />
  ) : null;

  return (
    <Dialog
      HeaderIcon={isPending ? undefined : BiListPlus}
      HeaderNode={
        isPending ? (
          <div className='flex h-12 items-center justify-center'>
            <Spinner className='h-8 w-8' />
          </div>
        ) : null
      }
      closeFn={wasSuccessful ? fetchInfo : closeFn}
      isOpen={isOpen}
      title={title}
      className='sm:w-[600px]'
    >
      {!isOpen ? (
        <div className='flex justify-center py-4'>
          <Spinner />
        </div>
      ) : (
        <>
          <SlippageErrorOrNull />

          {renderMessageIfSuccessful()}

          {isBuildingTransaction ? (
            <div className='flex flex-row items-center justify-center gap-2 py-4'>
              <Spinner />
              <Text level='labelLarge'>Re-calculating fees...</Text>
            </div>
          ) : null}

          <StakingSummary
            stakingFees={stakingFeesLocal}
            handleRemoveTransaction={handleRemoveTransaction}
            wasSuccessful={wasSuccessful}
          />

          {errorUiToShow}

          <FeeSummarySection
            availableBalance={availableBalance}
            transactionFee={transactionFeeLocal}
            stakingFees={stakingFeesLocal}
          />

          <FinancialSummarySection
            transactionTotalsResult={getTransactionTotalsResult}
          />

          {renderTermsCheckbox(wasSuccessful)}

          {txStatus?.message ? (
            <AlertError description={txStatus.message} />
          ) : null}
        </>
      )}

      {wasSuccessful ? null : (
        <div className='flex flex-col gap-4'>
          <Button
            className='w-full'
            disabled={isButtonDisabled}
            onClick={() => {
              void handleStakeConfirmClick({
                stakeTransactionSummaries,
                onSuccessCallback: () => {
                  setWasSuccessful(true);
                },
                doFullRefetchOnSuccess: false,
              });
            }}
            size='lg'
            type='button'
            variant='cta2'
          >
            {isPending ? 'Confirming...' : 'Confirm'}
            <BiListPlus className='ml-2' size={20} />
          </Button>
        </div>
      )}
    </Dialog>
  );
};

const SlippageErrorOrNull = () => {
  const { transactions, getFieldValues } = useSimpleUiContext();
  const { maxSlippage } = useStakeContext();
  const slippages = transactions.map((transaction) => {
    const { slippage } = getFieldValues(transaction.id);
    return BigNumber(slippage);
  });
  const hasSlippageErrors = slippages.some((slippage) =>
    slippage.gt(maxSlippage)
  );

  if (hasSlippageErrors) {
    return <SlippageError />;
  }

  return null;
};

const StakingSummary = ({
  stakingFees,
  handleRemoveTransaction,
  wasSuccessful,
}: {
  stakingFees?: number[];
  handleRemoveTransaction: (transactionId: string) => void;
  wasSuccessful: boolean;
}) => {
  const { transactions } = useSimpleUiContext();

  return (
    <div className='scrollbar-thin scrollbar-track-rounded-full scrollbar-track-white/20 scrollbar-thumb-white scrollbar-thumb-rounded-full hover:scrollbar-thumb-white/80 max-h-[30vh] overflow-y-auto pr-4'>
      {transactions.map((transaction, index) => (
        <TransactionSummary
          key={transaction.id}
          transaction={transaction}
          stakingFee={stakingFees?.[index]}
          removeTransaction={handleRemoveTransaction}
          wasSuccessful={wasSuccessful}
        />
      ))}
    </div>
  );
};

const taoAndAlphaFormat = {
  minimumFractionDigits: 2,
  maximumFractionDigits: 5,
};

const TransactionSummary = ({
  transaction,
  stakingFee,
  removeTransaction,
  wasSuccessful,
}: {
  transaction: Transaction;
  stakingFee?: number;
  removeTransaction: (transactionId: string) => void;
  wasSuccessful: boolean;
}) => {
  const { getFieldValues } = useSimpleUiContext();
  const { taoValue, alphaValue, slippage } = getFieldValues(transaction.id);
  const { getSubnetName, getSubnetSymbol } = useSubnetLookup();
  const { getValidatorName } = useValidatorLookup();

  if (
    transaction.netuid === undefined ||
    transaction.validatorHotkey === undefined
  ) {
    return null;
  }

  const isBuy = transaction.type === 'buy';

  const alphaDisplay = (
    <TaoOrAlphaValueDisplay
      valueTextLevel='buttonSmall'
      value={BigNumber(alphaValue).toNumber()}
      symbol={getSubnetSymbol(transaction.netuid)}
      areDecimalsSmall
      valueFormattingOptions={taoAndAlphaFormat}
      iconClassName='text-xs'
    />
  );

  const taoDisplay = (
    <TaoOrAlphaValueDisplay
      valueTextLevel='buttonSmall'
      value={BigNumber(taoValue).toNumber()}
      areDecimalsSmall
      valueFormattingOptions={taoAndAlphaFormat}
      iconClassName='text-xs'
    />
  );

  return (
    <Flex className='gap-4 border-b border-white/20 py-4'>
      <Flex className='w-full items-center gap-1'>
        {wasSuccessful ? null : (
          <Button
            variant='ghost'
            size='sm'
            className='px-0 pr-1 text-white/20 hover:text-red-400'
            onClick={() => {
              removeTransaction(transaction.id);
            }}
          >
            <BiTrash size={20} />
          </Button>
        )}

        <div className='w-full'>
          <Flex className='justify-between gap-2'>
            <Flex className='items-baseline gap-1 text-white/60'>
              <Text
                level='labelLarge'
                className={cn(isBuy ? 'text-accent-1' : 'text-accent-2')}
              >
                {isBuy ? 'Buy' : 'Sell'}
              </Text>
              {isBuy ? alphaDisplay : taoDisplay}

              <Text as='span' level='buttonSmall' className='text-white/60'>
                {' '}
                for{' '}
              </Text>
              <span className='flex-shrink-0'>
                {isBuy ? taoDisplay : alphaDisplay}
              </span>
            </Flex>

            {stakingFee ? (
              <Text
                level='labelExtraSmall'
                className='flex flex-row gap-1 italic text-white/40'
              >
                <TaoOrAlphaValueDisplay
                  valueTextLevel='labelExtraSmall'
                  value={stakingFee}
                  valueFormattingOptions={taoAndAlphaFormat}
                  valueClassName='text-white/40'
                  iconClassName='text-[10px]'
                />{' '}
                staking fee
              </Text>
            ) : (
              <span />
            )}
          </Flex>

          <Flex className='w-full justify-between'>
            <SubnetAndValidator
              subnetName={getSubnetName(transaction.netuid)}
              validatorName={getValidatorName(transaction.validatorHotkey)}
              className='text-white/60'
            />
            <SlippageMessage
              slippage={BigNumber(slippage).toNumber()}
              size='sm'
            />
          </Flex>
        </div>
      </Flex>
    </Flex>
  );
};

import { useState } from 'react';
import { BiListPlus } from 'react-icons/bi';
import { <PERSON><PERSON>, Spinner } from '@repo/ui/components';
import { notify } from '@repo/ui/lib';
import {
  useSubnetSummaries,
  useTransactionBuilder,
  useTransactionFeeCalculator,
} from '@/app/stake/lib/hooks';
import { StakingSummaryDialog } from '@/app/stake/ui/stake/staking-summary-dialog';
import { useTransactionSummaryBuilder } from '@/app/stake/ui/stake/staking-summary-dialog/lib';

export const StakeButton = () => {
  const { subnetSummaries, subnetSummariesWithValidDifference } =
    useSubnetSummaries();
  const { buildStakeTransactionSummaries } = useTransactionSummaryBuilder();
  const { buildTransaction } = useTransactionBuilder();
  const { getFees } = useTransactionFeeCalculator();
  const [isBuildingTransaction, setIsBuildingTransaction] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [transactionFeeLocal, setTransactionFeeLocal] = useState<
    number | undefined
  >();
  const [stakingFeesLocal, setStakingFeesLocal] = useState<number[]>([]);
  const openDialog = async () => {
    try {
      setIsBuildingTransaction(true);
      const stakeTransactionSummaries = buildStakeTransactionSummaries();
      const buildTransactionResult = buildTransaction(
        stakeTransactionSummaries
      );
      if (!buildTransactionResult?.tx) {
        notify.error('Could not build transaction');
        return;
      }
      const { transactionFee, stakingFees } = await getFees(
        stakeTransactionSummaries
      );
      setTransactionFeeLocal(transactionFee);
      setStakingFeesLocal(stakingFees);
      setIsOpen(true);
    } catch (e) {
      notify.error('There was an error when building the transaction');
      console.error(e);
    } finally {
      setIsBuildingTransaction(false);
    }
  };
  const closeDialog = () => {
    setIsOpen(false);
  };

  const isDisabled =
    isBuildingTransaction ||
    subnetSummaries.length <= 0 ||
    subnetSummariesWithValidDifference.length <= 0;

  return (
    <>
      <Button
        size='lg'
        variant='cta2'
        onClick={() => {
          void openDialog();
        }}
        disabled={isDisabled}
      >
        {isBuildingTransaction ? 'Calculating Fee...' : 'Next'}
        {isBuildingTransaction ? (
          <Spinner className='ml-2 h-4 w-4' />
        ) : (
          <BiListPlus size={24} className='ml-2' />
        )}
      </Button>
      {isOpen ? (
        <StakingSummaryDialog
          isOpen={isOpen}
          closeFn={closeDialog}
          transactionFee={transactionFeeLocal}
          stakingFees={stakingFeesLocal}
        />
      ) : null}
    </>
  );
};

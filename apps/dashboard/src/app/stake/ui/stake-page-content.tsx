'use client';

import { BigNumber } from 'bignumber.js';
import { Alert } from '@repo/ui/components';
import { getSelectedSubnetId } from '@/app/stake/lib/helpers';
import { stakingTypeSchema } from '@/app/stake/lib/types';
import { ConnectWalletUi } from '@/app/stake/ui/connect-wallet/conect-wallet-ui';
import { useAutoReconnectWallets } from '@/app/stake/ui/connect-wallet/utils';
import { ConnectedStakeMain } from '@/app/stake/ui/stake/stake-main';
import {
  AccountContextProvider,
  useAccountContext,
} from '@/contexts/chain-and-wallet/account-context';
import { useChainAndWalletContext } from '@/contexts/chain-and-wallet/chain-and-wallet-context';
import { MIN_STAKING_AMOUNT_TAO } from '@/contexts/chain-and-wallet/constants';

export const StakePageContent = ({ tab }: { tab?: string[] }) => {
  return (
    <AccountContextProvider>
      <StakeUi tab={tab} />
    </AccountContextProvider>
  );
};

const StakeUi = ({ tab }: { tab?: string[] }) => {
  const {
    state: { currentAccount },
  } = useChainAndWalletContext();

  useAutoReconnectWallets();

  return currentAccount ? <ConnectedWalletUi tab={tab} /> : <ConnectWalletUi />;
};

const ConnectedWalletUi = ({ tab }: { tab?: string[] }) => {
  const {
    state: { loading, availableBalance, delegateBalances },
  } = useAccountContext();

  // Get initial subnets to populate the UI with
  // Only include subnets with more than 0.0005 TAO staked
  // 0.0005 TAO === 500_000 RAO which is the minimum amount to stake/unstake on the chain
  const initialSubnets = delegateBalances
    .filter((delegatedBalance) =>
      BigNumber(delegatedBalance.taoAmount).isGreaterThan(
        MIN_STAKING_AMOUNT_TAO
      )
    )
    .map((delegatedBalance) => ({
      selectedSubnetId: getSelectedSubnetId({
        netuid: delegatedBalance.netuid,
        validatorSs58: delegatedBalance.hotkey,
      }),
      netuid: delegatedBalance.netuid,
      validatorSs58: delegatedBalance.hotkey,
      initialAmount: delegatedBalance.taoAmount,
    }));

  if (loading) {
    return (
      <Alert
        type='success'
        showSpinner
        description='Getting your info from the chain, please wait...'
        alignItems='center'
      />
    );
  }

  const tabParam = tab?.[0];
  const tabResult = stakingTypeSchema.safeParse(tabParam);
  const stakingType = tabResult.data ?? 'simple';

  return (
    <ConnectedStakeMain
      availableBalance={availableBalance}
      initialSubnets={initialSubnets}
      stakingType={stakingType}
    />
  );
};

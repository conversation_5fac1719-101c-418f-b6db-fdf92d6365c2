'use client';

import type { ReactNode } from 'react';
import { createContext, useContext, useEffect, useMemo, useState } from 'react';
import { BigNumber } from 'bignumber.js';
import { useLocalStorage } from 'usehooks-ts';
import { z } from 'zod';
import type { Account } from '@repo/types/website-api-types';
import type { CurrencySelectionOptions } from '@repo/ui/components';
import { portfolioDateRanges } from './constants';
import { useAccountLatestQuery, useSavedWalletsQuery } from './query-hooks';
import type { DateRange } from './types';
import { useLastUsedWallet } from './utils';
import useDebounce from '@/lib/hooks/use-debounce';

interface PortfolioContextValue {
  currencySelected: CurrencySelectionOptions;
  setCurrencySelected: (currency: CurrencySelectionOptions) => void;
  dateRangeSelected: DateRange;
  dates: {
    is1d: boolean;
    is1w: boolean;
    is1m: boolean;
    is1y: boolean;
  };
  setDateRangeSelected: (dateRange: DateRange) => void;
  walletAddressInput: string;
  walletAddress: string;
  setWalletAddress: (walletAddress: string) => void;
  setLatestWallet: (walletAddress: string) => void;
  accountLatestQuery: ReturnType<typeof useAccountLatestQuery>;
  accountData?: Account;
  walletsQuery: ReturnType<typeof useSavedWalletsQuery>;
  selectedWalletName: string | undefined;
  currency: {
    isTao: boolean;
    isUsd: boolean;
  };
}

const PortfolioContext = createContext<PortfolioContextValue | undefined>(
  undefined
);

interface PortfolioContextProviderProps {
  children: ReactNode;
  accountId: string;
}

const dateRangeSchema = z.enum(
  portfolioDateRanges.map((range) => range.value) as [DateRange, ...DateRange[]]
);

const useSelectedDateRange = () => {
  const [dateRangeStored, setDateRangeStored] = useLocalStorage<DateRange>(
    'selected-date-range',
    '1d'
  );

  const parsedDateRangeSelected = useMemo(() => {
    const parsed = dateRangeSchema.safeParse(dateRangeStored);
    if (parsed.success) {
      return parsed.data;
    }

    return '1d';
  }, [dateRangeStored]);

  return {
    dateRangeSelected: parsedDateRangeSelected,
    setDateRangeSelected: setDateRangeStored,
  };
};

export const PortfolioContextProvider = ({
  children,
  accountId,
}: PortfolioContextProviderProps) => {
  const { setLatestWallet } = useLastUsedWallet();
  const [walletAddress, setWalletAddress] = useState<string>(accountId);
  const walletAddressDebounced = useDebounce(walletAddress, 300);

  const [currencySelected, setCurrencySelected] =
    useLocalStorage<CurrencySelectionOptions>('selected-currency', 'TAO');
  const { dateRangeSelected, setDateRangeSelected } = useSelectedDateRange();

  const walletsQuery = useSavedWalletsQuery();
  const selectedWalletName = useMemo(
    () =>
      walletsQuery.data?.wallets.find(
        (wallet) => wallet.address === walletAddressDebounced
      )?.name,
    [walletsQuery.data?.wallets, walletAddressDebounced]
  );

  const accountLatestQuery = useAccountLatestQuery(walletAddressDebounced);
  const accountData = accountLatestQuery.data?.data[0];

  // If the account latest query data has just changed, then record the wallet address in local storage so it auto loads up next time
  useEffect(() => {
    if (accountData && BigNumber(accountData.balance_total).gt(0)) {
      setLatestWallet(accountData.address.ss58);
    }
  }, [accountData, setLatestWallet]);

  const value = useMemo<PortfolioContextValue>(() => {
    return {
      dateRangeSelected,
      setDateRangeSelected,
      dates: {
        is1d: dateRangeSelected === '1d',
        is1w: dateRangeSelected === '1w',
        is1m: dateRangeSelected === '1m',
        is1y: dateRangeSelected === '1y',
      },
      walletAddressInput: walletAddress,
      walletAddress: walletAddressDebounced,
      setWalletAddress,
      setLatestWallet,
      accountLatestQuery,
      accountData,
      walletsQuery,
      selectedWalletName,
      currencySelected,
      setCurrencySelected,
      currency: {
        isTao: currencySelected === 'TAO',
        isUsd: currencySelected === 'USD',
      },
    };
  }, [
    dateRangeSelected,
    setDateRangeSelected,
    walletAddress,
    walletAddressDebounced,
    setLatestWallet,
    accountLatestQuery,
    accountData,
    walletsQuery,
    selectedWalletName,
    currencySelected,
    setCurrencySelected,
  ]);

  return (
    <PortfolioContext.Provider value={value}>
      {children}
    </PortfolioContext.Provider>
  );
};

export const usePortfolioContext = () => {
  const context = useContext(PortfolioContext);
  if (context === undefined) {
    throw new Error(
      'usePortfolioContext must be used within a PortfolioContextProvider'
    );
  }
  return context;
};

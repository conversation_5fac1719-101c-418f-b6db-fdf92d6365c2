import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import type { PaginationState } from '@tanstack/react-table';
import type {
  AddPortfolioWalletRequest,
  DeletePortfolioWalletRequest,
} from '@repo/types/dashboard-api-types';
import { apiClient, handleResponse } from '@/app/api/[...route]/lib/api-client';

export const queryKeys = {
  account: 'account',
  coldkeyReport: 'coldkey-report',
  stakeBalance: 'stake-balance',
  stakeDetailed: 'stake-detailed',
  statsLatest: 'stats-latest',
  wallets: 'wallets',
} as const;

export const useAccountLatestQuery = (walletAddress: string) => {
  return useQuery({
    queryKey: [queryKeys.account, walletAddress],
    queryFn: async () =>
      handleResponse(
        await apiClient.account[`:address`].latest.$get({
          param: {
            address: walletAddress,
          },
        })
      ),
    enabled: <PERSON><PERSON><PERSON>(walletAddress) && walletAddress.length > 20,
    placeholderData:
      Bo<PERSON>an(walletAddress) && walletAddress.length > 20
        ? keepPreviousData
        : undefined,
  });
};

export const useColdkeyReportQuery = (
  walletAddress: string,
  fromDate: string,
  toDate: string
) => {
  return useQuery({
    queryKey: [queryKeys.coldkeyReport, walletAddress, fromDate, toDate],
    queryFn: async () =>
      handleResponse(
        await apiClient.account[`:address`].coldkeyReport[`:fromDate`][
          `:toDate`
        ].$get({
          param: {
            address: walletAddress,
            fromDate,
            toDate,
          },
        })
      ),
    enabled:
      Boolean(walletAddress) &&
      walletAddress.length > 10 &&
      Boolean(fromDate) &&
      Boolean(toDate),
    retry: 3,
    staleTime: 1000 * 60 * 60,
  });
};

export const useStakeBalanceLatestQuery = (
  walletAddress: string,
  days: number
) => {
  return useQuery({
    queryKey: [queryKeys.stakeBalance, walletAddress, days],
    queryFn: async () =>
      handleResponse(
        await apiClient.account[`:address`].stake.balance.$get({
          param: {
            address: walletAddress,
          },
          query: {
            days: days.toString(),
          },
        })
      ),
    enabled: Boolean(walletAddress) && walletAddress.length > 10,
    staleTime: 1000 * 30, // 30 secs
  });
};

export const useStatsLatestQuery = () => {
  return useQuery({
    queryKey: [queryKeys.statsLatest],
    queryFn: async () => handleResponse(await apiClient.stats.latest.$get()),
    staleTime: 1000 * 60,
  });
};

export const useSavedWalletsQuery = () => {
  return useQuery({
    queryKey: [queryKeys.wallets],
    queryFn: async () =>
      handleResponse(await apiClient.account.portfolio.wallets.$get()),
  });
};

export const useAddPortfolioWallet = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ address, name }: AddPortfolioWalletRequest) => {
      return handleResponse(
        await apiClient.account.portfolio.wallet.$post({
          json: { name, address },
        })
      );
    },
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: [queryKeys.account] });
      void queryClient.invalidateQueries({ queryKey: [queryKeys.wallets] });
    },
  });
};

export const useDeletePortfolioWallet = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ walletId }: DeletePortfolioWalletRequest) => {
      return handleResponse(
        await apiClient.account.portfolio.wallet[`:walletId`].$delete({
          param: { walletId },
        })
      );
    },
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: [queryKeys.account] });
      void queryClient.invalidateQueries({ queryKey: [queryKeys.wallets] });
    },
  });
};

type AccountStakeDetailedQueryParams = {
  walletAddress: string;
  pagination: PaginationState;
  amountMin?: string;
  subnetId?: string;
  timestampStart?: string;
  timestampEnd?: string;
};

export const useAccountStakeDetailedQuery = ({
  walletAddress,
  pagination,
  amountMin,
  subnetId,
  timestampStart,
  timestampEnd,
}: AccountStakeDetailedQueryParams) => {
  // Tanstack pagination is 0 indexed so that's what is received into this method
  // But the api is 1 indexed, so add 1 to the received pageIndex when passing into the api
  const pageIndex = pagination.pageIndex + 1;
  return useQuery({
    queryKey: [
      queryKeys.stakeDetailed,
      walletAddress,
      pageIndex,
      amountMin,
      subnetId,
      timestampStart,
      timestampEnd,
    ],
    queryFn: async () =>
      handleResponse(
        await apiClient.account[`:address`].stake.detailed[`:page`].$get({
          param: {
            address: walletAddress,
            page: pageIndex.toString(),
          },
          query: {
            amountMin: amountMin === 'ALL' ? undefined : amountMin?.toString(),
            subnetId: subnetId === 'ALL' ? undefined : subnetId?.toString(),
            timestampStart,
            timestampEnd,
          },
        })
      ),
    staleTime: 30000, // Consider data fresh for 30 seconds
    enabled: Boolean(walletAddress) && walletAddress.length > 10,
  });
};

export const useAccountStakeDetailedBySubnetQuery = (
  walletAddress: string,
  subnetId: number,
  pagination: PaginationState
) => {
  // Tanstack pagination is 0 indexed so that's what is received into this method
  // But the api is 1 indexed, so add 1 to the received pageIndex when passing into the api
  const pageIndex = pagination.pageIndex + 1;
  return useQuery({
    queryKey: [queryKeys.stakeDetailed, walletAddress, subnetId, pageIndex],
    queryFn: async () =>
      handleResponse(
        await apiClient.account[`:address`].stake.detailed[':subnetId'][
          ':page'
        ].$get({
          param: {
            address: walletAddress,
            page: pageIndex.toString(),
            subnetId: subnetId.toString(),
          },
        })
      ),
    staleTime: 30000, // Consider data fresh for 30 seconds
    enabled: Boolean(walletAddress) && walletAddress.length > 10,
  });
};

import { useCallback, useMemo } from 'react';
import { BigNumber } from 'bignumber.js';
import type { StakeBalance } from '@repo/types/website-api-types';
import { getHumanValueFromChainValue, notify } from '@repo/ui/lib';
import { usePortfolioContext } from './context';
import {
  useColdkeyReportQuery,
  useSavedWalletsQuery,
  useStakeBalanceLatestQuery,
} from './query-hooks';
import type { DateRange, NormalisedDelegation } from './types';
import { useSubnetLookup } from '@/lib/hooks/global-hooks';
import { useTaoToDollarConverter } from '@/lib/hooks/price-hooks';
import {
  convertDateRangeToDates,
  getNumberOfDaysFromDateRange,
} from '@/lib/utils';

export const useColdkeyData = () => {
  const { accountLatestQuery, dateRangeSelected } = usePortfolioContext();
  const { from, to } = useMemo(
    () => convertDateRangeToDates(dateRangeSelected),
    [dateRangeSelected]
  );
  const coldkeyReportQuery = useColdkeyReportQuery(
    accountLatestQuery.data?.data[0]?.address.ss58 ?? '',
    from,
    to
  );

  const coldkeyReportData = coldkeyReportQuery.data?.data;

  return {
    isLoading: coldkeyReportQuery.isLoading,
    isError: coldkeyReportQuery.isError,
    error: coldkeyReportQuery.error,
    isSuccess: coldkeyReportQuery.isSuccess,
    coldkeyReportData,
  };
};

export const useExistingWalletCheck = () => {
  const walletsQuery = useSavedWalletsQuery();

  const doesWalletExist = useCallback(
    (address: string) => {
      // Check if wallet already exists
      const existingWallet = walletsQuery.data?.wallets.find(
        (wallet) => wallet.address === address
      );

      if (existingWallet) {
        notify.error(
          `Wallet already exists for this address. It's called "${existingWallet.name}".`
        );
        return true;
      }

      return false;
    },
    [walletsQuery.data?.wallets]
  );

  return { doesWalletExist };
};

export const useDataNormaliser = () => {
  const {
    accountLatestQuery,
    currency: { isUsd },
    dateRangeSelected,
  } = usePortfolioContext();
  const { getDollarValue } = useTaoToDollarConverter();

  const totalStaked = useMemo(
    () => BigNumber(accountLatestQuery.data?.data[0]?.balance_staked ?? 0),
    [accountLatestQuery.data?.data]
  );

  const normaliseData = useCallback(
    (delegation: StakeBalance) => {
      const balanceAsTaoChain = BigNumber(delegation.balance_as_tao);

      const balanceAsTaoHuman = getHumanValueFromChainValue(
        delegation.balance_as_tao
      );
      const balanceAsUsd = getDollarValue(balanceAsTaoHuman);

      const earningsPercentage =
        balanceAsTaoHuman > 0
          ? BigNumber(
              isUsd
                ? delegation.total_earned_alpha_as_usd
                : delegation.total_earned_alpha_as_tao
            )
              .dividedBy(isUsd ? balanceAsUsd : balanceAsTaoChain)
              .multipliedBy(100)
              .toNumber()
          : 0;
      return {
        accountId: delegation.coldkey.ss58,
        validatorName: delegation.hotkey_name,
        validatorHotkey: delegation.hotkey.ss58,
        balanceAsAlpha: getHumanValueFromChainValue(delegation.balance),
        balanceAsTao: balanceAsTaoHuman,
        balanceAsUsd,
        earnedAlpha: getHumanValueFromChainValue(delegation.total_earned_alpha),
        earnedTao: getHumanValueFromChainValue(
          delegation.total_earned_alpha_as_tao
        ),
        earnedUsd: BigNumber(delegation.total_earned_alpha_as_usd).toNumber(),
        earningsPercentage,
        apy: getEstimateAprFromValueAndRange(
          earningsPercentage,
          dateRangeSelected
        ),
        percentageOfStake: balanceAsTaoChain
          .dividedBy(totalStaked)
          .multipliedBy(100)
          .toNumber(),
        hasTransactionsWithinTimeframe:
          delegation.total_buys > 0 ||
          delegation.total_sells > 0 ||
          delegation.total_transfers_in > 0 ||
          delegation.total_transfers_out > 0,
        hasTransfersWithinSelectedTimeframe:
          delegation.total_transfers_in > 0 ||
          delegation.total_transfers_out > 0,
      };
    },
    [dateRangeSelected, getDollarValue, isUsd, totalStaked]
  );

  return { normaliseData };
};

export const useBalancesData = () => {
  const { walletAddress, dateRangeSelected } = usePortfolioContext();
  const { data, isSuccess, isLoading, error } = useStakeBalanceLatestQuery(
    walletAddress,
    getNumberOfDaysFromDateRange(dateRangeSelected)
  );
  const { getSubnetName, getSubnetSymbol } = useSubnetLookup();
  const { normaliseData } = useDataNormaliser();

  const getBalancesData = useCallback((): NormalisedDelegation[] => {
    if (!isSuccess) {
      return [];
    }

    const getData = data.data.length > 0 ? data.data : [];
    if (getData.length === 0) {
      return [];
    }

    const delegations = getData.map((delegation) => {
      const subnetName = getSubnetName(delegation.netuid);
      const symbol = getSubnetSymbol(delegation.netuid);

      return {
        netuid: delegation.netuid,
        subnetName,
        subnetSymbol: symbol,
        ...normaliseData(delegation),
      };
    });

    return delegations;
  }, [isSuccess, data?.data, getSubnetName, getSubnetSymbol, normaliseData]);

  const balancesDataForSelectedWallet = useMemo(
    () => getBalancesData(),
    [getBalancesData]
  );

  return {
    isLoading,
    isSuccess,
    error,
    getBalancesData,
    balancesDataForSelectedWallet,
  };
};

export const getEstimateAprFromValueAndRange = (
  earningsPercentage: number,
  dateRange: DateRange
) => {
  const multiplier =
    dateRange === '1d'
      ? 365
      : dateRange === '1w'
        ? 52
        : dateRange === '1m'
          ? 12
          : getDaysSinceDtaoLaunch() / 365;

  const apr = earningsPercentage * multiplier;
  return apr;
};

const getDaysSinceDtaoLaunch = () => {
  const now = new Date();
  const targetDate = new Date('2025-02-13T00:00:00Z');
  const timeDifference = now.getTime() - targetDate.getTime();
  const daysDifference = Math.floor(timeDifference / (1000 * 60 * 60 * 24));
  return daysDifference;
};

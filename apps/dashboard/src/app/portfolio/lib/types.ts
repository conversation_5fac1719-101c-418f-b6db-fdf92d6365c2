import type { portfolioDateRanges } from './constants';

export type DateRange = (typeof portfolioDateRanges)[number]['value'];

export type Mode = 'buy' | 'sell';

export type SubnetForTxType = {
  netuid: number;
  validatorHotkey: string;
  txType: Mode;
};

export type TokenType = 'TAO' | 'ALPHA';

export type NormalisedDelegation = {
  accountId: string;
  netuid: number;
  subnetName: string;
  subnetSymbol: string;
  validatorName: string;
  validatorHotkey: string;
  balanceAsAlpha: number;
  balanceAsTao: number;
  balanceAsUsd: number;
  earnedAlpha: number;
  earnedTao: number;
  earnedUsd: number;
  earningsPercentage: number;
  apy: number;
  percentageOfStake: number;
  hasTransactionsWithinTimeframe: boolean;
  hasTransfersWithinSelectedTimeframe: boolean;
};

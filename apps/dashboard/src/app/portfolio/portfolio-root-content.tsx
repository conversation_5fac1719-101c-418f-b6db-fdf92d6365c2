'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useLastUsedWallet } from './lib/utils';
import { WalletPicker } from './ui/wallet-picker';

export const PortfolioRootContent = () => {
  const router = useRouter();
  const { latestWallet, setLatestWallet } = useLastUsedWallet();
  const [walletAddress, setWalletAddress] = useState<string>(latestWallet);

  useEffect(() => {
    if (latestWallet) {
      router.push(`/portfolio/${latestWallet}`);
    }
  }, [latestWallet, router]);

  const handleSetWalletAddress = (address: string) => {
    setWalletAddress(address);
    setLatestWallet(address);
  };

  return (
    <div className='flex flex-1 flex-col items-end gap-4'>
      <div className='flex w-full flex-col gap-4 sm:flex-row sm:items-center sm:justify-between'>
        <div className='w-full sm:relative sm:flex-grow'>
          <WalletPicker
            allowAccountAdd={false}
            allowAccountDelete={false}
            setWalletAddress={handleSetWalletAddress}
            walletAddressInput={walletAddress}
          />
        </div>
      </div>
    </div>
  );
};

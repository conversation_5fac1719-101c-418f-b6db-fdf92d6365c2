'use client';

import { memo } from 'react';
import { z } from 'zod';
import { AlertError } from '@repo/ui/components';
import { PortfolioAnalyticsContextProvider } from './lib/context';
import { PortfolioSubnetPageContent } from './ui/page-content';
import { PageLayout } from '@/components/page-layout';

const subnetIdSchema = z.coerce.number().int().nonnegative();

function PortfolioSubnetPage({ params }: { params: { subnetId: string } }) {
  const { subnetId: subnetIdParam } = params;
  const subnetIdResult = subnetIdSchema.safeParse(subnetIdParam);

  if (!subnetIdResult.success) {
    return (
      <PageLayout>
        <AlertError description='Subnet ID must be a valid number' />
      </PageLayout>
    );
  }

  const subnetId = subnetIdResult.data;
  return (
    <PageLayout>
      <PortfolioAnalyticsContextProvider subnetId={subnetId}>
        <PortfolioSubnetPageContent />
      </PortfolioAnalyticsContextProvider>
    </PageLayout>
  );
}

export default memo(PortfolioSubnetPage);

import { memo, useMemo } from 'react';
import { BigNumber } from 'bignumber.js';
import { BiErrorCircle } from 'react-icons/bi';
import type { DtaoSubnet, EnrichedSubnet } from '@repo/types/website-api-types';
import { TaoOrAlphaValueDisplay, Text } from '@repo/ui/components';
import { usePortfolioAnalyticsContext } from '@/app/portfolio/[accountId]/[subnetId]/lib/context';
import { ChangeBadge } from '@/app/portfolio/[accountId]/[subnetId]/ui/shared';
import { usePortfolioContext } from '@/app/portfolio/lib/context';
import { PortfolioStatCardSmall } from '@/app/portfolio/ui/stats/portfolio-stats-card-small';
import { useTaoToDollarConverter } from '@/lib/hooks/price-hooks';
import {
  chainConvertFormat,
  useChainAmountConverter,
} from '@/lib/utils/conversion/conversion-utils';

const MarketSectionContent = () => {
  const { subnet, subnetPool } = usePortfolioAnalyticsContext();

  if (!subnet || !subnetPool) {
    return null;
  }

  return <MarketSectionUi subnet={subnet} subnetPool={subnetPool} />;
};

const MarketSectionUiContent = ({
  subnet,
  subnetPool,
}: {
  subnet: EnrichedSubnet;
  subnetPool: DtaoSubnet;
}) => {
  const {
    dates: { is1d, is1w },
    currencySelected,
    currency: { isUsd },
  } = usePortfolioContext();
  const { convertAmount } = useChainAmountConverter();
  const { getDollarValue } = useTaoToDollarConverter();
  const marketData = useMemo(() => {
    const taoPrice = BigNumber(subnetPool.price).toNumber();
    const price = isUsd ? getDollarValue(taoPrice) : taoPrice;

    const priceChange = is1d
      ? subnetPool.price_change_1_day
      : is1w
        ? subnetPool.price_change_1_week
        : null;

    const marketCapInRao = BigNumber(subnetPool.market_cap).toNumber();
    const marketCapInTao = convertAmount(
      marketCapInRao,
      chainConvertFormat.cToH
    ).toNumber();
    const marketCap = isUsd ? getDollarValue(marketCapInTao) : marketCapInTao;

    const volumeInRao = BigNumber(subnetPool.tao_volume_24_hr).toNumber();
    const volumeInTao = convertAmount(
      volumeInRao,
      chainConvertFormat.cToH
    ).toNumber();
    const volume = isUsd ? getDollarValue(volumeInTao) : volumeInTao;

    const emissionsInRao = BigNumber(subnet.emission).toNumber() * 7200;
    const emissionsInTao = convertAmount(
      emissionsInRao,
      chainConvertFormat.cToH
    ).toNumber();
    const emissions = isUsd ? getDollarValue(emissionsInTao) : emissionsInTao;

    return {
      price,
      priceChange,
      marketCap,
      volume,
      emissions,
    };
  }, [
    subnetPool.price,
    subnetPool.price_change_1_day,
    subnetPool.price_change_1_week,
    subnetPool.market_cap,
    subnetPool.tao_volume_24_hr,
    isUsd,
    getDollarValue,
    is1d,
    is1w,
    convertAmount,
    subnet.emission,
  ]);

  return (
    <div className='flex flex-col gap-4'>
      <Text level='headerSmall'>Market</Text>
      <div className='grid max-w-screen-2xl gap-6 sm:grid-cols-4'>
        <MarketCard
          title='Price'
          tooltip={`Alpha token price in ${currencySelected}`}
          value={marketData.price}
          valueChange={marketData.priceChange}
          isUsd={isUsd}
          isCompactFormat={isUsd}
        />
        <MarketCard
          title='Market Cap / 24H'
          tooltip={`Subnet market cap in ${currencySelected}`}
          value={marketData.marketCap}
          valueChange={subnetPool.market_cap_change_1_day}
          isUsd={isUsd}
          isCompactFormat
        />
        <MarketCard
          title='Volume / 24H'
          tooltip={`Subnet volume in ${currencySelected}`}
          value={marketData.volume}
          valueChange={subnetPool.tao_volume_24_hr_change_1_day}
          isUsd={isUsd}
          isCompactFormat
        />
        <MarketCard
          title='Emissions / Day'
          tooltip={`Subnet emissions in ${currencySelected}`}
          value={marketData.emissions}
          valueChange={null}
          isUsd={isUsd}
          isCompactFormat
        />
      </div>
    </div>
  );
};

const MarketSectionUi = memo(MarketSectionUiContent);

const MarketCard = ({
  title,
  tooltip,
  value,
  valueChange,
  isUsd,
  isCompactFormat = false,
}: {
  title: string;
  tooltip: string;
  value: number;
  valueChange: string | null;
  isUsd: boolean;
  isCompactFormat?: boolean;
}) => {
  return (
    <PortfolioStatCardSmall
      title={title}
      TitleIcon={BiErrorCircle}
      titleIconTooltip={tooltip}
      value={
        <TaoOrAlphaValueDisplay
          value={value}
          valueTextLevel='headerSmall'
          valueClassName='font-normal'
          symbol={isUsd ? '$' : ''}
          valueFormattingOptions={
            isCompactFormat
              ? {
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 2,
                  notation: 'compact',
                }
              : {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 6,
                }
          }
          areDecimalsSmall
        />
      }
      valueSuffix={
        valueChange !== null ? <ChangeBadge val={Number(valueChange)} /> : null
      }
    />
  );
};

export const MarketSection = memo(MarketSectionContent);

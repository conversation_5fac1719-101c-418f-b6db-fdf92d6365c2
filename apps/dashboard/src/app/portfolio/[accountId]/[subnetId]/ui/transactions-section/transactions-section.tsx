import { Text } from '@repo/ui/components';
import { StakingTableContainer } from './staking-table-container';
import { DateRangeAndCurrencySelector } from '@/app/portfolio/ui/shared';

export const TransactionsSection = () => {
  return (
    <div className='flex flex-col gap-4'>
      <div className='bg-app-bg sticky top-0 z-10 flex flex-col-reverse items-start justify-between gap-4 py-4 sm:flex-row sm:items-center'>
        <Text level='headerSmall'>Transactions</Text>
        <DateRangeAndCurrencySelector />
      </div>

      <StakingTableContainer />
    </div>
  );
};

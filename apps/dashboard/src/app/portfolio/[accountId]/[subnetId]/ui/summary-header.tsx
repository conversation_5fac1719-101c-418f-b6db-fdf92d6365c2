'use client';

import { BigNumber } from 'bignumber.js';
import { TaoOrAlphaValueDisplay } from '@repo/ui/components';
import { usePortfolioAnalyticsContext } from '@/app/portfolio/[accountId]/[subnetId]/lib/context';
import { useSubnetDataContext } from '@/app/portfolio/[accountId]/[subnetId]/lib/subnet-data-context';
import { usePortfolioContext } from '@/app/portfolio/lib/context';
import {
  DateRangeAndCurrencySelector,
  PortfolioSummaryHeader,
} from '@/app/portfolio/ui/shared';
import { useTaoToDollarConverter } from '@/lib/hooks/price-hooks';

export const SummaryHeader = () => {
  const {
    currency: { isUsd },
  } = usePortfolioContext();
  const { normalisedDataForSubnet } = useSubnetDataContext();
  const { symbol } = usePortfolioAnalyticsContext();
  const { getDollarValue } = useTaoToDollarConverter();

  const { taoTotal, alphaTotal } = normalisedDataForSubnet.reduce(
    (acc, curr) => {
      return {
        taoTotal: acc.taoTotal.plus(curr.balanceAsTao),
        alphaTotal: acc.alphaTotal.plus(curr.balanceAsAlpha),
      };
    },
    { taoTotal: new BigNumber(0), alphaTotal: new BigNumber(0) }
  );
  const alphaValue = alphaTotal.toNumber();
  const dollarValue = getDollarValue(taoTotal.toNumber());

  return (
    <div className='flex flex-col gap-4 sm:flex-row sm:justify-between'>
      <PortfolioSummaryHeader
        symbol={symbol}
        taoOrAlphaValue={alphaValue}
        bottomValue={
          <TaoOrAlphaValueDisplay
            value={isUsd ? dollarValue : taoTotal.toNumber()}
            symbol={isUsd ? '$' : undefined}
            iconClassName='text-sm'
            valueTextLevel='displaySmall'
            valueFormattingOptions={{
              minimumFractionDigits: 2,
              maximumFractionDigits: isUsd ? 2 : 5,
            }}
            valueClassName='text-3xl font-normal text-white/60'
            areDecimalsSmall
          />
        }
      />
      <div className='flex flex-row items-end justify-end'>
        <DateRangeAndCurrencySelector />
      </div>
    </div>
  );
};

'use client';

import { memo, useMemo, useState } from 'react';
import type { PaginationState } from '@tanstack/react-table';
import { AlertError, Skeleton } from '@repo/ui/components';
import { StakingEventsDisplay } from './staking-events-display';
import { usePortfolioAnalyticsContext } from '@/app/portfolio/[accountId]/[subnetId]/lib/context';
import { usePortfolioContext } from '@/app/portfolio/lib/context';
import { useAccountStakeDetailedBySubnetQuery } from '@/app/portfolio/lib/query-hooks';

export const StakingTableContainerUi = () => {
  const { walletAddress } = usePortfolioContext();
  const { subnetId } = usePortfolioAnalyticsContext();
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 100,
  });
  const { isLoading, isSuccess, isError, error, data } =
    useAccountStakeDetailedBySubnetQuery(walletAddress, subnetId, pagination);
  const delegations = useMemo(() => data?.data || [], [data]);

  return (
    <>
      {isLoading ? (
        <div className='flex max-w-screen-2xl flex-col gap-4'>
          <Skeleton className='h-16 w-full' />
          <Skeleton className='h-16 w-full' />
          <Skeleton className='h-16 w-full' />
        </div>
      ) : null}

      {isError ? (
        <AlertError
          className='lg:w-[575px]'
          description={error.message}
          title='Error fetching Delegation Events'
        />
      ) : null}

      {isSuccess && delegations.length > 0 ? (
        <StakingEventsDisplay
          delegationEvents={delegations}
          total={delegations.length}
          pagination={pagination}
          setPagination={setPagination}
        />
      ) : null}
    </>
  );
};

export const StakingTableContainer = memo(StakingTableContainerUi);

import { useMemo } from 'react';
import { BigNumber } from 'bignumber.js';
import {
  Card,
  Skeleton,
  TaoOrAlphaValueDisplay,
  Text,
} from '@repo/ui/components';
import { getHumanValueFromChainValue } from '@repo/ui/lib';
import { BalanceDonutChart } from './balance-donut-chart';
import { usePortfolioAnalyticsContext } from '@/app/portfolio/[accountId]/[subnetId]/lib/context';
import { useSubnetDataContext } from '@/app/portfolio/[accountId]/[subnetId]/lib/subnet-data-context';
import { usePortfolioContext } from '@/app/portfolio/lib/context';
import { PortfolioStatsCardHeader } from '@/app/portfolio/ui/stats/portfolio-stats-card-header';

export const BalanceDonutCard = () => {
  const { isLoading, rawDataForSubnet } = useSubnetDataContext();
  const { currencySelected } = usePortfolioContext();
  const { symbol } = usePortfolioAnalyticsContext();

  const normalisedData = useMemo(() => {
    return rawDataForSubnet.map((d) => {
      return {
        alphaBought: getHumanValueFromChainValue(d.total_bought_alpha),
        alphaTransferredIn: getHumanValueFromChainValue(
          d.total_transferred_in_alpha
        ),
        alphaEarned: getHumanValueFromChainValue(d.total_earned_alpha),
        alphaSold: getHumanValueFromChainValue(d.total_sold_alpha),
        usdSpent: BigNumber(d.total_bought_alpha_as_usd).toNumber(),
        usdTransferredIn: BigNumber(
          d.total_transferred_in_alpha_as_usd
        ).toNumber(),
        usdEarned: BigNumber(d.total_earned_alpha_as_usd).toNumber(),
        usdSold: BigNumber(d.total_sold_alpha_as_usd).toNumber(),
      };
    });
  }, [rawDataForSubnet]);

  if (isLoading) {
    return (
      <Card contentContainerClassName='h-full'>
        <div className='flex h-full flex-row justify-between'>
          <div className='flex h-full flex-1 flex-col gap-6'>
            <div>
              <PortfolioStatsCardHeader title='Balance' />
            </div>
            <div className='flex w-full flex-col gap-2'>
              <Skeleton className='h-6 w-full' />
              <Skeleton className='h-6 w-full' />
              <Skeleton className='h-6 w-full' />
            </div>
          </div>
          <div className='flex w-1/2 flex-row items-center justify-end'>
            <Skeleton className='h-28 w-28 rounded-full' />
          </div>
        </div>
      </Card>
    );
  }

  const dataSummed = normalisedData.reduce(
    (acc, curr) => {
      return {
        alphaBought: acc.alphaBought + curr.alphaBought,
        alphaTransferredIn: acc.alphaTransferredIn + curr.alphaTransferredIn,
        alphaEarned: acc.alphaEarned + curr.alphaEarned,
        alphaSold: acc.alphaSold + curr.alphaSold,
        usdSpent: acc.usdSpent + curr.usdSpent,
        usdTransferredIn: acc.usdTransferredIn + curr.usdTransferredIn,
        usdEarned: acc.usdEarned + curr.usdEarned,
        usdSold: acc.usdSold + curr.usdSold,
      };
    },
    {
      alphaBought: 0,
      alphaTransferredIn: 0,
      alphaEarned: 0,
      alphaSold: 0,
      usdSpent: 0,
      usdTransferredIn: 0,
      usdEarned: 0,
      usdSold: 0,
    }
  );

  const dataForChart = [
    {
      label: 'Bought',
      value:
        currencySelected === 'TAO'
          ? dataSummed.alphaBought
          : dataSummed.usdSpent,
      colour: 'green',
    },
    {
      label: 'Earned',
      value:
        currencySelected === 'TAO'
          ? dataSummed.alphaEarned
          : dataSummed.usdEarned,
      colour: 'red',
    },
    {
      label: 'Transferred',
      value:
        currencySelected === 'TAO'
          ? dataSummed.alphaTransferredIn
          : dataSummed.usdTransferredIn,
      colour: 'yellow',
    },
    {
      label: 'Sold',
      value:
        currencySelected === 'TAO' ? dataSummed.alphaSold : dataSummed.usdSold,
      colour: 'blue',
    },
  ];

  return (
    <Card contentContainerClassName='h-full'>
      <div className='flex h-full flex-row justify-between'>
        <div className='flex h-full flex-col gap-6'>
          <div>
            <PortfolioStatsCardHeader title='Balance' />
          </div>
          <div>
            <table>
              <tbody>
                {dataForChart.map(({ label, value, colour }) => (
                  <tr key={label}>
                    <td className='pr-3'>
                      <div className='flex items-center'>
                        <div
                          className={`mr-2 h-2 w-2 rounded-full ${
                            colour === 'green'
                              ? 'bg-accent-1'
                              : colour === 'red'
                                ? 'bg-accent-2'
                                : colour === 'blue'
                                  ? 'bg-[#007AFF]'
                                  : 'bg-[#EBC247]'
                          }`}
                        />
                        <Text level='bodyMedium' className='text-white/60'>
                          {label}
                        </Text>
                      </div>
                    </td>
                    <td>
                      <TaoOrAlphaValueDisplay
                        value={value}
                        symbol={currencySelected === 'TAO' ? symbol : '$'}
                        valueTextLevel='bodyMedium'
                        colourClassName='text-white'
                        iconClassName='text-xs'
                        areDecimalsSmall={false}
                        valueFormattingOptions={{
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        }}
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
        <div className='flex w-full flex-col items-center 2xl:w-1/2 2xl:flex-row 2xl:items-end'>
          <BalanceDonutChart width={150} height={150} data={dataForChart} />
        </div>
      </div>
    </Card>
  );
};

'use client';

import { useMemo } from 'react';
import type { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { BiLinkExternal } from 'react-icons/bi';
import { EmptyCell, TaoOrAlphaValueDisplay, Text } from '@repo/ui/components';
import type { StakingEventViewDto } from '@/app/portfolio/[accountId]/[subnetId]/lib/types';
import {
  ActionBadge,
  ChangeBadge,
} from '@/app/portfolio/[accountId]/[subnetId]/ui/shared';
import { usePortfolioContext } from '@/app/portfolio/lib/context';

export const taoAndAlphaFormat: Intl.NumberFormatOptions = {
  minimumFractionDigits: 2,
  maximumFractionDigits: 6,
};

export const usdFormat: Intl.NumberFormatOptions = {
  minimumFractionDigits: 2,
  maximumFractionDigits: 2,
};

export const percentageFormat: Intl.NumberFormatOptions = {
  minimumFractionDigits: 2,
  maximumFractionDigits: 2,
};

export const useColumns = () => {
  const {
    currency: { isUsd },
  } = usePortfolioContext();
  const columns: ColumnDef<StakingEventViewDto>[] = useMemo(
    () => [
      {
        id: 'month',
        accessorFn: (row) => row.month,
        header: 'Month',
        enableGrouping: true,
      },
      {
        accessorKey: 'action',
        header: 'Type',
        cell: ({ row }) => {
          const d = row.original;
          return <ActionBadge action={d.action} />;
        },
      },
      {
        accessorKey: 'timestamp',
        header: 'Date/Time',
        cell: ({ row }) => {
          const timestamp = row.original.timestamp;
          const dt = new Date(timestamp);
          return (
            <div className='flex w-fit flex-col items-end gap-1'>
              <div>{format(dt, 'dd MMM yyyy')}</div>
              <Text level='labelExtraSmall' className='text-white/60'>
                at {format(dt, 'HH:mm')}
              </Text>
            </div>
          );
        },
      },
      {
        accessorKey: 'validatorName',
        header: 'Validator',
        cell: ({ row }) => {
          const validatorName = row.original.validatorName;
          return (
            <a
              href={`https://taostats.io/validators/${row.original.validatorHotkey}`}
              target='_blank'
              rel='noreferrer'
            >
              {validatorName}{' '}
              <BiLinkExternal className='ml-1 inline text-white' />
            </a>
          );
        },
      },
      {
        accessorKey: 'priceInTao',
        header: 'Price',
        cell: ({ row }) => {
          const { priceInTao, priceInUsd } = row.original;
          return (
            <TaoOrAlphaValueDisplay
              symbol={isUsd ? '$' : undefined}
              value={isUsd ? priceInUsd : priceInTao}
              valueFormattingOptions={isUsd ? usdFormat : taoAndAlphaFormat}
              valueTextLevel='labelLarge'
              areDecimalsSmall
            />
          );
        },
      },
      {
        accessorKey: 'alphaAmount',
        header: 'Amount',
        cell: ({ row }) => {
          const { subnetSymbol, alphaAmount } = row.original;
          return (
            <TaoOrAlphaValueDisplay
              symbol={subnetSymbol}
              value={alphaAmount}
              valueFormattingOptions={taoAndAlphaFormat}
              valueTextLevel='labelLarge'
              areDecimalsSmall
            />
          );
        },
      },
      {
        accessorKey: 'valueInTao',
        header: 'Tx Value',
        cell: ({ row }) => {
          const { valueInTao, valueInUsd } = row.original;
          return (
            <TaoOrAlphaValueDisplay
              symbol={isUsd ? '$' : undefined}
              value={isUsd ? valueInUsd : valueInTao}
              valueFormattingOptions={isUsd ? usdFormat : taoAndAlphaFormat}
              valueTextLevel='labelLarge'
              iconClassName='text-xs'
              areDecimalsSmall
            />
          );
        },
      },
      {
        accessorKey: 'currentAlphaValueInTao',
        header: 'Worth Now',
        cell: ({ row }) => {
          const { action, currentAlphaValueInUsd, currentAlphaValueInTao } =
            row.original;

          if (action === 'Sell') {
            return <EmptyCell />;
          }

          return (
            <TaoOrAlphaValueDisplay
              symbol={isUsd ? '$' : undefined}
              value={isUsd ? currentAlphaValueInUsd : currentAlphaValueInTao}
              valueFormattingOptions={isUsd ? usdFormat : taoAndAlphaFormat}
              valueTextLevel='labelLarge'
              iconClassName='text-xs'
              areDecimalsSmall
            />
          );
        },
      },
      {
        id: 'returns',
        header: 'Delta',
        accessorFn: (row) => {
          const { returnsInTao, returnsInUsd } = row;
          return isUsd ? returnsInUsd : returnsInTao;
        },
        cell: ({ row }) => {
          const { action, returnsInTao, returnsInUsd } = row.original;

          if (action === 'Sell') {
            return <EmptyCell />;
          }

          const val = isUsd ? returnsInUsd : returnsInTao;

          return <ChangeBadge val={val} />;
        },
      },
    ],
    [isUsd]
  );

  return columns;
};

import type { ReactNode } from 'react';
import { memo } from 'react';
import { format } from 'date-fns';
import {
  Card,
  EmptyCell,
  TaoOrAlphaValueDisplay,
  Text,
} from '@repo/ui/components';
import { taoAndAlphaFormat, usdFormat } from './config';
import type { StakingEventViewDto } from '@/app/portfolio/[accountId]/[subnetId]/lib/types';
import {
  ActionBadge,
  ChangeBadge,
} from '@/app/portfolio/[accountId]/[subnetId]/ui/shared';
import { usePortfolioContext } from '@/app/portfolio/lib/context';

const StakingEventsCardsUi = ({ data }: { data: StakingEventViewDto[] }) => {
  const {
    currency: { isUsd },
  } = usePortfolioContext();

  return (
    <div className='flex flex-col gap-6'>
      {data.map((d) => {
        const dt = new Date(d.timestamp);
        return (
          <Card
            key={`${d.timestamp}-${d.validatorHotkey}-${d.action}-${d.valueInTao}`}
          >
            <div className='flex flex-col gap-6'>
              <div className='grid grid-cols-3 gap-x-4 gap-y-6'>
                <CardDataDisplay
                  label='Transaction'
                  value={<ActionBadge action={d.action} />}
                />
                <CardDataDisplay
                  label='Date/Time'
                  value={
                    <div className='flex w-fit flex-col gap-1'>
                      <Text level='labelLarge'>
                        {format(dt, 'dd MMM yyyy')}
                      </Text>
                      <Text level='labelExtraSmall' className='text-white/60'>
                        at {format(dt, 'HH:mm')}
                      </Text>
                    </div>
                  }
                />
                <CardDataDisplay
                  label='Price'
                  value={
                    <TaoOrAlphaValueDisplay
                      symbol={isUsd ? '$' : undefined}
                      value={isUsd ? d.priceInUsd : d.priceInTao}
                      valueFormattingOptions={
                        isUsd ? usdFormat : taoAndAlphaFormat
                      }
                      valueTextLevel='labelLarge'
                      iconClassName='text-xs'
                    />
                  }
                />
                <CardDataDisplay
                  label='Amount'
                  value={
                    <TaoOrAlphaValueDisplay
                      symbol={d.subnetSymbol}
                      value={d.alphaAmount}
                      valueFormattingOptions={taoAndAlphaFormat}
                      valueTextLevel='labelLarge'
                    />
                  }
                />
                <CardDataDisplay
                  label='Value'
                  value={
                    <TaoOrAlphaValueDisplay
                      symbol={isUsd ? '$' : undefined}
                      value={isUsd ? d.valueInUsd : d.valueInTao}
                      valueFormattingOptions={
                        isUsd ? usdFormat : taoAndAlphaFormat
                      }
                      valueTextLevel='labelLarge'
                      iconClassName='text-xs'
                    />
                  }
                />
                <CardDataDisplay
                  label='Worth Now'
                  value={
                    d.action === 'Sell' ? (
                      <EmptyCell />
                    ) : (
                      <TaoOrAlphaValueDisplay
                        symbol={isUsd ? '$' : undefined}
                        value={
                          isUsd
                            ? d.currentAlphaValueInUsd
                            : d.currentAlphaValueInTao
                        }
                        valueFormattingOptions={
                          isUsd ? usdFormat : taoAndAlphaFormat
                        }
                        valueTextLevel='labelLarge'
                        iconClassName='text-xs'
                      />
                    )
                  }
                />
                <CardDataDisplay
                  label='Delta'
                  value={
                    d.action === 'Sell' ? (
                      <EmptyCell />
                    ) : (
                      <ChangeBadge
                        val={isUsd ? d.returnsInUsd : d.returnsInTao}
                      />
                    )
                  }
                />
              </div>
            </div>
          </Card>
        );
      })}
    </div>
  );
};

const CardDataDisplay = memo(function CardDataDisplay({
  label,
  value,
}: {
  label: string;
  value: ReactNode;
}) {
  return (
    <div className='flex flex-col gap-2'>
      <Text level='labelExtraSmall' className='text-white/60'>
        {label}
      </Text>
      {value}
    </div>
  );
});

export const StakingEventsCards = memo(StakingEventsCardsUi);

'use client';

import { memo } from 'react';
import { B<PERSON><PERSON><PERSON><PERSON><PERSON>, BiCoin<PERSON>tack, BiWallet } from 'react-icons/bi';
import { <PERSON>ton, <PERSON>, Text } from '@repo/ui/components';
import { HeaderRight } from './header-right';
import { usePortfolioAnalyticsContext } from '@/app/portfolio/[accountId]/[subnetId]/lib/context';
import { usePortfolioContext } from '@/app/portfolio/lib/context';
import {
  BuySellContextProvider,
  BuySellDialog,
  useBuySellContext,
} from '@/app/portfolio/ui/buy-sell';
import { isWalletConnected } from '@/app/portfolio/ui/current-balances/utils';
import { PageHeader } from '@/components/page-header';
import { AccountContextProvider } from '@/contexts/chain-and-wallet/account-context';
import { useChainAndWalletContext } from '@/contexts/chain-and-wallet/chain-and-wallet-context';
import { useSubnetLookup } from '@/lib/hooks/global-hooks';
import { HOTKEY_TAOSTATS_VALIDATOR } from '@/lib/utils';

export const Header = memo(function Header() {
  const { subnetId, symbol, subnetName } = usePortfolioAnalyticsContext();
  const { walletAddress } = usePortfolioContext();
  const subnetLookup = useSubnetLookup();
  const rank = subnetLookup.getSubnetRank(subnetId);

  return (
    <div className='flex max-w-screen-2xl flex-col gap-8'>
      <Link
        href={`/portfolio/${walletAddress}`}
        className='flow-row group flex gap-1 text-2xl'
      >
        <BiArrowBack className='text-accent-1' />{' '}
        <Text className='group-hover:underline'>Back</Text>
      </Link>

      <div className='flex flex-col gap-4 sm:flex-row sm:justify-between'>
        <div className='flex flex-col gap-4'>
          <HeaderLeft
            symbol={symbol}
            subnetName={subnetName}
            subnetId={subnetId}
            rank={rank}
          />
          <AccountContextProvider>
            <BuySellContextProvider>
              <BuyAndSellButtons />
              <BuySellDialog />
            </BuySellContextProvider>
          </AccountContextProvider>
        </div>
        <HeaderRight />
      </div>
    </div>
  );
});

const BuyAndSellButtons = memo(function BuyAndSellButtons() {
  const { subnetId } = usePortfolioAnalyticsContext();
  const {
    state: { currentAccount },
  } = useChainAndWalletContext();
  const { handleBuyClick, handleSellClick } = useBuySellContext();

  return (
    <div className='flex flex-row gap-2'>
      <Button
        className={`${currentAccount ? 'border-accent-1' : 'border-accent-1/50 text-white/50'} hover:bg-accent-1/20`}
        onClick={() => {
          if (isWalletConnected(currentAccount)) {
            handleBuyClick(subnetId, HOTKEY_TAOSTATS_VALIDATOR);
          }
        }}
      >
        Buy <BiWallet className='ml-1' />
      </Button>
      <Button
        className={`${currentAccount ? 'border-accent-2' : 'border-accent-2/50 text-white/50'} hover:bg-accent-2/20`}
        onClick={() => {
          if (isWalletConnected(currentAccount)) {
            handleSellClick(subnetId, HOTKEY_TAOSTATS_VALIDATOR);
          }
        }}
      >
        Sell <BiCoinStack className='ml-1' />
      </Button>
    </div>
  );
});

const HeaderLeft = memo(function HeaderLeft({
  symbol,
  subnetName,
  subnetId,
  rank,
}: {
  symbol: string;
  subnetName: string;
  subnetId: number;
  rank?: number;
}) {
  return (
    <div className='flex flex-row items-center gap-4'>
      <div className='flex h-12 w-12 items-center justify-center rounded-full bg-white font-medium'>
        <Text level='xl' className='text-black'>
          {symbol}
        </Text>
      </div>
      <PageHeader title={subnetName} />
      <Text level='headerMedium' className='text-white/60'>
        {subnetId}
      </Text>
      {rank !== undefined ? (
        <Text level='buttonSmall' className='text-white/60'>
          Subnet Rank #{rank}
        </Text>
      ) : null}
    </div>
  );
});

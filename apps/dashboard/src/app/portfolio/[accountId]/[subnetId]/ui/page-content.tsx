import { memo } from 'react';
import { AlertError, Spinner } from '@repo/ui/components';
import { Header } from './header';
import { MarketSection } from './market-section';
import { Stats } from './stats';
import { SummaryHeader } from './summary-header';
import { TransactionsSection } from './transactions-section';
import { usePortfolioAnalyticsContext } from '@/app/portfolio/[accountId]/[subnetId]/lib/context';
import { SubnetDataContextProvider } from '@/app/portfolio/[accountId]/[subnetId]/lib/subnet-data-context';

const Content = () => {
  const { isLoading, subnetId, subnet, subnetPool } =
    usePortfolioAnalyticsContext();

  if (isLoading) {
    return <Spinner />;
  }

  if (!subnet) {
    return <AlertError description={`Subnet not found with id: ${subnetId}`} />;
  }

  if (!subnetPool) {
    return (
      <AlertError description={`Subnet pool not found with id: ${subnetId}`} />
    );
  }

  return (
    <SubnetDataContextProvider>
      <div className='flex flex-col gap-8'>
        <Header />
        <SummaryHeader />
        <Stats />
        <MarketSection />
        <TransactionsSection />
      </div>
    </SubnetDataContextProvider>
  );
};

export const PortfolioSubnetPageContent = memo(Content);

import { BiTrophy } from 'react-icons/bi';
import { useSubnetDataContext } from '@/app/portfolio/[accountId]/[subnetId]/lib/subnet-data-context';
import { PortfolioStatCard } from '@/app/portfolio/ui/stats/portfolio-stats-card';
import { formatNumber } from '@/lib/utils';

export const RankCard = () => {
  const { isLoading, isSuccess, rawDataForSubnet } = useSubnetDataContext();

  if (!isSuccess) {
    return null;
  }

  const dataForSubnet =
    rawDataForSubnet.length > 0 ? rawDataForSubnet[0] : null;
  const rank = dataForSubnet?.subnet_rank;
  const accountsTotal = dataForSubnet?.subnet_total_holders;
  const rankPercentage =
    rank && accountsTotal ? (rank / accountsTotal) * 100 : 0;
  const footerText = accountsTotal
    ? `You're in the top ${rankPercentage < 5 ? rankPercentage.toFixed(3) : Math.round(rankPercentage)}% of alpha holders for this subnet`
    : undefined;

  return (
    <PortfolioStatCard
      isLoading={isLoading}
      title='Alpha Wallet Rank'
      value={rank ? formatNumber(rank) : '-'}
      valueSuffix={accountsTotal ? ` / ${formatNumber(accountsTotal)}` : ''}
      footerText={footerText}
      FooterIcon={BiTrophy}
      footerTooltip='The rank of your wallet for alpha holders on this subnet, based on the total alpha held.'
    />
  );
};

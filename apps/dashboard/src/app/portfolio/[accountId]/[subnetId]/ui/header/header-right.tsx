import { useRouter } from 'next/navigation';
import { usePortfolioAnalyticsContext } from '@/app/portfolio/[accountId]/[subnetId]/lib/context';
import { usePortfolioContext } from '@/app/portfolio/lib/context';
import { WalletPicker } from '@/app/portfolio/ui/wallet-picker';

export const HeaderRight = () => {
  const { walletAddress, setWalletAddress } = usePortfolioContext();
  const { subnetId } = usePortfolioAnalyticsContext();
  const router = useRouter();
  const handleSetWalletAddress = (address: string) => {
    setWalletAddress(address);
    router.push(`/portfolio/${address}/${subnetId}`);
  };
  return (
    <div className='flex w-full sm:w-1/2'>
      <div className='w-full sm:relative sm:flex-grow'>
        <WalletPicker
          allowAccountAdd={false}
          allowAccountDelete={false}
          setWalletAddress={handleSetWalletAddress}
          walletAddressInput={walletAddress}
        />
      </div>
    </div>
  );
};

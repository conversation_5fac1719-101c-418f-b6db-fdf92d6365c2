import { Fragment, memo, type Dispatch, type SetStateAction } from 'react';
import type {
  PaginationState,
  ColumnDef,
  Table as TanstackTable,
  Row,
} from '@tanstack/react-table';
import {
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
  flexRender,
} from '@tanstack/react-table';
import {
  TablePagination,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Card,
  Text,
} from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { useColumns } from './config';
import type { StakingEventViewDto } from '@/app/portfolio/[accountId]/[subnetId]/lib/types';

type StakingEventTableProps = {
  data: StakingEventViewDto[];
  pagination: PaginationState;
  setPagination: Dispatch<SetStateAction<PaginationState>>;
  total: number;
};

const StakingEventTableUi = ({
  data,
  pagination,
  setPagination,
  total,
}: StakingEventTableProps) => {
  const columns = useColumns();
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onPaginationChange: setPagination,
    manualPagination: true,
    rowCount: total,
    state: {
      columnVisibility: {
        month: false,
      },
      pagination,
    },
    debugTable: true,
  });

  return (
    <Card className='max-w-screen-2xl'>
      <div className='flex flex-col gap-4'>
        <TablePagination table={table} total={total} />
        <DataTable columns={columns} table={table} />
      </div>
    </Card>
  );
};

export const StakingEventTable = memo(StakingEventTableUi);

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  table: TanstackTable<TData>;
  hasBorder?: boolean;
  hasHeader?: boolean;
  tableClassName?: string;
  rowClassName?: (row: Row<TData>) => string;
}

function DataTable<TData, TValue>({
  table,
  columns,
  hasBorder = true,
  hasHeader = true,
  rowClassName,
  tableClassName,
}: DataTableProps<TData, TValue>) {
  return (
    <div className={cn('rounded-md', hasBorder && 'border')}>
      <Table className={tableClassName}>
        {hasHeader ? (
          <TableHeader hasBorder={hasBorder}>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} hasBorder={hasBorder}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
        ) : null}
        <TableBody>
          {table.getRowModel().rows.length ? (
            table.getRowModel().rows.map((row, index, rows) => {
              // Check if this is the first row of a group or the first row overall
              const isGroupStart =
                index === 0 ||
                row.getValue('month') !== rows[index - 1]?.getValue('month');

              return (
                <Fragment key={row.id}>
                  {/* Render group header if it's the start of a group */}
                  {isGroupStart ? (
                    <TableRow className='bg-muted/10'>
                      <TableCell
                        colSpan={columns.length}
                        className='p-2 font-medium'
                      >
                        <Text level='labelHeaderExtraSmall'>
                          {row.getValue('month')}
                        </Text>
                      </TableCell>
                    </TableRow>
                  ) : null}
                  {/* Render the actual data row */}
                  <TableRow
                    key={row.id}
                    hasBorder={hasBorder}
                    data-state={row.getIsSelected() && 'selected'}
                    className={cn(rowClassName?.(row), 'hover:bg-[#323232]')}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id} className='p-1 align-top sm:p-2'>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                </Fragment>
              );
            })
          ) : (
            <TableRow>
              <TableCell colSpan={columns.length} className='h-24 text-center'>
                No results.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}

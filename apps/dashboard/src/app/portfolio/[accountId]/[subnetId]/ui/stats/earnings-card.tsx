import { memo } from 'react';
import { BigNumber } from 'bignumber.js';
import {
  BigSmallValueDisplay,
  TaoOrAlphaValueDisplay,
} from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { usePortfolioAnalyticsContext } from '@/app/portfolio/[accountId]/[subnetId]/lib/context';
import { useSubnetDataContext } from '@/app/portfolio/[accountId]/[subnetId]/lib/subnet-data-context';
import { usePortfolioContext } from '@/app/portfolio/lib/context';
import { getEstimateAprFromValueAndRange } from '@/app/portfolio/lib/hooks';
import { EmptyCellWithTooltip } from '@/app/portfolio/ui/shared';
import { PortfolioStatCard } from '@/app/portfolio/ui/stats/portfolio-stats-card';

export const EarningsCard = memo(function EarningsCard() {
  const {
    dateRangeSelected,
    currency: { isUsd },
  } = usePortfolioContext();
  const { isLoading, normalisedDataForSubnet } = useSubnetDataContext();
  const { symbol } = usePortfolioAnalyticsContext();

  const {
    earningsAlphaTotal,
    earningsTaoTotal,
    earningsUsdTotal,
    balanceAsTaoTotal,
    balanceAsUsdTotal,
  } = normalisedDataForSubnet.reduce(
    (acc, curr) => {
      return {
        earningsAlphaTotal: acc.earningsAlphaTotal.plus(curr.earnedAlpha),
        earningsTaoTotal: acc.earningsTaoTotal.plus(curr.earnedTao),
        earningsUsdTotal: acc.earningsUsdTotal.plus(curr.earnedUsd),
        balanceAsTaoTotal: acc.balanceAsTaoTotal.plus(curr.balanceAsTao),
        balanceAsUsdTotal: acc.balanceAsUsdTotal.plus(curr.balanceAsUsd),
      };
    },
    {
      earningsAlphaTotal: new BigNumber(0),
      earningsTaoTotal: new BigNumber(0),
      earningsUsdTotal: new BigNumber(0),
      balanceAsTaoTotal: new BigNumber(0),
      balanceAsUsdTotal: new BigNumber(0),
    }
  );
  const hasTransactionsWithinTimeframe = normalisedDataForSubnet.some(
    (d) => d.hasTransactionsWithinTimeframe
  );
  const alphaValue = earningsAlphaTotal.toNumber();
  const taoValue = earningsTaoTotal.toNumber();
  const dollarValue = earningsUsdTotal.toNumber();
  const earningsPercentage = balanceAsTaoTotal.gt(0)
    ? BigNumber(isUsd ? earningsUsdTotal : earningsTaoTotal)
        .dividedBy(isUsd ? balanceAsUsdTotal : balanceAsTaoTotal)
        .multipliedBy(100)
        .toNumber()
    : 0;
  const apy = getEstimateAprFromValueAndRange(
    earningsPercentage,
    dateRangeSelected
  );

  return (
    <PortfolioStatCard
      isLoading={isLoading}
      title='Earnings'
      titleRight='Earnings %'
      value={
        <TaoOrAlphaValueDisplay
          value={alphaValue}
          symbol={symbol}
          valueTextLevel='headerLarge'
          valueFormattingOptions={{
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }}
          valueClassName='font-normal'
          areDecimalsSmall
        />
      }
      footerValue={
        <TaoOrAlphaValueDisplay
          value={isUsd ? dollarValue : taoValue}
          symbol={isUsd ? '$' : undefined}
          iconClassName='text-sm'
          valueTextLevel='headerMedium'
          valueFormattingOptions={{
            minimumFractionDigits: 2,
            maximumFractionDigits: isUsd ? 2 : 5,
          }}
          valueClassName='text-2xl font-light text-white/60'
          areDecimalsSmall
        />
      }
      rightValue={
        <div
          className={cn(
            'flex flex-col items-end gap-2',
            hasTransactionsWithinTimeframe && 'w-32'
          )}
        >
          {hasTransactionsWithinTimeframe ? (
            <>
              <EmptyCellWithTooltip />
              <EmptyCellWithTooltip />
            </>
          ) : (
            <>
              <BigSmallValueDisplay
                textLevel='headerMedium'
                value={earningsPercentage}
                valueFormattingOptions={{
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 3,
                }}
                areDecimalsSmall
                suffix='%'
              />
              <BigSmallValueDisplay
                textLevel='headerMedium'
                value={apy}
                valueFormattingOptions={{
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 3,
                }}
                areDecimalsSmall
                suffix='% APY'
              />
            </>
          )}
        </div>
      }
    />
  );
});

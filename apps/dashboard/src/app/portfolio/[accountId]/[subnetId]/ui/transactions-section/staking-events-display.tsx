import { memo, useMemo, type Dispatch, type SetStateAction } from 'react';
import type { PaginationState } from '@tanstack/react-table';
import { BigNumber } from 'bignumber.js';
import { format } from 'date-fns';
import { useWindowSize } from 'usehooks-ts';
import type { Delegate } from '@repo/types/website-api-types';
import { getHumanValueFromChainValue } from '@repo/ui/lib';
import { StakingEventsCards } from './staking-events-cards';
import { StakingEventTable } from './staking-events-table';
import { usePortfolioAnalyticsContext } from '@/app/portfolio/[accountId]/[subnetId]/lib/context';
import type { StakingEventViewDto } from '@/app/portfolio/[accountId]/[subnetId]/lib/types';
import { useValidatorLookup } from '@/lib/hooks/global-hooks';
import { useTaoToDollarConverter } from '@/lib/hooks/price-hooks';
import { useTaoToAlphaConverterWithApiData } from '@/lib/utils/conversion/conversion-utils';

type DelegationListProps = {
  delegationEvents: Delegate[];
  total: number;
  pagination: PaginationState;
  setPagination: Dispatch<SetStateAction<PaginationState>>;
};

const getAction = (e: Delegate) => {
  if (e.is_transfer) {
    return 'Transfer';
  }
  if (e.action === 'DELEGATE') {
    return 'Buy';
  }
  if (e.action === 'UNDELEGATE') {
    return 'Sell';
  }

  return '';
};

const StakingEventsDisplayUi = ({
  delegationEvents,
  pagination,
  setPagination,
  total,
}: DelegationListProps) => {
  const windowSize = useWindowSize();

  const { symbol } = usePortfolioAnalyticsContext();
  const { getValidatorName } = useValidatorLookup();
  const { convertAlphaToTao, chainConvertFormat } =
    useTaoToAlphaConverterWithApiData();
  const { getDollarValue } = useTaoToDollarConverter();

  const data = useMemo(
    () =>
      delegationEvents.map<StakingEventViewDto>((e) => {
        const valueInUsd = BigNumber(e.usd ?? 0).toNumber();
        const priceInUsd = BigNumber(e.alpha_price_in_usd ?? 0).toNumber();
        const priceInTao = BigNumber(e.alpha_price_in_tao ?? 0).toNumber();
        const alphaAmount = getHumanValueFromChainValue(
          BigNumber(e.alpha ?? 0).toNumber()
        );
        const valueInTao = getHumanValueFromChainValue(
          BigNumber(e.amount).toNumber()
        );
        const currentAlphaValueInTao =
          e.netuid !== null
            ? convertAlphaToTao(
                e.netuid,
                e.alpha ?? 0,
                chainConvertFormat.cToH
              ).toNumber()
            : 0;
        const currentAlphaValueInUsd = getDollarValue(currentAlphaValueInTao);

        return {
          month: format(new Date(e.timestamp), 'MMM yyyy'),
          action: getAction(e),
          timestamp: e.timestamp,
          validatorName: getValidatorName(e.delegate.ss58),
          validatorHotkey: e.delegate.ss58,
          priceInTao,
          priceInUsd,
          alphaAmount,
          valueInTao,
          valueInUsd,
          currentAlphaValueInTao,
          currentAlphaValueInUsd,
          returnsInTao:
            valueInTao !== 0
              ? ((currentAlphaValueInTao - valueInTao) / valueInTao) * 100
              : 0,
          returnsInUsd:
            valueInUsd !== 0
              ? ((currentAlphaValueInUsd - valueInUsd) / valueInUsd) * 100
              : 0,
          subnetSymbol: symbol,
        };
      }),
    [
      convertAlphaToTao,
      delegationEvents,
      chainConvertFormat.cToH,
      getDollarValue,
      getValidatorName,
      symbol,
    ]
  );

  if (windowSize.width < 640) {
    return <StakingEventsCards data={data} />;
  }

  return (
    <StakingEventTable
      data={data}
      pagination={pagination}
      setPagination={setPagination}
      total={total}
    />
  );
};

export const StakingEventsDisplay = memo(StakingEventsDisplayUi);

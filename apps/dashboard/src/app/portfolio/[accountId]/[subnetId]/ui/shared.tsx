import { CgArrowBottomLeft, CgArrowTopRight } from 'react-icons/cg';
import { Badge } from '@repo/ui/components';
import { formatNumber } from '@/lib/utils';

export const ChangeBadge = ({ val }: { val: number }) => {
  return (
    <Badge variant={val > 0 ? 'green3' : 'red1'}>
      {val > 0 ? (
        <CgArrowTopRight className='mr-1 inline text-xs' />
      ) : (
        <CgArrowBottomLeft className='mr-1 inline text-xs' />
      )}
      {formatNumber(val < 0 ? val * -1 : val, {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      })}
      %
    </Badge>
  );
};

export const ActionBadge = ({ action }: { action: string }) => {
  return (
    <Badge
      variant={
        action === 'Transfer'
          ? 'yellow1'
          : action === 'Buy'
            ? 'green4'
            : action === 'Sell'
              ? 'red2'
              : 'default'
      }
    >
      {action}
    </Badge>
  );
};

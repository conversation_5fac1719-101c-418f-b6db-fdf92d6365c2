import { useMemo } from 'react';
import { BigNumber } from 'bignumber.js';
import {
  Card,
  Skeleton,
  TaoOrAlphaValueDisplay,
  Text,
} from '@repo/ui/components';
import { getHumanValueFromChainValue } from '@repo/ui/lib';
import { GainsCardChart } from './chart';
import { useSubnetDataContext } from '@/app/portfolio/[accountId]/[subnetId]/lib/subnet-data-context';
import { usePortfolioContext } from '@/app/portfolio/lib/context';
import { PortfolioStatsCardHeader } from '@/app/portfolio/ui/stats/portfolio-stats-card-header';

export const GainsDonutCard = () => {
  const { currencySelected } = usePortfolioContext();
  const { isLoading, isSuccess, rawDataForSubnet } = useSubnetDataContext();

  const normalisedData = useMemo(() => {
    return rawDataForSubnet.map((d) => {
      return {
        realisedProfitTao: getHumanValueFromChainValue(d.realised_profit_tao),
        realisedProfitUsd: BigNumber(d.realised_profit_usd).toNumber(),
        unrealisedProfitTao: getHumanValueFromChainValue(
          d.unrealised_profit_tao
        ),
        unrealisedProfitUsd: BigNumber(d.unrealised_profit_usd).toNumber(),
      };
    });
  }, [rawDataForSubnet]);

  if (isLoading) {
    return (
      <Card contentContainerClassName='h-full'>
        <div className='flex h-full flex-row justify-between'>
          <div className='flex h-full flex-1 flex-col gap-6'>
            <div>
              <PortfolioStatsCardHeader title='Gains' />
            </div>
            <div className='flex w-full flex-col gap-2'>
              <Skeleton className='h-6 w-full' />
              <Skeleton className='h-6 w-full' />
              <Skeleton className='h-6 w-full' />
            </div>
          </div>
          <div className='flex w-1/2 flex-row items-center justify-end'>
            <Skeleton className='h-28 w-28 rounded-full' />
          </div>
        </div>
      </Card>
    );
  }

  if (!isSuccess) {
    return null;
  }

  const gains = normalisedData.reduce(
    (acc, curr) => {
      return {
        realisedProfitTao: acc.realisedProfitTao + curr.realisedProfitTao,
        realisedProfitUsd: acc.realisedProfitUsd + curr.realisedProfitUsd,
        unrealisedProfitTao: acc.unrealisedProfitTao + curr.unrealisedProfitTao,
        unrealisedProfitUsd: acc.unrealisedProfitUsd + curr.unrealisedProfitUsd,
      };
    },
    {
      realisedProfitTao: 0,
      realisedProfitUsd: 0,
      unrealisedProfitTao: 0,
      unrealisedProfitUsd: 0,
    }
  );

  const dataForChart = [
    {
      label: 'Realised',
      value:
        currencySelected === 'TAO'
          ? gains.realisedProfitTao
          : gains.realisedProfitUsd,
      colour: 'green',
    },
    {
      label: 'Unrealised',
      value:
        currencySelected === 'TAO'
          ? gains.unrealisedProfitTao
          : gains.unrealisedProfitUsd,
      colour: 'red',
    },
  ];

  return (
    <Card contentContainerClassName='min-h-[122px] h-full'>
      <div className='flex h-full flex-row justify-between'>
        <div className='flex h-full flex-col gap-6'>
          <div>
            <PortfolioStatsCardHeader title='Gains' />
          </div>
          <div>
            <table>
              <tbody>
                {dataForChart.map(({ label, value, colour }) => (
                  <tr key={label}>
                    <td className='pr-3'>
                      <div className='flex items-center'>
                        <div
                          className={`mr-2 h-2 w-2 rounded-full ${
                            colour === 'green' ? 'bg-accent-1' : 'bg-accent-2'
                          }`}
                        />
                        <Text level='bodyMedium' className='text-white/60'>
                          {label}
                        </Text>
                      </div>
                    </td>
                    <td>
                      <TaoOrAlphaValueDisplay
                        value={value}
                        symbol={currencySelected === 'USD' ? '$' : undefined}
                        valueTextLevel='bodyMedium'
                        colourClassName='text-white'
                        iconClassName='text-xs'
                        areDecimalsSmall={false}
                        valueFormattingOptions={{
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        }}
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
        <div className='flex w-1/2 flex-row items-end'>
          <GainsCardChart width={150} height={150} data={dataForChart} />
        </div>
      </div>
    </Card>
  );
};

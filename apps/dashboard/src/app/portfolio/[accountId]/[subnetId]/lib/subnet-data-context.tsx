'use client';

import type { ReactNode } from 'react';
import { createContext, useContext, useMemo } from 'react';
import type { StakeBalance } from '@repo/types/website-api-types';
import { useBalancesForSubnetQuery, useNormalisedDataForSubnet } from './hooks';
import type { NormalisedDelegationForSubnet } from './types';

interface SubnetDataContextValue {
  isLoading: boolean;
  isSuccess: boolean;
  rawDataForSubnet: StakeBalance[];
  normalisedDataForSubnet: NormalisedDelegationForSubnet[];
}

const SubnetDataContext = createContext<SubnetDataContextValue | undefined>(
  undefined
);

interface SubnetDataContextProviderProps {
  children: ReactNode;
}

export const SubnetDataContextProvider = ({
  children,
}: SubnetDataContextProviderProps) => {
  const {
    isLoading,
    isSuccess,
    rawDataForSubnet: rawData,
  } = useBalancesForSubnetQuery();
  const rawDataForSubnet = useMemo(() => rawData ?? [], [rawData]);
  const { getNormalisedData } = useNormalisedDataForSubnet(rawDataForSubnet);
  const normalisedDataForSubnet = useMemo(
    () => getNormalisedData(),
    [getNormalisedData]
  );

  const value = useMemo<SubnetDataContextValue>(() => {
    return {
      isLoading,
      isSuccess,
      rawDataForSubnet,
      normalisedDataForSubnet,
    };
  }, [isLoading, isSuccess, rawDataForSubnet, normalisedDataForSubnet]);

  return (
    <SubnetDataContext.Provider value={value}>
      {children}
    </SubnetDataContext.Provider>
  );
};

export const useSubnetDataContext = () => {
  const context = useContext(SubnetDataContext);
  if (context === undefined) {
    throw new Error(
      'useSubnetDataContext must be used within a SubnetDataContextProvider'
    );
  }
  return context;
};

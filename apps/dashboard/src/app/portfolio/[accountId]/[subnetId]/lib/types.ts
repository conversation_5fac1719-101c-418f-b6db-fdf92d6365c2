import type { NormalisedDelegation } from '@/app/portfolio/lib/types';

export type StakingEventViewDto = {
  month: string;
  action: string;
  timestamp: string;
  validatorName: string;
  validatorHotkey: string;
  priceInTao: number;
  priceInUsd: number;
  alphaAmount: number;
  valueInTao: number;
  valueInUsd: number;
  currentAlphaValueInTao: number;
  currentAlphaValueInUsd: number;
  returnsInTao: number;
  returnsInUsd: number;
  subnetSymbol: string;
};

export type NormalisedDelegationForSubnet = Omit<
  NormalisedDelegation,
  'netuid' | 'subnetName'
>;

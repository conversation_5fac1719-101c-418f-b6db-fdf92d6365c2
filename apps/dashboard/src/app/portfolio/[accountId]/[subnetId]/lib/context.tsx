'use client';

import type { ReactNode } from 'react';
import { createContext, useContext, useMemo } from 'react';
import type {
  DtaoSubnet,
  // DtaoSubnet,
  EnrichedSubnet,
} from '@repo/types/website-api-types';
import { useSubnetLookup } from '@/lib/hooks/global-hooks';

interface PortfolioAnalyticsContextValue {
  subnetId: number;
  isLoading: boolean;
  subnet: EnrichedSubnet | undefined;
  subnetPool: DtaoSubnet | undefined;
  symbol: string;
  subnetName: string;
}

const PortfolioAnalyticsContext = createContext<
  PortfolioAnalyticsContextValue | undefined
>(undefined);

interface PortfolioAnalyticsContextProviderProps {
  children: ReactNode;
  subnetId: number;
}

export const PortfolioAnalyticsContextProvider = ({
  children,
  subnetId,
}: PortfolioAnalyticsContextProviderProps) => {
  const subnetLookup = useSubnetLookup();
  const subnet = subnetLookup.getSubnet(subnetId);
  const subnetPool = subnetLookup.getSubnetPool(subnetId);

  const symbol = useMemo(
    () => subnetLookup.getSubnetSymbol(subnetId),
    [subnetLookup, subnetId]
  );
  const subnetName = useMemo(
    () => subnetLookup.getSubnetName(subnetId),
    [subnetLookup, subnetId]
  );

  const value = useMemo<PortfolioAnalyticsContextValue>(() => {
    return {
      subnetId,
      isLoading: subnetLookup.isLoading,
      subnet,
      subnetPool,
      symbol,
      subnetName,
    };
  }, [
    subnetId,
    subnetLookup.isLoading,
    subnet,
    subnetPool,
    symbol,
    subnetName,
  ]);

  return (
    <PortfolioAnalyticsContext.Provider value={value}>
      {children}
    </PortfolioAnalyticsContext.Provider>
  );
};

export const usePortfolioAnalyticsContext = () => {
  const context = useContext(PortfolioAnalyticsContext);
  if (context === undefined) {
    throw new Error(
      'usePortfolioAnalyticsContext must be used within a PortfolioAnalyticsContextProvider'
    );
  }
  return context;
};

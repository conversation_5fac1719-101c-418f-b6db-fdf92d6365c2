import { useCallback, useMemo } from 'react';
import type { StakeBalance } from '@repo/types/website-api-types';
import { usePortfolioAnalyticsContext } from './context';
import type { NormalisedDelegationForSubnet } from './types';
import { usePortfolioContext } from '@/app/portfolio/lib/context';
import { useDataNormaliser } from '@/app/portfolio/lib/hooks';
import { useStakeBalanceLatestQuery } from '@/app/portfolio/lib/query-hooks';
import { getNumberOfDaysFromDateRange } from '@/lib/utils';

export const useBalancesForSubnetQuery = () => {
  const { subnetId } = usePortfolioAnalyticsContext();
  const { walletAddress, dateRangeSelected } = usePortfolioContext();
  const { data, isSuccess, isLoading, error } = useStakeBalanceLatestQuery(
    walletAddress,
    getNumberOfDaysFromDateRange(dateRangeSelected)
  );

  const rawDataForSubnet = useMemo(() => {
    return data?.data.filter((d) => d.netuid === subnetId);
  }, [data?.data, subnetId]);

  return { rawDataForSubnet, isSuccess, isLoading, error };
};

export const useNormalisedDataForSubnet = (
  rawDataForSubnet: StakeBalance[]
) => {
  const { symbol } = usePortfolioAnalyticsContext();
  const { normaliseData } = useDataNormaliser();

  const getNormalisedData = useCallback((): NormalisedDelegationForSubnet[] => {
    const getData = rawDataForSubnet.length > 0 ? rawDataForSubnet : [];
    const delegations = getData.map((delegation) => {
      return {
        subnetSymbol: symbol,
        ...normaliseData(delegation),
      };
    });

    return delegations;
  }, [rawDataForSubnet, symbol, normaliseData]);

  return {
    getNormalisedData,
  };
};

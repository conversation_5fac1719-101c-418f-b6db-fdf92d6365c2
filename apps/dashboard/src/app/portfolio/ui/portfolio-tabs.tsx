import { useState } from 'react';
import { Button } from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { CurrentBalances } from './current-balances';
import { DateRangeAndCurrencySelector } from './shared';
import { Delegation } from './staking';

type Tab = 'holdings' | 'transactions';

const tabs: { name: string; tab: Tab }[] = [
  { name: 'Holdings', tab: 'holdings' },
  { name: 'Transactions', tab: 'transactions' },
];

export const PortfolioTabs = () => {
  const [currentTab, setCurrentTab] = useState<Tab>('holdings');

  return (
    <>
      <div className='flex flex-col gap-6 sm:flex-row sm:justify-between'>
        <div className='flex gap-0 self-start sm:gap-2'>
          {tabs.map((item) => (
            <Button
              variant='link'
              key={item.tab}
              className={cn(
                'text-md flex cursor-pointer items-center gap-3 rounded-xl px-3 py-2  hover:no-underline sm:px-3 md:text-base md:leading-4',
                currentTab === item.tab
                  ? 'bg-white/10 text-white'
                  : 'border-transparent text-neutral-500 hover:text-white '
              )}
              onClick={() => {
                setCurrentTab(item.tab);
              }}
            >
              {item.name}
            </Button>
          ))}
        </div>
        <DateRangeAndCurrencySelector />
      </div>
      {currentTab === 'holdings' && <CurrentBalances />}
      {currentTab === 'transactions' && <Delegation />}
    </>
  );
};

import { getHumanValueFromChainValue } from '@repo/ui/lib';
import { PortfolioSummaryHeader } from './shared';
import { usePortfolioContext } from '@/app/portfolio/lib/context';
import { useTaoToDollarConverter } from '@/lib/hooks/price-hooks';

export const Header = () => {
  const { accountLatestQuery } = usePortfolioContext();
  const { getDollarValue } = useTaoToDollarConverter();

  if (!accountLatestQuery.isSuccess) {
    return null;
  }

  const taoValue = getHumanValueFromChainValue(
    accountLatestQuery.data.data[0]?.balance_total || 0
  );
  const taoValue24HrsAgo = getHumanValueFromChainValue(
    accountLatestQuery.data.data[0]?.balance_total_24hr_ago || 0
  );
  const dollarValue = getDollarValue(taoValue);

  return (
    <PortfolioSummaryHeader
      taoOrAlphaValue={taoValue}
      taoOrAlphaValue24HrsAgo={taoValue24HrsAgo}
      bottomValue={dollarValue}
      isLoading={accountLatestQuery.isFetching}
    />
  );
};

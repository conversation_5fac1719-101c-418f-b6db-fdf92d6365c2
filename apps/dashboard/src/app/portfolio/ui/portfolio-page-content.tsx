'use client';

import { memo } from 'react';
import { AlertError, Skeleton } from '@repo/ui/components';
import { EarningsChart } from './earnings-chart';
import { Header } from './header';
import { PortfolioConfiguration } from './portfolio-configuration';
import { PortfolioTabs } from './portfolio-tabs';
import { DateRangeAndCurrencySelector } from './shared';
import { PortfolioStats } from './stats';
import { usePortfolioContext } from '@/app/portfolio/lib/context';

export const PortfolioPageContent = memo(function PortfolioPageContent() {
  const { accountData, accountLatestQuery } = usePortfolioContext();

  return (
    <>
      <div className='flex flex-col-reverse gap-8 sm:flex-row'>
        {accountData ? (
          <div className='flex-1'>
            <Header />
          </div>
        ) : null}

        <div className='flex flex-1 flex-col items-end gap-4'>
          <PortfolioConfiguration />
          {accountData ? (
            <div className='max-sm:hidden'>
              <DateRangeAndCurrencySelector />
            </div>
          ) : null}
        </div>
      </div>

      {accountLatestQuery.isLoading ? (
        <div className='flex flex-col gap-4'>
          <Skeleton className='h-16 w-full' />
          <Skeleton className='h-16 w-full' />
          <Skeleton className='h-16 w-full' />
        </div>
      ) : null}

      {accountLatestQuery.isError ? (
        <AlertError description='Failed to load account data. Please try again later.' />
      ) : null}

      {accountData ? (
        <>
          <div className='hidden max-sm:block'>
            <DateRangeAndCurrencySelector />
          </div>

          <div className='flex flex-col gap-6 sm:flex-row'>
            <div className='flex-shrink-0 sm:w-[350px]'>
              <PortfolioStats />
            </div>
            <div className='flex w-full min-w-[300px] overflow-hidden'>
              <EarningsChart />
            </div>
          </div>
          <PortfolioTabs />
        </>
      ) : null}
    </>
  );
});

'use client';

import type { ReactNode } from 'react';
import {
  createContext,
  useContext,
  useMemo,
  useState,
  useCallback,
} from 'react';
import { BigNumber } from 'bignumber.js';
import { useLocalStorage } from 'usehooks-ts';
import { z } from 'zod';
import { notify } from '@repo/ui/lib';
import type { Mode, TokenType } from '@/app/portfolio/lib/types';
import { useAccountContext } from '@/contexts/chain-and-wallet/account-context';
import { useChainAndWalletContext } from '@/contexts/chain-and-wallet/chain-and-wallet-context';
import {
  useSlippageCalculator,
  useTaoToAlphaConverterWithChainData,
} from '@/contexts/chain-and-wallet/hooks';
import { roundDown } from '@/lib/utils/number-utils';

interface BuySellFormContextProviderProps {
  children: ReactNode;
  subnetId: number;
}

type BuySellFormContextValue = {
  subnetId: number;
  isConnected: boolean;
  handleInputChange: (value: string, field: TokenType, mode: Mode) => void;
  resetInput: (mode: Mode) => void;
  getInputState: (mode: Mode) => InputState;
  getFieldValues: (mode: Mode) => {
    taoValue: string;
    alphaValue: string;
    slippage: string;
  };
  availableTao: number;
  availableAlpha: number;
  getDelegatedAlphaForHotkey: (hotkey: string) => number;
  maxSlippage: number;
  setMaxSlippage: (slippage: number) => void;
  price: number;
};

const BuySellFormContext = createContext<BuySellFormContextValue | undefined>(
  undefined
);

type InputState = {
  value: string;
  activeField: TokenType | null;
  mode: Mode;
};

const numberSchema = z.string().refine((val) => {
  if (val === '') return true;
  return !Number.isNaN(Number(val));
}, 'Must be a number');

export const BuySellFormContextProvider = ({
  children,
  subnetId,
}: BuySellFormContextProviderProps) => {
  const { state, isConnectedToChain } = useChainAndWalletContext();
  const {
    state: { delegateBalances },
  } = useAccountContext();

  const [buyInputState, setBuyInputState] = useState<InputState>({
    value: '',
    activeField: null,
    mode: 'buy',
  });

  const [sellInputState, setSellInputState] = useState<InputState>({
    value: '',
    activeField: null,
    mode: 'sell',
  });

  const {
    state: { subnetAmountsMap, availableBalance },
  } = useAccountContext();

  const { slippageCalculator } = useSlippageCalculator();
  const price = subnetAmountsMap[subnetId].price;
  const isConnected = Boolean(state.currentAccount);

  const [maxSlippage, setMaxSlippage] = useLocalStorage('max-slippage', 0.5);

  const { convertTaoToAlpha, chainConvertFormat } =
    useTaoToAlphaConverterWithChainData();
  const availableTao = BigNumber(availableBalance).toNumber();
  const availableAlpha = convertTaoToAlpha(
    Number(subnetId),
    availableBalance,
    chainConvertFormat.hToH
  ).toNumber();

  const getDelegatedAlphaForHotkey = useCallback(
    (hotkey: string) => {
      return (
        delegateBalances.find(
          (d) => d.netuid === subnetId && d.hotkey === hotkey
        )?.alphaAmount ?? 0
      );
    },
    [delegateBalances, subnetId]
  );

  const value = useMemo<BuySellFormContextValue>(() => {
    const handleInteraction = (action: () => void) => {
      if (!isConnectedToChain) {
        notify.error('Please connect your wallet');
        return;
      }
      action();
    };

    const handleInputChange = (
      inputValue: string,
      field: TokenType,
      mode: Mode
    ) => {
      handleInteraction(() => {
        const setInputState =
          mode === 'buy' ? setBuyInputState : setSellInputState;
        setInputState({
          value: inputValue,
          activeField: field,
          mode,
        });
      });
    };

    const resetInput = (mode: Mode) => {
      const setInputState =
        mode === 'buy' ? setBuyInputState : setSellInputState;
      setInputState({
        value: '',
        activeField: null,
        mode,
      });
    };

    const getInputState = (mode: Mode) => {
      return mode === 'buy' ? buyInputState : sellInputState;
    };

    const getFieldValues = (mode: Mode) => {
      const inputState = getInputState(mode);
      const defaultValues = {
        taoValue: '',
        alphaValue: '',
        slippage: '0',
      };

      if (!inputState.activeField || !inputState.value) {
        return defaultValues;
      }

      // For the active field, return the raw input value
      const result = numberSchema.safeParse(inputState.value);
      if (!result.success) {
        return {
          taoValue: inputState.activeField === 'TAO' ? inputState.value : '0',
          alphaValue:
            inputState.activeField === 'ALPHA' ? inputState.value : '0',
          slippage: '0',
        };
      }

      const activeFieldValue = BigNumber(inputState.value);
      const convertedValue =
        inputState.activeField === 'TAO'
          ? price > 0
            ? BigNumber(roundDown(activeFieldValue.div(price), 5))
            : BigNumber(0) // TAO to ALPHA
          : price > 0
            ? BigNumber(roundDown(activeFieldValue.times(price), 5))
            : BigNumber(0); // ALPHA to TAO

      const taoValue =
        inputState.activeField === 'TAO' ? activeFieldValue : convertedValue;
      const slippageCalcResult = slippageCalculator(
        subnetId,
        taoValue.toNumber()
      );

      return {
        taoValue:
          inputState.activeField === 'TAO'
            ? inputState.value
            : convertedValue.toString(),
        alphaValue:
          inputState.activeField === 'ALPHA'
            ? inputState.value
            : convertedValue.toString(),
        slippage: slippageCalcResult?.slippage ?? '0',
      };
    };

    return {
      subnetId,
      isConnected,
      handleInputChange,
      resetInput,
      getInputState,
      getFieldValues,
      availableTao,
      availableAlpha,
      getDelegatedAlphaForHotkey,
      maxSlippage,
      setMaxSlippage,
      price,
    };
  }, [
    availableAlpha,
    availableTao,
    buyInputState,
    getDelegatedAlphaForHotkey,
    isConnected,
    isConnectedToChain,
    maxSlippage,
    price,
    sellInputState,
    setMaxSlippage,
    slippageCalculator,
    subnetId,
  ]);

  return (
    <BuySellFormContext.Provider value={value}>
      {children}
    </BuySellFormContext.Provider>
  );
};

export const useBuySellFormContext = () => {
  const context = useContext(BuySellFormContext);
  if (context === undefined) {
    throw new Error(
      'useBuySellFormContext must be used within a BuySellFormContextProvider'
    );
  }
  return context;
};

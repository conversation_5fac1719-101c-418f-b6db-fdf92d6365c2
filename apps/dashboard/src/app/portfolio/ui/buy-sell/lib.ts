import { notify } from '@repo/ui/lib';
import { useBuySellContext } from './buy-sell-context';
import type { Mode } from '@/app/portfolio/lib/types';
import { useBuySellFormContext } from '@/app/portfolio/ui/buy-sell/buy-sell-form-context';
import { useChainAndWalletContext } from '@/contexts/chain-and-wallet/chain-and-wallet-context';
import { useTransactionSubmitter } from '@/contexts/chain-and-wallet/use-transaction-submitter';
import { usePriceLimitCalculator } from '@/lib/hooks/staking-hooks';
import { useChainAmountConverter } from '@/lib/utils/conversion/conversion-utils';

export const useTradeLogic = () => {
  const { submitTx, isPending, txStatus } = useTransactionSubmitter();
  const { subnetForTx, setErrorMessage } = useBuySellContext();
  const { subnetId, getFieldValues } = useBuySellFormContext();
  const { convertAmount } = useChainAmountConverter();
  const { getAddStakePriceLimit, getRemoveStakePriceLimit } =
    usePriceLimitCalculator();

  const {
    state: { api },
  } = useChainAndWalletContext();

  const handleConfirmClick = async (mode: Mode, onSuccess: () => void) => {
    setErrorMessage(null);

    if (!api) {
      notify.error('Could not find connection');
      return;
    }

    if (!subnetForTx) {
      notify.error('Could not find transaction. Try again later.');
      return;
    }

    if (!subnetForTx.validatorHotkey) {
      notify.error('Please select or enter a validator hotkey');
      return;
    }

    try {
      const { taoValue, alphaValue, slippage } = getFieldValues(mode);

      const amountInChainFormat = (
        mode === 'buy'
          ? convertAmount(Number(taoValue))
          : convertAmount(Number(alphaValue))
      ).toNumber();

      const priceLimit =
        mode === 'buy'
          ? getAddStakePriceLimit(
              subnetForTx.netuid,
              Number(slippage)
            ).toNumber()
          : getRemoveStakePriceLimit(
              subnetForTx.netuid,
              Number(slippage)
            ).toNumber();

      const hotkey = subnetForTx.validatorHotkey;

      const tx =
        mode === 'buy'
          ? api.tx.subtensorModule.addStakeLimit(
              hotkey,
              subnetId,
              amountInChainFormat,
              priceLimit,
              false
            )
          : api.tx.subtensorModule.removeStakeLimit(
              hotkey,
              subnetId,
              amountInChainFormat,
              priceLimit,
              false
            );

      // Submit the transaction
      await submitTx(tx, {
        onSuccessCallback: onSuccess,
      });
    } catch (e) {
      const message =
        e instanceof Error ? e.message : 'An unknown error occurred';
      setErrorMessage(message);
    }
  };

  return {
    handleConfirmClick,
    isPending,
    txStatus,
  };
};

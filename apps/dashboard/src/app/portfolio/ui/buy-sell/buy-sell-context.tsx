'use client';

import type { ReactNode } from 'react';
import {
  createContext,
  useCallback,
  useContext,
  useMemo,
  useState,
} from 'react';
import type { SubnetForTxType } from '@/app/portfolio/lib/types';

interface BuySellContextValue {
  handleBuyClick: (netuid: number, validatorHotkey: string) => void;
  handleSellClick: (netuid: number, validatorHotkey: string) => void;
  subnetForTx: SubnetForTxType | null;
  resetSubnetForTx: () => void;
  updateValidatorHotkey: (validatorHotkey: string) => void;
  setErrorMessage: (errorMessage: string | null) => void;
  errorMessage: string | null;
}

const BuySellContext = createContext<BuySellContextValue | undefined>(
  undefined
);

interface BuySellContextProviderProps {
  children: ReactNode;
}

export const BuySellContextProvider = ({
  children,
}: BuySellContextProviderProps) => {
  const [subnetForTx, setSubnetForTx] = useState<SubnetForTxType | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const handleBuyClick = useCallback(
    (netuid: number, validatorHotkey: string) => {
      setSubnetForTx({ netuid, txType: 'buy', validatorHotkey });
    },
    []
  );

  const handleSellClick = useCallback(
    (netuid: number, validatorHotkey: string) => {
      setSubnetForTx({ netuid, txType: 'sell', validatorHotkey });
    },
    []
  );

  const resetSubnetForTx = () => {
    setSubnetForTx(null);
  };

  const value = useMemo<BuySellContextValue>(() => {
    const updateValidatorHotkey = (validatorHotkey: string) => {
      if (!subnetForTx) {
        return;
      }
      setSubnetForTx({ ...subnetForTx, validatorHotkey });
    };

    return {
      subnetForTx,
      handleBuyClick,
      handleSellClick,
      resetSubnetForTx,
      updateValidatorHotkey,
      setErrorMessage,
      errorMessage,
    };
  }, [errorMessage, handleBuyClick, handleSellClick, subnetForTx]);

  return (
    <BuySellContext.Provider value={value}>{children}</BuySellContext.Provider>
  );
};

export const useBuySellContext = () => {
  const context = useContext(BuySellContext);
  if (context === undefined) {
    throw new Error(
      'useBuySellContext must be used within a BuySellContextProvider'
    );
  }
  return context;
};

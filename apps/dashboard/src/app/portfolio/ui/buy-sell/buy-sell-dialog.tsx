import { useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { BigNumber } from 'bignumber.js';
import {
  BiCoinStack,
  BiDownArrowAlt,
  BiSave,
  BiUpArrowAlt,
  BiWallet,
  BiX,
} from 'react-icons/bi';
import { CgCreditCard } from 'react-icons/cg';
import { z } from 'zod';
import {
  AlertError,
  Button,
  Dialog,
  Flex,
  Input,
  Spinner,
  Text,
} from '@repo/ui/components';
import { cn, notify } from '@repo/ui/lib';
import { useBuySellContext } from './buy-sell-context';
import {
  BuySellFormContextProvider,
  useBuySellFormContext,
} from './buy-sell-form-context';
import { useTradeLogic } from './lib';
import { usePortfolioContext } from '@/app/portfolio/lib/context';
import { queryKeys } from '@/app/portfolio/lib/query-hooks';
import type { SubnetForTxType, TokenType } from '@/app/portfolio/lib/types';
import { AntiMevCombobox } from '@/components/anti-mev-combobox';
import { ValidatorPicker } from '@/components/validator-picker';
import { WalletPicker } from '@/components/wallet/wallet-picker';
import { useChainAndWalletContext } from '@/contexts/chain-and-wallet/chain-and-wallet-context';
import { useSubnetLookup } from '@/lib/hooks/global-hooks';
import { useMaxSlippage, useSlippageHelper } from '@/lib/hooks/staking-hooks';
import { formatNumber, TAO_CURRENCY } from '@/lib/utils';

export const BuySellDialog = () => {
  const { subnetForTx, resetSubnetForTx } = useBuySellContext();

  return (
    <Dialog
      HeaderIcon={subnetForTx?.txType === 'buy' ? BiDownArrowAlt : BiUpArrowAlt}
      headerIconClassName={
        subnetForTx?.txType === 'buy'
          ? 'bg-accent-1'
          : subnetForTx?.txType === 'sell'
            ? 'bg-accent-2'
            : ''
      }
      closeFn={resetSubnetForTx}
      isOpen={subnetForTx !== null}
      title={subnetForTx?.txType === 'buy' ? 'Buy' : 'Sell'}
    >
      {subnetForTx ? (
        <BuySellFormContextProvider subnetId={subnetForTx.netuid}>
          <BuySellForm subnetForTx={subnetForTx} />
        </BuySellFormContextProvider>
      ) : (
        <div className='flex justify-center py-4'>
          <Spinner size='lg' />
        </div>
      )}
    </Dialog>
  );
};

const BuySellForm = ({ subnetForTx }: { subnetForTx: SubnetForTxType }) => {
  const [showMaxSlippageSection, setShowMaxSlippageSection] = useState(false);
  const { maxSlippage, updateMaxSlippage } = useMaxSlippage();
  const [maxSlippageInput, setMaxSlippageInput] = useState(
    maxSlippage.toString()
  );
  const { resetSubnetForTx, updateValidatorHotkey, errorMessage } =
    useBuySellContext();
  const { handleConfirmClick, isPending, txStatus } = useTradeLogic();
  const { getSubnetName } = useSubnetLookup();
  const queryClient = useQueryClient();
  const { walletAddress } = usePortfolioContext();

  const { getFieldValues } = useBuySellFormContext();
  const { slippage } = getFieldValues(subnetForTx.txType);

  const isSlippageExceeded = BigNumber(slippage).gt(maxSlippage);
  const isButtonDisabled = isPending || isSlippageExceeded;

  const handleMaxSlippageUpdate = () => {
    try {
      const schema = z.number().min(0).max(100);
      const value = schema.parse(Number(maxSlippageInput));
      updateMaxSlippage(value);
      setShowMaxSlippageSection(false);
    } catch (err) {
      notify.error('Please enter a number between 0 and 100');
    }
  };

  const txStatusText = txStatus?.status ? txStatus.status : '';

  return (
    <div className='flex flex-col gap-8'>
      {txStatusText ? (
        <div className='flex flex-col items-center justify-center gap-2'>
          {txStatusText !== 'Error' ? (
            <>
              <Spinner size='md' />
              <Text as='h3' className='text-center text-[#00DBBC]' level='md'>
                {txStatusText}
              </Text>
            </>
          ) : null}

          {txStatus?.message ? (
            <AlertError description={txStatus.message} />
          ) : null}
        </div>
      ) : null}
      <div className='flex flex-col gap-4'>
        <div className='flex flex-1 flex-col gap-2'>
          <Text level='labelSmall' className='font-medium opacity-60'>
            Source Wallet
          </Text>
          <WalletPicker
            hideDisconnectButton
            className='bg-input-primary hover:bg-input-primary flex h-12 w-full items-center rounded-lg border-transparent text-white/60'
          />
        </div>
        <div className='flex flex-col gap-4 sm:flex-row sm:gap-2'>
          <div className='flex flex-1 flex-col gap-2'>
            <Text level='labelSmall' className='font-medium opacity-60'>
              Subnet
            </Text>
            <div className='bg-input-primary hover:bg-input-primary flex h-12 w-full items-center rounded-lg border-transparent'>
              <div className='flex flex-row items-center gap-2 px-4 py-2'>
                <Text className='font-mono text-sm text-white/60'>
                  {subnetForTx.netuid}:{' '}
                </Text>
                <Text className='text-sm text-white/60'>
                  {getSubnetName(subnetForTx.netuid)}
                </Text>
              </div>
            </div>
          </div>
          <div className='flex flex-1 flex-col gap-2'>
            <ValidatorPicker
              subnetId={subnetForTx.netuid}
              value={subnetForTx.validatorHotkey}
              onChange={(value) => {
                updateValidatorHotkey(value);
              }}
              disabled={isPending}
              labelLevel='labelSmall'
              labelClassName='font-medium opacity-60'
              comboboxClassName='bg-input-primary hover:bg-input-primary h-12 w-full border-transparent text-sm text-white/60'
            />
          </div>
        </div>

        <InputFields disabled={isPending} />
        <SlippageAndAvailableSection
          showSlippageSection={() => {
            setShowMaxSlippageSection(true);
          }}
        />

        {isSlippageExceeded ? (
          <AlertError
            className='animate-in fade-in-10 duration-300'
            alignItems='center'
            description={
              <div className='flex flex-row items-center gap-2'>
                <Text level='labelSmall' className='font-medium opacity-60'>
                  Slippage is too high. Your max is currently set to{' '}
                  {maxSlippage}%.
                </Text>
                <Button
                  variant='cta2'
                  size='sm'
                  onClick={() => {
                    setShowMaxSlippageSection(true);
                  }}
                >
                  Update Max Slippage?
                </Button>
              </div>
            }
          />
        ) : null}
      </div>

      {showMaxSlippageSection ? (
        <div className='animate-in fade-in-10 grid grid-cols-[auto_1fr] items-center gap-4'>
          <div className='w-fit'>
            <Text level='labelSmall' className='font-medium opacity-60'>
              Max Slippage
            </Text>
          </div>
          <Flex className='gap-2'>
            <input
              id='max-slippage'
              type='text'
              value={maxSlippageInput}
              onChange={(e) => {
                setMaxSlippageInput(e.target.value);
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleMaxSlippageUpdate();
                } else if (e.key === 'Escape') {
                  setShowMaxSlippageSection(false);
                }
              }}
              className='bg-input-primary text-label-secondary w-[100px] rounded-lg p-3 text-sm leading-4 focus:outline-none'
            />
            <div className='flex flex-row-reverse items-center gap-2 sm:flex-row'>
              <Button
                variant='cta2'
                size='sm'
                onClick={() => {
                  handleMaxSlippageUpdate();
                }}
              >
                <BiSave className='mr-2 size-4' />
                Update
              </Button>
              <Button
                variant='cta3'
                size='sm'
                onClick={() => {
                  setShowMaxSlippageSection(false);
                }}
              >
                <BiX className='mr-2 size-4' />
                Cancel
              </Button>
            </div>
          </Flex>
          <div className='w-fit'>
            <Text level='labelSmall' className='font-medium opacity-60'>
              Anti MEV
            </Text>
          </div>
          <div>
            <AntiMevCombobox />
          </div>
        </div>
      ) : null}

      {errorMessage ? <AlertError description={errorMessage} /> : null}

      <Button
        variant='cta2'
        size='lg'
        onClick={() => {
          void handleConfirmClick(subnetForTx.txType, () => {
            resetSubnetForTx();
            void queryClient.invalidateQueries({
              queryKey: [queryKeys.stakeBalance, walletAddress],
            });
          });
        }}
        disabled={isButtonDisabled}
      >
        {subnetForTx.txType === 'buy' ? (
          <>
            Buy <BiWallet className='ml-1 size-4' />
          </>
        ) : (
          <>
            Sell <BiCoinStack className='ml-1 size-4' />
          </>
        )}
      </Button>
    </div>
  );
};

const FieldSuffix = ({ type }: { type: TokenType }) => {
  const { subnetForTx } = useBuySellContext();
  const { getSubnetSymbol } = useSubnetLookup();

  return (
    <div className='opacity-60'>
      {type === 'TAO' ? (
        <span className='px-2 text-white'>{TAO_CURRENCY}</span>
      ) : subnetForTx ? (
        <span className='px-2 text-white'>
          {getSubnetSymbol(subnetForTx.netuid)}
        </span>
      ) : null}
    </div>
  );
};

const InputFields = ({ disabled = false }: { disabled?: boolean }) => {
  const { isConnectedToChain } = useChainAndWalletContext();
  const { handleInputChange, getFieldValues, getInputState } =
    useBuySellFormContext();
  const { subnetForTx } = useBuySellContext();

  if (!subnetForTx) {
    return null;
  }

  const inputState = getInputState(subnetForTx.txType);
  const { taoValue, alphaValue } = getFieldValues(subnetForTx.txType);
  return (
    <>
      <Input
        disabled={disabled}
        inputId='tao-input'
        IconLeft={CgCreditCard}
        placeholder='0.0000'
        className={cn(
          'rounded-lg border bg-[#2E2E2E]',
          !isConnectedToChain && 'opacity-50',
          inputState.activeField === 'TAO'
            ? 'border-[#00DBBC]'
            : 'border-[#262626]'
        )}
        inputClassName='text-sm leading-4 font-normal rounded-lg'
        rightComponent={<FieldSuffix type='TAO' />}
        value={taoValue}
        onChange={(e) => {
          handleInputChange(e.target.value, 'TAO', subnetForTx.txType);
        }}
      />
      <Input
        disabled={disabled}
        inputId='alpha-input'
        IconLeft={CgCreditCard}
        placeholder='0.0000'
        className={cn(
          'rounded-lg border bg-[#2E2E2E]',
          !isConnectedToChain && 'opacity-50',
          inputState.activeField === 'ALPHA'
            ? 'border-[#00DBBC]'
            : 'border-[#262626]'
        )}
        inputClassName='text-sm leading-4 font-normal rounded-lg'
        rightComponent={<FieldSuffix type='ALPHA' />}
        value={alphaValue}
        onChange={(e) => {
          handleInputChange(e.target.value, 'ALPHA', subnetForTx.txType);
        }}
      />
    </>
  );
};

const useMaxAmount = () => {
  const { availableTao, getDelegatedAlphaForHotkey, handleInputChange } =
    useBuySellFormContext();
  const { subnetForTx } = useBuySellContext();

  const handleMaxClick = () => {
    if (!subnetForTx) {
      return;
    }

    const isBuy = subnetForTx.txType === 'buy';
    const amount = isBuy
      ? availableTao
      : getDelegatedAlphaForHotkey(subnetForTx.validatorHotkey);
    handleInputChange(
      amount.toString(),
      isBuy ? 'TAO' : 'ALPHA',
      subnetForTx.txType
    );
  };

  return { handleMaxClick };
};

const SlippageAndAvailableSection = ({
  showSlippageSection,
}: {
  showSlippageSection: () => void;
}) => {
  const { handleMaxClick } = useMaxAmount();
  const { getFieldValues } = useBuySellFormContext();
  const { subnetForTx } = useBuySellContext();
  const { maxSlippage } = useMaxSlippage();
  const { getSlippageTextClassName } = useSlippageHelper();

  if (!subnetForTx) {
    return null;
  }

  const { slippage } = getFieldValues(subnetForTx.txType);

  return (
    <div className='flex flex-col gap-2 max-sm:items-center max-sm:justify-center max-sm:gap-3 sm:flex-row sm:items-center sm:justify-between'>
      <div className='flex flex-row items-center gap-1 max-sm:justify-center'>
        <div className='flex flex-row items-center gap-1'>
          <Text
            level='labelSmall'
            className={cn(
              getSlippageTextClassName(BigNumber(slippage).toNumber()),
              'font-medium transition-colors duration-1000'
            )}
          >
            Slippage {slippage}%
          </Text>
          {/* eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions -- Allow this div to be clickable */}
          <div onClick={showSlippageSection} className='cursor-pointer'>
            <Text
              level='labelSmall'
              className={cn(
                getSlippageTextClassName(BigNumber(maxSlippage).toNumber()),
                'font-medium underline decoration-dotted underline-offset-4 transition-colors duration-1000 hover:underline hover:decoration-solid'
              )}
            >
              (Max: {maxSlippage}%)
            </Text>
          </div>
        </div>
      </div>

      <div className='flex flex-row items-center gap-1 max-sm:justify-center'>
        <Button
          size='sm'
          variant='outline'
          className='h-6 px-2 py-0.5'
          onClick={() => {
            handleMaxClick();
          }}
        >
          Max
        </Button>
        <AvailableSection />
      </div>
    </div>
  );
};

const AvailableSection = () => {
  const { availableTao, getDelegatedAlphaForHotkey } = useBuySellFormContext();
  const { subnetForTx } = useBuySellContext();
  const { handleMaxClick } = useMaxAmount();
  const { getSubnetSymbol } = useSubnetLookup();

  if (!subnetForTx) {
    return null;
  }

  const isBuy = subnetForTx.txType === 'buy';

  // For the buy section, we want to show the amount of tao we can use to buy alpha
  // For the sell section, we want to show the amount of alpha we currently have delegated to the selected validator which we can sell for tao
  const amount = isBuy
    ? availableTao
    : getDelegatedAlphaForHotkey(subnetForTx.validatorHotkey);

  return (
    // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions -- Allow this div to be clickable
    <div
      className='flex cursor-pointer flex-row items-center gap-1'
      onClick={handleMaxClick}
    >
      <Text
        level='labelSmall'
        className='font-medium opacity-60 hover:opacity-100'
      >
        {isBuy ? 'Available' : 'Delegated'}{' '}
        {isBuy ? 'τ' : getSubnetSymbol(subnetForTx.netuid)}{' '}
        {formatNumber(amount, {
          minimumFractionDigits: 2,
          maximumFractionDigits: 5,
        })}
      </Text>
    </div>
  );
};

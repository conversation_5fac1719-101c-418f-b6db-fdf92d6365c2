import { useMemo } from 'react';
import { getHumanValueFromChainValue } from '@repo/ui/lib';
import { usePortfolioContext } from '@/app/portfolio/lib/context';
import { useColdkeyData } from '@/app/portfolio/lib/hooks';
import { useTaoToDollarConverter } from '@/lib/hooks/price-hooks';

export interface DualAxisData {
  date: Date;
  leftValue: number;
  rightValue: number;
}

export const getDefaultMargin = (showTicks: boolean) =>
  showTicks
    ? { top: 40, right: 50, bottom: 40, left: 50 }
    : { top: 40, right: 0, bottom: 40, left: 0 };

export const colours = {
  green: '#00DBBC',
  red: '#EB5347',
};

const getBadgePosition = (
  value: number,
  min: number,
  max: number,
  height: number,
  defaultMargin: ReturnType<typeof getDefaultMargin>
) => {
  const range = max - min;
  const valueMinusMin = value - min;
  const percentagePosition = valueMinusMin / range;
  const percentagePositionRemainder = 1 - percentagePosition;
  const heightMinusMargins = height - defaultMargin.bottom - defaultMargin.top;
  const positionPercentToHeightValue =
    percentagePositionRemainder * heightMinusMargins;
  return positionPercentToHeightValue + 25;
};

const upScale = (val: number, scale = 1.1) => val * scale;
const downScale = (val: number, scale = 0.9) => val * scale;
const SHOW_TICKS = false;

const calculatePositionFactor = (
  axisVal: number,
  axisMin: number,
  axisMax: number
) => {
  // Get the amount of the axis value which is actually on the chart
  // e.g. if the axis starts at 9k an ends at 10k, and the value is 9.5k, the amount on the chart is 0.5k
  const amountOnChart = axisVal - axisMin;

  // Get the range of the axis
  const axisRange = axisMax - axisMin;

  // Get the position factor of the value on the axis
  const positionFactor = amountOnChart / axisRange;

  return positionFactor;
};

export const useChartConfig = (data: DualAxisData[], chartHeight: number) => {
  const showTicks = SHOW_TICKS;
  const defaultMargin = getDefaultMargin(showTicks);

  const rVals = useMemo(() => {
    return data.map((t) => t.rightValue);
  }, [data]);

  const lVals = useMemo(() => {
    return data.map((t) => t.leftValue);
  }, [data]);

  const chartConfig = useMemo(() => {
    const rightAxisMin = Math.min(...rVals);
    const rightAxisMax = Math.max(...rVals);
    const rightAxisMaxScale = upScale(rightAxisMax, 1.0001);
    const rightAxisMinScale = downScale(rightAxisMin, 0.9999);

    const leftAxisMin = Math.min(...lVals);
    const leftAxisMax = Math.max(...lVals);
    const leftAxisMaxScale = upScale(leftAxisMax, 1.0001);
    const leftAxisMinScale = downScale(leftAxisMin, 0.9999);

    const getRightValuePositionOnLeftAxis = (rightAxisVal: number) => {
      const rValPositionFactor = calculatePositionFactor(
        rightAxisVal,
        rightAxisMinScale,
        rightAxisMaxScale
      );

      // Get the range of the left axis
      const leftRange = leftAxisMaxScale - leftAxisMinScale;

      // Get the amount through the left axis the right value needs to be
      // e.g. if the right range is 100k (max: 500k, min 400k) and the right position factor is 0.5, the amount through the left axis is 50k
      const rValAmountThroughLeftAxis = rValPositionFactor * leftRange;

      // Get the final position of the right axis value on the left axis
      // e.g. if the left axis starts at 400k and the amount through the left axis is 50k, the final position is 450k
      const rValPositionOnLeftAxis =
        rValAmountThroughLeftAxis + leftAxisMinScale;

      return { rValPositionOnLeftAxis, rValPositionFactor };
    };

    const {
      rValPositionOnLeftAxis: rValMaxPositionOnLeftAxis,
      rValPositionFactor: rValMaxPositionFactor,
    } = getRightValuePositionOnLeftAxis(rightAxisMax);

    const {
      rValPositionOnLeftAxis: rValMinPositionOnLeftAxis,
      rValPositionFactor: rValMinPositionFactor,
    } = getRightValuePositionOnLeftAxis(rightAxisMin);

    const rightValMaxBadgePosition = getBadgePosition(
      rValMaxPositionOnLeftAxis,
      leftAxisMinScale,
      leftAxisMaxScale,
      chartHeight,
      defaultMargin
    );

    const rightValMinBadgePosition = getBadgePosition(
      rValMinPositionOnLeftAxis,
      leftAxisMinScale,
      leftAxisMaxScale,
      chartHeight,
      defaultMargin
    );

    const leftValMaxBadgePosition = getBadgePosition(
      leftAxisMax,
      leftAxisMinScale,
      leftAxisMaxScale,
      chartHeight,
      defaultMargin
    );

    const leftValMinBadgePosition = getBadgePosition(
      leftAxisMin,
      leftAxisMinScale,
      leftAxisMaxScale,
      chartHeight,
      defaultMargin
    );

    const lValMinPositionFactor = calculatePositionFactor(
      leftAxisMin,
      leftAxisMinScale,
      leftAxisMaxScale
    );

    const lValMaxPositionFactor = calculatePositionFactor(
      leftAxisMax,
      leftAxisMinScale,
      leftAxisMaxScale
    );

    return {
      rightAxisMin,
      rightAxisMax,
      rightAxisMaxScale,
      rightAxisMinScale,
      leftAxisMin,
      leftAxisMax,
      leftAxisMaxScale,
      leftAxisMinScale,
      rValMaxPositionOnLeftAxis,
      rValMinPositionOnLeftAxis,
      rightValMaxBadgePosition,
      rightValMinBadgePosition,
      leftValMaxBadgePosition,
      leftValMinBadgePosition,
      getRightValuePositionOnLeftAxis,
      rValMaxPositionFactor,
      rValMinPositionFactor,
      lValMinPositionFactor,
      lValMaxPositionFactor,
    };
  }, [defaultMargin, chartHeight, lVals, rVals]);

  return {
    chartConfig,
    showTicks,
    defaultMargin,
  };
};

const useGetLatestValues = () => {
  const { accountLatestQuery } = usePortfolioContext();
  const { getDollarValue } = useTaoToDollarConverter();

  const currentTaoValue = getHumanValueFromChainValue(
    accountLatestQuery.data?.data[0]?.balance_total || 0
  );
  const currentDollarValue = getDollarValue(currentTaoValue);

  return { currentTaoValue, currentDollarValue };
};

export const useChartData = (height: number) => {
  const { currentTaoValue, currentDollarValue } = useGetLatestValues();
  const { coldkeyReportData } = useColdkeyData();

  const initialData = useMemo(() => {
    const reportData =
      coldkeyReportData
        ?.filter((report) => report.total_balance !== null)
        .map((report) => ({
          date: new Date(report.timestamp),
          leftValue: Number(
            Number(report.total_balance) * Number(report.tao_price)
          ),
          rightValue: Number(report.total_balance),
        })) ?? [];

    // Add the current values to the data so the chart matches the current values on the portfolio page
    reportData.push({
      date: new Date(),
      leftValue: currentDollarValue,
      rightValue: currentTaoValue,
    });

    return reportData;
  }, [coldkeyReportData, currentDollarValue, currentTaoValue]);

  const { chartConfig, defaultMargin, showTicks } = useChartConfig(
    initialData,
    height
  );

  const data = useMemo(() => {
    return initialData.map((datum) => ({
      ...datum,
      rightValueForPosition: chartConfig.getRightValuePositionOnLeftAxis(
        datum.rightValue
      ).rValPositionOnLeftAxis,
    }));
  }, [initialData, chartConfig]);

  return { data, chartConfig, defaultMargin, showTicks };
};

'use client';

import { useCallback, memo } from 'react';
import { AxisRight } from '@visx/axis';
import { curveMonotoneX } from '@visx/curve';
import { LinearGradient } from '@visx/gradient';
import { ParentSize } from '@visx/responsive';
import { scaleLinear } from '@visx/scale';
import { defaultStyles as tooltipDefaultStyles } from '@visx/tooltip';
import {
  XYChart,
  Axis,
  LineSeries,
  AreaSeries,
  Grid,
  Tooltip,
  type TooltipData,
} from '@visx/xychart';
import { Card, Badge, Text, Skeleton, Alert } from '@repo/ui/components';
import type { DualAxisData } from './utils';
import { colours, useChartData } from './utils';
import { useColdkeyData } from '@/app/portfolio/lib/hooks';
import { formatNumber2dp, TAO_CURRENCY } from '@/lib/utils';

const dataKeys = {
  leftValueArea: 'Left Value Area',
  rightValueArea: 'Right Value Area',
  upperLeftThreshold: 'Upper Left Threshold',
  upperRightThreshold: 'Upper Right Threshold',
  lowerRightThreshold: 'Lower Right Threshold',
  lowerLeftThreshold: 'Lower Left Threshold',
} as const;

const useTooltipRenderer = () => {
  const handleRenderTooltip = useCallback(
    ({ tooltipData }: { tooltipData?: TooltipData }) => {
      const date = (
        tooltipData?.datumByKey[dataKeys.leftValueArea]?.datum as
          | DualAxisData
          | undefined
      )?.date;
      const leftValue = (
        tooltipData?.datumByKey[dataKeys.leftValueArea]?.datum as
          | DualAxisData
          | undefined
      )?.leftValue;
      const rightValue = (
        tooltipData?.datumByKey[dataKeys.rightValueArea]?.datum as
          | DualAxisData
          | undefined
      )?.rightValue;

      if (!date || !leftValue || !rightValue) {
        return null;
      }

      return (
        <div className='min-w-60.75 rounded-lg px-5 py-3'>
          <div className='flex flex-col gap-2'>
            <div>
              <Text level='labelLarge'>
                {date.toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric',
                  year: 'numeric',
                })}
              </Text>
            </div>
            <div className='flex flex-row items-center gap-2'>
              <div className='bg-accent-1 h-3 w-3 rounded-sm' />
              <Text level='bodySmall' className='text-white/60'>
                {TAO_CURRENCY}
                {formatNumber2dp(rightValue)}
              </Text>
            </div>
            <div className='flex flex-row items-center gap-2'>
              <div className='bg-accent-2 h-3 w-3 rounded-sm' />
              <Text level='bodySmall' className='text-white/60'>
                ${formatNumber2dp(leftValue)}
              </Text>
            </div>
          </div>
        </div>
      );
    },
    []
  );

  return handleRenderTooltip;
};

export const EarningsChart = memo(function EarningsChart() {
  const { isLoading, isError, isSuccess } = useColdkeyData();

  return (
    <Card
      className='w-full bg-transparent p-0 max-sm:h-[200px] sm:p-0'
      contentContainerClassName='h-full'
    >
      <div className='h-full w-full overflow-hidden'>
        {isLoading ? (
          <div className='flex w-full items-center justify-center'>
            <Skeleton className='h-full w-full' />
          </div>
        ) : null}

        {isError ? (
          <Alert
            type='error'
            description='An error occurred while getting the earnings data.'
            title='Error'
          />
        ) : null}

        {isSuccess ? (
          <div className='h-full w-full'>
            <ParentSize>
              {({ width, height }) => <Chart width={width} height={height} />}
            </ParentSize>
          </div>
        ) : null}
      </div>
    </Card>
  );
});

const Chart = memo(function Chart({
  width,
  height,
}: {
  width: number;
  height: number;
}) {
  const { data, chartConfig, defaultMargin, showTicks } = useChartData(height);
  const handleRenderTooltip = useTooltipRenderer();

  return (
    <div>
      <XYChart
        width={width}
        height={height}
        xScale={{ type: 'time' }}
        yScale={{
          type: 'linear',
          domain: [chartConfig.leftAxisMinScale, chartConfig.leftAxisMaxScale],
          zero: false,
        }}
        margin={defaultMargin}
      >
        <Grid
          columns={false}
          numTicks={5}
          lineStyle={{
            strokeOpacity: 0.1,
            strokeWidth: 0.5,
            stroke: '#FFFFFF',
          }}
        />
        <Axis
          orientation='bottom'
          hideAxisLine={false}
          axisLineClassName='stroke-[#ffffff]/10'
          hideTicks
          numTicks={5}
          tickFormat={(date) =>
            (date as Date).toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric',
            })
          }
        />
        <Axis
          hideAxisLine
          hideTicks
          orientation='left'
          tickLabelProps={{
            fill: '#888',
            fontSize: 11,
          }}
          tickFormat={(value) =>
            showTicks
              ? `$${(value as number).toLocaleString('en-US', {
                  notation: 'compact',
                  compactDisplay: 'short',
                  maximumFractionDigits: 1,
                })}`
              : ''
          }
        />
        <AxisRight
          hideAxisLine
          left={width - 49}
          top={defaultMargin.top}
          scale={scaleLinear<number>({
            domain: [
              chartConfig.rightAxisMinScale,
              chartConfig.rightAxisMaxScale,
            ],
            range: [height - defaultMargin.top - defaultMargin.bottom, 0],
          })}
          tickFormat={(value) =>
            showTicks
              ? (value as number).toLocaleString('en-US', {
                  notation: 'compact',
                  compactDisplay: 'short',
                  maximumFractionDigits: 3,
                })
              : ''
          }
          hideTicks
          tickLabelProps={{
            fill: '#888',
            fontSize: 11,
          }}
        />

        <LinearGradient
          id='left-area-gradient'
          from={colours.red}
          to={colours.red}
          fromOpacity={0.2}
          toOpacity={0}
        />
        <AreaSeries
          dataKey={dataKeys.leftValueArea}
          data={data}
          xAccessor={(d) => d.date}
          yAccessor={(d) => d.leftValue}
          fill='url(#left-area-gradient)'
          curve={curveMonotoneX}
          lineProps={{
            strokeWidth: 2,
            stroke: colours.red,
          }}
        />

        <LinearGradient
          id='right-area-gradient'
          from={colours.green}
          to={colours.green}
          fromOpacity={0.2}
          toOpacity={0}
        />
        <AreaSeries
          dataKey={dataKeys.rightValueArea}
          data={data}
          xAccessor={(d) => d.date}
          yAccessor={(d) => d.rightValueForPosition}
          fill='url(#right-area-gradient)'
          curve={curveMonotoneX}
          lineProps={{
            strokeWidth: 2,
            stroke: colours.green,
          }}
        />

        <LineSeries
          dataKey={dataKeys.upperLeftThreshold}
          data={data.map((d) => ({
            date: d.date,
            value: chartConfig.leftAxisMax,
          }))}
          xAccessor={(d) => d.date}
          yAccessor={(d) => d.value}
          stroke={colours.red}
          strokeWidth={1.5}
          strokeDasharray='3,6'
        />

        <LineSeries
          dataKey={dataKeys.upperRightThreshold}
          data={data.map((d) => ({
            date: d.date,
            value: chartConfig.rValMaxPositionOnLeftAxis,
          }))}
          xAccessor={(d) => d.date}
          yAccessor={(d) => d.value}
          stroke={colours.green}
          strokeWidth={1.5}
          strokeDasharray='3,6'
        />

        {chartConfig.rValMinPositionFactor > 0.02 ? (
          <LineSeries
            dataKey={dataKeys.lowerRightThreshold}
            data={data.map((d) => ({
              date: d.date,
              value: chartConfig.rValMinPositionOnLeftAxis,
            }))}
            xAccessor={(d) => d.date}
            yAccessor={(d) => d.value}
            stroke={colours.green}
            strokeWidth={1.5}
            strokeDasharray='3,6'
          />
        ) : null}

        {chartConfig.lValMinPositionFactor > 0.02 ? (
          <LineSeries
            dataKey={dataKeys.lowerLeftThreshold}
            data={data.map((d) => ({
              date: d.date,
              value: chartConfig.leftAxisMin,
            }))}
            xAccessor={(d) => d.date}
            yAccessor={(d) => d.value}
            stroke={colours.red}
            strokeWidth={1.5}
            strokeDasharray='3,6'
          />
        ) : null}

        <g
          transform={`translate(${showTicks ? width - defaultMargin.right - 85 : width - 90}, ${chartConfig.rightValMaxBadgePosition})`}
        >
          <foreignObject width={100} height={24}>
            <Badge variant='green4'>
              {chartConfig.rightAxisMax.toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              })}
              t
            </Badge>
          </foreignObject>
        </g>

        <g
          transform={`translate(${showTicks ? width - defaultMargin.right - 85 : width - 90}, ${chartConfig.rightValMinBadgePosition})`}
        >
          <foreignObject width={100} height={24}>
            <Badge variant='green4' className='bg-opacity-60'>
              {chartConfig.rightAxisMin.toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              })}
              t
            </Badge>
          </foreignObject>
        </g>

        <g
          transform={`translate(${showTicks ? defaultMargin.left + 20 : 10}, ${chartConfig.leftValMaxBadgePosition})`}
        >
          <foreignObject width={200} height={24}>
            <Badge variant='red2'>
              $
              {chartConfig.leftAxisMax.toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              })}
            </Badge>
          </foreignObject>
        </g>

        <g
          transform={`translate(${showTicks ? defaultMargin.left + 20 : 10}, ${chartConfig.leftValMinBadgePosition})`}
        >
          <foreignObject width={200} height={24}>
            <Badge variant='red2' className='bg-opacity-60'>
              $
              {chartConfig.leftAxisMin.toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              })}
            </Badge>
          </foreignObject>
        </g>

        <Tooltip
          style={{
            ...tooltipDefaultStyles,
            opacity: 0.8,
            backdropFilter: 'blur(23.8px)',
            backgroundColor: '#1d1d1d',
            borderRadius: '8px',
            padding: '10px',
          }}
          showVerticalCrosshair
          verticalCrosshairStyle={{
            strokeDasharray: '5 3',
          }}
          renderTooltip={handleRenderTooltip}
          showSeriesGlyphs
          renderGlyph={({ key }) => {
            if (
              key === dataKeys.leftValueArea ||
              key === dataKeys.rightValueArea
            ) {
              return (
                <circle
                  r={4}
                  fill={
                    key === dataKeys.leftValueArea ? colours.red : colours.green
                  }
                  stroke='white'
                  strokeWidth={2}
                />
              );
            }
            return null;
          }}
        />
      </XYChart>
    </div>
  );
});

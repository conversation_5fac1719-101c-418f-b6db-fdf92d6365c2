'use client';

import { useRouter } from 'next/navigation';
import { WalletPicker } from './wallet-picker';
import { usePortfolioContext } from '@/app/portfolio/lib/context';

export const PortfolioConfiguration = () => {
  const router = useRouter();
  const { walletAddress, setWalletAddress, setLatestWallet, accountData } =
    usePortfolioContext();
  const handleSetWalletAddress = (address: string) => {
    setWalletAddress(address);
    setLatestWallet(address);
    router.push(`/portfolio/${address}`);
  };
  return (
    <div className='flex w-full flex-col gap-4 sm:flex-row sm:items-center sm:justify-between'>
      <div className='w-full sm:relative sm:flex-grow'>
        <WalletPicker
          allowAccountAdd={Boolean(accountData)}
          setWalletAddress={handleSetWalletAddress}
          walletAddressInput={walletAddress}
        />
      </div>
    </div>
  );
};

import type { ReactNode } from 'react';
import type { IconType } from 'react-icons';
import {
  Card,
  Skeleton,
  Text,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@repo/ui/components';
import { PortfolioStatsCardHeader } from './portfolio-stats-card-header';

interface PortfolioStatCardSmallProps {
  isLoading?: boolean;
  title: string;
  TitleIcon?: IconType;
  titleIconTooltip?: string;
  valuePrefix?: ReactNode;
  value: ReactNode;
  valueSuffix?: ReactNode;
}

export const PortfolioStatCardSmall = ({
  isLoading,
  title,
  TitleIcon,
  titleIconTooltip,
  valuePrefix,
  value,
  valueSuffix,
}: PortfolioStatCardSmallProps) => {
  return (
    <Card className='p-3'>
      <div className='flex flex-col gap-1'>
        <div className='flex flex-row items-center gap-1'>
          <PortfolioStatsCardHeader title={title} />
          {TitleIcon ? (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <TitleIcon className='text-white/60' fontSize={16} />
                </TooltipTrigger>
                <TooltipContent>{titleIconTooltip}</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ) : null}
        </div>

        {!isLoading ? (
          <div className='flex flex-col gap-2'>
            <div className='flex flex-row items-baseline gap-2 overflow-hidden'>
              {valuePrefix ? valuePrefix : null}
              {typeof value === 'string' ? (
                <Text as='h3' level='headerSmall' className='font-normal'>
                  {value}
                </Text>
              ) : (
                value
              )}
              {valueSuffix ? <ValueSuffix valueSuffix={valueSuffix} /> : null}
            </div>
          </div>
        ) : (
          <>
            <Skeleton className='h-8 w-full' />
            <Skeleton className='h-12 w-full' />
          </>
        )}
      </div>
    </Card>
  );
};

const ValueSuffix = ({ valueSuffix }: { valueSuffix: ReactNode }) => {
  if (typeof valueSuffix === 'string') {
    return (
      <Text as='span' level='bodyLarge' className='font-normal text-white/60'>
        {valueSuffix}
      </Text>
    );
  }

  return valueSuffix;
};

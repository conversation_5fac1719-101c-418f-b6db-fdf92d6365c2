'use client';

import { BigNumber } from 'bignumber.js';
import { BiTrophy } from 'react-icons/bi';
import {
  BigSmallValueDisplay,
  TaoOrAlphaValueDisplay,
} from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { GainsCard } from './gains-card';
import { PortfolioStatCard } from './portfolio-stats-card';
import { usePortfolioContext } from '@/app/portfolio/lib/context';
import {
  getEstimateAprFromValueAndRange,
  useBalancesData,
} from '@/app/portfolio/lib/hooks';
import { useStatsLatestQuery } from '@/app/portfolio/lib/query-hooks';
import { EmptyCellWithTooltip } from '@/app/portfolio/ui/shared';
import { formatNumber } from '@/lib/utils';

export function PortfolioStats() {
  return (
    <div className='flex h-full flex-col gap-6'>
      <EarningsCard />
      <GainsCard />
      <RankCard />
    </div>
  );
}

const EarningsCard = () => {
  const {
    currency: { isUsd },
    dateRangeSelected,
  } = usePortfolioContext();
  const { isLoading, balancesDataForSelectedWallet } = useBalancesData();

  const {
    totalEarningsTao,
    totalEarningsUsd,
    balanceAsTaoTotal,
    balanceAsUsdTotal,
  } = balancesDataForSelectedWallet.reduce(
    (acc, curr) => {
      return {
        totalEarningsTao: acc.totalEarningsTao.plus(curr.earnedTao),
        totalEarningsUsd: acc.totalEarningsUsd.plus(curr.earnedUsd),
        balanceAsTaoTotal: acc.balanceAsTaoTotal.plus(curr.balanceAsTao),
        balanceAsUsdTotal: acc.balanceAsUsdTotal.plus(curr.balanceAsUsd),
      };
    },
    {
      totalEarningsTao: new BigNumber(0),
      totalEarningsUsd: new BigNumber(0),
      balanceAsTaoTotal: new BigNumber(0),
      balanceAsUsdTotal: new BigNumber(0),
    }
  );

  const hasTransfersWithinSelectedTimeframe =
    balancesDataForSelectedWallet.some(
      (d) => d.hasTransfersWithinSelectedTimeframe
    );

  const earningsPercentage = balanceAsTaoTotal.gt(0)
    ? BigNumber(isUsd ? totalEarningsUsd : totalEarningsTao)
        .dividedBy(isUsd ? balanceAsUsdTotal : balanceAsTaoTotal)
        .multipliedBy(100)
        .toNumber()
    : 0;
  const apy = getEstimateAprFromValueAndRange(
    earningsPercentage,
    dateRangeSelected
  );

  return (
    <PortfolioStatCard
      isLoading={isLoading}
      title='Earnings'
      value={
        <TaoOrAlphaValueDisplay
          value={totalEarningsTao.toNumber()}
          valueTextLevel='headerLarge'
          valueFormattingOptions={{
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }}
          valueClassName='font-normal'
          areDecimalsSmall
        />
      }
      footerValue={`$${totalEarningsUsd.toNumber().toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      })}`}
      rightValue={
        <div
          className={cn(
            'flex flex-col items-end gap-2',
            hasTransfersWithinSelectedTimeframe && 'w-32'
          )}
        >
          {hasTransfersWithinSelectedTimeframe ? (
            <div className='mt-2 w-full'>
              <EmptyCellWithTooltip />
              <EmptyCellWithTooltip />
            </div>
          ) : (
            <>
              <BigSmallValueDisplay
                textLevel='headerMedium'
                className='mt-[14px] text-2xl font-light text-white/60'
                value={earningsPercentage}
                valueFormattingOptions={{
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 3,
                }}
                areDecimalsSmall
                suffix='%'
              />
              <BigSmallValueDisplay
                textLevel='headerMedium'
                className='text-2xl font-light text-white/60'
                value={apy}
                valueFormattingOptions={{
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }}
                areDecimalsSmall
                suffix='% APY'
              />
            </>
          )}
        </div>
      }
    />
  );
};

const RankCard = () => {
  const { data: statsLatestData } = useStatsLatestQuery();
  const {
    accountLatestQuery: { isLoading, data: accountData },
  } = usePortfolioContext();

  const getAccountLatestData = accountData?.data ?? [];
  const accountLatestData =
    getAccountLatestData.length > 0 ? getAccountLatestData[0] : null;

  const rank = accountLatestData?.rank ?? 0;
  const accountsTotal = statsLatestData?.data[0]?.accounts ?? 0;
  const rankPercentage = (rank / accountsTotal) * 100;
  const footerText =
    accountsTotal > 0
      ? `You're in the top ${rankPercentage < 5 ? rankPercentage.toFixed(3) : Math.round(rankPercentage)}% of Tao owners`
      : undefined;

  return (
    <PortfolioStatCard
      isLoading={isLoading}
      title='Wallet Rank'
      value={formatNumber(rank)}
      valueSuffix={` / ${formatNumber(accountsTotal)}`}
      footerText={footerText}
      FooterIcon={BiTrophy}
      footerTooltip='The rank of your wallet in the Bittensor Network based on total Tao.'
    />
  );
};

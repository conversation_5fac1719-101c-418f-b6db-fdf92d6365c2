import { useMemo } from 'react';
import { BigNumber } from 'bignumber.js';
import {
  Card,
  Skeleton,
  TaoOrAlphaValueDisplay,
  Text,
} from '@repo/ui/components';
import { getHumanValueFromChainValue } from '@repo/ui/lib';
import { GainsCardChart } from './chart';
import { usePortfolioContext } from '@/app/portfolio/lib/context';
import { useStakeBalanceLatestQuery } from '@/app/portfolio/lib/query-hooks';
import { PortfolioStatsCardHeader } from '@/app/portfolio/ui/stats/portfolio-stats-card-header';
import { getNumberOfDaysFromDateRange } from '@/lib/utils';

export const GainsCard = () => {
  const { walletAddress, currencySelected, dateRangeSelected } =
    usePortfolioContext();
  const { data, isSuccess, isLoading } = useStakeBalanceLatestQuery(
    walletAddress,
    getNumberOfDaysFromDateRange(dateRangeSelected)
  );

  const normalisedData = useMemo(() => {
    return (
      data?.data.map((delegation) => {
        return {
          realisedProfitTao: getHumanValueFromChainValue(
            delegation.realised_profit_tao
          ),
          realisedProfitUsd: BigNumber(
            delegation.realised_profit_usd
          ).toNumber(),
          unrealisedProfitTao: getHumanValueFromChainValue(
            delegation.unrealised_profit_tao
          ),
          unrealisedProfitUsd: BigNumber(
            delegation.unrealised_profit_usd
          ).toNumber(),
        };
      }) ?? []
    );
  }, [data]);

  if (isLoading) {
    return (
      <Card contentContainerClassName='h-full'>
        <div className='flex h-full flex-row justify-between'>
          <div className='flex h-full flex-1 flex-col gap-6'>
            <div>
              <PortfolioStatsCardHeader title='Gains' />
            </div>
            <div className='flex w-full flex-col gap-2'>
              <Skeleton className='h-6 w-full' />
              <Skeleton className='h-6 w-full' />
            </div>
          </div>
          <div className='flex w-1/2 flex-row items-center justify-end'>
            <Skeleton className='h-28 w-28 rounded-full' />
          </div>
        </div>
      </Card>
    );
  }

  if (!isSuccess) {
    return null;
  }

  const gains = normalisedData.reduce(
    (acc, curr) => {
      return {
        realisedProfitTao: acc.realisedProfitTao + curr.realisedProfitTao,
        realisedProfitUsd: acc.realisedProfitUsd + curr.realisedProfitUsd,
        unrealisedProfitTao: acc.unrealisedProfitTao + curr.unrealisedProfitTao,
        unrealisedProfitUsd: acc.unrealisedProfitUsd + curr.unrealisedProfitUsd,
      };
    },
    {
      realisedProfitTao: 0,
      realisedProfitUsd: 0,
      unrealisedProfitTao: 0,
      unrealisedProfitUsd: 0,
    }
  );

  const dataForChart = [
    {
      label: 'Realised',
      value:
        currencySelected === 'TAO'
          ? gains.realisedProfitTao
          : gains.realisedProfitUsd,
      colour: 'green',
    },
    {
      label: 'Unrealised',
      value:
        currencySelected === 'TAO'
          ? gains.unrealisedProfitTao
          : gains.unrealisedProfitUsd,
      colour: 'red',
    },
  ];

  return (
    <Card contentContainerClassName='h-full'>
      <div className='flex h-full flex-row justify-between'>
        <div className='flex h-full w-fit flex-col gap-6'>
          <div>
            <PortfolioStatsCardHeader title='Gains' />
          </div>
          <div>
            <table>
              <tbody>
                {dataForChart.map(({ label, value, colour }) => (
                  <tr key={label}>
                    <td className='pr-3'>
                      <div className='flex items-center'>
                        <div
                          className={`mr-2 h-2 w-2 rounded-full ${
                            colour === 'green' ? 'bg-accent-1' : 'bg-accent-2'
                          }`}
                        />
                        <Text level='bodyMedium' className='text-white/60'>
                          {label}
                        </Text>
                      </div>
                    </td>
                    <td>
                      <TaoOrAlphaValueDisplay
                        value={value}
                        valueTextLevel='bodyMedium'
                        colourClassName='text-white'
                        iconClassName='text-xs'
                        areDecimalsSmall={false}
                        symbol={currencySelected === 'USD' ? '$' : undefined}
                        valueFormattingOptions={{
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        }}
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
        <div className='flex flex-1 flex-row items-end justify-end'>
          <GainsCardChart width={140} height={90} data={dataForChart} />
        </div>
      </div>
    </Card>
  );
};

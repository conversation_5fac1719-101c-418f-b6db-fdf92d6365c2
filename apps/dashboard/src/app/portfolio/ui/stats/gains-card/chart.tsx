import { Group } from '@visx/group';
import { ParentSize } from '@visx/responsive';
import Pie from '@visx/shape/lib/shapes/Pie';

export const GainsCardChart = (props: PieProps) => {
  return (
    <ParentSize>
      {({ width, height }) => {
        return <Chart {...props} width={width} height={height} />;
      }}
    </ParentSize>
  );
};

interface DataPoint {
  label: string;
  value: number;
  colour: string;
}

export type PieProps = {
  width: number;
  height: number;
  data: DataPoint[];
  donutThickness?: number;
  margin?: { top: number; right: number; bottom: number; left: number };
};

export function Chart({
  width,
  height,
  data,
  donutThickness = 7,
  margin = { top: 10, right: 0, bottom: 10, left: 0 },
}: PieProps) {
  if (width < 10) return null;

  const innerWidth = width - margin.left - margin.right;
  const innerHeight = height - margin.top - margin.bottom;
  const radius = Math.min(innerWidth, innerHeight) / 2;
  const centerY = innerHeight / 2;
  const centerX = innerWidth - radius - margin.right;
  return (
    <svg width='100%' height={height}>
      <Group
        top={centerY + margin.top}
        left={centerX + margin.left}
        className='items-end'
      >
        <Pie
          data={data}
          pieValue={(d) => d.value}
          outerRadius={radius}
          innerRadius={radius - donutThickness}
          padAngle={0.05}
          cornerRadius={3}
        >
          {({ arcs, path }) => (
            <g>
              {arcs.map((arc, i) => (
                <g key={`pie-arc-${arc.data.label}`}>
                  <path
                    className={`arc${i} m-1 rounded-full`}
                    d={path(arc) ?? undefined}
                    fill={arc.data.colour === 'green' ? '#00DBBC' : '#EB5347'}
                  />
                </g>
              ))}
            </g>
          )}
        </Pie>
      </Group>
    </svg>
  );
}

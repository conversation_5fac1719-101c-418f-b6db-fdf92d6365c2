import type { ReactNode } from 'react';
import type { IconType } from 'react-icons';
import {
  Card,
  Skeleton,
  Text,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@repo/ui/components';
import { PortfolioStatsCardHeader } from './portfolio-stats-card-header';

interface PortfolioStatCardProps {
  isLoading?: boolean;
  title: string;
  titleRight?: string;
  rightValue?: ReactNode;
  TitleIcon?: IconType;
  titleIconTooltip?: string;
  valuePrefix?: ReactNode;
  value: ReactNode;
  valueSuffix?: ReactNode;
  footerValue?: ReactNode;
  footerText?: string;
  FooterIcon?: IconType;
  footerTooltip?: string;
}

export const PortfolioStatCard = ({
  isLoading,
  title,
  titleRight,
  rightValue,
  TitleIcon,
  titleIconTooltip,
  valuePrefix,
  value,
  valueSuffix,
  footerValue,
  footerText,
  FooterIcon,
  footerTooltip,
}: PortfolioStatCardProps) => {
  return (
    <Card className='flex-grow'>
      <div className='flex flex-col gap-4'>
        <div className='flex flex-row justify-between'>
          <div className='flex flex-row items-center gap-1'>
            <PortfolioStatsCardHeader title={title} />
            {TitleIcon ? (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <TitleIcon className='text-white/60' fontSize={16} />
                  </TooltipTrigger>
                  <TooltipContent>{titleIconTooltip}</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ) : null}
          </div>
          {titleRight ? (
            <div className='flex flex-row items-center justify-end'>
              <PortfolioStatsCardHeader title={titleRight} />
            </div>
          ) : null}
        </div>

        {!isLoading ? (
          <div className='flex flex-row justify-between'>
            <div className='flex flex-col gap-2'>
              <div className='flex flex-row items-baseline gap-2 overflow-hidden'>
                {valuePrefix ? valuePrefix : null}
                {typeof value === 'string' ? (
                  <Text as='h3' level='headerLarge' className='font-normal'>
                    {value}
                  </Text>
                ) : (
                  value
                )}
                {valueSuffix ? <ValueSuffix valueSuffix={valueSuffix} /> : null}
              </div>
              <div className='flex items-baseline gap-2'>
                <FooterValue footerValue={footerValue} />
                <FooterTextAndIcon
                  footerText={footerText}
                  FooterIcon={FooterIcon}
                  footerTooltip={footerTooltip}
                />
              </div>
            </div>
            {rightValue ? (
              <div className='flex flex-row justify-end'>{rightValue}</div>
            ) : null}
          </div>
        ) : (
          <>
            <Skeleton className='h-10 w-full' />
            <Skeleton className='h-8 w-full' />
          </>
        )}
      </div>
    </Card>
  );
};

const ValueSuffix = ({ valueSuffix }: { valueSuffix: ReactNode }) => {
  if (typeof valueSuffix === 'string') {
    return (
      <Text as='span' level='bodyLarge' className='font-normal text-white/60'>
        {valueSuffix}
      </Text>
    );
  }

  return valueSuffix;
};

const FooterValue = ({ footerValue }: { footerValue?: ReactNode }) => {
  if (!footerValue) {
    return null;
  }

  if (typeof footerValue === 'string') {
    return (
      <Text level='headerMedium' className='text-2xl font-light text-white/60'>
        {footerValue}
      </Text>
    );
  }

  return footerValue;
};

const FooterTextAndIcon = ({
  footerText,
  FooterIcon,
  footerTooltip,
}: {
  footerText?: string;
  FooterIcon?: IconType;
  footerTooltip?: string;
}) => {
  if (!footerText && !FooterIcon) {
    return null;
  }

  return (
    <div className='flex items-center gap-2'>
      {FooterIcon ? (
        <div className='flex'>
          {footerTooltip ? (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <FooterIcon className='text-white/60' fontSize={16} />
                </TooltipTrigger>
                <TooltipContent>{footerTooltip}</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ) : (
            <FooterIcon className='text-white/60' fontSize={16} />
          )}
        </div>
      ) : null}

      {footerText ? (
        <Text level='labelExtraSmall' className='leading-8 text-white/60'>
          {footerText}
        </Text>
      ) : null}
    </div>
  );
};

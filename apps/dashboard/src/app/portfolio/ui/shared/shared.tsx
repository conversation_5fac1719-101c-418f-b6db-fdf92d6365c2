import type { ReactNode } from 'react';
import { memo } from 'react';
import { BiLinkExternal } from 'react-icons/bi';
import { CgArrowTopRight } from 'react-icons/cg';
import {
  CopyButton,
  EmptyCell,
  TaoOrAlphaValueDisplay,
  Text,
  TooltipTrigger,
  TooltipContent,
  Tooltip,
  TooltipProvider,
  Skeleton,
  CurrencySelector,
  Flex,
  Badge,
  Link,
} from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { DateRangeSelector } from './date-range-selector';
import { usePortfolioContext } from '@/app/portfolio/lib/context';
import { PortfolioStatsCardHeader } from '@/app/portfolio/ui/stats/portfolio-stats-card-header';
import { formatNumber, truncateString } from '@/lib/utils';

type WalletTextProps = {
  name?: string;
  address: string;
  truncateAddress?: boolean;
  allowCopy?: boolean;
  viewInTaostatsLink?: boolean;
};

export const WalletHeaderText = ({
  name = '',
  address,
  ...props
}: WalletTextProps) => {
  return (
    <Text level='labelMedium' className='truncate text-[#707070]'>
      <WalletText name={name} address={address} {...props} />
    </Text>
  );
};

export const WalletText = ({
  name = '',
  address,
  truncateAddress = false,
  allowCopy = false,
  viewInTaostatsLink = false,
}: WalletTextProps) => {
  return (
    <span className='flex flex-row items-center gap-1 truncate'>
      <span>{name ? name : ''}</span>
      <span className='text-accent-1'>{name ? `//` : ''}</span>
      <span className='font-mono'>
        {truncateAddress ? truncateString(address, 6) : address}
      </span>
      {allowCopy ? (
        <CopyButton size={14} value={address} className='text-white' />
      ) : null}
      {viewInTaostatsLink ? (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger className='w-full'>
              <Link
                href={`https://taostats.io/account/${address}`}
                target='_blank'
              >
                <BiLinkExternal />
              </Link>
            </TooltipTrigger>
            <TooltipContent>
              <div className='w-48'>
                <Text as='p' level='bodyMedium'>
                  View Account in Taostats
                </Text>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ) : null}
    </span>
  );
};

type PortfolioSummaryHeaderAndRangePickerProps = {
  taoOrAlphaValue: number;
  taoOrAlphaValue24HrsAgo?: number;
  bottomValue: ReactNode;
  symbol?: string;
  isLoading?: boolean;
};

export const PortfolioSummaryHeader = ({
  taoOrAlphaValue,
  taoOrAlphaValue24HrsAgo,
  bottomValue,
  symbol,
  isLoading = false,
}: PortfolioSummaryHeaderAndRangePickerProps) => {
  const { selectedWalletName, walletAddress } = usePortfolioContext();

  const amountChange = taoOrAlphaValue24HrsAgo
    ? taoOrAlphaValue - taoOrAlphaValue24HrsAgo
    : null;

  const percentageChange =
    taoOrAlphaValue24HrsAgo && amountChange
      ? (amountChange / taoOrAlphaValue24HrsAgo) * 100
      : null;

  return (
    <div className={cn('flex flex-col items-start gap-1')}>
      {isLoading ? (
        <div className='flex w-48 flex-col gap-2'>
          <Skeleton className='h-5' />
          <Skeleton className='h-12' />
          <Skeleton className='h-8' />
        </div>
      ) : (
        <div className='w-full sm:w-[350px]'>
          <WalletHeaderText
            name={selectedWalletName}
            address={walletAddress}
            truncateAddress
            allowCopy
            viewInTaostatsLink
          />
          <Flex className='items-center justify-between'>
            <div>
              <Flex className='items-center justify-between'>
                <TaoOrAlphaValueDisplay
                  className={cn('justify-start')}
                  symbol={symbol}
                  value={taoOrAlphaValue}
                  valueFormattingOptions={{
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  }}
                  valueTextLevel='displayMedium'
                  iconClassName='text-2xl'
                  areDecimalsSmall
                />
              </Flex>
              {typeof bottomValue === 'number' ? (
                <Text
                  level='displaySmall'
                  className='text-3xl font-normal text-white/60'
                >
                  ${formatNumber(bottomValue)}
                </Text>
              ) : (
                bottomValue
              )}
            </div>

            {percentageChange !== null && amountChange !== null && (
              <Flex col className='items-end gap-2'>
                <PortfolioStatsCardHeader title='24hr Change' />
                <Flex col className='items-end gap-1'>
                  <Flex>
                    <span
                      className={cn(
                        'transition-all duration-500',
                        amountChange >= 0
                          ? 'text-accent-1'
                          : 'text-accent-2 rotate-180'
                      )}
                    >
                      <CgArrowTopRight />
                    </span>
                    <TaoOrAlphaValueDisplay
                      className={cn('justify-start')}
                      symbol={symbol}
                      value={amountChange}
                      valueFormattingOptions={{
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      }}
                      colourClassName={
                        amountChange >= 0 ? 'text-accent-1' : 'text-accent-2'
                      }
                      valueTextLevel='labelMedium'
                      iconClassName='text-xs'
                    />
                  </Flex>
                  <Badge variant={percentageChange >= 0 ? 'green4' : 'red2'}>
                    {percentageChange >= 0 ? '+' : ''}
                    {percentageChange.toFixed(2)}%
                  </Badge>
                </Flex>
              </Flex>
            )}
          </Flex>
        </div>
      )}
    </div>
  );
};

export const DateRangeAndCurrencySelector = memo(
  function DateRangeAndCurrencySelector({ className }: { className?: string }) {
    const {
      dateRangeSelected,
      setDateRangeSelected,
      currencySelected,
      setCurrencySelected,
    } = usePortfolioContext();
    return (
      <div
        className={cn(
          'flex w-full flex-col items-center justify-end gap-8 sm:flex-row',
          className
        )}
      >
        <DateRangeSelector
          dateRangeSelected={dateRangeSelected}
          setDateRangeSelected={setDateRangeSelected}
        />
        <div className='flex w-full sm:w-auto'>
          <CurrencySelector
            selection={currencySelected}
            onSelectionChange={setCurrencySelected}
            options={['TAO', 'USD']}
          />
        </div>
      </div>
    );
  }
);

export const TaoAndUsdDisplay = memo(function TaoAndUsdDisplay({
  taoValue,
  usdValue,
  areDecimalsSmall = false,
  position = 'end',
  isUsd,
}: {
  taoValue: number;
  usdValue: number;
  areDecimalsSmall?: boolean;
  position?: 'start' | 'end';
  isUsd: boolean;
}) {
  return (
    <div
      className={cn(
        'flex flex-col gap-1',
        position === 'start' && 'items-start',
        position === 'end' && 'items-end'
      )}
    >
      <TaoOrAlphaValueDisplay
        value={isUsd ? usdValue : taoValue}
        symbol={isUsd ? '$' : ''}
        valueFormattingOptions={{
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }}
        valueTextLevel='labelLarge'
        iconClassName='text-xs'
        areDecimalsSmall={areDecimalsSmall}
      />
    </div>
  );
});

export const EmptyCellWithTooltip = memo(function EmptyCellWithTooltip() {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger className='w-full'>
          <EmptyCell />
        </TooltipTrigger>
        <TooltipContent>
          <div className='w-48'>
            <Text as='p' level='bodyMedium'>
              APY can only be accurately shown when there have been no changes
              in balance in the time period. Please select a shorter time period
              or let your assets accrue for a longer without trades.
            </Text>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
});

import { memo } from 'react';
import { portfolioDateRanges } from '@/app/portfolio/lib/constants';
import type { DateRange } from '@/app/portfolio/lib/types';

export const DateRangeSelector = memo(function DateRangeSelector({
  dateRangeSelected,
  setDateRangeSelected,
}: {
  dateRangeSelected: string;
  setDateRangeSelected: (dateRange: DateRange) => void;
}) {
  return (
    <div className='flex gap-8 sm:gap-2'>
      {portfolioDateRanges.map((range) => (
        <button
          type='button'
          key={range.value}
          className='data-[active=true]:border-accent-1 data-[active=true]:bg-accent-1/10 flex-1 rounded-lg border border-[#323232] bg-[#1D1D1D] px-2.5 py-[3px] text-sm hover:bg-emerald-500/20 hover:text-white data-[active=true]:text-white sm:flex-initial'
          data-active={range.value === dateRangeSelected}
          onClick={() => {
            setDateRangeSelected(range.value);
          }}
        >
          {range.label}
        </button>
      ))}
    </div>
  );
});

'use client';

import { BigNumber } from 'bignumber.js';
import { useLocalStorage } from 'usehooks-ts';
import { Button, Card } from '@repo/ui/components';
import { getHumanValueFromChainValue, notify } from '@repo/ui/lib';
import { CurrentBalancesDisplay } from './current-balances-display';
import { DelegationCard } from './delegation-card';
import { usePortfolioContext } from '@/app/portfolio/lib/context';
import { useStakeBalanceLatestQuery } from '@/app/portfolio/lib/query-hooks';
import {
  BuySellContextProvider,
  BuySellDialog,
} from '@/app/portfolio/ui/buy-sell';
import { AccountContextProvider } from '@/contexts/chain-and-wallet/account-context';
import { getNumberOfDaysFromDateRange } from '@/lib/utils';

export function CurrentBalances() {
  const { accountLatestQuery, walletAddress, dateRangeSelected } =
    usePortfolioContext();
  const [showZeroBalances, setShowZeroBalances] = useLocalStorage<boolean>(
    'show-zero-balances',
    false
  );
  const { isLoading, data: stakedData } = useStakeBalanceLatestQuery(
    walletAddress,
    getNumberOfDaysFromDateRange(dateRangeSelected)
  );

  if (!accountLatestQuery.isSuccess) {
    return null;
  }

  const data =
    accountLatestQuery.data.data.length > 0
      ? accountLatestQuery.data.data[0]
      : null;

  if (!data) {
    return null;
  }

  const totalStaked = BigNumber(data.balance_staked);
  const stakedToRoot = BigNumber(
    stakedData?.data
      .filter((s) => s.netuid === 0)
      .reduce((acc, curr) => acc.plus(curr.balance), new BigNumber(0)) ?? 0
  );
  const stakedAsAlpha = totalStaked.minus(stakedToRoot);

  return (
    <Card className='bg-hero-primary max-w-screen-2xl'>
      <div className='grid gap-6 sm:grid-cols-2 xl:grid-cols-4'>
        <DelegationCard
          title='Total Staked'
          value={getHumanValueFromChainValue(totalStaked.toNumber())}
        />

        <DelegationCard
          title='Staked to Root'
          value={getHumanValueFromChainValue(stakedToRoot.toNumber())}
          subValue={stakedToRoot.div(totalStaked).multipliedBy(100).toNumber()}
          isFetching={isLoading}
        />

        <DelegationCard
          title='Tao Staked as Alpha'
          value={getHumanValueFromChainValue(stakedAsAlpha.toNumber())}
          subValue={stakedAsAlpha.div(totalStaked).multipliedBy(100).toNumber()}
          isFetching={isLoading}
        />

        <DelegationCard
          title='Not Staked'
          value={getHumanValueFromChainValue(data.balance_free)}
        />
      </div>

      <div className='flex flex-col-reverse justify-between sm:-mb-6 sm:mt-6 sm:flex-row'>
        <div className='mt-12 flex flex-row justify-center sm:mt-0 sm:justify-start'>
          <Button
            size='sm'
            className='border-accent-1 bg-accent-1/10 rounded-lg text-white'
            onClick={() => {
              setShowZeroBalances(!showZeroBalances);
              notify.info(
                `${showZeroBalances ? 'Hiding' : 'Showing'} zero balances`
              );
            }}
          >
            {showZeroBalances ? 'Hide' : 'Show'} Zero Balances
          </Button>
        </div>
      </div>

      <AccountContextProvider>
        <BuySellContextProvider>
          <CurrentBalancesDisplay showZeroBalances={showZeroBalances} />
          <BuySellDialog />
        </BuySellContextProvider>
      </AccountContextProvider>
    </Card>
  );
}

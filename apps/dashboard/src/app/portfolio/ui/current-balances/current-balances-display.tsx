import { useWindowSize } from 'usehooks-ts';
import { Spinner } from '@repo/ui/components';
import { CurrentBalancesCards } from './current-balances-cards';
import { CurrentBalancesTable } from './current-balances-table';
import { useBalancesData } from '@/app/portfolio/lib/hooks';

export const CurrentBalancesDisplay = ({
  showZeroBalances,
}: {
  showZeroBalances: boolean;
}) => {
  const windowSize = useWindowSize();
  const { isLoading, isSuccess, balancesDataForSelectedWallet } =
    useBalancesData();

  if (isLoading) {
    return (
      <div className='mt-6 flex h-full w-full items-center justify-center'>
        <Spinner />
      </div>
    );
  }

  if (!isSuccess) {
    return null;
  }

  const data = showZeroBalances
    ? balancesDataForSelectedWallet
    : balancesDataForSelectedWallet.filter((d) => d.balanceAsAlpha > 0);

  if (windowSize.width < 640) {
    return <CurrentBalancesCards data={data} />;
  }

  return <CurrentBalancesTable data={data} />;
};

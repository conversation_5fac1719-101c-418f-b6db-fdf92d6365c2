import { useState } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { BiCoinStack, BiWallet } from 'react-icons/bi';
import { CgArrowRight } from 'react-icons/cg';
import {
  BigSmallValueDisplay,
  Button,
  Card,
  EmptyCell,
  Spinner,
  SubnetNameDisplay,
  TaoOrAlphaValueDisplay,
  Text,
} from '@repo/ui/components';
import { cn, getIconUrl } from '@repo/ui/lib';
import { isWalletConnected } from './utils';
import { usePortfolioContext } from '@/app/portfolio/lib/context';
import type { NormalisedDelegation } from '@/app/portfolio/lib/types';
import { useBuySellContext } from '@/app/portfolio/ui/buy-sell';
import { TaoAndUsdDisplay } from '@/app/portfolio/ui/shared';
import { useChainAndWalletContext } from '@/contexts/chain-and-wallet/chain-and-wallet-context';
import { TAO_CURRENCY, truncateString } from '@/lib/utils';

export const CurrentBalancesCards = ({
  data,
}: {
  data: NormalisedDelegation[];
}) => {
  const {
    state: { currentAccount },
  } = useChainAndWalletContext();
  const {
    currency: { isUsd },
  } = usePortfolioContext();
  const { handleBuyClick, handleSellClick } = useBuySellContext();
  const [clickedSubnetId, setClickedSubnetId] = useState<number | null>(null);
  const router = useRouter();

  return (
    <div className='flex flex-col gap-6'>
      {data.map((d) => {
        const isClicked = clickedSubnetId === d.netuid;
        return (
          <Card key={`${d.netuid}-${d.validatorHotkey}`}>
            <div className='flex flex-col gap-6'>
              <SubnetNameDisplay
                subnetName={d.subnetName}
                netuid={d.netuid}
                isClickable
              />
              <div className='flex flex-row items-center gap-4'>
                <Image
                  height={32}
                  width={32}
                  sizes='32px'
                  src={getIconUrl(d.validatorHotkey)}
                  className='rounded-full'
                  quality={100}
                  alt={`${d.validatorName || truncateString(d.validatorHotkey)} icon`}
                />

                <span>
                  <a
                    href={`https://taostats.io/validators/${d.validatorHotkey}`}
                    target='_blank'
                    rel='noopener noreferrer'
                    className='hover:text-accent-1 underline transition-colors duration-1000 hover:no-underline'
                  >
                    {d.validatorName || truncateString(d.validatorHotkey)}
                  </a>
                </span>
              </div>
              <div className='grid grid-cols-3 gap-x-4 gap-y-6'>
                <CardDataDisplay
                  label='α Balance'
                  value={
                    <TaoOrAlphaValueDisplay
                      symbol={d.subnetSymbol}
                      value={d.balanceAsAlpha}
                      valueFormattingOptions={{
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      }}
                      valueTextLevel='labelLarge'
                      iconClassName='text-xs'
                      areDecimalsSmall
                    />
                  }
                />
                <CardDataDisplay
                  label={`${isUsd ? '$' : TAO_CURRENCY} Balance`}
                  value={
                    <TaoAndUsdDisplay
                      taoValue={d.balanceAsTao}
                      usdValue={d.balanceAsUsd}
                      areDecimalsSmall
                      position='start'
                      isUsd={isUsd}
                    />
                  }
                />
                <CardDataDisplay
                  label='% of Stake'
                  value={
                    <BigSmallValueDisplay
                      textLevel='labelLarge'
                      value={d.percentageOfStake}
                      valueFormattingOptions={{
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 2,
                      }}
                      areDecimalsSmall
                      suffix='%'
                    />
                  }
                />

                <CardDataDisplay
                  label='α Earnings'
                  value={
                    <TaoOrAlphaValueDisplay
                      symbol={d.subnetSymbol}
                      value={d.earnedAlpha}
                      valueFormattingOptions={{
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      }}
                      valueTextLevel='labelLarge'
                      iconClassName='text-xs'
                      areDecimalsSmall
                    />
                  }
                />
                <CardDataDisplay
                  label={`${isUsd ? '$' : TAO_CURRENCY} Earnings`}
                  value={
                    <TaoAndUsdDisplay
                      taoValue={d.earnedTao}
                      usdValue={d.earnedUsd}
                      areDecimalsSmall
                      position='start'
                      isUsd={isUsd}
                    />
                  }
                />
                <CardDataDisplay
                  label='% Earnings'
                  value={
                    d.hasTransactionsWithinTimeframe ? (
                      <EmptyCell />
                    ) : (
                      <BigSmallValueDisplay
                        textLevel='labelLarge'
                        value={d.earningsPercentage}
                        valueFormattingOptions={{
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 3,
                        }}
                        areDecimalsSmall
                        suffix='%'
                      />
                    )
                  }
                />
                <CardDataDisplay
                  label='Est. APY'
                  value={
                    d.hasTransactionsWithinTimeframe ? (
                      <EmptyCell />
                    ) : (
                      <BigSmallValueDisplay
                        textLevel='labelLarge'
                        value={d.apy}
                        valueFormattingOptions={{
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 3,
                        }}
                        areDecimalsSmall
                        suffix='%'
                      />
                    )
                  }
                />
              </div>

              <div className='flex flex-row flex-wrap gap-4'>
                <Button
                  className={cn(
                    'flex-1',
                    currentAccount
                      ? 'border-accent-1'
                      : 'border-accent-1/50 text-white/50',
                    'hover:bg-accent-1/20'
                  )}
                  onClick={() => {
                    if (isWalletConnected(currentAccount)) {
                      handleBuyClick(d.netuid, d.validatorHotkey);
                    }
                  }}
                >
                  Buy <BiWallet className='ml-1' />
                </Button>
                <Button
                  className={cn(
                    'flex-1',
                    currentAccount
                      ? 'border-accent-2'
                      : 'border-accent-2/50 text-white/50',
                    'hover:bg-accent-2/20'
                  )}
                  onClick={() => {
                    if (isWalletConnected(currentAccount)) {
                      handleSellClick(d.netuid, d.validatorHotkey);
                    }
                  }}
                >
                  Sell <BiCoinStack className='ml-1' />
                </Button>
                <Button
                  className='flex-1 border-white/40'
                  variant='cta3'
                  disabled={isClicked}
                  onClick={() => {
                    setClickedSubnetId(d.netuid);
                    router.push(`/portfolio/${d.accountId}/${d.netuid}/`);
                  }}
                >
                  Data{' '}
                  {isClicked ? (
                    <Spinner size='sm' className='ml-1' />
                  ) : (
                    <CgArrowRight className='ml-1' />
                  )}
                </Button>
              </div>
            </div>
          </Card>
        );
      })}
    </div>
  );
};

const CardDataDisplay = ({
  label,
  value,
}: {
  label: string;
  value: React.ReactNode;
}) => {
  return (
    <div className='flex flex-col gap-2'>
      <Text level='labelExtraSmall' className='text-white/60'>
        {label}
      </Text>
      {value}
    </div>
  );
};

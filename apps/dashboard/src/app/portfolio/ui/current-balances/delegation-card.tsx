import {
  BigSmallValueDisplay,
  Card,
  Skeleton,
  TaoOrAlphaValueDisplay,
  Text,
} from '@repo/ui/components';

interface DelegationCardProps {
  title: string;
  value: number;
  subValue?: number;
  isFetching?: boolean;
}

export const DelegationCard = ({
  title,
  value,
  subValue,
  isFetching = false,
}: DelegationCardProps) => {
  return (
    <Card>
      <div className='flex flex-col gap-4'>
        <Text
          as='h3'
          level='headerExtraSmall'
          className='text-xs uppercase tracking-[.12em] text-white/60'
        >
          {title}
        </Text>
        <div className='flex flex-row items-baseline gap-2'>
          {isFetching ? (
            <Skeleton className='h-12 w-full' />
          ) : (
            <>
              <TaoOrAlphaValueDisplay
                value={value}
                valueTextLevel='headerLarge'
                valueClassName='font-normal'
                valueFormattingOptions={{
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }}
                iconClassName='text-2xl'
                areDecimalsSmall
              />
              {subValue !== undefined ? (
                <>
                  {/* eslint-disable-next-line react/jsx-no-comment-textnodes -- allowed here */}
                  <Text level='labelLarge' className='text-accent-1'>
                    //
                  </Text>
                  <BigSmallValueDisplay
                    className='font-normal text-white/60'
                    textLevel='headerExtraSmall'
                    suffix='%'
                    value={subValue < 0 ? 0 : subValue}
                    valueFormattingOptions={{
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 2,
                    }}
                  />
                </>
              ) : null}
            </>
          )}
        </div>
      </div>
    </Card>
  );
};

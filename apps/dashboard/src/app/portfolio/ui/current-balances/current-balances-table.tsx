import { memo, useState } from 'react';
import type { SortingState } from '@tanstack/react-table';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
} from '@tanstack/react-table';
import { DataTable } from '@repo/ui/components';
import { useColumns } from './utils';
import type { NormalisedDelegation } from '@/app/portfolio/lib/types';

export const CurrentBalancesTable = memo(function CurrentBalancesTable({
  data,
}: {
  data: NormalisedDelegation[];
}) {
  const [sorting, setSorting] = useState<SortingState>([
    { id: 'holdings-col', desc: false },
  ]);
  const columns = useColumns();
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    state: {
      sorting,
    },
    debugTable: true,
  });
  return <DataTable table={table} hasBorder={false} />;
});

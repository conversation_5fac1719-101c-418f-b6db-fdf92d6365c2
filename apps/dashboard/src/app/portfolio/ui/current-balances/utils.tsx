import { useMemo, useState } from 'react';
import type { ColumnDef } from '@tanstack/react-table';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { BiCoinStack, BiWallet } from 'react-icons/bi';
import { CgArrowRight } from 'react-icons/cg';
import {
  BigSmallValueDisplay,
  Button,
  Spinner,
  SubnetNameDisplay,
  TaoOrAlphaValueDisplay,
} from '@repo/ui/components';
import { notify } from '@repo/ui/lib';
import { usePortfolioContext } from '@/app/portfolio/lib/context';
import type { NormalisedDelegation } from '@/app/portfolio/lib/types';
import { useBuySellContext } from '@/app/portfolio/ui/buy-sell';
import {
  EmptyCellWithTooltip,
  TaoAndUsdDisplay,
} from '@/app/portfolio/ui/shared';
import { useChainAndWalletContext } from '@/contexts/chain-and-wallet/chain-and-wallet-context';
import { TAO_CURRENCY, truncateString } from '@/lib/utils';
import { getIconUrl } from '@/lib/utils/validator-utils';

export const isWalletConnected = (
  currentAccount: ReturnType<
    typeof useChainAndWalletContext
  >['state']['currentAccount']
) => {
  if (!currentAccount) {
    notify.error('No wallet connected. Please connect a wallet to continue.');
    return false;
  }

  return true;
};

export const useColumns = () => {
  const {
    state: { currentAccount },
  } = useChainAndWalletContext();
  const [clickedSubnetId, setClickedSubnetId] = useState<number | null>(null);
  const { handleBuyClick, handleSellClick } = useBuySellContext();
  const {
    currency: { isUsd },
  } = usePortfolioContext();
  const router = useRouter();

  const columns: ColumnDef<NormalisedDelegation>[] = useMemo(
    () => [
      {
        id: 'holdings-col',
        accessorKey: 'netuid',
        header: 'Holdings',
        cell: ({ row }) => {
          const d = row.original;
          return (
            <div className='max-w-44'>
              <SubnetNameDisplay
                subnetName={d.subnetName}
                netuid={d.netuid}
                isClickable
              />
            </div>
          );
        },
      },
      {
        id: 'validator-col',
        accessorKey: 'validatorName',
        header: 'Validator',
        cell: ({ row }) => {
          const d = row.original;
          return (
            <div className='flex flex-row items-center gap-2'>
              <Image
                height={32}
                width={32}
                sizes='32px'
                src={getIconUrl(d.validatorHotkey)}
                className='rounded-full'
                quality={100}
                alt={`${d.validatorName || truncateString(d.validatorHotkey)} icon`}
              />

              <span>
                <a
                  href={`https://taostats.io/validators/${d.validatorHotkey}`}
                  target='_blank'
                  rel='noopener noreferrer'
                  className='hover:text-accent-1 transition-colors duration-500'
                >
                  {d.validatorName || truncateString(d.validatorHotkey)}
                </a>
              </span>
            </div>
          );
        },
      },
      {
        id: 'percentage-of-stake-col',
        accessorKey: 'percentageOfStake',
        header: '% of Stake',
        meta: {
          align: 'end',
        },
        cell: ({ row }) => {
          const d = row.original;
          return (
            <div className='flex justify-end gap-2'>
              <BigSmallValueDisplay
                textLevel='labelLarge'
                value={d.percentageOfStake}
                valueFormattingOptions={{
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 2,
                }}
                areDecimalsSmall
                suffix='%'
              />
            </div>
          );
        },
      },
      {
        id: 'alpha-balance-col',
        accessorKey: 'balanceAsAlpha',
        header: 'α Balance',
        meta: {
          align: 'end',
        },
        cell: ({ row }) => {
          const d = row.original;
          return (
            <div className='flex justify-end gap-2'>
              <TaoOrAlphaValueDisplay
                symbol={d.subnetSymbol}
                value={d.balanceAsAlpha}
                valueFormattingOptions={{
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }}
                valueTextLevel='labelLarge'
                iconClassName='text-xs'
                areDecimalsSmall
              />
            </div>
          );
        },
      },
      {
        id: 'tao-balance-col',
        accessorKey: 'balanceAsTao',
        header: () => `${isUsd ? '$' : TAO_CURRENCY} Balance`,
        meta: {
          align: 'end',
        },
        cell: ({ row }) => {
          const d = row.original;
          return (
            <TaoAndUsdDisplay
              taoValue={d.balanceAsTao}
              usdValue={d.balanceAsUsd}
              areDecimalsSmall
              isUsd={isUsd}
            />
          );
        },
      },
      {
        id: 'alpha-earnings-col',
        accessorKey: 'earnedAlpha',
        header: 'α Earnings',
        meta: {
          align: 'end',
        },
        cell: ({ row }) => {
          const d = row.original;
          return (
            <div className='flex justify-end gap-2'>
              <TaoOrAlphaValueDisplay
                symbol={d.subnetSymbol}
                value={d.earnedAlpha}
                valueFormattingOptions={{
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }}
                valueTextLevel='labelLarge'
                iconClassName='text-xs'
                areDecimalsSmall
              />
            </div>
          );
        },
      },
      {
        id: 'tao-earnings-col',
        accessorKey: 'earnedTao',
        header: () => `${isUsd ? '$' : TAO_CURRENCY} Earnings`,
        meta: {
          align: 'end',
        },
        cell: ({ row }) => {
          const d = row.original;
          return (
            <TaoAndUsdDisplay
              taoValue={d.earnedTao}
              usdValue={d.earnedUsd}
              areDecimalsSmall
              isUsd={isUsd}
            />
          );
        },
      },
      {
        id: 'percentage-earnings-col',
        accessorKey: 'earningsPercentage',
        header: '% Earnings',
        meta: {
          align: 'end',
        },
        cell: ({ row }) => {
          const d = row.original;

          if (d.hasTransactionsWithinTimeframe) {
            return <EmptyCellWithTooltip />;
          }

          return (
            <div className='flex justify-end gap-2'>
              <BigSmallValueDisplay
                textLevel='labelLarge'
                value={d.earningsPercentage}
                valueFormattingOptions={{
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 3,
                }}
                areDecimalsSmall
                suffix='%'
              />
            </div>
          );
        },
      },
      {
        id: 'apy-col',
        accessorKey: 'apy',
        header: 'Est. APY',
        meta: {
          align: 'end',
        },
        cell: ({ row }) => {
          const d = row.original;

          if (d.hasTransactionsWithinTimeframe) {
            return <EmptyCellWithTooltip />;
          }

          return (
            <div className='flex justify-end gap-2'>
              <BigSmallValueDisplay
                textLevel='labelLarge'
                value={d.apy}
                valueFormattingOptions={{
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 2,
                }}
                areDecimalsSmall
                suffix='%'
              />
            </div>
          );
        },
      },
      {
        id: 'actions-col',
        cell: ({ row }) => {
          const d = row.original;
          const isClicked = clickedSubnetId === d.netuid;
          return (
            <div className='flex flex-row justify-center gap-2'>
              <Button
                className={`${currentAccount ? 'border-accent-1' : 'border-accent-1/50 text-white/50'} hover:bg-accent-1/20`}
                onClick={() => {
                  if (isWalletConnected(currentAccount)) {
                    handleBuyClick(d.netuid, d.validatorHotkey);
                  }
                }}
              >
                Buy <BiWallet className='ml-1' />
              </Button>
              <Button
                className={`${currentAccount ? 'border-accent-2' : 'border-accent-2/50 text-white/50'} hover:bg-accent-2/20`}
                onClick={() => {
                  if (isWalletConnected(currentAccount)) {
                    handleSellClick(d.netuid, d.validatorHotkey);
                  }
                }}
              >
                Sell <BiCoinStack className='ml-1' />
              </Button>
              <Button
                variant='cta3'
                className='border-white/40'
                disabled={isClicked}
                onClick={() => {
                  setClickedSubnetId(d.netuid);
                  router.push(`/portfolio/${d.accountId}/${d.netuid}/`);
                }}
              >
                Data{' '}
                {isClicked ? (
                  <Spinner size='sm' className='ml-1' />
                ) : (
                  <CgArrowRight className='ml-1' />
                )}
              </Button>
            </div>
          );
        },
      },
    ],
    [
      clickedSubnetId,
      currentAccount,
      handleBuyClick,
      handleSellClick,
      isUsd,
      router,
    ]
  );

  return columns;
};

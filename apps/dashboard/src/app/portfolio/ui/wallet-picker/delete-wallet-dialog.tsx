import { useEffect } from 'react';
import { BiTrash } from 'react-icons/bi';
import type { PortfolioWallet } from '@repo/types/dashboard-api-types';
import { <PERSON><PERSON>, But<PERSON>, Dialog, Spinner, Text } from '@repo/ui/components';
import { notify } from '@repo/ui/lib';
import { useDeletePortfolioWallet } from '@/app/portfolio/lib/query-hooks';
import { WalletText } from '@/app/portfolio/ui/shared';

export const DeleteWalletDialog = ({
  walletToDelete,
  clearWalletToDelete,
}: {
  walletToDelete: PortfolioWallet | null;
  clearWalletToDelete: () => void;
}) => {
  const deleteWallet = useDeletePortfolioWallet();
  const handleDeleteWallet = () => {
    if (!walletToDelete) {
      notify.error('Did not receive a wallet to delete');
      return;
    }

    deleteWallet.mutate(
      { walletId: walletToDelete.walletId },
      {
        onSuccess: () => {
          notify.success('Wallet deleted');
          clearWalletToDelete();
        },
      }
    );
  };

  useEffect(() => {
    if (walletToDelete === null) {
      deleteWallet.reset();
    }
  }, [walletToDelete, deleteWallet]);

  return (
    <Dialog
      HeaderIcon={BiTrash}
      closeFn={clearWalletToDelete}
      isOpen={walletToDelete !== null}
      title='Delete Wallet'
    >
      {walletToDelete ? (
        <div className='flex flex-col gap-6'>
          <Text as='p' level='bodyMedium'>
            You&apos;re going to delete the wallet:
          </Text>

          <div className='bg-gray-highlight flex items-center gap-2 rounded-md border border-white/10 p-3'>
            <span className='truncate text-center'>
              <WalletText
                name={walletToDelete.name}
                address={walletToDelete.address}
              />
            </span>
          </div>

          <Alert
            type='warning'
            description='Please note: This cannot be undone. Are you sure you want to proceed?'
          />
        </div>
      ) : (
        <div className='flex justify-center py-4'>
          <Spinner />
        </div>
      )}

      <div className='flex flex-col gap-4'>
        <Button
          className='group w-full hover:bg-red-500 hover:text-white'
          disabled={!walletToDelete || deleteWallet.isPending}
          onClick={handleDeleteWallet}
          size='lg'
          type='button'
          variant='default'
        >
          <BiTrash className='mr-2 h-5 w-5 text-red-500 group-hover:text-white' />
          Yes, Delete Wallet
        </Button>
        {deleteWallet.isError ? (
          <Alert
            type='error'
            description='An error occurred while deleting the wallet.'
            title='Error'
          />
        ) : null}
      </div>
    </Dialog>
  );
};

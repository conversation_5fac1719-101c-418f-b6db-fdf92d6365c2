import { useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import type { SubmitHandler } from 'react-hook-form';
import { Controller, useForm } from 'react-hook-form';
import { BiCheck, BiEnvelopeOpen, BiPlus, BiWallet } from 'react-icons/bi';
import { z } from 'zod';
import {
  AlertError,
  Spinner,
  Button,
  Input,
  Dialog,
} from '@repo/ui/components';
import { notify } from '@repo/ui/lib';
import { useExistingWalletCheck } from '@/app/portfolio/lib/hooks';
import { useAddPortfolioWallet } from '@/app/portfolio/lib/query-hooks';

type CreateWalletDialogProps = {
  closeFn: () => void;
  isOpen: boolean;
  address: string | null;
};

export const CreateWalletDialog = ({
  closeFn,
  isOpen,
  address,
}: CreateWalletDialogProps) => {
  return (
    <Dialog
      HeaderIcon={BiWallet}
      closeFn={closeFn}
      isOpen={isOpen}
      title='Name your wallet'
    >
      {address ? (
        <Form address={address} closeFn={closeFn} />
      ) : (
        <div className='flex justify-center py-4'>
          <Spinner />
        </div>
      )}
    </Dialog>
  );
};

const formId = 'change-rate-limit-form';

const walletNameSchema = z.object({
  walletName: z
    .string({
      required_error: 'Wallet name is required',
    })
    .min(1, 'Wallet name is required'),
});

type WalletNameSchema = z.infer<typeof walletNameSchema>;

type WalletNameDialogFormProps = Pick<CreateWalletDialogProps, 'closeFn'> & {
  address: string; // Not nullable within the form
};

const Form = ({ address, closeFn }: WalletNameDialogFormProps) => {
  const { doesWalletExist } = useExistingWalletCheck();

  const form = useForm<WalletNameSchema>({
    resolver: zodResolver(walletNameSchema),
    defaultValues: {
      walletName: '',
    },
  });

  useEffect(() => {
    setTimeout(() => {
      form.setFocus('walletName');
    }, 100);
  }, [form]);

  const addWallet = useAddPortfolioWallet();
  const handleWalletCreate: SubmitHandler<WalletNameSchema> = async (data) => {
    const isValid = await form.trigger();
    if (!isValid) {
      return;
    }

    if (!address) {
      notify.error('Could not find address. Please try later.');
      return;
    }

    if (doesWalletExist(address)) {
      return;
    }

    const { walletName } = data;
    addWallet.mutate(
      { address, name: walletName },
      {
        onSuccess: () => {
          notify.success('Wallet added');
          closeFn();
        },
      }
    );
  };
  return (
    <div className='flex flex-col gap-12'>
      <form
        id={formId}
        onSubmit={(e) => {
          void form.handleSubmit(handleWalletCreate)(e);
        }}
      >
        <Controller
          control={form.control}
          name='walletName'
          render={({ field }) => (
            <Input
              IconLeft={BiEnvelopeOpen}
              IconRight={field.value ? BiCheck : undefined}
              error={form.formState.errors.walletName?.message}
              forwardRef={field.ref}
              inputId='walletName'
              placeholder='Wallet Name'
              onChange={(e) => {
                field.onChange(e.target.value);
              }}
              value={field.value}
            />
          )}
        />
      </form>

      <div className='flex flex-col gap-4'>
        <Button
          variant='cta2'
          className='flex w-full items-center justify-center gap-2'
          disabled={addWallet.isPending}
          form={formId}
          size='lg'
          type='submit'
        >
          Add Wallet
          {addWallet.isPending ? <Spinner /> : <BiPlus size={20} />}
        </Button>
        {addWallet.isError ? (
          <AlertError description={addWallet.error.message} />
        ) : null}
      </div>
    </div>
  );
};

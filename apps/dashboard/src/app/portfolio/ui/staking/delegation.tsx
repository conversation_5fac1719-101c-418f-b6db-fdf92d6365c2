'use client';

import { useMemo, useState } from 'react';
import type { PaginationState } from '@tanstack/react-table';
import { AlertError, Skeleton } from '@repo/ui/components';
import { StakingEventTable } from './staking-event-table';
import { usePortfolioContext } from '@/app/portfolio/lib/context';
import { useAccountStakeDetailedQuery } from '@/app/portfolio/lib/query-hooks';

export const Delegation = () => {
  const { walletAddress, dateRangeSelected } = usePortfolioContext();
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 100,
  });
  const [amountMin, setAmountMin] = useState<string>('ALL');
  const [subnetId, setSubnetId] = useState<string>('ALL');

  // Convert dateRangeSelected to start and end timestamps
  const { timestampStart, timestampEnd } = useMemo(() => {
    const now = Math.floor(Date.now() / 1000); // Current timestamp in seconds

    // Get the target date based on the range
    const targetDate = new Date(now * 1000); // Convert to milliseconds for Date object
    switch (dateRangeSelected) {
      case '1d':
        targetDate.setDate(targetDate.getDate() - 1);
        break;
      case '1w':
        targetDate.setDate(targetDate.getDate() - 7);
        break;
      case '1m':
        targetDate.setDate(targetDate.getDate() - 30);
        break;
      case '1y':
        targetDate.setDate(targetDate.getDate() - 365);
        break;
    }

    // Set time to start of day (midnight)
    targetDate.setHours(0, 0, 0, 0);

    // Create end date and set to end of current day
    const endDate = new Date();
    endDate.setHours(23, 59, 59, 999);

    return {
      timestampEnd: Math.floor(endDate.getTime() / 1000).toString(),
      timestampStart: Math.floor(targetDate.getTime() / 1000).toString(),
    };
  }, [dateRangeSelected]);

  const { isLoading, isSuccess, isError, error, data } =
    useAccountStakeDetailedQuery({
      walletAddress,
      pagination,
      amountMin,
      subnetId,
      timestampStart,
      timestampEnd,
    });
  const delegations = useMemo(() => data?.data || [], [data]);

  return (
    <>
      {isLoading ? (
        <div className='flex flex-col gap-4 '>
          <Skeleton className='h-16 w-full' />
          <Skeleton className='h-16 w-full' />
          <Skeleton className='h-16 w-full' />
        </div>
      ) : null}

      {isSuccess ? (
        <StakingEventTable
          delegationEvents={delegations}
          total={delegations.length}
          pagination={pagination}
          setPagination={setPagination}
          amountMin={amountMin}
          subnetId={subnetId}
          setAmountMin={setAmountMin}
          setSubnetId={setSubnetId}
        />
      ) : null}

      {isError ? (
        <AlertError
          className='lg:w-[575px]'
          description={error.message}
          title='Error fetching Delegation Events'
        />
      ) : null}
    </>
  );
};

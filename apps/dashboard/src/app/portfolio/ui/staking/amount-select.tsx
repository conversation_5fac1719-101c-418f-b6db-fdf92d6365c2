import { useCallback, useState } from 'react';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Text,
} from '@repo/ui/components';
import { cn } from '@repo/ui/lib';

export const AmountSelect = ({
  defaultSelect = 'ALL',
  placeholder,
  onChange,
}: {
  defaultSelect?: string;
  placeholder?: string;
  onChange?: (value: string) => void;
}) => {
  const [selectValue, setSelectValue] = useState<string>(defaultSelect);

  const selectOptions = [
    { label: '> 100k', value: '100000000000000' },
    { label: '> 50k', value: '50000000000000' },
    { label: '> 10k', value: '10000000000000' },
    { label: '> 5k', value: '5000000000000' },
    { label: '> 1k', value: '1000000000000' },
    { label: '> 500', value: '500000000000' },
    { label: '> 100', value: '100000000000' },
    { label: 'ALL', value: 'ALL' },
  ];

  const handleValueChange = useCallback(
    (value: string) => {
      setSelectValue(value);
      if (onChange) {
        onChange(value);
      }
    },
    [onChange]
  );

  return (
    <div className='ml-1 flex w-full flex-col gap-1'>
      <Text level='xs' className='text-[#939393]'>
        Amount
      </Text>
      <Select value={selectValue} onValueChange={handleValueChange}>
        <SelectTrigger
          className={cn(
            'h-9 w-28 text-xs !outline-none !ring-0',
            selectValue && selectValue.length > 0
              ? 'text-white'
              : 'text-neutral-500'
          )}
        >
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent className='border-[#404040] bg-[#141414] text-sm'>
          <SelectGroup>
            {selectOptions.map(({ label, value }) => (
              <SelectItem value={value} key={label}>
                {label}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  );
};

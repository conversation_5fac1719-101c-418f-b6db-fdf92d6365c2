'use client';

import { useMemo } from 'react';
import type { ColumnDef } from '@tanstack/react-table';
import { BigNumber } from 'bignumber.js';
import { formatDistanceToNow, format } from 'date-fns';
import { BiLinkExternal } from 'react-icons/bi';
import type { Delegate } from '@repo/types/website-api-types';
import {
  Badge,
  CopyButton,
  Link,
  SubnetNameDisplay,
  TaoOrAlphaValueDisplay,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@repo/ui/components';
import { getHumanValueFromChainValue } from '@repo/ui/lib';
import { usePortfolioContext } from '@/app/portfolio/lib/context';
import { useSubnetLookup, useValidatorLookup } from '@/lib/hooks/global-hooks';
import { truncateString } from '@/lib/utils';

const taoAndAlphaFormat: Intl.NumberFormatOptions = {
  minimumFractionDigits: 2,
  maximumFractionDigits: 6,
};

const usdFormat: Intl.NumberFormatOptions = {
  minimumFractionDigits: 2,
  maximumFractionDigits: 2,
};

export const colours = {
  buyRowColourClassName: 'text-accent-1',
  sellRowColourClassName: 'text-accent-2',
  transferInRowColourClassName: 'text-yellow-600',
  transferOutRowColourClassName: 'text-orange-600',
};

export const getRowColourClassName = (action: string, isTransfer = false) => {
  if (isTransfer) {
    return action === 'DELEGATE'
      ? colours.transferInRowColourClassName
      : colours.transferOutRowColourClassName;
  }

  return action === 'DELEGATE'
    ? colours.buyRowColourClassName
    : colours.sellRowColourClassName;
};

export const useColumns = () => {
  const {
    currency: { isUsd },
  } = usePortfolioContext();
  const { getSubnetName, getSubnetSymbol } = useSubnetLookup();
  const { getValidatorName } = useValidatorLookup();
  const columns: ColumnDef<Delegate>[] = useMemo(
    () => [
      {
        accessorKey: 'timestamp',
        header: 'Time',
        cell: ({ row }) => {
          const timestamp = row.original.timestamp;
          const dt = new Date(timestamp);
          return (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <span className='text-white'>
                    {formatDistanceToNow(dt, {
                      addSuffix: true,
                    })}
                  </span>
                </TooltipTrigger>
                <TooltipContent>
                  <span>{format(dt, 'MMM d yyyy, HH:mm')}</span>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          );
        },
      },
      {
        accessorKey: 'action',
        header: 'Type',
        cell: ({ row }) => {
          const { action, is_transfer: isTransfer } = row.original;
          return (
            <Badge
              variant={
                isTransfer
                  ? 'yellow2'
                  : action === 'DELEGATE'
                    ? 'green4'
                    : 'red2'
              }
            >
              {isTransfer ? 'Transfer' : action === 'DELEGATE' ? 'Buy' : 'Sell'}
            </Badge>
          );
        },
      },
      {
        accessorKey: 'netuid',
        header: 'Subnet',
        cell: ({ row }) => {
          const netuid =
            row.original.netuid !== null ? row.original.netuid : null;
          if (netuid === null) {
            return null;
          }
          const subnetName = getSubnetName(netuid);
          return subnetName ? (
            <SubnetNameDisplay
              netuid={netuid}
              subnetName={subnetName}
              isClickable
            />
          ) : null;
        },
      },
      {
        accessorKey: 'delegate',
        header: 'Validator',
        cell: ({ row }) => {
          const delegate = row.original.delegate;
          const validatorName = getValidatorName(delegate.ss58);
          return (
            <a
              href={`https://taostats.io/validators/${delegate.ss58}`}
              target='_blank'
              rel='noreferrer'
            >
              {validatorName}{' '}
              <BiLinkExternal className='ml-1 inline text-white' />
            </a>
          );
        },
      },
      {
        accessorKey: 'alpha',
        header: 'Alpha',
        meta: {
          align: 'end',
        },
        cell: ({ row }) => {
          const {
            action,
            netuid,
            alpha,
            is_transfer: isTransfer,
          } = row.original;
          if (netuid === null || alpha === null) {
            return null;
          }
          return (
            <TaoOrAlphaValueDisplay
              className='justify-end'
              symbol={getSubnetSymbol(netuid)}
              value={getHumanValueFromChainValue(BigNumber(alpha).toNumber())}
              valueFormattingOptions={taoAndAlphaFormat}
              valueTextLevel='labelLarge'
              colourClassName={getRowColourClassName(action, isTransfer)}
            />
          );
        },
      },
      {
        accessorKey: 'tao',
        header: isUsd ? 'USD' : 'Tao',
        meta: {
          align: 'end',
        },
        cell: ({ row }) => {
          const {
            action,
            netuid,
            amount,
            usd,
            is_transfer: isTransfer,
          } = row.original;
          if (netuid === null) {
            return null;
          }
          return (
            <TaoOrAlphaValueDisplay
              className='justify-end'
              symbol={isUsd ? '$' : undefined}
              value={
                isUsd
                  ? BigNumber(usd ?? 0).toNumber()
                  : getHumanValueFromChainValue(BigNumber(amount).toNumber())
              }
              valueFormattingOptions={isUsd ? usdFormat : taoAndAlphaFormat}
              valueTextLevel='labelLarge'
              colourClassName={getRowColourClassName(action, isTransfer)}
              iconClassName='text-xs'
            />
          );
        },
      },
      {
        accessorKey: 'alpha_price_in_tao',
        header: isUsd ? 'USD Price' : 'Tao Price',
        meta: {
          align: 'end',
        },
        cell: ({ row }) => {
          const {
            action,
            is_transfer: isTransfer,
            netuid,
            alpha_price_in_tao: alphaPriceInTao,
            alpha_price_in_usd: alphaPriceInUsd,
          } = row.original;
          if (netuid === null || alphaPriceInTao === null) {
            return null;
          }
          return (
            <TaoOrAlphaValueDisplay
              className='justify-end'
              symbol={isUsd ? '$' : undefined}
              value={
                isUsd
                  ? BigNumber(alphaPriceInUsd ?? 0).toNumber()
                  : BigNumber(alphaPriceInTao).toNumber()
              }
              valueFormattingOptions={isUsd ? usdFormat : taoAndAlphaFormat}
              valueTextLevel='labelLarge'
              colourClassName={getRowColourClassName(action, isTransfer)}
              iconClassName='text-[10px]'
            />
          );
        },
      },
      {
        accessorKey: 'tx_account',
        header: 'Transfer',
        cell: ({ row }) => {
          const { transfer_address: transferAddress, is_transfer: isTransfer } =
            row.original;
          if (!transferAddress || !isTransfer) {
            return null;
          }

          return (
            <span className='flex flex-row gap-1'>
              <Link
                href={`https://taostats.io/account/${transferAddress.ss58}`}
                target='_blank'
                rel='noreferrer'
              >
                {truncateString(transferAddress.ss58, 4)}
              </Link>
              <CopyButton
                value={transferAddress.ss58}
                className='text-white/50'
                size={16}
              />
            </span>
          );
        },
      },
      {
        accessorKey: 'extrinsic_id',
        header: 'TXN',
        cell: ({ row }) => {
          const { extrinsic_id: extrinsicId } = row.original;
          return (
            <Link
              className='bg-hero-primary border-primary-button-border flex w-fit rounded-md border p-2 '
              href={`https://taostats.io/extrinsic/${extrinsicId}`}
              target='_blank'
              rel='noreferrer'
            >
              <BiLinkExternal className='size-4 text-white' />
            </Link>
          );
        },
      },
    ],
    [getSubnetName, getSubnetSymbol, getValidatorName, isUsd]
  );

  return columns;
};

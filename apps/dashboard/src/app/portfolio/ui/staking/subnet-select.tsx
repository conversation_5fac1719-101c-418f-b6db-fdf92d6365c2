import { useCallback, useMemo, useState } from 'react';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Text,
} from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { useSubnets } from '@/lib/hooks/global-hooks';

export const SubnetSelect = ({
  defaultSelect = 'ALL',
  placeholder,
  onChange,
}: {
  defaultSelect?: string;
  placeholder?: string;
  onChange?: (value: string) => void;
}) => {
  const [selectValue, setSelectValue] = useState<string>(defaultSelect);
  const { data: subnetData } = useSubnets();

  const selectOptions = useMemo(() => {
    const options =
      subnetData?.data.map((item) => ({
        label: `Subnet ${item.netuid}`,
        value: `${item.netuid}`,
      })) ?? [];

    options.unshift({ label: 'ALL', value: 'ALL' });

    return options;
  }, [subnetData?.data]);

  const handleValueChange = useCallback(
    (value: string) => {
      setSelectValue(value);
      if (onChange) {
        onChange(value);
      }
    },
    [onChange]
  );

  return (
    <div className='ml-1 flex w-full flex-col gap-1'>
      <Text level='xs' className='text-[#939393]'>
        Subnet
      </Text>
      <Select value={selectValue} onValueChange={handleValueChange}>
        <SelectTrigger
          className={cn(
            'h-9 w-28 text-xs !outline-none !ring-0',
            selectValue && selectValue.length > 0
              ? 'text-white'
              : 'text-neutral-500'
          )}
        >
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent className='border-[#404040] bg-[#141414] text-sm'>
          <SelectGroup>
            {selectOptions.map(({ label, value }) => (
              <SelectItem value={value} key={label}>
                {label}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  );
};

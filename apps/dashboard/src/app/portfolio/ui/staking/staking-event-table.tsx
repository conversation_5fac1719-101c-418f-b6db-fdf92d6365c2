import { useMemo, type Dispatch, type SetStateAction } from 'react';
import type { PaginationState, Row } from '@tanstack/react-table';
import {
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table';
import type { Delegate } from '@repo/types/website-api-types';
import { Alert, DataTable, TablePagination } from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { AmountSelect } from './amount-select';
import { getRowColourClassName, useColumns } from './config';
import { SubnetSelect } from './subnet-select';

type DelegationListProps = {
  delegationEvents: Delegate[];
  total: number;
  pagination: PaginationState;
  setPagination: Dispatch<SetStateAction<PaginationState>>;
  amountMin: string;
  subnetId: string;
  setAmountMin: Dispatch<SetStateAction<string>>;
  setSubnetId: Dispatch<SetStateAction<string>>;
};

const getRowClassName = (row: Row<Delegate>) => {
  const { action, is_transfer: isTransfer } = row.original;
  const rowColourClassName = getRowColourClassName(action, isTransfer);
  return cn(rowColourClassName, 'bg-[#26262633]');
};

export const StakingEventTable = ({
  delegationEvents,
  pagination,
  setPagination,
  amountMin,
  subnetId,
  setAmountMin,
  setSubnetId,
  total,
}: DelegationListProps) => {
  const columns = useColumns();
  const data = useMemo(() => delegationEvents, [delegationEvents]);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onPaginationChange: setPagination,
    manualPagination: true,
    rowCount: total,
    enableSorting: false,
    state: {
      pagination,
    },
    debugTable: true,
  });

  return (
    <div className='container flex flex-col gap-2'>
      <div className='flex flex-col justify-between gap-4 sm:flex-row'>
        <div className='flex flex-row gap-2'>
          <SubnetSelect
            defaultSelect={subnetId}
            onChange={(e) => {
              setSubnetId(e);
            }}
          />
          <AmountSelect
            defaultSelect={amountMin}
            onChange={(e) => {
              setAmountMin(e);
            }}
          />
        </div>
        <div className='flex flex-row justify-end gap-4'>
          <TablePagination table={table} total={total} />
        </div>
      </div>

      {data.length > 0 ? (
        <DataTable
          hasBorder={false}
          table={table}
          tableClassName='[&_.buy-row]:text-accent-1 [&_.sell-row]:text-accent-2 [&_.transfer-row]:text-[#ca8a04]'
          rowClassName={getRowClassName}
          hasRoundedRows
        />
      ) : (
        <Alert
          type='info'
          title='No data found'
          description='No data found for the selected filters'
        />
      )}
    </div>
  );
};

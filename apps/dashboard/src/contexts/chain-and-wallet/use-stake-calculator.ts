import type { ApiPromise } from '@polkadot/api';
import { BigNumber } from 'bignumber.js';
import { notify } from '@repo/ui/lib';
import { useChainAndWalletContext } from './chain-and-wallet-context';
import type { StakeTransactionSummary } from './types';
import { log } from '@/lib/utils';

type RuntimeCallResponse<T> = {
  toJSON: () => T;
};

type GetStakeFeeFn<T> = (
  originHotkeyAndNetuid: [string, number] | null,
  originColdkey: string,
  destinationHotkeyAndNetuid: [string, number] | null,
  destinationColdkey: string,
  amount: number
) => Promise<RuntimeCallResponse<T>>;

interface StakeInfoRuntimeApi<T> {
  getStakeFee: GetStakeFeeFn<T>;
}

interface ExtendedApiPromise extends ApiPromise {
  call: ApiPromise['call'] & {
    stakeInfoRuntimeApi?: StakeInfoRuntimeApi<number>;
  };
}

const defaultStakeFeeResult = { stakingFees: [], stakingFeesTotal: 0 };

export const useStakeFeeCalculator = () => {
  const {
    state: { api: chainApi },
    isConnectedToChain,
  } = useChainAndWalletContext();

  const calculateStakeFee = async (
    calculateStakeFeeParams: StakeTransactionSummary[]
  ) => {
    try {
      log('DEBUG', {
        calculateStakeFeeParams,
      });

      if (!isConnectedToChain) {
        log('🚩 Not connected to chain');
        return defaultStakeFeeResult;
      }

      const api = chainApi as ExtendedApiPromise | null;
      if (!api) {
        log('🚩 No api available');
        return defaultStakeFeeResult;
      }

      if (!api.call.stakeInfoRuntimeApi) {
        log('🚩 No stakeInfoRuntimeApi available');
        return defaultStakeFeeResult;
      }

      // Store the reference after the check
      const stakeInfoRuntimeApi = api.call.stakeInfoRuntimeApi;

      /* 
    getStakeFee(
      [originHotkey, originNetuid], 
      originColdkey, 
      [destinationHotkey, destinationNetuid], 
      destinationColdkey, 
      amount
    )
    
    `getStakeFee` covers cases: 
    - add stake
    - remove stake
    - move stake (i.e. change hotkey)
    - transfer stake (i.e. change coldkey)

    Note: `getStakeFee` also covers swap stake (i.e. change netuid), which can be done via the moveStake and transferStake functions, but we don't yet offer those options via the UI, so they are not included in the `calculateStakeFeeParams` type and they are not covered by the feePromises below yet.

    For more info on the getStakeFee method, see: https://github.com/opentensor/subtensor/blob/6b86ebf30d3fb83f9d43ed4ce713c43204394e67/pallets/subtensor/src/tests/staking2.rs#L819
    */

      const feePromises = calculateStakeFeeParams
        .map((stakeFeeParam) => {
          switch (stakeFeeParam.type) {
            case 'add_stake':
              return stakeInfoRuntimeApi.getStakeFee(
                null,
                stakeFeeParam.originColdkey,
                [
                  stakeFeeParam.destinationHotkey,
                  stakeFeeParam.destinationNetuid,
                ],
                stakeFeeParam.originColdkey,
                stakeFeeParam.taoAmount
              );

            case 'remove_stake':
              return stakeInfoRuntimeApi.getStakeFee(
                [
                  stakeFeeParam.removeFromHotkey,
                  stakeFeeParam.removeFromNetuid,
                ],
                stakeFeeParam.originColdkey,
                null,
                stakeFeeParam.originColdkey,
                stakeFeeParam.alphaAmount
              );

            case 'move_stake':
              return stakeInfoRuntimeApi.getStakeFee(
                [stakeFeeParam.originHotkey, stakeFeeParam.originNetuid],
                stakeFeeParam.originColdkey,
                [stakeFeeParam.destinationHotkey, stakeFeeParam.originNetuid],
                stakeFeeParam.originColdkey,
                stakeFeeParam.alphaAmount
              );

            case 'transfer_stake':
              return stakeInfoRuntimeApi.getStakeFee(
                [stakeFeeParam.hotkey, stakeFeeParam.netuid],
                stakeFeeParam.originColdkey,
                [stakeFeeParam.hotkey, stakeFeeParam.netuid],
                stakeFeeParam.destinationColdkey,
                stakeFeeParam.alphaAmount
              );

            default:
              return null;
          }
        })
        .filter((promise) => promise !== null);

      const getStakeFeeResponses = await Promise.all(feePromises);
      const stakingFees = getStakeFeeResponses.map((feeResult) => {
        const fee = feeResult?.toJSON() ?? 0;
        return fee !== 0 ? BigNumber(fee).shiftedBy(-9).toNumber() : 0;
      });

      log('DEBUG', { stakingFees });

      const stakingFeesTotal = stakingFees
        .reduce((acc, fee) => acc.plus(fee), BigNumber(0))
        .dp(5, BigNumber.ROUND_UP) // marginally overestimate the fee to be safe
        .toNumber();

      return { stakingFees, stakingFeesTotal };
    } catch (error) {
      log('🚩 Error calculating stake fee', { error });
      notify.error('Error calculating stake fee');
      return defaultStakeFeeResult;
    }
  };

  return { calculateStakeFee };
};

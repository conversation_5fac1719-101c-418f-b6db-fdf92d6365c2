import { useState } from 'react';
import type { SubmittableResult } from '@polkadot/api';
import type { SubmittableExtrinsic } from '@polkadot/api/types';
import { BigNumber } from 'bignumber.js';
import { notify } from '@repo/ui/lib';
import type { HandleSubmitTransactionParams } from './types';
import { useAccountContext } from '@/contexts/chain-and-wallet/account-context';
import {
  useChainAndWalletContext,
  useTransactionErrorDecoder,
} from '@/contexts/chain-and-wallet/chain-and-wallet-context';
import { RATE_LIMIT_ERROR_MESSAGE } from '@/contexts/chain-and-wallet/constants';
import { log } from '@/lib/utils';

type TxStatus = SubmittableResult['status']['type'] | 'Success' | 'Error';

type TxStatusWithMessage = {
  status: TxStatus;
  message: string;
};

// Helper to create a tx status object with message
const _tx = (status: TxStatus, message = '') => {
  return {
    status,
    message,
  };
};

export const useTransactionSubmitter = () => {
  const {
    state: { currentAccount, currentSigner },
  } = useChainAndWalletContext();
  const { fetchInfo } = useAccountContext();
  const { decodeExtrinsicFailedEvent, decodeError } =
    useTransactionErrorDecoder();

  const [isPending, setIsPending] = useState(false);
  const [txStatus, setTxStatus] = useState<TxStatusWithMessage | null>(null);

  const txResHandler = async (
    res: SubmittableResult,
    {
      doFullRefetchOnSuccess = true,
      onSuccessCallback,
    }: HandleSubmitTransactionParams
  ) => {
    const { status, events, dispatchError } = res;

    setTxStatus(_tx(status.type));
    if (status.isFinalized) {
      events.forEach((e) => {
        log('🎉', { e, method: e.event.method.toString() });
      });

      const extrinsicFailedEvent = events.find(
        (e) => e.event.method.toString() === 'ExtrinsicFailed'
      );
      if (extrinsicFailedEvent !== undefined) {
        if (dispatchError) {
          const decodedError = decodeExtrinsicFailedEvent(dispatchError);
          setTxStatus(
            _tx(
              'Error',
              decodedError.name === 'StakeRateLimitExceeded'
                ? RATE_LIMIT_ERROR_MESSAGE
                : (decodedError.docs as string)
            )
          );
        } else {
          setTxStatus(_tx('Error', ''));
        }
      } else {
        setTxStatus(_tx('Success'));
        notify.success('🎉 Transaction successful');

        if (doFullRefetchOnSuccess) {
          await fetchInfo();
        }

        if (onSuccessCallback) {
          onSuccessCallback();
        }
      }

      setIsPending(false);
    }
  };

  const txErrHandler = (err: unknown) => {
    try {
      const getErrorData = decodeError(err);
      if (getErrorData.name === 'Cancelled') {
        setIsPending(false);
        return;
      }
      setIsPending(false);
      setTxStatus(_tx('Error', getErrorData.docs as string));
    } catch (error) {
      console.error('Error handling transaction error:', error);
      setIsPending(false);
      setTxStatus(
        _tx(
          'Error',
          'An unexpected error occurred while processing the transaction error.'
        )
      );
    }
  };

  const submitTx = async (
    tx: SubmittableExtrinsic<'promise'> | null,
    params: HandleSubmitTransactionParams
  ) => {
    if (!tx) {
      notify.error('Could not build transaction to submit');
      return;
    }

    if (!currentAccount) {
      notify.error('No current account');
      return;
    }

    // reset tx status
    setTxStatus(null);
    setIsPending(true);

    const address = currentAccount.address;
    await tx
      .signAndSend(
        address,
        {
          signer: currentSigner,
          withSignedTransaction: true,
          // Having withSignedTransaction set to true causes an error with talisman on testnet
          // However not having this set to true may be causing talisman with ledger users to fail in production
          // So enabling it here to test if it helps the users with talisman with ledger users
          // withSignedTransaction: currentAccount.meta.source !== 'talisman',
        },
        (res) => txResHandler(res, params)
      )
      .catch(txErrHandler);
  };

  const calculateTransactionFee = async (
    tx: SubmittableExtrinsic<'promise'>
  ) => {
    if (!currentAccount) {
      notify.error(
        'No current account found. Can not calculate transaction fee.'
      );
      return null;
    }

    try {
      const info = await tx.paymentInfo(currentAccount.address);
      const totalFeeAsString = info.partialFee.toString();
      const feeInTao = BigNumber(totalFeeAsString).shiftedBy(-9).toNumber();
      return feeInTao;
    } catch (error) {
      console.error('Error calculating transaction fee:', error);
      return null;
    }
  };

  return {
    calculateTransactionFee,
    submitTx,
    isPending,
    txStatus,
  };
};

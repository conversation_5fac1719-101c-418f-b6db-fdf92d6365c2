import { useCallback } from 'react';
import { BigNumber } from 'bignumber.js';
import type { SubnetAmounts } from './types';
import { useAccountContext } from '@/contexts/chain-and-wallet/account-context';
import type { ChainConvertFormat } from '@/lib/utils/conversion/conversion-types';
import { chainConvertFormat } from '@/lib/utils/conversion/conversion-utils';

function getAlphaFromTaoWithSlippageApplied(
  pool: SubnetAmounts,
  tao: BigNumber
) {
  const taoReserves = BigNumber(pool.tao);
  const alphaReserves = BigNumber(pool.alpha);
  const k = alphaReserves.multipliedBy(taoReserves);
  const denominator = taoReserves.plus(tao);
  const alpha = alphaReserves.minus(k.dividedBy(denominator));

  return alpha;
}

export const useSlippageCalculator = () => {
  const {
    state: { subnetAmountsMap },
  } = useAccountContext();

  const slippageCalculator = useCallback(
    (netuid: number, taoAmount: number) => {
      const subnetAmounts = subnetAmountsMap[netuid];

      if (typeof subnetAmounts === 'undefined') {
        return null;
      }

      if (netuid === 0 || taoAmount === 0) {
        return {
          slippage: '0',
        };
      }

      const tao = BigNumber(taoAmount).shiftedBy(9);
      const alpha = getAlphaFromTaoWithSlippageApplied(subnetAmounts, tao);
      const expectedAlpha = tao.dividedBy(subnetAmounts.price);
      const slippageRaw = expectedAlpha.minus(alpha).dividedBy(expectedAlpha);

      const result = {
        slippage: slippageRaw.multipliedBy(100).toFixed(3),
      };

      return result;
    },
    [subnetAmountsMap]
  );

  return { slippageCalculator };
};

// This hook is used to convert tao to alpha and vice versa
// It uses a lookup based on the current user's connected account
// If you need to convert taa/alpha outside of a user's account context, use
export const useTaoToAlphaConverterWithChainData = () => {
  const {
    state: { subnetAmountsMap },
  } = useAccountContext();

  // Converts a tao amount to an alpha amount
  // If inputFormat is human, the taoAmount is in human readable format e.g. 5.2
  // If inputFormat is chain, the taoAmount is in chain format e.g. 5.2 * 1_000_000_000 = 5,200,000,000
  // If outputFormat is human, the alphaAmount is in human readable format e.g. 4.02
  // If outputFormat is chain, the alphaAmount is in chain format e.g. 4.02 * 1_000_000_000 = 4,020,000,000
  const convertTaoToAlpha = useCallback(
    (
      netuid: number,
      taoAmount: number,
      {
        inputFormat,
        outputFormat,
      }: ChainConvertFormat = chainConvertFormat.hToC
    ) => {
      const getSubnetAmounts = subnetAmountsMap[netuid];
      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition -- This is a valid check
      if (!getSubnetAmounts) {
        return BigNumber(0);
      }

      const { price } = getSubnetAmounts;

      const taoChainFormat =
        inputFormat === 'human'
          ? BigNumber(taoAmount).shiftedBy(9)
          : BigNumber(taoAmount);
      const alphaChain = taoChainFormat.dividedBy(price);
      return outputFormat === 'human'
        ? alphaChain.shiftedBy(-9)
        : alphaChain.integerValue(BigNumber.ROUND_FLOOR);
    },
    [subnetAmountsMap]
  );

  const convertAlphaToTao = useCallback(
    (
      netuid: number,
      alphaAmount: number | string,
      {
        inputFormat,
        outputFormat,
      }: ChainConvertFormat = chainConvertFormat.hToC
    ) => {
      const getSubnetAmounts = subnetAmountsMap[netuid];
      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition -- This is a valid check
      if (!getSubnetAmounts) {
        return BigNumber(0);
      }

      const { price } = getSubnetAmounts;
      const alphaChainFormat =
        inputFormat === 'human'
          ? BigNumber(alphaAmount).shiftedBy(9)
          : BigNumber(alphaAmount);

      const taoChain = alphaChainFormat.multipliedBy(price);
      return outputFormat === 'human'
        ? taoChain.shiftedBy(-9)
        : taoChain.integerValue(BigNumber.ROUND_FLOOR);
    },
    [subnetAmountsMap]
  );

  return {
    convertTaoToAlpha,
    convertAlphaToTao,
    chainConvertFormat,
  };
};

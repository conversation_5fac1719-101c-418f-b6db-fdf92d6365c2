export type SubnetAmounts = {
  alpha: number;
  tao: number;
  price: number;
};

export type StakeTransactionSummary =
  | {
      type: 'add_stake';
      originColdkey: string;
      destinationHotkey: string;
      destinationNetuid: number;
      taoAmount: number;
      priceLimit: number;
    }
  | {
      type: 'remove_stake';
      originColdkey: string;
      removeFromHotkey: string;
      removeFromNetuid: number;
      alphaAmount: number;
      taoAmount: number;
      priceLimit: number;
    }
  | {
      type: 'move_stake';
      originColdkey: string;
      originHotkey: string;
      originNetuid: number;
      destinationHotkey: string;
      alphaAmount: number;
    }
  | {
      type: 'transfer_stake';
      originColdkey: string;
      destinationColdkey: string;
      hotkey: string;
      netuid: number;
      alphaAmount: number;
    };

export type HandleSubmitTransactionParams = {
  onSuccessCallback?: () => void;
  doFullRefetchOnSuccess?: boolean;
};

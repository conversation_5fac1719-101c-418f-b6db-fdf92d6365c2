import { useState } from 'react';
import { Button, Flex, Alert } from '@repo/ui/components';
import { useAccountContext } from '@/contexts/chain-and-wallet/account-context';

export const useTransactionSuccessLogic = (callback?: () => void) => {
  const { fetchInfo } = useAccountContext();
  const [wasSuccessful, setWasSuccessful] = useState(false);

  const renderMessageIfSuccessful = () => {
    if (!wasSuccessful) {
      return null;
    }

    return (
      <Alert
        type='success'
        description={
          <Flex col className='gap-2'>
            <span>Thanks, your transaction was successful.</span>
            <Button
              variant='cta2'
              size='sm'
              className='w-fit'
              onClick={() => {
                void fetchInfo();
                callback?.();
              }}
            >
              Close
            </Button>
          </Flex>
        }
      />
    );
  };

  return {
    wasSuccessful,
    setWasSuccessful,
    renderMessageIfSuccessful,
    fetchInfo,
  };
};

import type { ReactNode } from 'react';
import React, {
  useReducer,
  useContext,
  useEffect,
  createContext,
  useCallback,
} from 'react';
import type { ApiPromise } from '@polkadot/api';
import { BigNumber } from 'bignumber.js';
import { useChainAndWalletContext } from './chain-and-wallet-context';
import { RAO_PER_TAO } from './constants';
import type { SubnetAmounts } from './types';
import { log } from '@/lib/utils';

BigNumber.config({
  EXPONENTIAL_AT: 1e9,
});

type SubnetNetuid = number;

type SubnetAmountsMap = Record<SubnetNetuid, SubnetAmounts>;

const calculatePoolPrice = (tao: number, alpha: number) => {
  return BigNumber(tao).div(BigNumber(alpha));
};

type DelegatedBalanceInfo = {
  hotkey: string; // Validator hotkey
  netuid: number; // Subnet netuid
  alphaAmount: number; // Current amount delegated to the validator for this subnet in alpha
  taoAmount: number; // Current amount delegated to the validator for this subnet in tao
};

type AccountProxy = {
  proxyAddress: string;
  proxyType: string;
};

type State = {
  // Current amount of tao available to delegate
  availableBalance: number;

  // Current amount delegated to each subnet/validator combo
  delegateBalances: DelegatedBalanceInfo[];

  // Current proxies
  proxies: AccountProxy[];

  // Total amount delegated to each subnet
  subnetAmountsMap: SubnetAmountsMap;

  // Loading state
  loading: boolean;
};

type AccountAction =
  | { type: 'LOAD' }
  | { type: 'SET_BALANCE'; payload: number }
  | {
      type: 'SET_PROXIES';
      payload: AccountProxy[];
    }
  | { type: 'SET_SUBNET_AMOUNTS'; payload: SubnetAmountsMap }
  | { type: 'FINISH'; payload: DelegatedBalanceInfo[] };

const initialState: State = {
  availableBalance: 0,
  delegateBalances: [],
  proxies: [],
  subnetAmountsMap: {},
  loading: false,
};

const reducer = (state: State, action: AccountAction): State => {
  switch (action.type) {
    case 'LOAD':
      return { ...state, loading: true };
    case 'SET_BALANCE':
      return { ...state, availableBalance: action.payload };
    case 'SET_PROXIES':
      return { ...state, proxies: action.payload };
    case 'SET_SUBNET_AMOUNTS':
      return { ...state, subnetAmountsMap: action.payload };
    case 'FINISH':
      return {
        ...state,
        delegateBalances: action.payload,
        loading: false,
      };
    default:
      throw new Error(
        `Unknown type in account context: ${JSON.stringify(action)}`
      );
  }
};

const fetchUserBalance = async (api: ApiPromise | null, address: string) => {
  if (!api?.query.system?.account) {
    return 0;
  }

  const res = await api.query.system.account(address);

  if (res.isEmpty) {
    return 0;
  }

  const { data } = res.toJSON() as { data: { free: number } };
  return data.free / RAO_PER_TAO;
};

const getBigNumberFromBits = (bits: string) => {
  return BigNumber(bits);
};

const calculateAccountAlphaBalance = (
  coldkeyShares: BigNumber,
  totalHotkeyShares: BigNumber,
  totalHotkeyAlpha: BigNumber
) => {
  const coldkeyProportion =
    coldkeyShares.isZero() || totalHotkeyShares.isZero()
      ? BigNumber(0)
      : BigNumber(coldkeyShares).div(totalHotkeyShares);
  const balance = BigNumber(
    totalHotkeyAlpha.times(coldkeyProportion).div(RAO_PER_TAO)
  );
  return balance;
};

const calculateTaoBalanceFromAlpha = (
  alphaAmount: BigNumber,
  poolPrice: BigNumber
) => {
  return alphaAmount.times(poolPrice).toNumber();
};

let _netuids: number[] = [];
const getNetuids = async (api: ApiPromise) => {
  if (_netuids.length > 0) {
    return _netuids;
  }

  const getTotalSubnets = await api.query.subtensorModule.totalNetworks();
  const totalSubnets = getTotalSubnets.toJSON() as number;
  _netuids = Array.from({ length: totalSubnets }, (_, i) => i);
  return _netuids;
};

const getSubnetAmountsFromChain = async (api: ApiPromise) => {
  const chainStorage = api.query.subtensorModule;
  const netuids = await getNetuids(api);

  // Get pool price for all netuids
  const subnetAlphaQuery = await chainStorage.subnetAlphaIn.multi(netuids);
  const subnetAlpha = subnetAlphaQuery.map((sa) => sa.toJSON()) as number[]; // array of numbers

  const subnetTaoQuery = await chainStorage.subnetTAO.multi(netuids);
  const subnetTao = subnetTaoQuery.map((st) => st.toJSON()) as number[]; // array of numbers

  const subnetAmountsMap = netuids.reduce<SubnetAmountsMap>(
    (acc, netuid, index) => ({
      ...acc,
      [netuid]: {
        alpha: subnetAlpha[index],
        tao: subnetTao[index],
        price:
          netuid === 0
            ? 1
            : calculatePoolPrice(
                subnetTao[index],
                subnetAlpha[index]
              ).toNumber(),
      },
    }),
    {}
  );
  log({ subnetAlpha, subnetTao, subnetAmountsMap });
  return subnetAmountsMap;
};

const getDelegatedBalances = async (
  api: ApiPromise | null,
  address: string
) => {
  // Check we have the subtensorModule available
  if (!api?.query.subtensorModule) {
    return [];
  }

  const chainStorage = api.query.subtensorModule;

  // stakingHotkeys is the array of validator hotkeys the user has staked to
  const stakingHotkeysQuery = await chainStorage.stakingHotkeys(address);
  const stakingHotkeys = stakingHotkeysQuery.toJSON() as string[];

  if (!Array.isArray(stakingHotkeys) || stakingHotkeys.length === 0) {
    return [];
  }

  // Got some hotkeys, which means the user has staked to some validators
  const subnetTotalAmounts = await getSubnetAmountsFromChain(api);

  const netuids = await getNetuids(api);

  // Create array of arrays with [hotkey, address, netuid] for each combination of sn/vali
  // This is the format required for the alpha.multi query
  // The alpha query returns the alpha shares for the netuid/vali (hotkey) combo for the given cold key (aka user account)
  const alphaParams = stakingHotkeys.flatMap((hotkey: string) =>
    netuids.map((netuid) => [hotkey, address, netuid])
  );

  // Get the alpha shares for the given cold key (aka user account)
  const userAlphaQuery = await chainStorage.alpha.multi(alphaParams);
  const userAlpha = userAlphaQuery.map((ua) =>
    getBigNumberFromBits((ua.toJSON() as { bits: string }).bits)
  );

  const valiParams = alphaParams.map(([hotkey, _, netuid]) => [hotkey, netuid]);
  const validatorAlphaQuery =
    await chainStorage.totalHotkeyAlpha.multi(valiParams);

  const validatorAlpha = validatorAlphaQuery.map((va) => {
    return BigNumber(va.toJSON() as number);
  });

  const validatorSharesQuery =
    await chainStorage.totalHotkeyShares.multi(valiParams);
  const validatorShares = validatorSharesQuery.map((vs) =>
    getBigNumberFromBits((vs.toJSON() as { bits: string }).bits)
  );
  log({
    stakingHotkeys,
    alphaParams,
    userAlpha: userAlpha.map((a) => a.toNumber()),
    validatorAlpha: validatorAlpha.map((a) => a.toNumber()),
    validatorShares: validatorShares.map((a) => a.toNumber()),
  });

  const delegatedBalances = alphaParams.map((_, i) => {
    const accountAlphaBalance = calculateAccountAlphaBalance(
      userAlpha[i],
      validatorShares[i],
      validatorAlpha[i]
    );

    const netuid = valiParams[i][1] as number;
    const poolPrice = BigNumber(subnetTotalAmounts[netuid].price);

    const delegatedBalanceForSubnetAndValidator = {
      hotkey: valiParams[i][0] as string,
      netuid: valiParams[i][1] as number,
      alphaAmount: accountAlphaBalance.gt(0)
        ? accountAlphaBalance.toNumber()
        : 0,
      taoAmount: calculateTaoBalanceFromAlpha(accountAlphaBalance, poolPrice),
    };

    return delegatedBalanceForSubnetAndValidator;
  });

  log('🎉', { delegatedBalances });

  return delegatedBalances;
};

const getSubnetAmountsFromChainAndDispatch = async (
  api: ApiPromise,
  dispatch: React.Dispatch<AccountAction>
) => {
  const subnetAmounts = await getSubnetAmountsFromChain(api);
  dispatch({
    type: 'SET_SUBNET_AMOUNTS',
    payload: subnetAmounts,
  });
};

const getProxies = async (api: ApiPromise, address: string) => {
  // Query the proxy list for the address
  const result = await api.query.proxy.proxies(address);

  const [proxyList, _] = result.toJSON() as [
    { delegate: string; proxyType: string; delay: number }[],
    number,
  ];

  if (proxyList.length === 0) {
    log(`No proxy accounts found for ${address}.`);
    return [];
  }

  return proxyList.map((p) => {
    log(
      `Proxy: address: ${p.delegate}, Type: ${p.proxyType}, Delay: ${p.delay}`
    );
    return {
      proxyAddress: p.delegate,
      proxyType: p.proxyType,
    };
  });
};

const getCurrentPosition = async (
  api: ApiPromise,
  addr: string,
  dispatch: React.Dispatch<AccountAction>
) => {
  dispatch({ type: 'LOAD' });

  const availableBalance = await fetchUserBalance(api, addr);
  dispatch({
    type: 'SET_BALANCE',
    payload: availableBalance,
  });

  const proxies = await getProxies(api, addr);
  dispatch({
    type: 'SET_PROXIES',
    payload: proxies,
  });

  await getSubnetAmountsFromChainAndDispatch(api, dispatch);

  const delegateBalances = await getDelegatedBalances(api, addr);
  dispatch({
    type: 'FINISH',
    payload: delegateBalances,
  });
};

type AccountContextValue = {
  state: State;
  fetchInfo: () => Promise<void>;
};

const AccountContext = createContext<AccountContextValue | undefined>(
  undefined
);

export const AccountContextProvider = (props: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(reducer, initialState);
  const {
    state: { api, currentAccount },
    isConnectedToChain,
    disconnectWallet,
  } = useChainAndWalletContext();

  const fetchInfo = useCallback(() => {
    if (!api?.query.subtensorModule || !currentAccount || !isConnectedToChain) {
      return Promise.resolve();
    }
    try {
      return getCurrentPosition(api, currentAccount.address, dispatch);
    } catch (error) {
      console.error('Error fetching account info:', error);
      return Promise.resolve();
    }
  }, [api, currentAccount, isConnectedToChain]);

  useEffect(() => {
    // We need api?.query?.system?.account and api?.query?.subtensorModule?.stake
    // to be available in order to fetch the account balance and delegated balances
    // They are not available in the initial state of the api context
    // So re-run this effect when they do become available
    // Just having `api` in the dependency array is not enough
    // React is not re-running the effect when the api prop is updated as system?.account and subtensorModule?.stake become available
    // So we need to add the specific api properties to the dependency array to ensure the effect is re-run when they are available
    if (!api) {
      return;
    }

    if (currentAccount) {
      void fetchInfo();
    }
  }, [
    api,
    api?.query.system?.account,
    api?.query.subtensorModule,
    currentAccount,
    fetchInfo,
  ]);

  // const unsubscribeFn = useRef<(() => void) | undefined>(undefined);

  // Subscribe to the new headers on-chain. The callback is fired when new headers
  // are found, the call itself returns a promise with a subscription that can be
  // used to unsubscribe from the newHead subscription
  // When new headers are found, we get the subnet amounts from the chain and dispatch them to the state
  // This is ultimately what we use to keep the subnet tao/alpha prices up to date
  useEffect(() => {
    let isMounted = true;
    let unsubscribeFn: (() => void) | null = null;

    if (!api?.rpc.chain) {
      log('🚩 No chain rpc available');
      // disconnectWallet();
      return;
    }

    const subscribeToNewHeads = async () => {
      try {
        const unsubscribe = await api.rpc.chain.subscribeNewHeads(
          async (header) => {
            log(
              `Chain update received. Chain is at block: #${header.number.toString()}`
            );

            try {
              await getSubnetAmountsFromChainAndDispatch(api, dispatch);
            } catch (error) {
              console.error('Error fetching subnet amounts:', error);
            }
          }
        );

        // Because `subscribeNewHeads` is an async call, it means the component could have unmounted before the call completed
        // That would mean the component is unmounted, but the subscription is still active
        // So check if we are still mounted, and if not, unsubscribe from the subscription now and return
        // If we are still mounted, we can keep the subscription for later when the component is unmounted
        if (!isMounted) {
          log(
            '😴 Component unmounted before initial subscription was completed. Cancelling subscription.'
          );
          unsubscribe();
          return;
        }

        // We are still mounted, so we can keep the subscription
        unsubscribeFn = unsubscribe;

        log('✅ Subscribed to chain updates', {
          unsubscribeFn,
        });
      } catch (error) {
        console.error('Error subscribing to chain:', error);
      }
    };

    // Call the async function
    void subscribeToNewHeads();

    // Cleanup function
    return () => {
      isMounted = false;

      if (unsubscribeFn) {
        log('🥱 Unsubscribing from chain');
        unsubscribeFn();
        unsubscribeFn = null;
      }
    };
  }, [api, disconnectWallet]);

  return (
    <AccountContext.Provider
      value={{
        state,
        fetchInfo,
      }}
    >
      {props.children}
    </AccountContext.Provider>
  );
};

export const useAccountContext = () => {
  const context = useContext(AccountContext);
  if (context === undefined) {
    throw new Error(
      'useAccountContext must be used within an AccountContextProvider'
    );
  }
  return context;
};

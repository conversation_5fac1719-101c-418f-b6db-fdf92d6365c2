// The original version of this file was brought in from the old delegation app
// There was much less typescript enforcement there, hence lots of eslint and typescript errors
// Leaving for now because this is a quick implementation
// TODO: come back and fix typescript issues

/* eslint-disable @typescript-eslint/no-unsafe-call -- TODO: fix this */
/* eslint-disable @typescript-eslint/no-unsafe-member-access -- TODO: fix this */
/* eslint-disable @typescript-eslint/no-unsafe-argument -- TODO: fix this */
/* eslint-disable @typescript-eslint/no-unsafe-assignment -- TODO: fix this */
/* eslint-disable @typescript-eslint/no-explicit-any -- TODO: fix this */

import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useReducer,
  useRef,
} from 'react';
import type { MutableRefObject, Dispatch } from 'react';
import { ApiPromise, WsProvider } from '@polkadot/api';
import type { Signer } from '@polkadot/api/types';
import {
  web3Accounts,
  web3Enable,
  web3FromSource,
} from '@polkadot/extension-dapp';
import type { DispatchError } from '@polkadot/types/interfaces';
import jsonrpcGlobal from '@polkadot/types/interfaces/jsonrpc';
import type { DefinitionRpcExt } from '@polkadot/types/types';
import { z } from 'zod';
import { notify } from '@repo/ui/lib';
import {
  APP_NAME,
  LOCAL_STORAGE_ACCOUNTS,
  LOCAL_STORAGE_CURRENT_ACCOUNT,
} from '@/contexts/chain-and-wallet/constants';
import { isDevOrBeta, log } from '@/lib/utils';

///
// Initial state for `useReducer`

type ApiState = 'CONNECT_INIT' | 'CONNECTING' | 'READY' | 'ERROR';

type KeyringState = 'LOADING' | 'READY' | 'ERROR' | 'ERROR_NO_EXTENSIONS';

type Account = {
  address: string;
  meta: {
    name: string;
    source: string;
  };
};

type State = {
  socket: string;
  jsonrpc: Record<string, Record<string, DefinitionRpcExt>>;
  keyringState: KeyringState | null;
  api: ApiPromise | null;
  apiError: any;
  apiState: ApiState | null;
  accounts: Account[];
  currentAccount: Account | null;
  currentSigner: Signer | undefined;
};

const getSocket = () => {
  const socketUrl = isDevOrBeta()
    ? 'wss://test.chain.opentensor.ai:443'
    : 'wss://openrpc.taostats.io:443';

  return socketUrl;
};

const initialState: State = {
  socket: getSocket(),
  jsonrpc: { ...jsonrpcGlobal },
  keyringState: null,
  api: null,
  apiError: null,
  apiState: null,
  currentAccount: null,
  currentSigner: undefined,
  accounts: [],
};

type WalletAction =
  | { type: 'CONNECT_INIT' }
  | { type: 'CONNECT'; payload: ApiPromise }
  | { type: 'CONNECT_SUCCESS' }
  | { type: 'CONNECT_ERROR'; payload: any }
  | { type: 'LOAD_KEYRING' }
  | { type: 'KEYRING_ERROR'; payload?: any }
  | { type: 'KEYRING_ERROR_NO_EXTENSIONS' }
  | { type: 'SET_CURRENT_ACCOUNT'; payload: Account | null }
  | { type: 'SET_CURRENT_SIGNER'; payload: Signer }
  | { type: 'SET_ACCOUNTS'; payload: Account[] }
  | { type: 'DISCONNECT' }
  | { type: 'RESET' };

///
// Reducer function for `useReducer`

const reducer = (state: State, action: WalletAction): State => {
  switch (action.type) {
    case 'CONNECT_INIT':
      return { ...state, apiState: 'CONNECT_INIT' };
    case 'CONNECT':
      return {
        ...state,
        api: action.payload,
        apiState: 'CONNECTING',
      };
    case 'CONNECT_SUCCESS':
      return { ...state, apiState: 'READY' };
    case 'CONNECT_ERROR':
      return { ...state, apiState: 'ERROR', apiError: action.payload };
    case 'LOAD_KEYRING':
      return { ...state, keyringState: 'LOADING' };
    case 'KEYRING_ERROR':
      return { ...state, keyringState: 'ERROR' };
    case 'KEYRING_ERROR_NO_EXTENSIONS':
      return { ...state, keyringState: 'ERROR_NO_EXTENSIONS' };
    case 'SET_CURRENT_ACCOUNT':
      return { ...state, currentAccount: action.payload };
    case 'SET_CURRENT_SIGNER':
      return { ...state, currentSigner: action.payload };
    case 'SET_ACCOUNTS':
      return { ...state, accounts: action.payload, keyringState: 'READY' };
    case 'DISCONNECT':
      return { ...initialState };
    case 'RESET':
      return { ...initialState };
    default:
      throw new Error(`Unknown type: ${JSON.stringify(action)}`);
  }
};

///
// Connecting to the Substrate node

const connectToChain = (
  state: State,
  dispatch: Dispatch<WalletAction>,
  isAttemptingConnection: MutableRefObject<boolean>
) => {
  const { apiState, socket, jsonrpc } = state;

  if (isAttemptingConnection.current) {
    log('🔄 Already attempting connection');
    return;
  }

  // We only want this function to be performed once
  if (apiState) return;

  dispatch({ type: 'CONNECT_INIT' });

  log('🔌 Connecting to web socket', { socket });
  const provider = new WsProvider(socket);
  const api = new ApiPromise({ provider, rpc: jsonrpc });
  isAttemptingConnection.current = true;

  // Set listeners for disconnection and reconnection event.
  api.on('connected', () => {
    dispatch({ type: 'CONNECT', payload: api });
    isAttemptingConnection.current = false;
  });
  api.on('ready', () => {
    dispatch({ type: 'CONNECT_SUCCESS' });
    isAttemptingConnection.current = false;
  });
  api.on('error', (err) => {
    dispatch({ type: 'CONNECT_ERROR', payload: err });
    void provider.disconnect();
    isAttemptingConnection.current = false;
  });
};

// Load accounts
const loadAccounts = async (dispatch: Dispatch<WalletAction>) => {
  dispatch({ type: 'LOAD_KEYRING' });

  try {
    let allAccounts: Account[] = [];
    const accounts = localStorage.getItem(LOCAL_STORAGE_ACCOUNTS);

    if (accounts) {
      const accountSchema = z.custom<Account>().array();
      const parsed = accountSchema.safeParse(JSON.parse(accounts));
      if (!parsed.success) {
        console.error(
          'Failed to parse accounts from localStorage',
          parsed.error
        );
        allAccounts = [];
      } else {
        allAccounts = parsed.data;
      }
    }
    if (!accounts || allAccounts.length === 0) {
      const extensions = await web3Enable(APP_NAME);
      if (extensions.length === 0) {
        dispatch({ type: 'KEYRING_ERROR_NO_EXTENSIONS' });
        notify.error(
          'No wallet extensions found. Please install an extension and try again.'
        );
        return;
      }
      const getAccounts = await web3Accounts();
      allAccounts = getAccounts.map(({ address, meta }) => ({
        address,
        meta: { ...meta, name: `${meta.name} (${meta.source})` },
      }));
      localStorage.setItem(LOCAL_STORAGE_ACCOUNTS, JSON.stringify(allAccounts));
    }

    dispatch({ type: 'SET_ACCOUNTS', payload: allAccounts });

    // Set current account based on either local storage or first account
    const current = localStorage.getItem(LOCAL_STORAGE_CURRENT_ACCOUNT);
    const account = current
      ? allAccounts.find((acc) => acc.address === current) ?? allAccounts[0]
      : allAccounts[0];
    dispatch({ type: 'SET_CURRENT_ACCOUNT', payload: account });

    if (!current) {
      localStorage.setItem(LOCAL_STORAGE_CURRENT_ACCOUNT, account.address);
    }
  } catch {
    dispatch({ type: 'KEYRING_ERROR' });
  }
};

const disconnect = (dispatch: Dispatch<WalletAction>) => {
  localStorage.removeItem(LOCAL_STORAGE_CURRENT_ACCOUNT);
  localStorage.removeItem(LOCAL_STORAGE_ACCOUNTS);
  dispatch({ type: 'DISCONNECT' });
};

type ChainAndWalletContextType = {
  state: State;
  connectWallet: () => void;
  setCurrentAccount: (acct: Account) => void;
  disconnectWallet: () => void;
  resetApiState: () => void;
  isError: boolean;
  errorType: string | null;
  isLoading: boolean;
  isConnectedToChain: boolean;
  isChainError: boolean;
};

const ChainAndWalletContext = createContext<
  ChainAndWalletContextType | undefined
>(undefined);

export const ChainAndWalletContextProvider = (props: {
  children: React.ReactNode;
}) => {
  const [state, dispatch] = useReducer<React.Reducer<State, WalletAction>>(
    reducer,
    initialState
  );

  const connectWallet = useCallback(() => {
    if (state.currentAccount) {
      // Already connected, don't try again.
      return;
    }
    void loadAccounts(dispatch);
  }, [state.currentAccount]);

  const isConnectedToChain = state.apiState === 'READY';
  const isChainError = state.apiState === 'ERROR';
  const isAttemptingConnection = useRef(false);

  useEffect(() => {
    if (
      !isConnectedToChain &&
      !isChainError &&
      !isAttemptingConnection.current
    ) {
      connectToChain(state, dispatch, isAttemptingConnection);
    }
  }, [state, isConnectedToChain, isChainError]);

  useEffect(() => {
    if (
      !state.currentAccount &&
      localStorage.getItem(LOCAL_STORAGE_CURRENT_ACCOUNT)
    ) {
      connectWallet();
    }
  }, [connectWallet, state.currentAccount]);

  useEffect(() => {
    const getInjector = async () => {
      const currentAccount = state.currentAccount;
      if (!currentAccount) return;

      try {
        const extensions = await web3Enable(APP_NAME);
        if (extensions.length === 0) {
          dispatch({ type: 'KEYRING_ERROR', payload: {} });
          return;
        }
        const injector = await web3FromSource(currentAccount.meta.source);
        dispatch({ type: 'SET_CURRENT_SIGNER', payload: injector.signer });
      } catch {
        dispatch({ type: 'KEYRING_ERROR', payload: {} });
      }
    };

    void getInjector();
  }, [state.currentAccount]);

  function setCurrentAccount(acct: Account) {
    localStorage.setItem(LOCAL_STORAGE_CURRENT_ACCOUNT, acct.address || '');
    dispatch({ type: 'SET_CURRENT_ACCOUNT', payload: acct });
  }

  const disconnectWallet = () => {
    disconnect(dispatch);
  };

  const resetApiState = () => {
    dispatch({ type: 'RESET' });
  };

  const isLoading =
    state.apiState === 'CONNECT_INIT' ||
    state.apiState === 'CONNECTING' ||
    state.keyringState === 'LOADING';

  const { isError, errorType } = useErrorState(state);

  return (
    <ChainAndWalletContext.Provider
      value={{
        state,
        connectWallet,
        setCurrentAccount,
        disconnectWallet,
        resetApiState,
        isLoading,
        isError,
        errorType: !isError ? null : errorType,
        isConnectedToChain: state.apiState === 'READY',
        isChainError,
      }}
    >
      {props.children}
    </ChainAndWalletContext.Provider>
  );
};

export const useChainAndWalletContext = () => {
  const context = useContext(ChainAndWalletContext);
  if (context === undefined) {
    throw new Error(
      'useChainAndWalletContext must be used within a ChainAndWalletContextProvider'
    );
  }
  return context;
};

export default {
  ChainAndWalletContextProvider,
  useChainAndWalletContext,
};

const useErrorState = (state: State) => {
  const apiError = state.apiState === 'ERROR';
  const walletError =
    state.keyringState === 'ERROR' ||
    (state.keyringState === 'READY' && state.accounts.length === 0);
  const isError = apiError || walletError;
  const errorType = apiError
    ? 'apiError'
    : state.keyringState === 'READY' && state.accounts.length === 0
      ? 'walletError'
      : 'noExtensions';
  return { isError, errorType };
};

export const useTransactionErrorDecoder = () => {
  const {
    state: { api },
  } = useChainAndWalletContext();

  const getErrorFromApi = (metaErrorContent: any) => {
    const decoded = api?.registry.findMetaError(metaErrorContent);
    return getErrorResult(decoded);
  };

  // When there's a SubmittableResult returned from signAndSend, it can contain a `dispatchError` object if there was an error during the transaction e.g. due to rate limiting
  // This doesn't cause the signAndSend error handler to trigger, but it does cause the transaction to fail
  // If that's the case, this method decodes the error and returns so that it can be displayed to the user
  const decodeExtrinsicFailedEvent = (dispatchError: DispatchError) => {
    try {
      if (dispatchError.isModule) {
        return getErrorFromApi(dispatchError.asModule);
      }
      console.error(
        'dispatchError is not a module. Returning string error: ',
        dispatchError.toString()
      );
      return unknownErrorResult(
        `An unknown error occurred: ${dispatchError.toString()}`
      );
    } catch (error) {
      console.error('Error decoding dispatchError:', { dispatchError, error });
      return unknownErrorResult(
        'An unknown error occurred while decoding dispatchError'
      );
    }
  };

  const errorMessageParser = (errorMessage: string) => {
    const msgToCheck = errorMessage.toLowerCase().trim();
    if (msgToCheck.includes('custom error: 0')) {
      return 'Your coldkey is set to be swapped. No transfer operations are possible.';
    }

    if (msgToCheck.includes('custom error: 1')) {
      return 'The amount you are staking/unstaking/moving is below the minimum TAO equivalent. The transaction minimum is 500,000 RAO or 0.0005 TAO.';
    }

    if (msgToCheck.includes('custom error: 2')) {
      return 'You are trying to stake more than you have available. This could be due to a price change. Please reset and try again.';
    }

    if (msgToCheck.includes('custom error: 3')) {
      return 'This subnet does not exist.';
    }

    if (msgToCheck.includes('custom error: 4')) {
      return 'Hotkey is not registered on Bittensor network.';
    }

    if (msgToCheck.includes('custom error: 5')) {
      return 'You do not have enough TAO equivalent stake to remove/move/transfer, including the unstake fee.';
    }

    if (msgToCheck.includes('custom error: 6')) {
      return 'Rate limit exceeded. Please try again later.';
    }

    if (msgToCheck.includes('custom error: 7')) {
      return 'You do not have enough TAO equivalent stake to remove/move/transfer, including the unstake fee.';
    }

    if (msgToCheck.includes('custom error: 8')) {
      return 'The slippage is higher than you have set the limit at. Either increase your slippage, or decrease the amount.';
    }

    if (msgToCheck.includes('custom error: 9')) {
      return 'This subnet does not allow stake transfer.';
    }

    if (msgToCheck.includes('custom error: 10')) {
      return 'The hotkey is not registered in the selected subnet.';
    }

    return errorMessage;
  };

  // The intention for this method is to decode the error recieved from the signAndSend method
  // This is a different flow to the decodeExtrinsicFailedEvent method above, which is used to decode errors from the extrinsic events
  // Although they perform similar functions, they are separate methods because they are used in different contexts
  const decodeError = (errorData: any) => {
    if (
      errorData?.message === 'Cancelled' || // Returned by talisman and polkadot-js when user cancels
      errorData?.message === 'Rejected by user' // Returned by subwallet when user cancels
    ) {
      return {
        section: 'Cancelled',
        name: 'Cancelled',
        docs: 'The transaction was cancelled',
      };
    }

    try {
      if (errorData?.message) {
        return {
          section: 'General',
          name: 'General',
          docs: errorMessageParser(errorData.message),
        };
      }

      if (errorData?.data) {
        return {
          section: errorData?.name ?? 'Unknown',
          name: errorData?.name ?? 'Unknown',
          docs: errorMessageParser(errorData.data),
        };
      }

      // Parse the error data
      const { error, index } = errorData.value;
      if (!error || !index) {
        console.error('Invalid errorData:', errorData, error, index);
        return unknownErrorResult();
      }

      const errorIndex = new Uint8Array([
        index,
        parseInt(error.slice(2, 4), 16),
      ]);
      return getErrorFromApi(errorIndex);
    } catch (error) {
      // Handling this in the catch is a bit nasty, but errorData above is not always present
      // This was the quickest way I could see to get hold of the error information in some cases
      // TODO: Revisit this and work out a better way to handle this in the try.
      if (errorData?.data) {
        return {
          section: errorData?.name ?? 'Unknown',
          name: errorData?.name ?? 'Unknown',
          docs: errorMessageParser(errorData.data),
        };
      }

      console.error('Error decoding errorData:', { errorData, error });
      return unknownErrorResult();
    }
  };

  return { decodeExtrinsicFailedEvent, decodeError };
};

const getErrorResult = (decoded: any) => {
  const result = {
    section: decoded.section,
    name: decoded.name,
    docs: decoded.docs.join(' '),
  };
  logErrorRetrievalResult(result);
  return result;
};

const logErrorRetrievalResult = (result: {
  section: string;
  name: string;
  docs: string;
}) => {
  log(`Error in ${result.section}::${result.name}: ${result.docs}`);
};

const unknownErrorResult = (customMsg?: string) => ({
  section: 'Unknown',
  name: 'Unknown',
  docs: customMsg ?? 'An unknown error occurred',
});

FROM node:18-alpine AS base

ARG APP_ENV
ENV APP_ENV=$APP_ENV

ARG WEBSITE_API_URL
ENV WEBSITE_API_URL=$WEBSITE_API_URL

ARG WEBSITE_API_TOKEN
ENV WEBSITE_API_TOKEN=$WEBSITE_API_TOKEN

ARG NEXT_PUBLIC_WEBSITE_BASE_URL
ENV NEXT_PUBLIC_WEBSITE_BASE_URL=$NEXT_PUBLIC_WEBSITE_BASE_URL

FROM base AS builder
RUN apk update
RUN apk add --no-cache libc6-compat

# Set working directory
WORKDIR /app

# Install pnpm and turbo
RUN npm install -g pnpm turbo
COPY . .

# Generate a partial monorepo with a pruned lockfile for a target workspace.
# "web" is the name entered in the project's package.json: { name: "web" }
RUN turbo prune web --docker

# Add lockfile and package.json's of isolated subworkspace
FROM base AS installer
RUN apk update
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install pnpm
RUN npm install -g pnpm

# First install the dependencies (as they change less often)
COPY --from=builder /app/out/json/ .
RUN pnpm install

# Build the project
COPY --from=builder /app/out/full/ .
RUN pnpm turbo run build

FROM base AS runner
WORKDIR /app

# Don't run production as root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
USER nextjs

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=installer --chown=nextjs:nodejs /app/apps/web/.next/standalone ./
COPY --from=installer --chown=nextjs:nodejs /app/apps/web/.next/static ./apps/web/.next/static
COPY --from=installer --chown=nextjs:nodejs /app/apps/web/public ./apps/web/public

EXPOSE 3001

CMD node apps/web/server.js

# Web

A modern Next.js application built with App Router and Tailwind CSS, integrated into the TaoStats monorepo.

## Features

- ⚡ **Next.js 14** with App Router
- 🎨 **Tailwind CSS** for styling
- 📦 **Monorepo Integration** with shared packages
- 🔧 **TypeScript** for type safety
- 🎯 **ESLint** for code quality
- 🚀 **Turbo** for fast builds

## Getting Started

### Prerequisites

Make sure you have the following installed:
- Node.js 18+
- pnpm

### Installation

From the root of the monorepo:

```bash
# Install dependencies
pnpm install

# Start the development server
pnpm dev
```

The application will be available at [http://localhost:3001](http://localhost:3001).

### Available Scripts

- `pnpm dev` - Start the development server
- `pnpm build` - Build the application for production
- `pnpm start` - Start the production server
- `pnpm lint` - Run ESLint
- `pnpm type-check` - Run TypeScript type checking

## Project Structure

```
apps/web/
├── src/
│   ├── app/                 # Next.js App Router pages
│   │   ├── layout.tsx       # Root layout
│   │   ├── page.tsx         # Home page
│   │   └── globals.css      # Global styles
│   └── components/          # React components
│       ├── header.tsx
│       └── footer.tsx
├── public/                  # Static assets
├── package.json
├── next.config.js
├── tailwind.config.ts
└── tsconfig.json
```

## Shared Packages

This app leverages shared packages from the monorepo:

- `@repo/ui` - Shared UI components
- `@repo/config` - Shared configurations (Tailwind, TypeScript)
- `@repo/eslint-config` - ESLint configurations
- `@repo/types` - Shared TypeScript types

## Development

### Adding New Pages

Create new pages in the `src/app` directory following the App Router conventions:

```tsx
// src/app/about/page.tsx
export default function AboutPage() {
  return (
    <div>
      <h1>About</h1>
    </div>
  );
}
```

### Using Shared Components

Import components from the shared UI package:

```tsx
import { Button } from '@repo/ui/components/ui/button';
import { Card } from '@repo/ui/components/ui/card';
```

### Styling

This app uses Tailwind CSS with a shared configuration. You can use all Tailwind classes and the custom design tokens defined in the shared config.

## Deployment

The app is configured for standalone deployment with Next.js. Build the application:

```bash
pnpm build
```

The built application will be in the `.next` directory and can be deployed to any Node.js hosting platform.

import { useCallback } from 'react';
import { atom, useAtom } from 'jotai';

export const latestPriceAtom = atom<number>(0);

export const useLatestPriceAtom = () => {
  const [latestPrice, setLatestPrice] = useAtom(latestPriceAtom);

  const onLatestPriceChange = useCallback(
    (value: number) => {
      setLatestPrice(value);
    },
    [setLatestPrice]
  );

  return {
    latestPrice,
    setLatestPrice,
    onLatestPriceChange,
  };
};

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
	--foreground-rgb: 255, 255, 255;
	--background-rgb: 18, 18, 18;

	--background: 0 0% 8%;
	--foreground: 210 40% 98%;

	--card: 222.2 84% 4.9%;
	--card-foreground: 210 40% 98%;

	--popover: 222.2 84% 4.9%;
	--popover-foreground: 210 40% 98%;

	--primary: 210 40% 98%;
	--primary-foreground: 222.2 47.4% 11.2%;

	--secondary: 0, 0%, 85%;
	--secondary-foreground: 210 40% 98%;

	--muted: 217.2 32.6% 17.5%;
	--muted-foreground: 0, 0%, 56.5%;

	--accent: 217.2 32.6% 17.5%;
	--accent-foreground: 210 40% 98%;

	--destructive: 0 62.8% 30.6%;
	--destructive-foreground: 210 40% 98%;

	--border: 217.2 32.6% 17.5%;
	--input: 0, 0%, 14.5%;
	--ring: 212.7 26.8% 83.9%;
}

body {
	color: rgb(var(--foreground-rgb));
	background: rgb(var(--background-rgb));
}

@layer utilities {
	.text-balance {
		text-wrap: balance;
	}

	.no-scrollbar::-webkit-scrollbar {
		display: none;
	}
	.no-scrollbar {
		-ms-overflow-style: none; /* IE and Edge */
		scrollbar-width: none; /* Firefox */
	}

	.scrollbar::-webkit-scrollbar {
		display: block;
	}
	.scrollbar {
		-ms-overflow-style: auto; /* IE and Edge */
		scrollbar-width: auto; /* Firefox */
	}
}

/* .visx-tooltip {
	@apply z-1 !shadow-none;
} */

/* .bg-subnet-gradient {
	@apply h-64 bg-subnet-gradient-xs sm:bg-subnet-gradient-sm md:h-80 md:bg-subnet-gradient-md lg:h-96 lg:bg-subnet-gradient-lg xl:h-[420px] xl:bg-subnet-gradient-xl 2xl:h-[500px] 2xl:bg-subnet-gradient-2xl;
}

tr:hover svg {
	@apply visible;
} */

* {
	scrollbar-width: thin;
	scrollbar-color: #656565 #2b2b2b;
}

::-webkit-scrollbar {
	width: 4px;
	height: 8px;
}

::-webkit-scrollbar-thumb {
	background: #656565;
	border-radius: 1px;
}

.prose table {
	@apply w-full rounded-lg overflow-hidden;
}
.prose th,
.prose td {
	@apply px-4 py-2 w-full;
}
.prose th {
	@apply bg-neutral-900;
	&:hover {
		@apply text-neutral-300;
	}
}
.prose tr {
	@apply bg-neutral-800/20;
}
.prose tr:hover td {
	@apply bg-neutral-800;
}

@keyframes blink {
	0% {
		opacity: 0.2;
	}
	20% {
		opacity: 1;
	}
	100% {
		opacity: 0.2;
	}
}

import { Hono } from 'hono';
import {
  fetchAllIdentity,
  fetchChartData,
  fetchChildPerformance,
  fetchDtaoValidator,
  fetchDtaoValidatorPerformanceLatest,
  fetchFullMetrics,
  fetchFullValidators,
  fetchPerformanceHistory,
  fetchTopValidatorsPerSubnet,
  fetchValidator,
} from '@/api-proxy/validators';

const app = new Hono()
  .get('/validators', async (c) => {
    const response = await fetchValidator({ ...c.req.query() });
    return c.json(response);
  })
  .get('/fullValidators', async (c) => {
    const response = await fetchFullValidators({ ...c.req.query() });
    return c.json(response);
  })
  .get('validatorPerformanceHistory', async (c) => {
    const query = c.req.query();
    const path = query.hotkey;
    const subnetId = parseInt(query.subnetId, 10);
    const response = await fetchPerformanceHistory(path, subnetId);
    return c.json(response);
  })
  .get('childPerformance', async (c) => {
    const hotkey = c.req.query().hotkey;
    const response = await fetchChildPerformance(hotkey);
    return c.json(response);
  })
  .get('validatorStakeChart', async (c) => {
    const hotkey = c.req.query().hotkey;
    const response = await fetchChartData(hotkey);
    return c.json(response);
  })
  .get('metricsChart', async (c) => {
    const netuid = Number(c.req.query().netuid);
    const hotkey = c.req.query().hotkey;
    const coldkey = c.req.query().coldkey;
    const response = await fetchFullMetrics({ netuid, hotkey, coldkey });
    return c.json(response);
  })
  .get('dtaoValidatorPerformanceLatest', async (c) => {
    const response = await fetchDtaoValidatorPerformanceLatest({
      ...c.req.query(),
    });
    return c.json(response);
  })
  .get('dtaoValidators', async (c) => {
    const response = await fetchDtaoValidator();
    return c.json(response);
  })
  .get('dtaoValidatorsForSubnet', async (c) => {
    const response = await fetchTopValidatorsPerSubnet();
    return c.json(response);
  })
  .get('/validatorIdentity', async (c) => {
    const response = await fetchAllIdentity();
    return c.json(response);
  });

export const validatorApi = app;

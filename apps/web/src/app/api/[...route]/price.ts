import { Hono } from 'hono';
import { PricePeriod } from '@repo/types/website-api-types';
import {
  fetchLatestPrice,
  fetchPriceFullOhlc,
  fetchPriceHistory,
} from '@/api-proxy/price';

const app = new Hono()
  .get('/price', async (c) => {
    const response = await fetchLatestPrice({ asset: 'TAO' });
    return c.json(response);
  })
  .get('/priceHistory', async (c) => {
    const response = await fetchPriceHistory({ asset: 'TAO' });
    return c.json(response);
  })
  .get('/priceOhlc', async (c) => {
    const response = await fetchPriceFullOhlc({
      asset: 'TAO',
      period: PricePeriod.DAY,
    });
    return c.json(response);
  })
  .get('/dailyPriceChart', async (c) => {
    const timestampStart = Number(c.req.query().timestampStart);
    const timestampEnd = Number(c.req.query().timestampEnd);
    const response = await fetchPriceFullOhlc({
      asset: 'TAO',
      period: PricePeriod.DAY,
      timestamp_start: timestampStart,
      timestamp_end: timestampEnd,
    });
    return c.json(response);
  })
  .get('/priceHistoryChart', async (c) => {
    const response = await fetchPriceHistory({ asset: 'TAO', limit: 200 });
    return c.json(response);
  });

export const priceApi = app;

import { Hono } from 'hono';
import {
  fetchDelegationBalance,
  fetchDelegations,
  fetchDtaoDelegationBalance,
  fetchTopDelegatorTransactions,
} from '@/api-proxy/delegate';

const app = new Hono()
  .get('/delegate', async (c) => {
    const response = await fetchDelegations({ ...c.req.query() });
    return c.json(response);
  })
  .get('/delegateBalance', async (c) => {
    const response = await fetchDelegationBalance({ ...c.req.query() });
    return c.json(response);
  })
  .get('/topDelegator', async (c) => {
    const validator = c.req.query().validator;
    const response = await fetchTopDelegatorTransactions({ validator });
    return c.json(response);
  })
  .get('/dtaoDelegateBalance', async (c) => {
    const response = await fetchDtaoDelegationBalance({ ...c.req.query() });
    return c.json(response);
  });

export const delegateApi = app;

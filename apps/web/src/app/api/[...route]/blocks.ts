import { Hono } from 'hono';
import type { BlockFrequency } from '@repo/types/website-api-types';
import { fetchBlockIntervalChart, fetchBlocks } from '@/api-proxy/blocks';

const app = new Hono()
  .get('/block', async (c) => {
    const response = await fetchBlocks({ ...c.req.query() });
    return c.json(response);
  })
  .get('/blockInterval', async (c) => {
    const frequency = c.req.query().frequency as BlockFrequency;
    const response = await fetchBlockInterval<PERSON>hart(frequency);
    return c.json(response);
  });

export const blocksApi = app;

import { Hono } from 'hono';
import {
  fetchEvents,
  fetchSuccessToFailureRatio,
  fetchWeeklyExtrinsic,
} from '@/api-proxy/event';

const app = new Hono()
  .get('/event', async (c) => {
    const response = await fetchEvents({ ...c.req.query() });
    return c.json(response);
  })
  .get('/successToFailureRatio', async (c) => {
    const response = await fetchSuccessToFailureRatio();
    return c.json(response);
  })
  .get('/weeklyExtrinsic', async (c) => {
    const type = c.req.query().type;
    const response = await fetchWeeklyExtrinsic(type);
    return c.json(response);
  });

export const eventApi = app;

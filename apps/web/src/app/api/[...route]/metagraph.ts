import { Hono } from 'hono';
import {
  fetchMetagraph,
  fetchMetagraphHistory,
  fetchRootMetagraph,
  fetchRootMetagraphHistory,
} from '@/api-proxy/metagraph';

const app = new Hono()
  .get('/metagraph', async (c) => {
    const response = await fetchMetagraph({ ...c.req.query() });
    return c.json(response);
  })
  .get('/rootMetagraph', async (c) => {
    const response = await fetchRootMetagraph({ ...c.req.query() });
    return c.json(response);
  })
  .get('/rootMetagraphHistory', async (c) => {
    const response = await fetchRootMetagraphHistory({ ...c.req.query() });
    return c.json(response);
  })
  .get('/metagraphHistory', async (c) => {
    const response = await fetchMetagraphHistory({ ...c.req.query() });
    return c.json(response);
  });

export const metagraphApi = app;

import { Hono } from 'hono';
import {
  fetchAllStatsHistory,
  fetchDailyNumberSubnets,
  fetchStats,
  fetchYield,
} from '@/api-proxy/stats';

const app = new Hono()
  .get('/stats', async (c) => {
    const response = await fetchStats();
    return c.json(response);
  })
  .get('/statsHistory', async (c) => {
    const response = await fetchAllStatsHistory();
    return c.json(response);
  })
  .get('/subnetGrowth', async (c) => {
    const response = await fetchDailyNumberSubnets();
    return c.json(response);
  })
  .get('/statsYieldHistory', async (c) => {
    const response = await fetchYield({ ...c.req.query() });
    return c.json(response);
  });

export const statsApi = app;

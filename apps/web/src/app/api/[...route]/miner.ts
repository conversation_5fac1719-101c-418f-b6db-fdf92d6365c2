import { Hono } from 'hono';
import {
  fetchMinerPerformanceByNetuid,
  fetchMinerWeightsChart,
} from '@/api-proxy/miner';

const app = new Hono()
  .get('/minerWeightsChart', async (c) => {
    const netuid = Number(c.req.query().netuid);
    const minerUid = Number(c.req.query().minerUid);
    const response = await fetchMinerWeightsChart({
      netuid,
      minerUid,
    });
    return c.json(response);
  })
  .get('/minerPerformanceByNetuid', async (c) => {
    const netuid = Number(c.req.query().netuid);
    const response = await fetchMinerPerformanceByNetuid({
      netuid,
    });
    return c.json(response);
  });

export const minerApi = app;

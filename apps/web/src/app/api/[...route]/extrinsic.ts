import { Hono } from 'hono';
import { fetchExtrinsics } from '@/api-proxy/extrinsic';

const app = new Hono()
  .get('/extrinsic', async (c) => {
    const response = await fetchExtrinsics({ ...c.req.query() });
    return c.json(response);
  })
  .get('/sudo', async (c) => {
    const response = await fetchExtrinsics({ ...c.req.query() });
    return c.json(response);
  });

export const extrinsicApi = app;

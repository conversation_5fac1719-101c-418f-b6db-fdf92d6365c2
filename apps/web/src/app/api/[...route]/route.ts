import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { handle } from 'hono/vercel';
import { accountApi } from './account';
import { blocksApi } from './blocks';
import { contractApi } from './contract';
import { delegateApi } from './delegate';
import { dtaoApi } from './dtao';
import { eventApi } from './event';
import { evmApi } from './evm';
import { exchangeApi } from './exchange';
import { extrinsicApi } from './extrinsic';
import { metagraphApi } from './metagraph';
import { minerApi } from './miner';
import { priceApi } from './price';
import { runtimeApi } from './runtime';
import { statsApi } from './stats';
import { subnetApi } from './subnet';
import { transactionApi } from './transaction';
import { transfersApi } from './transfers';
import { validatorApi } from './validator';

export const runtime = 'nodejs';

const app = new Hono().basePath('/api');

// Middleware
app.use('*', cors());
app.use('*', logger());

// Health check
app.get('/health', (c) => {
  return c.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'web-api',
  });
});

const routes = app
  .route('/subnet', subnetApi)
  .route('/validator', validatorApi)
  .route('/miner', minerApi)
  .route('/blocks', blocksApi)
  .route('/stats', statsApi)
  .route('/price', priceApi)
  .route('/transfer', transfersApi)
  .route('/transaction', transactionApi)
  .route('/runtime', runtimeApi)
  .route('/metagraph', metagraphApi)
  .route('/extrinsic', extrinsicApi)
  .route('/exchange', exchangeApi)
  .route('/contract', contractApi)
  .route('/account', accountApi)
  .route('/delegate', delegateApi)
  .route('/event', eventApi)
  .route('/evm', evmApi)
  .route('/dtao', dtaoApi)
  .onError((err, c) => {
    console.error('API Error:', err);
    return c.json(
      {
        error: err.message || 'Internal Server Error',
      },
      500
    );
  });

export type ApiTypes = typeof routes;
export const GET = handle(app);
export const POST = handle(app);
export const PUT = handle(app);
export const DELETE = handle(app);

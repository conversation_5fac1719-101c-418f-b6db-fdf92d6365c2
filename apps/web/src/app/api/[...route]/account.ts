import { Hono } from 'hono';
import {
  fetchAccountHistory,
  fetchAccounts,
  fetchRecentAccounts,
} from '@/api-proxy/account';

const app = new Hono()
  .get('/account', async (c) => {
    const response = await fetchAccounts({ ...c.req.query() });
    return c.json(response);
  })
  .get('/accountHistory', async (c) => {
    const response = await fetchAccountHistory({ ...c.req.query() });
    return c.json(response);
  })
  .get('/recentAccounts', async (c) => {
    const response = await fetchRecentAccounts();
    return c.json(response);
  });

export const accountApi = app;

import { Hono } from 'hono';
import { fetchTransaction } from '@/api-proxy/transaction';

const app = new Hono()
  .get('/transaction', async (c) => {
    const response = await fetchTransaction({ ...c.req.query() });
    return c.json(response);
  })
  .get('/walletTransaction', async (c) => {
    const address = c.req.query().address;
    const response = await Promise.allSettled([
      fetchTransaction({ from: address }),
      fetchTransaction({ to: address }),
    ]);
    const initialData1 =
      response[0].status === 'fulfilled' ? response[0].value.data : [];
    const initialData2 =
      response[1].status === 'fulfilled' ? response[1].value.data : [];
    return c.json([...initialData1, ...initialData2]);
  });

export const transactionApi = app;

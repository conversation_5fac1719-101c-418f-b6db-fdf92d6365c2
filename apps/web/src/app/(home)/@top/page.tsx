import { Suspense } from 'react';
import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { ChevronRight } from 'lucide-react';
import Image from 'next/image';
import { Button, Thin, Text } from '@repo/ui/components';
import { Taostats } from '@/components/icons/taostats';
import { AnalyticCards } from '@/components/views/home/<USER>';
import { BittensorChartSectionDynamic } from '@/components/views/home/<USER>';
import { ChartSectionSkeleton } from '@/components/views/home/<USER>';
import { HomeTableGroupDynamic } from '@/components/views/home/<USER>';
import { prefetchStats } from '@/lib/server-utils';
import { makeQueryClient } from '@/lib/utils/make-query-client';

/**
 * Server component that prefetches chart section data using React Query
 * and provides it to the client component via HydrationBoundary
 */
export default function TopSection() {
  return (
    <>
      <Suspense fallback={<ChartSectionSkeleton />}>
        <BittensorChartSectionComponent />
      </Suspense>
      <HomeTableGroupDynamic />
      <div className='px-3 md:px-10'>
        <div className='group relative w-full space-y-3 overflow-hidden rounded-3xl border border-neutral-900 p-4 md:h-80 md:p-20'>
          <Image
            width={300}
            height={320}
            src='https://taostats-static.b-cdn.net/card-bg-dark.png'
            className='pointer-events-none absolute left-0 top-0 h-full w-full object-cover transition duration-1000 group-hover:scale-125'
            alt='bg-taostats'
          />
          <Text level='md' className='text-ocean flex items-center gap-1'>
            <Taostats fontSize={8} />
            Taostats
            <Thin>DASHBOARD</Thin>
          </Text>
          <Text level='lgTitle' className='max-w-lg text-white'>
            Manage your Stake, Portfolio and API access all in one place.
          </Text>
          <Button variant='link' asChild className='p-0'>
            <span>
              View Dashboard
              <ChevronRight size={16} />
            </span>
          </Button>
        </div>
      </div>
    </>
  );
}

const BittensorChartSectionComponent = async () => {
  const queryClient = makeQueryClient();

  await Promise.all([prefetchStats(queryClient)]);

  return (
    <>
      <HydrationBoundary state={dehydrate(queryClient)}>
        <BittensorChartSectionDynamic />
      </HydrationBoundary>
      <div className='flex items-center justify-center px-3 md:px-10 min-[1100px]:hidden'>
        <AnalyticCards />
      </div>
    </>
  );
};

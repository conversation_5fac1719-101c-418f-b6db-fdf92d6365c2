import { Suspense } from 'react';
import { SessionProvider } from 'next-auth/react';
import NextTopLoader from 'nextjs-toploader';
import { cn, everettFont, fontFira } from '@repo/ui/lib';
import ScrollToTop from '@/components/elements/scroll-to-top';
import { TopNavbarPrefetched } from '@/components/navigation/top-navbar-prefetched';
import { TopNavbarSkeleton } from '@/components/navigation/top-navbar-skeleton';
import { constructMetadata } from '@/lib/meta';
import ReactQueryProvider from '@/lib/providers/react-query-provider';
import { getServerSession } from '@/lib/utils/auth-utils';
import './globals.css';

export const metadata = constructMetadata({});
export const revalidate = 10;

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const { session } = await getServerSession();

  return (
    <html lang='en'>
      <head>
        <meta
          name='viewport'
          content='width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no'
        />
      </head>
      <body
        id='root'
        className={cn(
          'font-everett relative flex min-h-screen flex-col overflow-x-hidden',
          everettFont.variable,
          fontFira.variable
        )}
      >
        <ReactQueryProvider>
          <NextTopLoader
            color='#00DBBC'
            initialPosition={0.08}
            crawlSpeed={200}
            crawl
            height={3}
            easing='cubic-bezier(0.5,0.21,0,1)'
            showSpinner={false}
            shadow='0 0 10px #00DBBC,0 0 5px #00DBBC'
          />
          <SessionProvider basePath='/api/auth' session={session}>
            <Suspense fallback={<TopNavbarSkeleton />}>
              <TopNavbarPrefetched />
            </Suspense>
          </SessionProvider>
          {children}
          <ScrollToTop />
          {/* <Toaster /> */}
        </ReactQueryProvider>
      </body>
    </html>
  );
}

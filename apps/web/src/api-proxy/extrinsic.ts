/* eslint-disable @typescript-eslint/no-explicit-any -- TODO: using `any` here is legacy from the website, fix later */
'use server';

import { ExtrinsicOrder } from '@repo/types/website-api-types';
import type {
  ExtrinsicAPI,
  ExtrinsicQueryParams,
  WeightEventParams,
} from '@repo/types/website-api-types';
import { fetchValidatorHistory } from './validators';
import { get } from '.';
import { constructURL } from '@/lib/utils';

export async function fetchExtrinsics(params: ExtrinsicQueryParams) {
  const response = await get<ExtrinsicAPI>(
    constructURL('/extrinsic/v1', params)
  );

  return response;
}

export async function fetchWeightEvents({
  cold_key: coldKey,
}: WeightEventParams) {
  const response = await Promise.allSettled([
    fetchValidatorHistory({
      hotkey: coldKey,
    }),
  ]);

  const responseData =
    response[0].status === 'fulfilled' ? response[0].value.data : null;

  if (!responseData) {
    return '';
  }

  const initialResponse = await Promise.allSettled([
    fetchExtrinsics({
      signer_address: responseData[0].coldkey.ss58,
      full_name: 'set_root_weights',
      limit: 11,
      order: ExtrinsicOrder.TimestampDesc,
    }),
  ]);

  const initialData =
    initialResponse[0].status === 'fulfilled'
      ? initialResponse[0].value.data
      : [];

  const getAllUpdates: any = [];

  initialData.forEach((item) => {
    const timestamp = item.timestamp;
    const sns = item.call_args.dests as number[];
    const weights = item.call_args.weights as number[];

    const weightSum = weights.reduce((acc: number, w: number) => acc + w, 0);
    const subnetWeights: Record<string, number> = {};
    sns.forEach((node: number, idx: number) => {
      subnetWeights[node] = (weights[idx] / weightSum) * 100;
    });
    const temp = {
      timestamp,
      sns,
      weights,
      subnetWeights,
    };
    getAllUpdates.push(temp);
  });

  return getAllUpdates;
}

'use server';

import { utc } from 'moment';
import { BlockFrequency } from '@repo/types/website-api-types';
import type {
  BlockAPI,
  BlockIntervalAPI,
  BlockIntervalQueryParams,
  BlockQueryParams,
} from '@repo/types/website-api-types';
import { get } from '.';
import { constructURL } from '@/lib/utils';

export async function fetchBlocks(params: BlockQueryParams) {
  const response = await get<BlockAPI>(constructURL('/block/v1', params));

  return response;
}

export async function fetchBlockInterval(params: BlockIntervalQueryParams) {
  const response = await get<BlockIntervalAPI>(
    constructURL('/block/interval/v1', params)
  );

  return response;
}

export async function fetchBlockIntervalChart(frequency: BlockFrequency) {
  const end = utc().endOf('hour').unix();
  const start = utc()
    .subtract(frequency === BlockFrequency.ByDay ? 366 : 32, 'days')
    .startOf('hour')
    .unix();

  const responses = await Promise.allSettled(
    Array.from({ length: frequency === BlockFrequency.ByDay ? 2 : 4 }).map(
      async (_, index) =>
        fetchBlockInterval({
          timestamp_start: start,
          timestamp_end: end,
          frequency,
          page: index + 1,
          limit: 200,
        })
    )
  );

  const blocks = responses.flatMap((item) => {
    if (item.status === 'fulfilled') {
      return item.value.data;
    }

    return [];
  });

  return blocks;
}

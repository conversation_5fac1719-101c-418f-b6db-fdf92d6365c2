/* eslint-disable @typescript-eslint/no-explicit-any -- TODO: using `any` here is legacy from the website, fix later */
'use server';

import moment from 'moment';
import type {
  MetagraphAPI,
  MetagraphHistoryQueryParams,
  MetagraphQueryParams,
  RootMetagraphAPI,
  RootMetagraphHistoryAPI,
  RootMetagraphHistoryParams,
  RootMetagraphQueryParams,
} from '@repo/types/website-api-types';
import { fetchBlocks } from './blocks';
import { fetchHotkey } from './hotkey';
import { fetchSubnet } from './subnets';
import { get } from '.';
import { constructURL } from '@/lib/utils';

export async function fetchRootMetagraph(params: RootMetagraphQueryParams) {
  const response = await get<RootMetagraphAPI>(
    constructURL('/metagraph/root/latest/v1', params)
  );

  return response;
}

export async function fetchRootSubnet() {
  const response = await Promise.allSettled([
    fetchRootMetagraph({}),
    fetchSubnet({}),
  ]);

  const responseData =
    response[0].status === 'fulfilled' ? response[0].value.data : null;

  const subnets =
    response[1].status === 'fulfilled' ? response[1].value.data : null;

  if (!responseData) {
    return [];
  }

  if (!subnets) {
    return [];
  }

  const minStake = 0;
  const allValiWeights: Record<string, Record<string, string>> = {};
  const subnetWeights: Record<string, number[]> = {};

  responseData.forEach((weight) => {
    if (Number.parseFloat(weight.stake) > minStake) {
      const hk = weight.hotkey.ss58;
      const weights = weight.subnet_weights;
      allValiWeights[hk] = weights;
    }
  });

  for (const [_, subnetsList] of Object.entries(allValiWeights)) {
    for (const [subnet, weight] of Object.entries(subnetsList)) {
      const floatWeight = Number.parseFloat(weight);
      if (!(subnet in subnetWeights)) {
        subnetWeights[subnet] = [floatWeight];
      } else {
        subnetWeights[subnet].push(floatWeight);
      }
    }
  }

  const subnetAvgWeights: Record<
    string,
    {
      avgWeight: number;
      stdDev: number;
    }
  > = {};

  for (const [subnet, weights] of Object.entries(subnetWeights)) {
    const avgWeight =
      Number(
        subnets.filter((item) => item.netuid === Number(subnet))[0]?.emission ??
          0
      ) / 10000000;

    const stdDev =
      Math.sqrt(
        weights.reduce((sum, weight) => sum + (weight - avgWeight) ** 2, 0)
      ) /
        (weights.filter((weight) => weight > 0).length - 1) <
      1
        ? 1
        : Math.sqrt(
            weights.reduce((sum, weight) => sum + (weight - avgWeight) ** 2, 0)
          ) /
          (weights.filter((weight) => weight > 0).length - 1);

    subnetAvgWeights[subnet] = { avgWeight, stdDev };
  }

  const validatorScores: Record<string, Record<string, number>> = {};

  for (const [validator, subnetsTemp] of Object.entries(allValiWeights)) {
    for (const [subnet, weight] of Object.entries(subnetsTemp)) {
      const floatWeight = Number.parseFloat(weight);
      const { avgWeight, stdDev } = subnetAvgWeights[subnet];

      let zScore: number;
      if (stdDev === 0) {
        zScore = 0;
      } else {
        zScore = (floatWeight - avgWeight) / stdDev;
      }

      if (!validatorScores[validator]) {
        validatorScores[validator] = {};
      }
      validatorScores[validator][subnet] = zScore;
    }
  }

  return { score: validatorScores };
}

export async function fetchRootMetagraphHistory(
  params: RootMetagraphHistoryParams
) {
  const response = await get<RootMetagraphHistoryAPI>(
    constructURL('/metagraph/root/history/v1', params)
  );

  return response;
}

export async function fetchMetagraph(params: MetagraphQueryParams) {
  const response = await get<MetagraphAPI>(
    constructURL('/metagraph/latest/v1', params)
  );

  return response;
}

export async function fetchMetagraphHistory(
  params: MetagraphHistoryQueryParams
) {
  const response = await get<MetagraphAPI>(
    constructURL('/metagraph/history/v1', params)
  );

  return response;
}

// export async function fetchNeuronHistory(params: any) {
//   const response = await get(constructURL('/neuron/history/v1', params));

//   return response;
// }

// export async function fetchMinerIncentiveDistribution(params: any) {
//   const response = await get(
//     constructURL('/neuron/incentive_distribution/v1', params)
//   );

//   return response;
// }

export async function fetchHistoricalWeights({
  subnetId,
  from,
  to,
}: {
  subnetId: number;
  from?: number;
  to?: number;
}) {
  const stupidTimeFix = (timestamp: string) => {
    let dateObj: any;
    if (timestamp.length > 20) {
      dateObj = moment(timestamp, 'YYYY-MM-DDTHH:mm:ss.SSS[Z]');
    } else {
      dateObj = moment(timestamp, 'YYYY-MM-DDTHH:mm:ss[Z]');
    }

    return dateObj as string;
  };

  const blocksBack = (from ?? 60) * 7200;

  const response = await Promise.allSettled([fetchBlocks({ limit: 1 })]);

  const blockTotal =
    response[0].status === 'fulfilled'
      ? response[0].value.data[0].block_number
      : 0;

  const blockEnd = blockTotal - (to ?? 0) * 7200;

  const blockStart = blockEnd - blocksBack;

  const subnetWeightData: Record<
    string,
    Record<
      string,
      {
        hotkey: string;
        weight: number;
      }[]
    >
  > = {};

  const eventResponses = await Promise.allSettled([
    fetchRootMetagraphHistory({
      block_start: blockStart,
      block_end: blockEnd,
      page: 1,
      limit: 200,
    }),
  ]);

  const totalPages =
    eventResponses[0].status === 'fulfilled'
      ? eventResponses[0].value.pagination.total_pages
      : 0;

  await Promise.all(
    Array.from({ length: totalPages }).map(async (_, index) => {
      const temp = await Promise.allSettled([
        fetchRootMetagraphHistory({
          block_start: blockStart,
          block_end: blockEnd,
          page: index + 1,
          limit: 200,
        }),
      ]);

      const valiData = temp[0].status === 'fulfilled' ? temp[0].value.data : [];

      valiData.forEach((vali) => {
        const hk = vali.hotkey.ss58;
        const time = stupidTimeFix(vali.timestamp);
        const date = moment(time, 'YYYY-MM-DDTHH:mm:ss[Z]');
        const stringDate = date.format('YYYY-MM-DDTHH:mm:ss');
        const weights = vali.subnet_weights;
        const stake = Number(vali.stake) / 1e9;

        for (const [idx, value] of Object.entries(weights)) {
          const netuid = Number.parseInt(idx);
          const weight = Number.parseFloat(value);
          if (!(netuid in subnetWeightData)) {
            subnetWeightData[netuid] = {};
          }
          if (!(stringDate in subnetWeightData)) {
            subnetWeightData[netuid][stringDate] = [];
          }
          if (weight > 0 && stake > 1024) {
            subnetWeightData[netuid][stringDate].push({
              hotkey: hk,
              weight,
            });
          }
        }
      });
    })
  );

  const tempData: Record<
    string,
    {
      x: string;
      y: number;
    }[]
  > = {};

  Object.entries(subnetWeightData[subnetId]).forEach(([timestamp, hotkeys]) => {
    (hotkeys as { hotkey: string; weight: number }[]).forEach(
      ({ hotkey, weight }) => {
        if (!tempData[hotkey]) {
          tempData[hotkey] = [];
        }
        tempData[hotkey].push({ x: timestamp, y: weight });
      }
    );
  });
  return Object.keys(tempData).map((key) => ({
    label: key,
    data: tempData[key],
  }));
}

export async function fetchMetagraphChildHotkey({
  hotkey,
  netuid,
}: {
  hotkey?: string;
  netuid?: number;
}) {
  const responses = await Promise.allSettled([
    fetchHotkey({
      hotkey,
      netuid,
      limit: 100,
    }),
  ]);

  const hotkeyFamilyData =
    responses[0].status === 'fulfilled' ? responses[0].value : null;

  const parentHotkeyData = hotkeyFamilyData?.data.filter(
    (item) => item.parents.length > 0
  );

  const finalData =
    parentHotkeyData?.map((item) => {
      return {
        parent: item.parents.map((parent) => ({
          hotkey: parent.hotkey.ss58,
          subnetId: item.netuid,
          stake: parent.proportion_total_alpha_stake,
          proportion: Number(parent.proportion),
          take: parent.take,
          root_weight: parent.proportion_root_stake,
          root_stake_as_alpha: parent.proportion_root_stake_as_alpha,
          alpha_weight: parent.alpha_stake,
        })),
        child: {
          hotkey: item.hotkey.ss58,
          stake: item.total_alpha_stake,
          take: item.childkey_take,
          root_weight: item.root_stake,
          root_stake_as_alpha: item.root_stake_as_alpha,
          alpha_weight: item.alpha_stake,
          subnetId: item.netuid,
          proportion:
            1 -
            item.children.reduce((sum: number, child: any) => {
              return sum + child.proportion;
            }, 0),
        },
      };
    }) ?? [];

  return finalData[0];
}

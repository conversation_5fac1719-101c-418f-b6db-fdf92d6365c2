'use server';

import type {
  StatsAPI,
  StatsHistoryAPI,
  StatsHistoryQueryParams,
} from '@repo/types/website-api-types';
import { Frequency } from '@repo/types/website-api-types';
import { get } from '.';
import { constructURL } from '@/lib/utils';

export async function fetchStats() {
  const response = await get<StatsAPI>('/stats/latest/v1');

  return response;
}

export async function fetchStatsHistory(params: StatsHistoryQueryParams) {
  const response = await get<StatsHistoryAPI>(
    constructURL('/stats/history/v1', params)
  );

  return response;
}

export async function fetchYield(params: StatsHistoryQueryParams) {
  const response = await Promise.allSettled([
    fetchStatsHistory({
      ...(params.timestamp_start !== undefined && {
        timestamp_start: params.timestamp_start,
      }),
    }),
  ]);
  const total =
    response[0].status === 'fulfilled'
      ? response[0].value.pagination.total_items
      : 0;

  const responses = await Promise.allSettled(
    Array.from({ length: Math.ceil(total / 200) }).map((_, index) =>
      fetchStatsHistory({
        ...(params.timestamp_start !== undefined && {
          timestamp_start: params.timestamp_start,
        }),
        page: index + 1,
        limit: 200,
      })
    )
  );

  const data = responses.flatMap((item) =>
    item.status === 'fulfilled' ? item.value.data : []
  );

  return data;
}

export async function fetchAllStatsHistory() {
  const initResponse = await Promise.allSettled([
    fetchStatsHistory({ limit: 200, frequency: Frequency.ByDay }),
  ]);

  const initStats =
    initResponse[0].status === 'fulfilled' ? initResponse[0].value : null;

  if (!initStats?.data) {
    return [];
  }

  const responses = await Promise.allSettled(
    Array.from({ length: initStats.pagination.total_pages - 1 }).map(
      (_, index) =>
        fetchStatsHistory({
          frequency: Frequency.ByDay,
          page: index + 2,
          limit: 200,
        })
    )
  );

  const statsHistory = responses.flatMap((item) => {
    if (item.status === 'fulfilled') {
      return item.value.data;
    }

    return [];
  });

  return [...initStats.data, ...statsHistory].sort(
    (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );
}

export async function fetchDailyNumberSubnets() {
  const initResponse = await Promise.allSettled([
    fetchStatsHistory({ limit: 200 }),
  ]);

  const days =
    initResponse[0].status === 'fulfilled'
      ? initResponse[0].value.pagination.total_items
      : 0;

  const limit = 200;
  const snCount: { subnets: number; timestamp: string }[] = [];

  const numPages = Math.ceil(days / limit);

  await Promise.all(
    Array.from({ length: numPages }).map(async (_, index) => {
      const page = index + 1;
      const dailyResponses = await Promise.allSettled([
        fetchStatsHistory({
          limit,
          page,
        }),
      ]);

      const dailyNumber =
        dailyResponses[0].status === 'fulfilled'
          ? dailyResponses[0].value.data
          : [];

      dailyNumber.map((item) =>
        snCount.push({ subnets: item.subnets, timestamp: item.timestamp })
      );
    })
  );

  return snCount.reverse();
}

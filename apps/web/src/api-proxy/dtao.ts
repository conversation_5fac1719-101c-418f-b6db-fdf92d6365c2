'use server';

import { format, addDays, parseISO } from 'date-fns';
import emojiRegex from 'emoji-regex';
import { utc } from 'moment';
import { PoolHistoryOrder, PoolOrder } from '@repo/types/website-api-types';
import type {
  TradingviewHistory,
  ColdkeyAlphaSharesQueryParams,
  DtaoDelegateQueryParams,
  HotkeyAlphaSharesLatestAPI,
  HotkeyAlphaSharesLatestQueryParams,
  HotkeyAlphaSharesQueryParams,
  HotkeyEmissionQueryParams,
  PoolAPI,
  PoolHistoryAPI,
  PoolHistoryQueryParams,
  PoolQueryParams,
  PoolTotalPriceHistoryAPI,
  PoolTotalPriceHistoryQueryParams,
  StakeBalance,
  StakeBalanceAggregatedQueryParams,
  StakeBalanceAPI,
  StakeBalanceHistoryQueryParams,
  StakeBalanceQueryParams,
  SubnetEmissionQueryParams,
  TradingviewHistoryQueryParams,
  ValidatorYieldLatestQueryParams,
  SubnetEmissionAPI,
  HotkeyEmissionAPI,
  HotkeyAlphaSharesAPI,
  ColdkeyAlphaSharesAPI,
  StakeBalanceAggregatedAPI,
  ValidatorYieldLatestAPI,
} from '@repo/types/website-api-types';
import { fetchSubnetIdentity, fetchSubnet } from './subnets';
import { fetchDtaoValidatorLatest } from './validators';
import { get } from '.';
import { constructURL } from '@/lib/utils';

type DateRange = {
  from: string;
  to: string;
};

export async function fetchDtaoDelegations(params: DtaoDelegateQueryParams) {
  const response = await get(constructURL('/dtao/delegation/v1', params));

  return response;
}

export async function fetchPool(params: PoolQueryParams) {
  const response = await get<PoolAPI>(
    constructURL('/dtao/pool/latest/v1', params)
  );

  return response;
}

export async function fetchPoolHistory(params: PoolHistoryQueryParams) {
  const response = await get<PoolHistoryAPI>(
    constructURL('/dtao/pool/history/v1', params)
  );

  return response;
}

export async function fetchTaoStakedAlpha(params: PoolHistoryQueryParams) {
  const taoStaked: Record<
    number,
    { stakes: number[]; times: string[] } | undefined
  > = {};
  const taoStakedTime: Record<string, number> = {};

  const response = await Promise.allSettled([
    fetchPoolHistory({
      frequency: params.frequency,
      timestamp_start: params.timestamp_start,
      limit: 200,
    }),
  ]);
  const total =
    response[0].status === 'fulfilled'
      ? response[0].value.pagination.total_items
      : 0;

  const responses = await Promise.allSettled(
    Array.from({ length: Math.ceil(total / 200) }).map((_, index) =>
      fetchPoolHistory({
        frequency: params.frequency,
        timestamp_start: params.timestamp_start,
        page: index + 1,
        order: PoolHistoryOrder.TimestampAsc,
        limit: 200,
      })
    )
  );

  const data = responses.flatMap((item) =>
    item.status === 'fulfilled' ? item.value.data : []
  );

  data.forEach((pool) => {
    const netuid = pool.netuid;
    if (netuid > 0) {
      if (!taoStaked[netuid]) {
        taoStaked[netuid] = { stakes: [], times: [] };
      }
      const rootStaked = Number.parseFloat(pool.total_tao) / 1e9;
      const timestamp = pool.timestamp;
      if (!taoStakedTime[timestamp]) {
        taoStakedTime[timestamp] = 0;
      }

      taoStaked[netuid].stakes.push(rootStaked);
      taoStaked[netuid].times.push(timestamp);
      taoStakedTime[timestamp] += rootStaked;
    }
  });

  return taoStakedTime;
}

export async function fetchTaoStakedRoot(params: PoolHistoryQueryParams) {
  const response = await Promise.allSettled([
    fetchPoolHistory({
      netuid: 0,
      frequency: params.frequency,
      timestamp_start: params.timestamp_start,
      limit: 200,
    }),
  ]);
  const total =
    response[0].status === 'fulfilled'
      ? response[0].value.pagination.total_items
      : 0;

  const responses = await Promise.allSettled(
    Array.from({ length: Math.ceil(total / 200) }).map((_, index) =>
      fetchPoolHistory({
        netuid: 0,
        frequency: params.frequency,
        timestamp_start: params.timestamp_start,
        page: index + 1,
        order: PoolHistoryOrder.TimestampAsc,
        limit: 200,
      })
    )
  );

  const data = responses.flatMap((item) =>
    item.status === 'fulfilled' ? item.value.data : []
  );

  return data;
}

export async function fetchTotalPriceLatest() {
  const response = await get(
    constructURL('/dtao/pool/total_price/latest/v1', {})
  );

  return response;
}

export async function fetchSubnetEmission(params: SubnetEmissionQueryParams) {
  const response = await get<SubnetEmissionAPI>(
    constructURL('/dtao/subnet_emission/v1', params)
  );

  return response;
}

export async function fetchHotkeyEmission(params: HotkeyEmissionQueryParams) {
  const response = await get<HotkeyEmissionAPI>(
    constructURL('/dtao/hotkey_emission/v1', params)
  );

  return response;
}

export async function fetchTradingviewHistory(
  params: TradingviewHistoryQueryParams
) {
  const response = await get(
    constructURL('/dtao/tradingview/udf/history', params)
  );

  return response;
}

export async function fetchSubnetPrice({
  id,
  type,
  timestamp_start: timestampStart,
}: {
  id: number;
  type: string;
  timestamp_start: string;
}) {
  const createDateRanges = (
    startDate: string,
    endDate: string
  ): DateRange[] => {
    const start = parseISO(startDate);
    const end = parseISO(endDate);
    const ranges: DateRange[] = [];

    let currentStart = start;

    while (currentStart < end) {
      let currentEnd = addDays(currentStart, 19);
      if (currentEnd > end) {
        currentEnd = end;
      }

      const range: DateRange = {
        from: format(currentStart, 'yyyy-MM-dd'),
        to: format(currentEnd, 'yyyy-MM-dd'),
      };

      ranges.push(range);
      currentStart = currentEnd;
    }

    return ranges;
  };

  const data = createDateRanges(
    timestampStart,
    format(new Date(), 'yyyy-MM-dd')
  );

  const responses = await Promise.allSettled(
    Array.from({ length: data.length }).map((_, index) =>
      fetchTradingviewHistory({
        symbol: `SUB-${id}`,
        resolution: type,
        from: utc(data[index].from).startOf('day').unix(),
        to: utc(data[index].to).startOf('day').unix(),
      })
    )
  );

  const result = responses.flatMap((item) => {
    if (item.status === 'fulfilled') {
      const temp = item.value;

      return temp;
    }

    return [];
  });

  const finalResult = result.reduce(
    (acc, item) => {
      if (item.t) acc.t.push(...item.t);
      if (item.c) acc.c.push(...item.c);
      if (item.o) acc.o.push(...item.o);
      if (item.h) acc.h.push(...item.h);
      if (item.l) acc.l.push(...item.l);
      if (item.v) acc.v.push(...item.v);
      return acc;
    },
    { s: 'ok', t: [], c: [], o: [], h: [], l: [], v: [] }
  ) as TradingviewHistory;

  return finalResult;
}

export async function fetchHotkeyAlphaShares(
  params: HotkeyAlphaSharesQueryParams
) {
  const response = await get<HotkeyAlphaSharesAPI>(
    constructURL('/dtao/hotkey_alpha_shares/history/v1', params)
  );

  return response;
}

export async function fetchHotkeyAlphaSharesLatest(
  params: HotkeyAlphaSharesLatestQueryParams
) {
  const response = await get<HotkeyAlphaSharesLatestAPI>(
    constructURL('/dtao/hotkey_alpha_shares/latest/v1', params)
  );

  return response;
}

export async function fetchColdkeyAlphaShares(
  params: ColdkeyAlphaSharesQueryParams
) {
  const response = await get<ColdkeyAlphaSharesAPI>(
    constructURL('/dtao/coldkey_alpha_shares/history/v1', params)
  );

  return response;
}

export async function fetchValidatorYieldLatest(
  params: ValidatorYieldLatestQueryParams
) {
  const response = await get<ValidatorYieldLatestAPI>(
    constructURL('/dtao/validator/yield/latest/v1', params)
  );

  return response;
}

export async function fetchStakeBalance(params: StakeBalanceQueryParams) {
  const response = await get<StakeBalanceAPI>(
    constructURL('/dtao/stake_balance/latest/v1', params)
  );

  return response;
}

export async function fetchPoolTotalPriceHistory(
  params: PoolTotalPriceHistoryQueryParams
) {
  const response = await get<PoolTotalPriceHistoryAPI>(
    constructURL('/dtao/pool/total_price/history/v1', params)
  );

  return response;
}

export async function fetchAllPoolTotalPriceHistory() {
  const response = await Promise.allSettled([
    fetchPoolTotalPriceHistory({ frequency: 'by_day' }),
  ]);

  const total =
    response[0].status === 'fulfilled'
      ? response[0].value.pagination.total_pages
      : 0;

  const responses = await Promise.allSettled(
    Array.from({ length: Math.ceil(total / 1024) }).map((_, index) =>
      fetchPoolTotalPriceHistory({
        frequency: 'by_day',
        page: index + 1,
        limit: 1024,
      })
    )
  );

  const data = responses.flatMap((item) =>
    item.status === 'fulfilled' ? item.value.data : []
  );

  return data;
}

export async function fetchStakeBalanceAggregated(
  params: StakeBalanceAggregatedQueryParams
) {
  const response = await get<StakeBalanceAggregatedAPI>(
    constructURL('/dtao/stake_balance_aggregated/latest/v1', params)
  );

  return response;
}

export async function fetchStakeBalanceHistory(
  params: StakeBalanceHistoryQueryParams
) {
  const response = await get(
    constructURL('/dtao/stake_balance/history/v1', params)
  );

  return response;
}

export async function fetchAllStakeBalances({
  coldkey,
  hotkey,
  netuid,
}: {
  coldkey: string;
  hotkey?: string;
  netuid?: number;
}) {
  const query = {
    coldkey,
    ...(hotkey !== undefined && { hotkey }),
    ...(netuid !== undefined && { netuid }),
    page: 1,
    limit: 200,
  };

  const firstPageResponse = await Promise.allSettled([
    fetchStakeBalance(query),
  ]);

  const totalPages =
    firstPageResponse[0].status === 'fulfilled'
      ? firstPageResponse[0].value.pagination.total_pages
      : 0;

  const remainingPageResponses = await Promise.allSettled(
    Array.from({ length: totalPages })
      .map((_, index) => index + 1) // Generate page numbers
      .slice(1) // Skip first page
      .map((page) =>
        fetchStakeBalance({
          coldkey,
          page,
          limit: 200,
        })
      )
  );

  const result = [firstPageResponse[0], ...remainingPageResponses].flatMap(
    (item) => {
      if (item.status === 'fulfilled') {
        const temp = item.value;

        return temp.data;
      }

      return [];
    }
  );

  return result;
}

export async function fetchStakeBalanceList(params: {
  coldkey: string;
  hotkey?: string;
  netuid?: number;
}) {
  const stakeBalance = await fetchAllStakeBalances({ ...params });

  type Result = Record<string, Set<string>>;

  const result = stakeBalance.reduce((acc: Result, item: StakeBalance) => {
    const netuid = item.netuid;
    if (!acc[netuid]) {
      acc[item.netuid] = new Set();
    }
    acc[item.netuid].add(item.hotkey.ss58);
    return acc;
  }, {});

  const jsonResult: Record<string, string[]> = {};
  for (const key in result) {
    jsonResult[key] = Array.from(result[key]);
  }

  return jsonResult;
}

export async function fetchDtaoSubnet({
  netuid,
  page,
  limit,
  order,
}: {
  netuid?: number;
  page?: number;
  limit?: number;
  order?: PoolOrder;
}) {
  const response = await Promise.allSettled([
    fetchPool({
      ...(limit !== undefined && { limit }),
      ...(netuid !== undefined && { netuid }),
      ...(page !== undefined && { page }),
      ...(order !== undefined && { order }),
    }),
    fetchSubnetIdentity({}),
    fetchSubnet({
      ...(netuid !== undefined && { netuid }),
    }),
  ]);

  const res = response[0].status === 'fulfilled' ? response[0].value.data : [];

  const pagination =
    response[0].status === 'fulfilled'
      ? response[0].value.pagination.total_items
      : 0;

  const metadata =
    response[1].status === 'fulfilled' ? response[1].value.data : [];

  const subnet =
    response[2].status === 'fulfilled' ? response[2].value.data : [];

  const result = res.map((item) => {
    const currentSubnet = subnet.find((it) => it.netuid === item.netuid);
    const currentMetadata = metadata.find((it) => it.netuid === item.netuid);

    return {
      ...item,
      subnet_name: currentMetadata?.subnet_name.replace(emojiRegex(), '') ?? '',
      subnet_description: '',
      description: currentMetadata?.description ?? '',
      recycled_lifetime: currentSubnet?.recycled_lifetime ?? '',
      recycled_24_hours: currentSubnet?.recycled_24_hours ?? '',
      recycled_since_registration:
        currentSubnet?.recycled_since_registration ?? '',
      registration_timestamp: currentSubnet?.registration_timestamp ?? '',
      owner: currentSubnet?.owner?.ss58 ?? '',
      emission: currentSubnet?.emission ?? '',
      neuron_registration_cost: currentSubnet?.neuron_registration_cost ?? '',
      active_keys: currentSubnet?.active_keys ?? 0,
      max_neurons: currentSubnet?.max_neurons ?? 0,
      github: currentMetadata?.github_repo ?? '',
      discord_url: currentMetadata?.discord ?? '',
      rank: item.rank,
      subnet_contact: currentMetadata?.subnet_contact ?? '',
      subnet_url: currentMetadata?.subnet_url ?? '',
    };
  });

  return { data: result, total: pagination };
}

export async function fetchDtaoSubnetData() {
  const response = await Promise.allSettled([
    fetchPool({ ...{ order: PoolOrder.MarketCapDesc } }),
    fetchSubnetIdentity({}),
    fetchSubnet({}),
  ]);

  const res = response[0].status === 'fulfilled' ? response[0].value.data : [];

  // const pagination = response[0].status === 'fulfilled' ? response[0].value.total : 0;
  const pagination =
    response[0].status === 'fulfilled'
      ? response[0].value.pagination.total_items
      : 0;

  const metadata =
    response[1].status === 'fulfilled' ? response[1].value.data : [];

  const subnet =
    response[2].status === 'fulfilled' ? response[2].value.data : [];

  const result = res.map((item) => {
    const currentSubnet = subnet.find((it) => it.netuid === item.netuid);
    const currentMetadata = metadata.find((it) => it.netuid === item.netuid);

    return {
      ...item,
      subnet_name: currentMetadata?.subnet_name ?? '',
      subnet_description: '',
      description: currentMetadata?.description ?? '',
      recycled_lifetime: currentSubnet?.recycled_lifetime ?? '',
      recycled_24_hours: currentSubnet?.recycled_24_hours ?? '',
      recycled_since_registration:
        currentSubnet?.recycled_since_registration ?? '',
      registration_timestamp: currentSubnet?.registration_timestamp ?? '',
      owner: currentSubnet?.owner?.ss58 ?? '',
      emission: currentSubnet?.emission ?? '',
      neuron_registration_cost: currentSubnet?.neuron_registration_cost ?? '',
      active_keys: currentSubnet?.active_keys ?? 0,
      max_neurons: currentSubnet?.max_neurons ?? 0,
      github: currentMetadata?.github_repo ?? '',
      discord_url: currentMetadata?.discord ?? '',
      rank: item.rank,
      subnet_contact: currentMetadata?.subnet_contact ?? '',
      subnet_url: currentMetadata?.subnet_url ?? '',
    };
  });
  return { data: result, total: pagination };
}

export async function fetchTradingDelegations(params: DtaoDelegateQueryParams) {
  const response = await get(constructURL('/dtao/delegation/v1', params));

  return response;
}

export async function fetchHeatmapHotkeyNetuid() {
  const subnetResponse = await Promise.allSettled([fetchSubnet({})]);
  const subnetCount =
    subnetResponse[0].status === 'fulfilled'
      ? subnetResponse[0].value.pagination.total_items
      : 0;

  const alphaMin = 1000 * 1e9;
  const alphaHoldings: Record<string, Record<number, number>> = {};

  await Promise.all(
    Array.from({ length: subnetCount }).map(async (_, index) => {
      const res = await Promise.allSettled([
        fetchHotkeyAlphaSharesLatest({
          alpha_min: alphaMin.toString(),
          netuid: index + 1,
          limit: 200,
        }),
      ]);

      const totalPage =
        res[0].status === 'fulfilled' ? res[0].value.pagination.total_items : 0;

      await Promise.all(
        Array.from({ length: totalPage }).map(async () => {
          const response = await Promise.allSettled([
            fetchHotkeyAlphaSharesLatest({
              alpha_min: alphaMin.toString(),
              netuid: index + 1,
              limit: 200,
            }),
          ]);

          const valiData =
            response[0].status === 'fulfilled' ? response[0].value.data : [];

          valiData.forEach((vali) => {
            const hk = vali.hotkey.ss58;
            const alpha = Number.parseFloat(vali.alpha) / 1e9;
            if (alpha > 0) {
              if (!(hk in alphaHoldings)) {
                alphaHoldings[hk] = {};
              }
              alphaHoldings[hk][index + 1] = alpha;
            }
          });
        })
      );
    })
  );

  const alphaHoldingsData: Record<string, Record<number, number>> = {};

  const response = await Promise.allSettled([fetchDtaoValidatorLatest({})]);

  const res = response[0].status === 'fulfilled' ? response[0].value.data : [];

  for (const [index, holding] of Object.entries(alphaHoldings)) {
    if (Object.entries(holding).length > 5) {
      let name = index;

      const validator = res.find((item) => item.hotkey.ss58 === index);
      if (validator?.name) {
        name = validator.name;
      } else {
        name = '';
      }

      alphaHoldingsData[name] = holding;
    }
  }

  return alphaHoldingsData;
}

'use server';

import { utc } from 'moment';
import type {
  DtaoValidatorAvailableResponse,
  DtaoValidatorHistoryQueryParams,
  DtaoValidatorLatestAPI,
  DtaoValidatorLatestQueryParams,
  DtaoValidatorPerformanceAPI,
  DtaoValidatorPerformanceLatestQueryParams,
  IdentityAPI,
  IdentityQueryParams,
  MetagraphAPI,
  Validator,
  ValidatorAPI,
  ValidatorHistoryQueryParams,
  ValidatorIdentityQueryParams,
  ValidatorLatestQueryParams,
  ValidatorMetricsAPI,
  ValidatorMetricsHistoryQueryParams,
  ValidatorMetricsLatestQueryParams,
  ValidatorPerformanceAPI,
  ValidatorPerformanceQueryParams,
  ValidatorWeightsHistoryQueryParams,
  ValidatorWeightsLatestQueryParams,
} from '@repo/types/website-api-types';
import {
  DtaoValidatorLatestOrder,
  ValidatorHistoryOrder,
} from '@repo/types/website-api-types';
import { taoDivider, VALIDATOR_MINIMUM_STAKE } from '@repo/ui/lib';
import { fetchHotkey } from './hotkey';
import { get } from '.';
import { weightCopiers } from '@/lib/constants/data/validator';
import { constructURL } from '@/lib/utils';

export async function fetchValidatorHistory(
  params: ValidatorHistoryQueryParams
) {
  const response = await get<ValidatorAPI>(
    constructURL('/validator/history/v1', params)
  );

  return response;
}

export async function fetchValidatorIdentity(
  params: ValidatorIdentityQueryParams
) {
  const response = await get<IdentityAPI>(
    constructURL('/validator/identity/v1', params)
  );

  return response;
}

export async function fetchFullValidators(params: ValidatorLatestQueryParams) {
  const response = await get<ValidatorAPI>(
    constructURL('/validator/latest/v1', params)
  );

  return response;
}

export async function fetchDtaoValidatorHistory(
  params: DtaoValidatorHistoryQueryParams
) {
  const response = await get<DtaoValidatorLatestAPI>(
    constructURL('/dtao/validator/history/v1', params)
  );

  return response;
}

export async function fetchDtaoValidatorLatest(
  params: DtaoValidatorLatestQueryParams
) {
  const response = await get<DtaoValidatorLatestAPI>(
    constructURL('/dtao/validator/latest/v1', params)
  );

  return response;
}

export async function fetchIdentity(params: IdentityQueryParams) {
  const response = await get<IdentityAPI>(
    constructURL('/identity/latest/v1', params)
  );

  return response;
}

export async function fetchTopValidatorsPerSubnet() {
  const response = await get<DtaoValidatorAvailableResponse>(
    constructURL('/dtao/validator/available/v1', {})
  );

  return response.data;
}

export async function fetchAllIdentity() {
  const response = await Promise.allSettled([fetchIdentity({})]);
  const total =
    response[0].status === 'fulfilled'
      ? response[0].value.pagination.total_items
      : 0;

  const responses = await Promise.allSettled(
    Array.from({ length: Math.ceil(total / 200) }).map((_, index) =>
      fetchIdentity({ page: index + 1, limit: 200 })
    )
  );

  const data = responses.flatMap((item) =>
    item.status === 'fulfilled' ? item.value.data : []
  );

  return data;
}

export async function fetchIdentityId(path: string) {
  const response = await Promise.allSettled([
    fetchIdentity({ validator_hotkey: path }),
  ]);

  const res = response[0].status === 'fulfilled' ? response[0].value.data : [];

  const validatorName = res.find((item) => item.validator_hotkey?.ss58 === path)
    ? res.find((item) => item.validator_hotkey?.ss58 === path)?.name ?? ''
    : path;

  return validatorName;
}

export async function fetchDtaoValidatorPerformanceLatest(
  params: DtaoValidatorPerformanceLatestQueryParams
) {
  const response = await get<DtaoValidatorPerformanceAPI>(
    constructURL('/dtao/validator/performance/latest/v1', params)
  );

  return response;
}

export async function fetchValidator(params: ValidatorLatestQueryParams) {
  const response = await Promise.allSettled([fetchFullValidators(params)]);

  const res = response[0].status === 'fulfilled' ? response[0].value.data : [];

  const validators = res
    .filter((item) => Number(item.stake) / taoDivider > VALIDATOR_MINIMUM_STAKE)
    .sort((a, b) => Number(b.stake) - Number(a.stake));

  return validators;
}

export async function fetchDtaoValidator() {
  const response = await fetchDtaoValidatorLatest({
    limit: 200,
    order: DtaoValidatorLatestOrder.GlobalWeightedStakeDesc,
  });

  const validators = response.data
    .filter(
      (item) =>
        Number(item.global_weighted_stake) / taoDivider >
        VALIDATOR_MINIMUM_STAKE
    )
    .sort(
      (a, b) =>
        Number(b.global_weighted_stake) - Number(a.global_weighted_stake)
    );

  return validators;
}

export async function fetchMetricsHistory(
  params: ValidatorMetricsHistoryQueryParams
) {
  const response = await get(
    constructURL('/validator/metrics/history/v1', params)
  );

  return response;
}

export async function fetchMetrics(params: ValidatorMetricsLatestQueryParams) {
  const response = await get<ValidatorMetricsAPI>(
    constructURL('/validator/metrics/latest/v1', params)
  );

  return response;
}

export async function fetchFullMetrics(
  params: ValidatorMetricsLatestQueryParams
) {
  const initialResponse = await Promise.allSettled([
    fetchMetrics({ ...params, limit: 200 }),
  ]);

  const initialData =
    initialResponse[0].status === 'fulfilled' ? initialResponse[0].value : null;

  if (!initialData || initialData.data.length === 0) return [];

  const responses = await Promise.allSettled(
    Array.from({ length: initialData.pagination.total_pages - 1 }).map(
      (_, index) => fetchMetrics({ ...params, page: index + 2, limit: 200 })
    )
  );

  const data = responses.flatMap((item) =>
    item.status === 'fulfilled' ? item.value.data : []
  );

  return [...initialData.data, ...data];
}

export async function fetchPerformance(
  params: ValidatorPerformanceQueryParams
) {
  const response = await get<ValidatorPerformanceAPI>(
    constructURL('/validator/performance/v1', params)
  );

  return response;
}

export async function fetchWeightsHistory(
  params: ValidatorWeightsHistoryQueryParams
) {
  const response = await get(
    constructURL('/validator/weights/history/v1', params)
  );

  return response;
}

export async function fetchWeights(params: ValidatorWeightsLatestQueryParams) {
  const response = await get(
    constructURL('/validator/weights/latest/v2', params)
  );

  return response;
}

export async function fetchTopValidatorHistory(valis: Validator[]) {
  const topValidators = valis
    .filter((item) => !weightCopiers.includes(item.hotkey.ss58))
    .sort(
      (a, b) =>
        Number(b.nominator_return_per_k) - Number(a.nominator_return_per_k)
    )
    .slice(0, 12);

  const validatorResponse = await Promise.allSettled(
    topValidators.map(
      async (item) =>
        await fetchValidatorHistory({
          hotkey: item.hotkey.ss58,
          order: ValidatorHistoryOrder.TimestampDesc,
          limit: 9,
        })
    )
  );

  const validators = validatorResponse.flatMap((item) => {
    if (item.status === 'fulfilled') {
      const history = item.value.data;

      const latestItem = topValidators.find(
        (it) => it.hotkey.ss58 === history[0]?.hotkey.ss58
      );

      if (latestItem) {
        if (history.length > 0) {
          // Check if first entry in the history (i.e. most recent) is from yesterday
          // If so, prepend the latest data to the history and return it
          const yesterday = utc().startOf('day').subtract(1, 'days');
          const timestamp = utc(history[0].timestamp);

          if (timestamp.isSame(yesterday, 'day')) {
            return [latestItem, ...history];
          }
        } else {
          // We have no history data, just return the latest data
          return [latestItem];
        }
      }

      return history;
    }
    return [];
  });

  return {
    validators,
    topValidators: topValidators.map((item) => item.hotkey.ss58),
  };
}

export async function fetchChartData(hotkey: string) {
  const response = await Promise.allSettled(
    Array.from({ length: 3 }).map(async (_, index) => {
      const history = await fetchValidatorHistory({
        hotkey,
        page: index + 1,
        limit: 200,
        order: ValidatorHistoryOrder.TimestampDesc,
      });

      return history.data;
    })
  );

  return response.flatMap((item) =>
    item.status === 'fulfilled' ? item.value : []
  );
}

export async function fetchPerformanceHistory(path: string, subnetId?: number) {
  if (subnetId === undefined) return [];

  const responses = await Promise.allSettled([
    fetchPerformance({
      hotkey: path,
      netuid: subnetId,
      limit: 200,
    }),
  ]);

  return responses.flatMap((item) =>
    item.status === 'fulfilled' ? item.value.data : []
  );
}

export async function fetchChildPerformance(id: string) {
  const response = await Promise.allSettled([
    fetchHotkey({ hotkey: id, limit: 100 }),
  ]);

  const hotkey = response[0].status === 'fulfilled' ? response[0].value : null;

  if (hotkey) {
    const children = hotkey.data.flatMap((item) =>
      item.children.map((child) => ({
        hotkey: child.hotkey,
        netuid: item.netuid,
        stake: child.proportion_staked,
        proportion: child.proportion,
        take: child.childkey_take,
      }))
    );

    const performanceResponses = await Promise.allSettled(
      children.map((item) =>
        get<MetagraphAPI>(
          constructURL('/metagraph/latest/v1', {
            hotkey: item.hotkey.ss58,
            netuid: item.netuid,
          })
        )
      )
    );

    const performance = performanceResponses.flatMap((item) =>
      item.status === 'fulfilled' ? item.value.data : []
    );

    return performance
      .sort((a, b) => a.netuid - b.netuid)
      .map((item) => {
        const child = children.find(
          (p) => p.hotkey.ss58 === item.hotkey.ss58 && p.netuid === item.netuid
        );

        return {
          ...item,
          parentStake: child?.stake,
          percentage: child?.proportion,
          take: child?.take,
        };
      });
  }

  return [];
}

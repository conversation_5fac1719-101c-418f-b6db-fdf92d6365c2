'use server';

import { utc } from 'moment';
import { AccountOrder } from '@repo/types/website-api-types';
import type {
  AccountAPI,
  AccountHistoryQueryParams,
  AccountQueryParams,
} from '@repo/types/website-api-types';
import { get } from '.';
import { constructURL } from '@/lib/utils';

export async function fetchAccounts(params: AccountQueryParams) {
  const response = await get<AccountAPI>(
    constructURL('/account/latest/v1', params)
  );

  return response;
}

export async function fetchAccountHistory(params: AccountHistoryQueryParams) {
  const response = await get<AccountAPI>(
    constructURL('/account/history/v1', params)
  );

  return response;
}

export async function fetchRecentAccounts() {
  const end = utc().endOf('hour').unix();
  const start = utc().subtract(24, 'hours').startOf('hour').unix();

  const initialResponses = await Promise.allSettled([
    fetchAccounts({
      created_on_timestamp_start: start,
      limit: 200,
      order: AccountOrder.CreatedAtTimeStampDesc,
    }),
  ]);

  const initialData =
    initialResponses[0].status === 'fulfilled'
      ? initialResponses[0].value
      : null;

  if (!initialData) {
    return [];
  }

  const responses = await Promise.allSettled(
    Array.from({ length: initialData.pagination.total_pages - 1 }).map(
      async (_, index) =>
        fetchAccounts({
          created_on_timestamp_start: start,
          created_on_timestamp_end: end,
          page: index + 2,
          limit: 200,
          order: AccountOrder.CreatedAtTimeStampDesc,
        })
    )
  );

  const data = responses.flatMap((item) =>
    item.status === 'fulfilled' ? item.value.data : []
  );

  return [...initialData.data, ...data];
}

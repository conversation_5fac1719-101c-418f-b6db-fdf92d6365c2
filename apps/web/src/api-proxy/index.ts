import axios from 'axios';
import { WEBSITE_API_TOKEN, WEBSITE_API_URL } from '@/lib/config';

export async function get<ResponseType>(path: string) {
  const response = await axios.get<ResponseType>(`${WEBSITE_API_URL}${path}`, {
    headers: {
      Authorization: WEBSITE_API_TOKEN,
      Accept: 'application/json',
    },
  });

  return response.data;
}

export async function blankGet<ResponseType>(url: string) {
  const response = await axios.get<ResponseType>(url);
  return response.data;
}

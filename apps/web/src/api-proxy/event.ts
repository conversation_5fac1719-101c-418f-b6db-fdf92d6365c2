'use server';

import type { EventAPI, EventQueryParams } from '@repo/types/website-api-types';
import { fetchBlocks } from './blocks';
import { get } from '.';
import { constructURL } from '@/lib/utils';

export async function fetchEvents(params: EventQueryParams) {
  const response = await get<EventAPI>(constructURL('/event/v1', params));

  return response;
}

export async function fetchSuccessToFailureRatio() {
  const blockResponse = await Promise.allSettled([
    fetchBlocks({
      limit: 1,
    }),
  ]);

  const currentBlock =
    blockResponse[0].status === 'fulfilled'
      ? blockResponse[0].value.data[0].block_number
      : 0;

  const responses = await Promise.all(
    Array.from({ length: 53 }).map(async (_, index) => {
      const start = currentBlock - 7200 * 7 * (53 - index);
      const end = currentBlock - 7200 * 7 * (53 - index - 1);
      const eventResponses = await Promise.allSettled([
        fetchEvents({
          full_name: 'System.ExtrinsicSuccess',
          block_start: start,
          block_end: end,
        }),
        fetchEvents({
          full_name: 'System.ExtrinsicFailed',
          block_start: start,
          block_end: end,
        }),
      ]);

      const success =
        eventResponses[0].status === 'fulfilled'
          ? eventResponses[0].value.pagination.total_items
          : 0;
      const failed =
        eventResponses[1].status === 'fulfilled'
          ? eventResponses[1].value.pagination.total_items
          : 0;

      return success / failed;
    })
  );

  return responses;
}

export async function fetchWeeklyExtrinsic(type: string) {
  const blocksPerDay = 7200;
  const intervals = type === 'Daily' ? 31 : 53;
  const blocksPerInterval = type === 'Daily' ? blocksPerDay : blocksPerDay * 7;

  const blockResponse = await Promise.allSettled([
    fetchBlocks({
      limit: 1,
    }),
  ]);

  const currentBlock =
    blockResponse[0].status === 'fulfilled'
      ? blockResponse[0].value.data[0].block_number
      : 0;

  const responseSuccess = await Promise.all(
    Array.from({ length: intervals }).map(async (_, index) => {
      const start = currentBlock - blocksPerInterval * (intervals - index);
      const end = currentBlock - blocksPerInterval * (intervals - index - 1);
      const eventResponses = await Promise.allSettled([
        fetchEvents({
          full_name: 'System.ExtrinsicSuccess',
          block_start: start,
          block_end: end,
        }),
      ]);

      const success =
        eventResponses[0].status === 'fulfilled'
          ? eventResponses[0].value.pagination.total_items
          : 0;

      return success;
    })
  );

  const responsesFailed = await Promise.all(
    Array.from({ length: intervals }).map(async (_, index) => {
      const start = currentBlock - blocksPerInterval * (intervals - index);
      const end = currentBlock - blocksPerInterval * (intervals - index - 1);
      const eventResponses = await Promise.allSettled([
        fetchEvents({
          full_name: 'System.ExtrinsicFailed',
          block_start: start,
          block_end: end,
        }),
      ]);

      const failed =
        eventResponses[0].status === 'fulfilled'
          ? eventResponses[0].value.pagination.total_items
          : 0;

      return failed;
    })
  );

  return responseSuccess.map((item, index) => {
    return {
      date: new Date().setDate(
        new Date().getDate() -
          (type === 'Daily' ? intervals - index : (intervals - index) * 7)
      ),
      success: item,
      failed: responsesFailed[index],
    };
  });
}

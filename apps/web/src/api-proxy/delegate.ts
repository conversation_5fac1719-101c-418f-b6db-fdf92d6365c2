'use server';

import { DelegateBalanceOrder } from '@repo/types/website-api-types';
import type {
  DelegateAPI,
  DelegateBalanceAPI,
  DelegateBalanceQueryParams,
  DelegateQueryParams,
  DtaoDelegateBalanceAPI,
  DtaoDelegateBalanceQueryParams,
  TopDelegatorTransactionQueryParams,
} from '@repo/types/website-api-types';
import { get } from '.';
import { constructURL } from '@/lib/utils';

export async function fetchDelegations(params: DelegateQueryParams) {
  const response = await get<DelegateAPI>(
    constructURL('/delegation/v1', params)
  );

  return response;
}

export async function fetchDelegationBalance(
  params: DelegateBalanceQueryParams
) {
  const response = await get<DelegateBalanceAPI>(
    constructURL('/stake_balance/latest/v1', params)
  );

  return response;
}

export async function fetchDtaoDelegationBalance(
  params: DtaoDelegateBalanceQueryParams
) {
  const response = await get<DtaoDelegateBalanceAPI>(
    constructURL('/dtao/stake_balance/latest/v1', params)
  );

  return response;
}

export async function fetchTopDelegatorTransactions({
  validator,
}: TopDelegatorTransactionQueryParams) {
  const initialResponse = await Promise.allSettled([
    fetchDelegationBalance({
      hotkey: validator,
      limit: 2,
      order: DelegateBalanceOrder.StakeDesc,
    }),
  ]);

  const topDelegator =
    initialResponse[0].status === 'fulfilled'
      ? initialResponse[0].value.data
      : null;

  if (!topDelegator) return [];

  const responses = await Promise.allSettled(
    topDelegator.map(async ({ hotkey, coldkey }) =>
      fetchDelegations({
        delegate: hotkey.ss58,
        nominator: coldkey.ss58,
        limit: 200,
      })
    )
  );

  const transactions =
    responses
      .map((item) => (item.status === 'fulfilled' ? item.value.data : []))
      .find((item) => item.length > 0) ?? [];

  return transactions;
}

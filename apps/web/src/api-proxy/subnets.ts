'use server';

import { utc } from 'moment';
import type {
  DistributionColdkeyQueryParams,
  DistributionIncentiveQueryParams,
  DistributionIPQueryParams,
  NeuronDeregistrationAPI,
  NeuronDeregistrationQueryParams,
  NeuronRegistrationAPI,
  NeuronRegistrationQueryParams,
  PoolHistoryQueryParams,
  RegistrationCostAPI,
  RegistrationCostHistoryQueryParams,
  SubnetAPI,
  SubnetHistoryQueryParams,
  SubnetIdentityAPI,
  SubnetIdentityQueryParams,
  SubnetOwnerAPI,
  SubnetOwnerQueryParams,
  SubnetQueryParams,
  SubnetRegistrationQueryParams,
} from '@repo/types/website-api-types';
import {
  NeuronDeregistrationOrder,
  NeuronRegistrationOrder,
  PoolHistoryFrequency,
  SubnetHistoryOrder,
} from '@repo/types/website-api-types';
import { fetchPoolHistory } from './dtao';
import { get } from '.';
import { constructURL } from '@/lib/utils';

export async function fetchSubnetIdentity(params: SubnetIdentityQueryParams) {
  const response = await get<SubnetIdentityAPI>(
    constructURL('/subnet/identity/v1', params)
  );

  return response;
}

export async function fetchDistributionColdkey(
  params: DistributionColdkeyQueryParams
) {
  const response = await get(
    constructURL('/subnet/distribution/coldkey/v1', params)
  );

  return response;
}

export async function fetchDistributionIncentive(
  params: DistributionIncentiveQueryParams
) {
  const response = await get(
    constructURL('/subnet/distribution/incentive/v1', params)
  );

  return response;
}

export async function fetchDistributionIP(params: DistributionIPQueryParams) {
  const response = await get(
    constructURL('/subnet/distribution/ip/v1', params)
  );

  return response;
}

export async function fetchSubnetHistory(params: SubnetHistoryQueryParams) {
  const response = await get<SubnetAPI>(
    constructURL('/subnet/history/v1', params)
  );

  return response;
}

export async function fetchSubnet(params: SubnetQueryParams) {
  const response = await get<SubnetAPI>(
    constructURL('/subnet/latest/v1', params)
  );

  return response;
}

export async function fetchNeuronDeregistration(
  params: NeuronDeregistrationQueryParams
) {
  const response = await get<NeuronDeregistrationAPI>(
    constructURL('/subnet/neuron/deregistration/v1', params)
  );

  return response;
}

export async function fetchNeuronRegistration(
  params: NeuronRegistrationQueryParams
) {
  const response = await get<NeuronRegistrationAPI>(
    constructURL('/subnet/neuron/registration/v1', params)
  );

  return response;
}

export async function fetchImmune({
  netuid,
  timestamp_start: timestampStart,
}: NeuronRegistrationQueryParams) {
  const immuneResponse = await Promise.allSettled([
    fetchNeuronRegistration({
      netuid,
      timestamp_start: timestampStart,
      order: NeuronRegistrationOrder.TimestampDesc,
    }),
  ]);
  const count =
    immuneResponse[0].status === 'fulfilled'
      ? immuneResponse[0].value.pagination.total_items
      : 0;

  const response = await Promise.allSettled(
    Array.from({ length: Math.ceil(count / 200) }).map((_, index) =>
      fetchNeuronRegistration({
        netuid,
        timestamp_start: timestampStart,
        limit: 200,
        page: index + 1,
        order: NeuronRegistrationOrder.TimestampDesc,
      })
    )
  );

  return response.flatMap((item) =>
    item.status === 'fulfilled' ? item.value.data : []
  );
}

export async function fetchSubnetOwner(params: SubnetOwnerQueryParams) {
  const response = await get<SubnetOwnerAPI>(
    constructURL('/subnet/owner/v1', params)
  );

  return response;
}

export async function fetchSubnetRegistration(
  params: SubnetRegistrationQueryParams
) {
  const response = await get(constructURL('/subnet/registration/v1', params));

  return response;
}

export async function fetchRegistrationCostHistory(
  params: RegistrationCostHistoryQueryParams
) {
  const response = await get<RegistrationCostAPI>(
    constructURL('/subnet/registration_cost/history/v1', params)
  );

  return response;
}

export async function fetchTopSubnetEmission() {
  const subnets = await fetchSubnet({});

  const topSubnets = subnets.data
    .sort((a, b) => Number(b.emission) - Number(a.emission))
    .map((item) => item.netuid)
    .slice(0, 12);

  const responses = await Promise.allSettled(
    topSubnets.map(async (id) => {
      const emissionHistory = await fetchSubnetHistory({
        order: SubnetHistoryOrder.TimestampDesc,
        limit: 200,
        netuid: id,
      });

      return emissionHistory.data;
    })
  );

  return responses.map((item) =>
    item.status === 'fulfilled' ? item.value : []
  );
}

export async function fetchSubnetRegistrationCostHistory(
  params: RegistrationCostHistoryQueryParams
) {
  if (params.limit === 400) {
    const count = await Promise.allSettled([fetchRegistrationCostHistory({})]);

    const total =
      count[0].status === 'fulfilled'
        ? Math.ceil(count[0].value.pagination.total_items / 200)
        : 0;

    const response = await Promise.allSettled(
      Array.from({ length: total }).map(async (_, index) => {
        const res = await fetchRegistrationCostHistory({
          order: SubnetHistoryOrder.TimestampDesc,
          page: index + 1,
          limit: 200,
        });

        return res;
      })
    );

    return response.flatMap((item) =>
      item.status === 'fulfilled' ? item.value.data : []
    );
  }

  const response = await Promise.allSettled([
    fetchRegistrationCostHistory({
      order: SubnetHistoryOrder.TimestampDesc,
      limit: params.limit,
    }),
  ]);

  return response[0].status === 'fulfilled' ? response[0].value.data : [];
}

export async function fetchDeregistration({
  netuid,
  timestamp_start: timestampStart,
}: NeuronDeregistrationQueryParams) {
  const dereg = await Promise.allSettled(
    Array.from({ length: 8 }).map(
      async (_, index) =>
        await fetchNeuronDeregistration({
          netuid,
          limit: 200,
          ...(timestampStart !== undefined && {
            timestamp_start: timestampStart,
          }),
          order: NeuronDeregistrationOrder.TimestampDesc,
          page: index + 1,
        })
    )
  );

  if (
    dereg.filter((item) => item.status === 'rejected').length === dereg.length
  ) {
    return Promise.reject(
      new Error('All neuron deregistration fetch attempts failed')
    );
  }

  return dereg.flatMap((item) =>
    item.status === 'fulfilled' ? item.value.data : []
  );
}

export async function fetchRegistration(id: string) {
  const regCost = await Promise.allSettled(
    Array.from({ length: 2 }).map(
      async (_, index) =>
        await fetchNeuronRegistration({
          netuid: Number(id),
          page: index + 1,
          limit: 200,
        })
    )
  );

  if (
    regCost.filter((item) => item.status === 'rejected').length ===
    regCost.length
  ) {
    return Promise.reject(
      new Error('All neuron registration fetch attempts failed')
    );
  }

  return regCost.flatMap((item) =>
    item.status === 'fulfilled' ? item.value.data : []
  );
}

export async function fetchSubnetMetadata() {
  const response = await Promise.allSettled([fetchSubnetIdentity({})]);

  return response[0].status === 'fulfilled' ? response[0].value.data : [];
}

export async function fetchSubnetRecycledData({
  netuid,
  limit = 200,
  frequency,
}: SubnetHistoryQueryParams) {
  const response = await Promise.allSettled(
    Array.from({ length: Math.ceil(limit / 200) }).map((_, index) =>
      fetchSubnetHistory({
        netuid,
        order: SubnetHistoryOrder.TimestampDesc,
        frequency,
        limit: 200,
        page: index + 1,
      })
    )
  );

  return response.flatMap((item) =>
    item.status === 'fulfilled' ? item.value.data : []
  );
}

export async function fetchDailyRegistrationRecycleData({
  limit,
}: {
  limit?: number;
}) {
  const snDailyBurn: Record<number, number[]> = {};
  const timestamps: string[] = [];

  const subnetResponse = await Promise.allSettled([fetchSubnet({})]);
  const subnetCount =
    subnetResponse[0].status === 'fulfilled'
      ? subnetResponse[0].value.pagination.total_items
      : 0;
  const totals = Array(limit).fill(0);

  await Promise.all(
    Array.from({ length: subnetCount }).map(async (_, index) => {
      const response = await Promise.allSettled([
        fetchSubnetHistory({
          netuid: index,
          limit,
          order: SubnetHistoryOrder.TimestampDesc,
        }),
      ]);

      const historyData =
        response[0].status === 'fulfilled' ? response[0].value.data : [];

      snDailyBurn[index] = [];

      historyData.forEach((item) => {
        const burned24 = Number.parseFloat(item.recycled_24_hours) / 1e9;
        snDailyBurn[index].push(burned24);
        timestamps.push(item.timestamp);
      });
    })
  );

  for (const [_, recycle] of Object.entries(snDailyBurn)) {
    recycle.forEach((burn: number, index: number) => {
      totals[index] += burn;
    });
  }

  return totals.map((daily, index) => ({
    amount: Number(daily),
    timestamp: timestamps[index],
  }));
}

export async function fetchSubnetEmissionData({
  netuid,
  timestamp_start: timestampStart,
  frequency,
}: SubnetHistoryQueryParams) {
  const subnetResponse = await Promise.allSettled([
    fetchSubnetHistory({
      netuid,
      frequency,
      timestamp_start: timestampStart,
    }),
  ]);
  const subnetCount =
    subnetResponse[0].status === 'fulfilled'
      ? subnetResponse[0].value.pagination.total_items
      : 0;

  const response = await Promise.allSettled(
    Array.from({ length: Math.ceil(subnetCount / 200) }).map((_, index) =>
      fetchSubnetHistory({
        netuid,
        order: SubnetHistoryOrder.TimestampDesc,
        timestamp_start: timestampStart,
        frequency,
        limit: 200,
        page: index + 1,
      })
    )
  );

  return response.flatMap((item) =>
    item.status === 'fulfilled' ? item.value.data : []
  );
}

export async function fetchRootProportionData({
  netuid,
}: PoolHistoryQueryParams) {
  const rootProportionResponse = await Promise.allSettled([
    fetchPoolHistory({
      netuid,
      frequency: PoolHistoryFrequency.ByDay,
      timestamp_start: utc('2025-02-13').startOf('day').unix(),
      limit: 200,
    }),
  ]);

  return rootProportionResponse.flatMap((item) =>
    item.status === 'fulfilled' ? item.value.data : []
  );
}

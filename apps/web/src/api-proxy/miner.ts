'use server';

import { utc } from 'moment';
import type {
  MinerPerformanceByNetuidParams,
  MinerWeightChartParams,
  MinerWeightHistory,
  MinerWeightHistoryAPI,
  MinerWeightsHistoryQueryParams,
} from '@repo/types/website-api-types';
import {
  MetagraphHistoryOrder,
  MinerWeightHistoryOrder,
} from '@repo/types/website-api-types';
import { fetchMetagraph, fetchMetagraphHistory } from './metagraph';
import { get } from '.';
import { constructURL } from '@/lib/utils';

export async function fetchMinerWeightsHistory(
  params: MinerWeightsHistoryQueryParams
) {
  const response = await get<MinerWeightHistoryAPI>(
    constructURL('/miner/weights/history/v1', params)
  );

  return response;
}

export async function fetchMinerWeightsChart({
  netuid,
  minerUid,
}: MinerWeightChartParams) {
  const start = utc().subtract(24, 'hours').startOf('hour').unix();

  const initialResponses = await Promise.allSettled([
    fetchMinerWeightsHistory({
      netuid,
      miner_uid: minerUid,
      timestamp_start: start,
      limit: 200,
      order: MinerWeightHistoryOrder.TimestampAsc,
    }),
  ]);

  const initialData =
    initialResponses[0].status === 'fulfilled'
      ? initialResponses[0].value
      : null;

  if (!initialData?.data) {
    return null;
  }

  const historyResponse = await Promise.allSettled(
    Array.from({
      length: initialData.pagination.total_pages - 1,
    }).map(async (_, index) =>
      fetchMinerWeightsHistory({
        netuid,
        miner_uid: minerUid,
        timestamp_start: start,
        page: index + 2,
        limit: 200,
        order: MinerWeightHistoryOrder.TimestampAsc,
      })
    )
  );

  const history = historyResponse.flatMap((item) =>
    item.status === 'fulfilled' ? item.value.data : []
  );

  const chartData = [...initialData.data, ...history].reduce<
    MinerWeightHistory[][]
  >((accumulator, current) => {
    const validatorUid = current.validator_uid;

    if (!accumulator[validatorUid]) {
      accumulator[validatorUid] = [];
    }

    accumulator[validatorUid].push(current);

    return accumulator;
  }, []);

  const validatorsResponse = await Promise.allSettled(
    chartData.map(async (item) =>
      fetchMetagraph({ netuid: item[0].netuid, uid: item[0].validator_uid })
    )
  );

  const validators = validatorsResponse.flatMap((item) =>
    item.status === 'fulfilled' ? item.value.data[0] : []
  );

  return {
    chartData,
    validators,
  };
}

export async function fetchMinerPerformanceByNetuid({
  netuid,
}: MinerPerformanceByNetuidParams) {
  const metagraphResponse = await Promise.allSettled([
    fetchMetagraph({ netuid }),
  ]);

  const metagraph = metagraphResponse.flatMap((item) =>
    item.status === 'fulfilled' ? item.value.data : []
  );

  const miners = metagraph.filter((item) => Number(item.validator_trust) <= 0);

  if (miners.length === 0) return null;

  const topEmissionMiner = miners.sort(
    (a, b) => Number(b.emission) - Number(a.emission)
  )[0];

  const sortedMiner = miners.sort(
    (a, b) => Number(b.incentive) - Number(a.incentive)
  );

  const highestMiner = sortedMiner.find((item) => item.is_immunity_period);
  const lowestMiner = sortedMiner.findLast((item) => !item.is_immunity_period);

  const last5Miners = sortedMiner.filter((item) => !item.is_immunity_period)[
    miners.length - 5
  ];
  const last10Miners = sortedMiner[miners.length - 10];

  const minerList = [
    {
      label: 'Top Emission',
      uid: topEmissionMiner.uid,
      color: '#00ff00',
    },
    {
      label: 'Highest Miner',
      uid: highestMiner?.uid,
      color: '#f2495c',
    },
    {
      label: 'Lowest Miner',
      uid: lowestMiner?.uid,
      color: '#4a90e2',
    },
    {
      label: 'Last 5 Miners',
      uid: last5Miners.uid,
      color: '#fade2a',
    },
    {
      label: 'Last 10 Miners',
      uid: last10Miners.uid,
      color: '#fe9d04',
    },
  ];

  const start = utc().subtract(24, 'hours').startOf('hour').unix();

  const minerHistoryResponse = await Promise.allSettled(
    minerList.map(async ({ uid }) =>
      fetchMetagraphHistory({
        netuid,
        uid,
        timestamp_start: start,
        limit: 200,
        order: MetagraphHistoryOrder.TimestampAsc,
      })
    )
  );

  const minerHistory = minerHistoryResponse.map((item) =>
    item.status === 'fulfilled' ? item.value.data : []
  );

  return {
    minerList,
    minerHistory,
  };
}

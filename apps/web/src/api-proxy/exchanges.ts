'use server';

import { utc } from 'moment';
import { TransferOrder } from '@repo/types/website-api-types';
import type {
  AccountAPI,
  TransferAPI,
  Transfer,
} from '@repo/types/website-api-types';
import { encodeSS58Address } from '@repo/ui/lib';
import { fetchAccounts } from './account';
import { fetchTransfers } from './transfers';

async function fetchExchangeInfo(exchangeAddress: string) {
  const response = (await fetchAccounts({
    address: encodeSS58Address(exchangeAddress),
  })) as AccountAPI | null;
  return response;
}

async function fetchExchangeTransactions(exchangeAddress: string) {
  const startTime = utc().subtract(24, 'hours').startOf('hour').unix();

  const response = (await fetchTransfers({
    address: exchangeAddress,
    limit: 200,
    timestamp_start: startTime,
    order: TransferOrder.TimestampDesc,
  })) as TransferAPI | null;

  if (!response) return [];

  if (response.pagination.total_pages === 1) return response.data;

  const responses = await Promise.all(
    Array.from({ length: response.pagination.total_pages - 1 }).map(
      async (_, index) => {
        const temp = (await fetchTransfers({
          address: exchangeAddress,
          page: index + 2,
          limit: 200,
          timestamp_start: startTime,
          order: TransferOrder.TimestampDesc,
        })) as TransferAPI | null;

        return temp?.data ?? [];
      }
    )
  );

  return [...response.data, ...responses.flat()];
}

function calculateInflows(transactions: Transfer[], exchangeAddress: string) {
  return transactions
    .filter((transaction) => transaction.to.ss58 === exchangeAddress)
    .reduce((acc, transaction) => acc + Number(transaction.amount), 0)
    .toString();
}

function calculateOutflows(transactions: Transfer[], exchangeAddress: string) {
  return transactions
    .filter((transaction) => transaction.from.ss58 === exchangeAddress)
    .reduce((acc, transaction) => acc + Number(transaction.amount), 0)
    .toString();
}

export async function fetchExchangeData({
  exchangeAddresses,
}: {
  exchangeAddresses: string[];
}) {
  const exchangeDataPromises = exchangeAddresses.map(fetchExchangeInfo);
  const exchangeTransactionsPromises = exchangeAddresses.map(
    fetchExchangeTransactions
  );

  const [exchangeData, exchangeTransactions] = await Promise.all([
    Promise.all(exchangeDataPromises),
    Promise.all(exchangeTransactionsPromises),
  ]);

  const exchangeDataWithInflowsOutflows = exchangeData.map(
    (exchange, index) => {
      const transactions = exchangeTransactions[index];
      const inflows = calculateInflows(transactions, exchangeAddresses[index]);
      const outflows = calculateOutflows(
        transactions,
        exchangeAddresses[index]
      );

      const exchangeId = exchange?.data[0].address.ss58 ?? '';
      const balanceTotal = exchange?.data[0].balance_total ?? '';

      return {
        exchangeId,
        balanceTotal,
        inflows,
        outflows,
        inCount: transactions.filter(
          (item) => item.to.ss58 === exchangeAddresses[index]
        ).length,
        outCount: transactions.filter(
          (item) => item.from.ss58 === exchangeAddresses[index]
        ).length,
      };
    }
  );

  return exchangeDataWithInflowsOutflows;
}

export interface Exchange {
  account: string;
  amountIn: number;
  amountOut: number;
  in: number;
  out: number;
}

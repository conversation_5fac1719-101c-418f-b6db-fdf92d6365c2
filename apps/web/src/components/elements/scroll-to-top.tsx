'use client';

import React, { useCallback, useEffect, useState } from 'react';
import { CgArrowUp } from 'react-icons/cg';
import { Button } from '@repo/ui/components';

function ScrollToTop() {
  const [isVisible, setIsVisible] = useState(false);

  // Show button when page is scrolled upwards, otherwise hide
  const toggleVisibility = useCallback(() => {
    if (window.pageYOffset > 300) {
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }
  }, []);

  // Scroll to top smooth
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  useEffect(() => {
    window.addEventListener('scroll', toggleVisibility);
    return () => {
      window.removeEventListener('scroll', toggleVisibility);
    };
  }, [toggleVisibility]);

  return (
    <div className='fixed bottom-8 right-10 z-10'>
      {isVisible ? (
        <Button className='h-8 w-8 rounded-full p-1.5' onClick={scrollToTop}>
          <CgArrowUp size={20} />
        </Button>
      ) : null}
    </div>
  );
}

export default ScrollToTop;

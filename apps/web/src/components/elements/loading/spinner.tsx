import { cn } from '@repo/ui/lib';
import { cva } from 'class-variance-authority';
import type { VariantProps } from 'class-variance-authority';
import { Loader2Icon } from 'lucide-react';

const spinnerVariants = cva('text-muted-foreground animate-spin', {
  variants: {
    size: {
      default: 'h-4 w-4',
      sm: 'h-2 w-2',
      lg: 'h-6 w-6',
      icon: 'h-10 w-10',
    },
  },
  defaultVariants: {
    size: 'default',
  },
});

type SpinnerProps = VariantProps<typeof spinnerVariants>;

export const Spinner = ({ size }: SpinnerProps) => {
  return <Loader2Icon className={cn(spinnerVariants({ size }))} />;
};

export const SpinnerDiv = ({ size = 'lg' }: SpinnerProps) => {
  return (
    <div className='animate-appear m-20 flex items-center justify-center'>
      <Spinner size={size} />
    </div>
  );
};

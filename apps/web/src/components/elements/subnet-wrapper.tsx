import { Link, Text } from '@repo/ui/components';
import { appRoutes, cn } from '@repo/ui/lib';
import { useSubnetNames } from '@/lib/hooks/use-subnet-name';

export default function SubnetWrapper({
  info,
  isName = true,
  className,
}: {
  info: number;
  isName?: boolean;
  className?: string;
}) {
  const subnetIndex = info;

  const { subnetMetadata } = useSubnetNames();

  const currentSubnet = subnetMetadata.find((it) => it.netuid === subnetIndex);
  const subnetName =
    currentSubnet?.subnet_name && currentSubnet.subnet_name.length > 0
      ? currentSubnet.subnet_name
      : 'Unregistered';

  if (
    typeof subnetIndex !== 'number' &&
    (subnetIndex as string).toLowerCase() === 'total'
  ) {
    return <Text level='xs'>{subnetIndex}</Text>;
  }

  return (
    <Text
      level='xs'
      className={cn(
        'flex-1',
        !currentSubnet?.subnet_name || !isName ? 'text-neutral-500' : '',
        className
      )}
    >
      <Link href={appRoutes.subnets.detail(subnetIndex.toString())}>
        {isName ? subnetName : subnetIndex}
      </Link>
    </Text>
  );
}

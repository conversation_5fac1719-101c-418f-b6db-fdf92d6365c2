export const Bittensor = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width='1.5em'
      height='1.5em'
      xmlns='http://www.w3.org/2000/svg'
      x='0px'
      y='0px'
      viewBox='0 0 3118.1 2721.3'
      color='white'
      {...props}
    >
      <title>Bittensor</title>

      <path
        d='M1808.1,1907.8c-78.4,52.6-159,55.4-240.5,19.1c-76.2-34-111.3-97.6-111.5-179.7c-0.6-236.6-0.2-473.2-0.2-709.9
		c0-14.4,0-28.7,0-47.2c-147,0-292.1,0-436.8,0c-9.2-88.9,66.2-185.9,152.2-198.2c36-5.1,72.6-7.9,109-8
		c259-0.7,517.9-0.4,776.9-0.4c14.2,0,28.5,0,42.8,0c-1.2,110-78.1,201-202.2,204.2c-126.1,3.3-252.3,0.7-379.9,2.9
		c66,27.5,118,69.7,141.7,138c10,28.9,16,60.7,16.3,91.2c1.5,176.1-1,352.3,1.3,528.4C1678.7,1866.8,1722.4,1886,1808.1,1907.8z'
        fill='currentColor'
      />
    </svg>
  );
};

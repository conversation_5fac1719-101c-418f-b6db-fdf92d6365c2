import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { TopNavbar } from './top-navbar';
import { prefetchNavigationData } from '@/lib/server-utils';
import { makeQueryClient } from '@/lib/utils/make-query-client';

/**
 * Server component that prefetches navigation data using React Query
 * and provides it to the client component via HydrationBoundary
 */
export async function TopNavbarPrefetched() {
  const queryClient = makeQueryClient();

  // Prefetch all navigation data on the server
  await prefetchNavigationData(queryClient);

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <TopNavbar />
    </HydrationBoundary>
  );
}

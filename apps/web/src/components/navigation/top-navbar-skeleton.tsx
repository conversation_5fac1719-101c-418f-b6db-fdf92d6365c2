import { Skeleton } from '@repo/ui/components';

/**
 * Skeleton loading component for the TopNavbar
 * Matches the structure of the actual navigation bar
 */
export function TopNavbarSkeleton() {
  return (
    <div className='sticky top-0 z-50 flex w-full flex-col'>
      {/* Metadata bar skeleton */}
      <div className='border-dark-gray text-gray z-10 flex flex-row items-center justify-between gap-3 border-b bg-black/60 px-2 py-2 backdrop-blur-md sm:px-5 lg:px-6'>
        <div className='flex w-full flex-1 items-center justify-between gap-2 sm:justify-start sm:gap-5 md:w-max md:gap-8'>
          {/* Price data skeleton */}
          <div className='flex flex-col items-center gap-1 sm:flex-row sm:gap-3'>
            <Skeleton className='h-4 w-16' />
            <Skeleton className='h-4 w-20' />
          </div>
          
          {/* Market cap skeleton */}
          <div className='flex flex-col items-center gap-1 sm:flex-row sm:gap-3'>
            <Skeleton className='h-4 w-20' />
            <Skeleton className='h-4 w-16' />
          </div>
          
          {/* 24hr volume skeleton - hidden on smaller screens */}
          <div className='hidden items-center gap-3 lg:flex'>
            <Skeleton className='h-4 w-20' />
            <Skeleton className='h-4 w-16' />
          </div>
          
          {/* Block data skeleton */}
          <div className='hidden items-center gap-2 min-[470px]:flex'>
            <Skeleton className='h-5 w-5 rounded-full' />
            <Skeleton className='h-4 w-16' />
          </div>
          
          {/* Right side controls skeleton */}
          <div className='flex flex-row items-center justify-end gap-2 sm:flex-1 sm:gap-5 md:gap-8'>
            {/* Wallet connector skeleton */}
            <div className='max-sm:hidden'>
              <Skeleton className='h-6 w-8' />
            </div>
            
            {/* Profile dropdown skeleton */}
            <Skeleton className='h-6 w-6 rounded-full' />
          </div>
        </div>
      </div>
      
      {/* Secondary navbar skeleton */}
      <div className='flex w-full flex-row items-center justify-between gap-1 bg-[#121212]/70 px-5 py-4 backdrop-blur-xl sm:gap-3 sm:px-8 md:py-3'>
        {/* Logo skeleton */}
        <div className='2xl:gap-17 flex w-full flex-1 items-center justify-between gap-4 lg:w-max lg:justify-start xl:gap-10'>
          <Skeleton className='h-10 w-[101px]' />
        </div>
        
        {/* Navigation links skeleton */}
        <div className='flex flex-row items-center gap-1 sm:gap-4'>
          <div className='hidden gap-4 md:flex'>
            <Skeleton className='h-8 w-16' />
            <Skeleton className='h-8 w-20' />
            <Skeleton className='h-8 w-18' />
            <Skeleton className='h-8 w-16' />
          </div>
        </div>
        
        {/* Mobile menu skeleton */}
        <div className='md:hidden'>
          <Skeleton className='h-6 w-6' />
        </div>
      </div>
    </div>
  );
}

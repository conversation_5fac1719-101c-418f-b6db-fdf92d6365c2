'use client';

import { NavigationBar, type NavbarProps } from '@repo/ui/components';
import { TopNavbarSkeleton } from './top-navbar-skeleton';
import { WEBSITE_BASE_URL } from '@/lib/config';
import { useNavigationData } from '@/lib/hooks/navigation-hooks';

export function TopNavbar() {
  const {
    priceData,
    latestBlock,
    subnetMetaData,
    validatorData,
    validatorMetadata,
    subnetsData,
    isLoading,
    isError,
  } = useNavigationData();

  // Show loading state or fallback data while queries are loading
  if (isLoading) {
    return <TopNavbarSkeleton />;
  }

  // Handle error state
  if (isError) {
    console.error('Error loading navigation data');
  }

  const navbarProps: NavbarProps = {
    latestBlock,
    priceData,
    subnetPoolData: subnetsData,
    showBlockData: true,
    subnetData: subnetMetaData,
    dashboardUrl: 'https://dashboard.taostats.io',
    websiteUrl: WEBSITE_BASE_URL,
    validatorData,
    userProfile: null, // No auth in web app
    validatorMetadata: null,
    validatorIdentityData: validatorMetadata,
    signOut: () => {
      // No-op for web app - removed async since no await
      return Promise.resolve();
    },
    walletConnected: false,
    connectWallet: () => {
      // No-op for web app
    },
    disconnectWallet: () => {
      // No-op for web app
    },
  };

  return <NavigationBar {...navbarProps} />;
}

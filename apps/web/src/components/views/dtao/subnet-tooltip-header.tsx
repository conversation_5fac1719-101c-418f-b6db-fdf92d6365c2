import { BiErrorCircle } from 'react-icons/bi';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  Text,
} from '@repo/ui/components';
import { cn } from '@repo/ui/lib';

export function SubnetTooltipHeader({
  title,
  description,
  className,
}: {
  title: string;
  description: string;
  className?: string;
}) {
  return (
    <div className={cn('-mr-2 flex flex-row items-center gap-0.5', className)}>
      {title}
      <TooltipProvider>
        <Tooltip delayDuration={1}>
          <TooltipTrigger>
            <BiErrorCircle size={16} className='rotate-180' />
          </TooltipTrigger>
          <TooltipContent
            side='bottom'
            className='max-w-76 inline-flex flex-col items-start justify-start gap-3 rounded-xl border border-neutral-700 bg-[#171717] p-4 backdrop-blur-3xl'
          >
            <Text level='xs' className='whitespace-break-spaces'>
              {description}
            </Text>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
}

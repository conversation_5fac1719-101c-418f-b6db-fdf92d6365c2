'use client';

import type { ReactNode } from 'react';
import { useMemo } from 'react';
import { format } from 'numerable';
import { cn } from '@repo/ui/lib';
import { useLatestPriceAtom } from '@/store/use-latest-price';

export const PriceFormatter = ({
  data,
  className,
  withUSD,
  prefix,
  suffix,
  mainClassName,
  contentClassName,
  variant,
  type,
}: {
  data: string | number;
  className?: string;
  withUSD?: boolean;
  prefix?: string | ReactNode;
  suffix?: string | ReactNode;
  mainClassName?: string;
  contentClassName?: string;
  variant?: 'Price' | 'LongPrice' | 'Currency';
  type?: string;
}) => {
  const { latestPrice: taoValue } = useLatestPriceAtom();
  const value = withUSD ? Number(data) * taoValue : Number(data);

  const [first, last] = useMemo(() => {
    switch (variant) {
      case 'Price':
        return format(value, '0.000000a').split('.');
      case 'LongPrice':
        return format(value, '0,0.000000000').split('.');
      case 'Currency':
        return format(value, '0,0.00').split('.');
      default:
        return format(value, '0.00a').split('.');
    }
  }, [value, variant]);

  if (type === 'percentage') {
    return (
      <div className={cn('flex flex-row items-end', className)}>
        <p
          className={cn(
            'flex flex-row items-end text-base font-normal leading-[18px]',
            contentClassName
          )}
        >
          {prefix ? prefix : null}
          {format(value, '0.00a')}
          {suffix ? suffix : null}
        </p>
      </div>
    );
  }

  if (type === 'small') {
    return (
      <div className={cn('flex flex-row items-end', className)}>
        <p
          className={cn(
            'flex flex-row items-end text-base font-normal leading-5',
            contentClassName
          )}
        >
          {prefix ? prefix : null}
          {first}.{last}
          {suffix ? suffix : null}
        </p>
      </div>
    );
  }

  return (
    <div className={cn('flex flex-row items-end', className)}>
      <p
        className={cn(
          'flex flex-row items-end text-2xl font-normal leading-7',
          mainClassName
        )}
      >
        {prefix ? prefix : null}
        {first}.
      </p>
      <p
        className={cn(
          'flex flex-row items-end text-base font-normal leading-5',
          contentClassName
        )}
      >
        {last}
        {suffix ? suffix : null}
      </p>
    </div>
  );
};

import { useState } from 'react';
import { BiErrorCircle } from 'react-icons/bi';
import {
  Text,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@repo/ui/components';

export function TooltipIcon({ description }: { description: string }) {
  const [open, setOpen] = useState(false);
  return (
    <TooltipProvider delayDuration={0}>
      <Tooltip open={open}>
        <TooltipTrigger asChild>
          <button
            type='button'
            className='cursor-pointer'
            onMouseEnter={() => {
              setOpen(true);
            }}
            onMouseLeave={() => {
              setOpen(false);
            }}
            onTouchStart={(e) => {
              e.stopPropagation();
              setOpen(!open);
            }}
          >
            <BiErrorCircle size={16} className='rotate-180 opacity-60' />
          </button>
        </TooltipTrigger>

        <TooltipContent
          side='bottom'
          className='inline-flex max-w-64 flex-col items-start justify-start gap-3 rounded-xl border border-neutral-700 bg-[#171717] p-3'
        >
          <Text level='sm' className='whitespace-break-spaces opacity-70'>
            {description}
          </Text>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

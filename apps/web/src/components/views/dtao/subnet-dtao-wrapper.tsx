import type { ReactNode } from 'react';
import { format } from 'numerable';
import { cn, taoDivider } from '@repo/ui/lib';
import { Bittensor } from '@/components/icons/bittensor';
import { useLatestPriceAtom } from '@/store/use-latest-price';

export const SubnetDtaoWrapper = ({
  info,
  isChange = false,
  withUSD,
  columnId,
  className,
  currencyClassName,
  number,
  onlyUSD,
  isTotal,
  minimumFractionDigits = 1,
  maximumFractionDigits = 2,
  isAbbreviated,
  amount,
  variant = 'SUBSTRATE',
  currentUSD,
  suffix,
  isFormat,
}: {
  info?: number;
  isChange?: boolean;
  withUSD?: boolean;
  columnId?: string;
  className?: string;
  currencyClassName?: string;
  number?: boolean;
  onlyUSD?: boolean;
  isTotal?: boolean;
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
  isAbbreviated?: boolean;
  isFormat?: boolean;
  amount?: number;
  currentUSD?: boolean;
  variant?: 'EVM' | 'SUBSTRATE';
  suffix?: string | ReactNode;
}) => {
  const value = number
    ? info ?? amount ?? 0
    : Number(info ?? amount) /
      (variant === 'SUBSTRATE' ? taoDivider : taoDivider * taoDivider);
  const { latestPrice: taoValue } = useLatestPriceAtom();

  if (Number.isNaN(value)) return;

  if (isTotal) {
    return (
      <p className='font-regular text-sm'>
        $
        {value.toLocaleString('en-US', {
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        })}
      </p>
    );
  }

  return (
    <div>
      {!onlyUSD && !currentUSD && (
        <div
          className={cn(
            'flex items-center truncate text-sm',
            isChange ? (value < 0 ? 'text-fire' : 'text-lime-green') : '',
            className
          )}
        >
          {isChange && value > 0 ? '+' : ''}
          {suffix ? suffix : <Bittensor className={currencyClassName} />}
          {value !== undefined && value !== null
            ? isAbbreviated
              ? format(value, '0 a')
              : isFormat
                ? format(value, '0.00 a')
                : Number(value).toLocaleString('en-US', {
                    minimumFractionDigits,
                    maximumFractionDigits,
                  })
            : '-'}
        </div>
      )}
      {withUSD ? (
        <p className='font-regular text-xs italic opacity-20'>
          ($
          {(taoValue * value).toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })}
          )
        </p>
      ) : null}
      {onlyUSD ? (
        <p className={cn('font-regular text-sm', className)}>
          $
          {value.toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })}
        </p>
      ) : null}
      {currentUSD ? (
        <p className={cn('flex items-center truncate text-sm', className)}>
          ${format(value * taoValue, '0.00 a')}
        </p>
      ) : null}
    </div>
  );
};

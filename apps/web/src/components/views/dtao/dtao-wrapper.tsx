import type { ReactNode } from 'react';
import { format } from 'numerable';
import { Text } from '@repo/ui/components';
import { cn, taoDivider } from '@repo/ui/lib';
import { Bittensor } from '@/components/icons/bittensor';
import { useLatestPriceAtom } from '@/store/use-latest-price';

export const DtaoWrapper = ({
  info,
  amount,
  className,
  currentUSD,
  number,
  isSubnetPrice,
  variant,
  suffix,
  minimumFractionDigits = 0,
  maximumFractionDigits = 4,
}: {
  info?: number;
  amount?: number;
  className?: string;
  number?: boolean;
  currentUSD?: boolean;
  isSubnetPrice?: boolean;
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
  variant?: 'MarketCap' | 'Stake';
  suffix?: string | ReactNode;
}) => {
  let value = number
    ? Number(info ?? amount)
    : Number(info ?? amount) / taoDivider;
  const { latestPrice: taoValue } = useLatestPriceAtom();

  if (currentUSD) {
    value = taoValue * value;

    if (isSubnetPrice) {
      const prices = String(value).split('.');
      const final =
        prices[1] && prices[1]?.length > 4
          ? Number(value).toLocaleString('en-US', {
              minimumFractionDigits: 0,
              maximumFractionDigits: 2,
            })
          : value;

      value = Number(final);
    }
  }

  return (
    <div className={cn('flex w-full justify-end', className)}>
      {!currentUSD && (
        <Text level='sm' className='flex items-center whitespace-nowrap'>
          {suffix ? suffix : <Bittensor className='mb-0.25 -mr-1' />}
          {value !== undefined && value !== null
            ? number
              ? value.toLocaleString('en-US', {
                  minimumFractionDigits: 0,
                  maximumFractionDigits,
                })
              : variant === 'MarketCap'
                ? format(value, '0.00a')
                : variant === 'Stake'
                  ? value.toLocaleString('en-US', {
                      minimumFractionDigits,
                      maximumFractionDigits,
                    })
                  : format(value, '0a')
            : '-'}
        </Text>
      )}
      {currentUSD ? (
        <Text level='sm' className='flex items-center truncate'>
          $
          {number
            ? value.toLocaleString('en-US', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 4,
              })
            : variant === 'MarketCap'
              ? format(value, '0.00a')
              : format(value, '0a')}
        </Text>
      ) : null}
    </div>
  );
};

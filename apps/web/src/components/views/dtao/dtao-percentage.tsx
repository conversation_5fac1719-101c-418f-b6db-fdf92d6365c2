import { Text } from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { SubnetDtaoWrapper } from './subnet-dtao-wrapper';
import { TooltipIcon } from './tooltip-icon';
import { PriceFormatter } from '@/components/views/dtao/single-subnet/price-formatter';

export const DtaoPercentage = ({
  root,
  stake,
  leftContent,
  rightContent,
  className,
  barClassName,
  bottom,
  contentClassName,
  actualStake,
  tooltip = true,
  isFormat,
  withUSD,
}: {
  root?: string | number;
  stake?: string | number;
  leftContent?: string;
  rightContent?: string;
  className?: string;
  barClassName?: string;
  bottom?: boolean;
  contentClassName?: string;
  actualStake?: string;
  tooltip?: boolean;
  isFormat?: boolean;
  withUSD?: boolean;
}) => {
  return (
    <div className={cn('flex w-48 flex-col gap-1', className)}>
      <div className='flex flex-row justify-between'>
        <div className='flex flex-row items-end gap-1'>
          <Text level='xs' className='font-normal opacity-60'>
            {leftContent}
          </Text>
          <PriceFormatter
            data={(1 / (Number(root) + Number(stake))) * Number(root) * 100}
            type='percentage'
            suffix={
              <span className='ml-0.5 text-sm font-normal leading-[18px]'>
                %
              </span>
            }
            contentClassName={contentClassName}
          />
        </div>
        <div className='flex flex-row items-end gap-1'>
          <Text level='xs' className='font-normal opacity-60'>
            {rightContent}
          </Text>
          <PriceFormatter
            data={(1 / (Number(root) + Number(stake))) * Number(stake) * 100}
            type='percentage'
            suffix={
              <span className='ml-0.5 text-sm font-normal leading-[18px]'>
                %
              </span>
            }
            contentClassName={contentClassName}
          />
        </div>
      </div>
      <div className='flex flex-row gap-0.5'>
        <div
          className={cn(
            'h-2 w-full rounded-full border border-[#262626] bg-[#00DBBC]',
            barClassName
          )}
          style={{
            width: `${
              (1 / (Number(root) + Number(stake))) * Number(root) * 100
            }%`,
          }}
        />
        <div
          className={cn(
            'h-2 w-full rounded-full border border-[#262626] bg-[#EB5347]',
            barClassName
          )}
          style={{
            width: `${
              (1 / (Number(root) + Number(stake))) * Number(stake) * 100
            }%`,
          }}
        />
      </div>
      {bottom ? (
        <div className='flex flex-row justify-between'>
          <div className='flex flex-col gap-0.5'>
            <div className='flex flex-row items-center gap-2'>
              <SubnetDtaoWrapper
                amount={Number(root)}
                maximumFractionDigits={2}
                minimumFractionDigits={0}
                currencyClassName='-mr-1 -ml-1.5 -mb-0.5'
                isFormat
                currentUSD={withUSD}
              />
              {tooltip ? (
                <>
                  <Text level='xs' className='opacity-60'>
                    weighted
                  </Text>
                  <TooltipIcon description='weighted' />
                </>
              ) : null}
            </div>
            {actualStake?.length ? (
              <div className='flex flex-row items-center gap-2'>
                <SubnetDtaoWrapper
                  amount={Number(actualStake)}
                  maximumFractionDigits={2}
                  minimumFractionDigits={0}
                  currencyClassName='-mr-1 -ml-1.5 -mb-0.5'
                  isFormat
                  currentUSD={withUSD}
                />
                <Text level='xs' className='opacity-60'>
                  delegated
                </Text>
                <TooltipIcon description='delegated' />
              </div>
            ) : null}
          </div>
          <SubnetDtaoWrapper
            amount={Number(stake)}
            maximumFractionDigits={2}
            minimumFractionDigits={0}
            currencyClassName='-mr-1 -mb-0.5'
            isFormat={isFormat}
            currentUSD={withUSD}
          />
        </div>
      ) : null}
    </div>
  );
};

import type { CellContext } from '@tanstack/react-table';
import { TableText } from '@repo/ui/components';
import type { TableData } from '@/components/views/home/<USER>';

export const NominatorsCell = ({ row }: CellContext<TableData, unknown>) => (
  <TableText
    info={row.original.global_nominators}
    className='w-17 flex justify-end !text-white'
  />
);

export const NominatorsHeader = () => (
  <p className='w-17 -mr-2 flex justify-end'>Nominators</p>
);

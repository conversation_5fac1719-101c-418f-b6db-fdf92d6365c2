import type { CellContext } from '@tanstack/react-table';
import { SubnetNameDisplay } from '@repo/ui/components';
import type { TableData } from '@/components/views/home/<USER>';

export const NameCell = ({ row }: CellContext<TableData, unknown>) => (
  <SubnetNameDisplay
    isClickable
    subnetName={row.original.subnet_name}
    netuid={row.original.netuid}
    className='!w-64 !max-w-none'
  />
);

export const NameHeader = () => <span className='-mr-2 w-64'>Name</span>;

import type { CellContext } from '@tanstack/react-table';
import { SubnetTooltipHeader } from '@/components/views/dtao';
import { SubnetDtaoWrapper } from '@/components/views/dtao/subnet-dtao-wrapper';
import type { TableData } from '@/components/views/home/<USER>';

export const WeightCell = ({ row }: CellContext<TableData, unknown>) => (
  <SubnetDtaoWrapper
    info={row.original.global_weighted_stake}
    maximumFractionDigits={0}
    minimumFractionDigits={0}
    currencyClassName='-mr-1'
    className='flex w-28 justify-end'
  />
);

export const WeightHeader = () => (
  <SubnetTooltipHeader
    title='Weight'
    description='18% of Root stake plus sum of all alpha staked (converted to tao).'
    className='flex w-28 justify-end'
  />
);

import type { CellContext } from '@tanstack/react-table';
import { D<PERSON>o<PERSON>rapper, SubnetTooltipHeader } from '@/components/views/dtao';
import type { TableData } from '@/components/views/home/<USER>';

export const MarketCapCell = ({ row }: CellContext<TableData, unknown>) => (
  <DtaoWrapper
    info={row.original.market_cap}
    variant='MarketCap'
    className='flex w-20 justify-end'
  />
);

export const MarketCapHeader = () => (
  <SubnetTooltipHeader
    title='Market Cap'
    description='The total market value, calculated as Current Price in Tao multiplied by Circulating Supply.'
  />
);

import type { CellContext } from '@tanstack/react-table';
import { CopyButton, Link } from '@repo/ui/components';
import { appRoutes, walletFormat } from '@repo/ui/lib';
import type { TableData } from '@/components/views/home/<USER>';

export const hashCell = ({ row }: CellContext<TableData, unknown>) => {
  const value = row.original.hash;
  return (
    <div className='flex max-w-32 items-center gap-2 opacity-40'>
      <Link
        href={appRoutes.blockchain.heightDetail(value)}
        className='flex-1 truncate hover:underline'
      >
        {walletFormat(value)}
      </Link>
      <CopyButton value={value} size={14} />
    </div>
  );
};

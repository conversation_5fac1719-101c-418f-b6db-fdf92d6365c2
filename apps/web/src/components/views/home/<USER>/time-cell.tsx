import type { CellContext } from '@tanstack/react-table';
import { StaticTableDateFormatter } from '@repo/ui/components';
import type { TableData } from '@/components/views/home/<USER>';

export const timeCell = (
  context: CellContext<TableData, unknown>,
  inline?: boolean
) => {
  const value = context.row.original.timestamp;
  return <StaticTableDateFormatter timestamp={value} noTooltip={!inline} />;
};

import type { CellContext } from '@tanstack/react-table';
import { format } from 'numerable';
import { MdCallReceived, MdOutlineNorthEast } from 'react-icons/md';
import { cn, taoDivider } from '@repo/ui/lib';
import { Bittensor } from '@/components/icons/bittensor';
import type { TableData } from '@/components/views/home/<USER>';

export const WeightChangeCell = ({ row }: CellContext<TableData, unknown>) => {
  const value = row.original.global_weighted_stake_24_hr_change;
  return (
    <div className='w-31 flex justify-end'>
      <div
        className={cn(
          'flex w-fit flex-row items-center gap-0.5 rounded-full px-2 py-0.5 text-xs',
          value >= 0
            ? 'bg-[#00DBBC1A] text-[#00DBBC]'
            : 'bg-[#EB53471A] text-[#EB5347]'
        )}
      >
        {value >= 0 ? (
          <MdOutlineNorthEast size={16} />
        ) : (
          <MdCallReceived size={16} />
        )}
        <Bittensor
          className={cn(
            '-ml-1 -mr-1.5',
            value >= 0 ? 'text-[#00DBBC]' : 'text-[#EB5347]'
          )}
        />
        {format(Number(value) / taoDivider, '0.00a')}
      </div>
    </div>
  );
};

export const WeightChangeHeader = () => (
  <span className='w-31 -mr-2 flex justify-end'>Weight Change (24h)</span>
);

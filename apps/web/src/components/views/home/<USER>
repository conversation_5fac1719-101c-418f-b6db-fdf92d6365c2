'use client';

import { use<PERSON><PERSON>back, useMemo } from 'react';
import type { Row, SortingState } from '@tanstack/react-table';
import { ChevronRight } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { BiCheckDouble } from 'react-icons/bi';
import { CgArrowTopRight } from 'react-icons/cg';
import type { ColumnSchema } from '@repo/types/website-api-types';
import { BubbleTable, Button, Link, Text } from '@repo/ui/components';
import { appRoutes, cn } from '@repo/ui/lib';
import {
  AddressCell,
  AddressHeader,
  ChartCell,
  NominatorsCell,
  NominatorsHeader,
  WeightCell,
  WeightChangeCell,
  WeightChangeHeader,
  WeightHeader,
} from './table-cell';
import { useDtaoValidators } from '@/lib/hooks';

export interface TableData {
  id: string;
  global_weighted_stake: number;
  global_weighted_stake_24_hr_change: number;
  global_nominators: number;
  weighted_root_stake: number;
  global_alpha_stake_as_tao: number;
}

export default function ValidatorTable({ inline }: { inline?: boolean }) {
  const router = useRouter();
  const { data, isPending } = useDtaoValidators({});

  const tableData = useMemo(() => {
    return (
      data
        ?.map((validator) => {
          return {
            id: validator.hotkey.ss58,
            global_weighted_stake: Number(validator.global_weighted_stake),
            global_weighted_stake_24_hr_change: Number(
              validator.global_weighted_stake_24_hr_change
            ),
            global_nominators: validator.global_nominators,
            weighted_root_stake: Number(validator.weighted_root_stake),
            global_alpha_stake_as_tao: Number(
              validator.global_alpha_stake_as_tao
            ),
          };
        })
        .sort((a, b) => b.global_weighted_stake - a.global_weighted_stake)
        .slice(0, 8) ?? []
    );
  }, [data]);

  const homeCols = useMemo<ColumnSchema[]>(
    () => [
      {
        id: 'id',
        header: AddressHeader,
        cell: AddressCell,
        enableSorting: false,
      },
      {
        id: 'global_weighted_stake',
        header: WeightHeader,
        cell: WeightCell,
      },
      {
        id: 'global_weighted_stake_24_hr_change',
        header: WeightChangeHeader,
        cell: WeightChangeCell,
      },
      {
        id: 'global_nominators',
        header: () => NominatorsHeader,
        cell: NominatorsCell,
      },
      {
        id: 'chart',
        header: () => '',
        cell: ChartCell,
        enableSorting: false,
      },
    ],
    []
  );

  const defaultSortBy: SortingState = [
    {
      id: 'global_weighted_stake',
      desc: true,
    },
  ];

  const handleRowClick = useCallback(
    (event: React.MouseEvent, { original }: Row<TableData>) => {
      const url = appRoutes.validator.detail(original.id);
      if (event.metaKey || event.ctrlKey) {
        window.open(url, '_blank');
      } else {
        router.push(url);
      }
    },
    [router]
  );

  return (
    <div
      className={cn(
        'flex w-full flex-col rounded-2xl p-2 md:border md:border-neutral-900 md:bg-neutral-900 md:p-6 md:py-10',
        inline && 'border-none bg-transparent'
      )}
    >
      <Button variant='link' className='w-fit' asChild>
        <Link href={appRoutes.validator.home}>
          <Text level='mdTitle' className='flex items-center gap-3 font-medium'>
            <BiCheckDouble /> Validators <ChevronRight size={12} />
          </Text>
        </Link>
      </Button>
      <div className={cn('flex flex-col gap-4 pt-4')} id='validator-table'>
        <BubbleTable
          // stickyHeader
          // stickyHeaderClass="bg-darkgrey md:bg-neutral-900"
          initialSortBy={defaultSortBy}
          columnSchemas={homeCols}
          rowData={tableData}
          link
          isManual={false}
          isFetching={isPending}
          onRowClick={(event, rowInfo) => {
            handleRowClick(event, rowInfo);
          }}
        />
        {!inline && (
          <Button
            asChild
            variant='link'
            className='w-max gap-1 text-[#8A8989] decoration-[#8A8989]'
          >
            <Link href={appRoutes.validator.home}>
              View All Validators
              <CgArrowTopRight size={12} />
            </Link>
          </Button>
        )}
      </div>
    </div>
  );
}

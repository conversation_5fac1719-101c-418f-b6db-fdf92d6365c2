'use client';

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react';
import type { SortingState } from '@tanstack/react-table';
import { useAtom } from 'jotai';
import { ChevronRight } from 'lucide-react';
import { utc } from 'moment';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { BiCubeAlt, BiSolidCircle } from 'react-icons/bi';
import { CgArrowTopRight } from 'react-icons/cg';
import type {
  BlockParamsAtom,
  ColumnSchema,
} from '@repo/types/website-api-types';
import {
  BubbleTable,
  Button,
  Link,
  StaticTableDateFormatter,
  Switch,
  Text,
} from '@repo/ui/components';
import { appRoutes, cn } from '@repo/ui/lib';
import {
  eventsCell,
  extrinsicsCell,
  hashCell,
  heightCell,
  specVersionCell,
  timeCell,
} from './table-cell';
import TablePagination from '@/components/views/blockchain/table-pagination';
import { REFETCH_INTERVAL } from '@/lib/config';
import { useBlock } from '@/lib/hooks';

export interface TableData {
  block_height: number;
  spec_version: number;
  events_count: number;
  hash: string;
  extrinsics_count: number;
  timestamp: string;
}

export default function LatestBlockTable({ inline }: { inline?: boolean }) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [page, setPage] = useState<number>(1);
  const [total, setTotal] = useState<number>(0);

  const [blockParams, setBlockParams] = useState<BlockParamsAtom>({
    refetchInterval: REFETCH_INTERVAL,
  });

  const columns: ColumnSchema[] = useMemo(
    () => [
      {
        id: 'block_height',
        header: 'Height',
        cell: (info) => heightCell(info, page),
      },
      {
        id: 'spec_version',
        header: 'Spec V.',
        cell: specVersionCell,
      },
      {
        id: 'hash',
        header: 'Hash',
        cell: hashCell,
      },
      {
        id: 'events_count',
        header: 'Events',
        cell: eventsCell,
      },
      {
        id: 'extrinsics_count',
        header: 'Extrinsics',
        cell: extrinsicsCell,
      },
      {
        id: 'timestamp',
        header: 'Time',
        cell: (info) => timeCell(info, inline),
      },
    ],
    [page, inline]
  );

  const { data, isPending, isRefetching } = useBlock(blockParams);

  const firstItemTimestamp = useMemo(() => data?.data[0]?.timestamp, [data]);

  const tableData: TableData[] = useMemo(
    () =>
      data?.data.map((item) => ({
        block_height: item.block_number,
        spec_version: item.spec_version,
        events_count: item.events_count,
        hash: item.hash,
        extrinsics_count: item.extrinsics_count,
        timestamp: item.timestamp,
      })) ?? [],
    [data]
  );

  const handleFilterChange = useCallback(
    (filter: string, sorting: SortingState) => {
      if (!inline) return;
      const params = new URLSearchParams(searchParams);

      let isChanged = false;
      if (filter.length > 0) {
        params.set('search', filter);
        params.delete('page');
        isChanged = true;
      } else if (params.get('search')) {
        params.delete('search');
        params.delete('page');
        isChanged = true;
      }

      if (sorting.length > 0) {
        params.set(
          'order',
          `${sorting[0].id}:${sorting[0].desc ? 'desc' : 'asc'}`
        );
        isChanged = true;
      } else if (params.get('order')) {
        params.delete('order');
        isChanged = true;
      }

      if (isChanged) {
        router.push(`${pathname}?${params.toString()}`, { scroll: false });
      }
    },
    [searchParams, router, pathname, inline]
  );

  useEffect(() => {
    const keyword = searchParams.get('search');
    const limit = searchParams.get('limit');
    const pageParam = searchParams.get('page');
    const order = searchParams.get('order');

    setBlockParams((prev) => ({
      page: inline && pageParam ? Number(pageParam) : undefined,
      search: keyword ?? undefined,
      limit: inline && limit ? Number.parseInt(limit) : 10,
      order: order ? mapOrderToEnum(order, BlockOrder) : undefined,
      refetchInterval: prev.refetchInterval,
    }));
  }, [inline, searchParams, setBlockParams]);

  useEffect(() => {
    const currentPage = searchParams.get('page');

    setPage(Number(currentPage ?? '1'));
  }, [searchParams]);

  useEffect(() => {
    const blockData = data;

    if (blockData) {
      setTotal(blockData.pagination.total_items);
    }
  }, [data]);

  return (
    <div
      className={cn(
        'flex w-full flex-col rounded-2xl p-2 md:px-6 md:py-10',
        inline
          ? 'p-0 !pt-4'
          : 'pt-6 md:border md:border-neutral-900 md:bg-neutral-900'
      )}
    >
      {!inline && (
        <div className='flex h-8 items-center justify-between sm:gap-10'>
          <Button
            variant='link'
            className='xs-sm:gap-3 max-xs-sm:px-0 flex items-center gap-2'
            asChild
          >
            <Link href={appRoutes.blockchain.blocks}>
              <BiCubeAlt size={30} />
              <Text level='mdTitle' className='font-medium'>
                Blocks
              </Text>
              <ChevronRight size={12} />
            </Link>
          </Button>
          <div className='flex items-center gap-0.5 sm:gap-4'>
            <Button variant='empty' className='text-ocean gap-2'>
              <BiSolidCircle className='animate-ping' size={6} />
              <span className='hidden sm:block'>Live</span>
            </Button>
            <div
              className={cn(
                'xs-sm:px-4 xs-sm:py-3 w-max rounded-lg border p-2 transition-colors duration-700 sm:w-48',
                isRefetching
                  ? 'border-ocean'
                  : 'border-transparent bg-white/[0.04]'
              )}
            >
              <div className='flex items-center gap-2 pb-2'>
                <BiCubeAlt
                  className={cn(
                    isRefetching
                      ? 'text-ocean animate-spin'
                      : 'text-neutral-600'
                  )}
                  size={14}
                />
                <Text className='font-fira opacity-40' level='xs'>
                  Latest Block
                </Text>
              </div>
              {firstItemTimestamp ? (
                <StaticTableDateFormatter
                  timestamp={utc(firstItemTimestamp).toDate()}
                />
              ) : null}
            </div>
          </div>
        </div>
      )}
      {inline ? (
        <div className='flex items-center gap-1 self-end'>
          <Switch
            className='h-4 w-7'
            thumbClassName='h-3 w-3 data-[state=checked]:translate-x-3'
            checked={REFETCH_INTERVAL === blockParams.refetchInterval}
            onCheckedChange={(e) => {
              e
                ? setBlockParams((prev) => ({
                    ...prev,
                    refetchInterval: REFETCH_INTERVAL,
                  }))
                : setBlockParams((prev) => ({
                    ...prev,
                    refetchInterval: REFETCH_INTERVAL * 100,
                  }));
            }}
          />
          <Text level='sm' className='opacity-40'>
            Auto Refresh
          </Text>
        </div>
      ) : null}
      <div
        className={cn('relative flex w-full flex-col gap-4', !inline && 'pt-8')}
        id='latest-block-table'
      >
        <BubbleTable
          searchable={inline}
          selector={inline}
          searchPlaceholder='Search by Height, Hash'
          latestHighlight={page === 1}
          columnSchemas={columns}
          rowData={tableData}
          onFilterChange={handleFilterChange}
          isFetching={isPending}
          isManual={inline === true}
        />
        {inline ? (
          <TablePagination total={total} />
        ) : (
          <Button
            asChild
            variant='link'
            className='w-max gap-1 text-[#8A8989] decoration-[#8A8989]'
          >
            <Link href={appRoutes.blockchain.blocks}>
              View All Blocks
              <CgArrowTopRight size={12} />
            </Link>
          </Button>
        )}
      </div>
    </div>
  );
}

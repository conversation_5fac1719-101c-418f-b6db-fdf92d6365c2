'use client';

import ExtrinsicWrapper from '@/components/elements/extrinsic-wrapper';
import { Link } from '@/components/elements/link';
import { Text } from '@/components/elements/typography';
import { BubbleTable } from '@/components/ui/bubble-table';
import { Button } from '@/components/ui/button';
import cn from '@/lib/cn';
import { REFETCH_INTERVAL, RETRY_DELAY } from '@/lib/config';
import { fetchTransfers } from '@/lib/hooks/actions/transfers';
import { appRoutes } from '@/lib/routes';
import { AddressFormatter } from '@/lib/utils/address/address-formatter';
import { StaticTableDateFormatter } from '@/lib/utils/date/static-table-date-formatter';
import { SubnetDtaoWrapper } from '@/lib/utils/tao/subnet-dtao-wrapper';
import type { ColumnSchema, TransferAPI } from '@/types';
import { useQuery } from '@tanstack/react-query';
import { ChevronRight } from 'lucide-react';
import { useMemo } from 'react';
import { BiSolidCircle } from 'react-icons/bi';
import { CgArrowTopRight, CgArrowsExchange } from 'react-icons/cg';

export default function LatestTransactionTable({
  inline,
}: {
  inline?: boolean;
}) {
  const columns: ColumnSchema[] = useMemo(
    () => [
      {
        id: 'from',
        header: 'From',
        cell: (info) => <AddressFormatter info={info} className='text-sm' />,
      },
      {
        id: 'to',
        header: 'To',
        cell: (info) => <AddressFormatter info={info} className='text-sm' />,
      },
      {
        id: 'amount',
        header: 'Amount',
        cell: (info) => <SubnetDtaoWrapper info={info} />,
        sortingFn: 'alphanumeric',
      },
      {
        id: 'extrinsic',
        header: 'Extrinsic',
        cell: (info) => (
          <ExtrinsicWrapper info={info} className='text-sm !opacity-100' />
        ),
      },
      {
        id: 'time',
        header: 'Time',
        cell: (info) => (
          <StaticTableDateFormatter
            info={info}
            noTooltip={!inline}
            className='text-sm !opacity-100'
          />
        ),
      },
    ],
    [inline]
  );

  const { data: transferData, isPending } = useQuery<TransferAPI>({
    queryKey: ['transfer'],
    queryFn: async () => {
      const fetchedData = await fetchTransfers({
        limit: 10,
      });
      return fetchedData;
    },
    refetchInterval: REFETCH_INTERVAL,
    staleTime: REFETCH_INTERVAL,
    retry: (failureCount) => {
      return failureCount < 3;
    },
    retryDelay: RETRY_DELAY,
    initialData: initialData ?? undefined,
  });

  const tableData = useMemo(
    () =>
      transferData?.data?.map((transfer) => ({
        amount: transfer.amount,
        extrinsic: transfer.extrinsic_id,
        from: transfer.from.ss58,
        to: transfer.to.ss58,
        time: transfer.timestamp,
        network: transfer.network,
      })) ?? [],
    [transferData]
  );

  return (
    <div
      className={cn(
        'flex w-full flex-col rounded-2xl p-2 md:border md:border-neutral-900 md:bg-neutral-900 md:p-6 md:py-10',
        inline && 'p-0 pr-0'
      )}
    >
      <div className='flex h-8 items-center justify-between sm:gap-10'>
        <Button
          variant={'link'}
          className='xs-sm:gap-3 max-xs-sm:px-0 flex items-center gap-2'
          asChild
        >
          <Link href={appRoutes.blockchain.transfer}>
            <CgArrowsExchange size={30} />
            <Text level={'mdTitle'} className='font-medium'>
              Transfers
            </Text>
            <ChevronRight size={12} />
          </Link>
        </Button>
        <div className='flex items-center gap-4'>
          <Button variant={'empty'} className='text-ocean gap-2'>
            <BiSolidCircle className='animate-ping' size={6} />
            <span className='hidden sm:block'>Live</span>
          </Button>
        </div>
      </div>
      <div
        className={cn('relative flex w-full flex-col gap-4', !inline && 'pt-8')}
        id='latest-transaction-table'
      >
        <BubbleTable
          // stickyHeader
          // stickyHeaderClass={stickyHeaderClass}
          searchable={inline}
          columnSchemas={columns}
          rowData={tableData}
          isFetching={isPending}
          isManual={inline === true}
        />
        {!inline && (
          <Button
            asChild
            variant={'link'}
            className='w-max gap-1 text-[#8A8989] decoration-[#8A8989]'
          >
            <Link href={appRoutes.blockchain.transfer}>
              View All Transactions
              <CgArrowTopRight size={12} />
            </Link>
          </Button>
        )}
      </div>
    </div>
  );
}

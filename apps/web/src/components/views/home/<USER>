'use client';

import { useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { AnalyticCards } from './analytic-cards';
import BittensorPriceView from './bittensor-price-view';
import TradingViewWidget from '@/components/chart/bittensor-tradingview';
import { BittensorZoom } from '@/components/chart/bittensor-zoom';
import { useTradingView } from '@/store/atoms/trading-view-atom';

export default function BittensorChartSection() {
  const { tradingView, setTradingView } = useTradingView();
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleWheel = (e: WheelEvent) => {
      if (chartRef.current?.contains(e.target as Node)) {
        e.preventDefault();
        window.scrollBy(0, e.deltaY);
      }
    };

    window.addEventListener('wheel', handleWheel, { passive: false });
    return () => {
      window.removeEventListener('wheel', handleWheel);
    };
  }, []);

  return (
    <motion.div
      layout='position'
      initial={{ opacity: 0, y: 40 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className='!mx-2 -mt-10 flex flex-col gap-3 rounded-3xl px-2 md:mx-8 md:px-10 lg:gap-0'
    >
      <div className='flex flex-col justify-between py-10 lg:flex-row lg:items-center'>
        <BittensorPriceView />
        <AnalyticCards className='hidden min-[1100px]:grid' />
      </div>
      <div className='relative my-6 w-full' ref={chartRef}>
        {!tradingView ? (
          <BittensorZoom />
        ) : (
          <div className='h-[21rem] overflow-hidden'>
            <TradingViewWidget />
          </div>
        )}
        {tradingView ? (
          <div className='absolute -bottom-12 right-0 flex flex-row items-center gap-2'>
            <Button
              variant='secondary'
              onClick={() => {
                setTradingView(!tradingView);
              }}
              className={cn(
                'h-fit py-1.5 text-xs font-medium hover:opacity-80',
                'bg-ocean/10 text-ocean'
              )}
            >
              Trading View
            </Button>
          </div>
        ) : null}
      </div>
    </motion.div>
  );
}

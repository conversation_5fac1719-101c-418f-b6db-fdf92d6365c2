'use client';

import { useMemo } from 'react';
import Image from 'next/image';
import { format } from 'numerable';
import { MdCallReceived, MdOutlineNorthEast } from 'react-icons/md';
import type { Price } from '@repo/types/website-api-types';
import { Text } from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { PriceFormatter } from '@/components/views/dtao/single-subnet/price-formatter';
import { usePrice } from '@/lib/hooks/price-hooks';
import { imagePaths } from '@/lib/image-paths';

export default function BittensorPriceView() {
  const { data: priceData } = usePrice();

  const currentPrice = useMemo<Price | undefined>(
    () => priceData?.data[0],
    [priceData]
  );

  return (
    <div className='flex flex-col gap-5 md:px-10'>
      <div className='flex items-center gap-4'>
        <Image
          src={imagePaths.logo.landingStatus}
          alt='Bittensor Price Logo'
          width={32}
          height={32}
          className='h-8 w-8 rounded-full bg-black invert'
        />
        <Text
          level='base'
          className='flex items-center gap-2 font-medium leading-[18px]'
        >
          Bittensor <span className='opacity-60'>($TAO)</span>
        </Text>
      </div>
      <PriceFormatter
        data={Number(currentPrice?.price)}
        prefix='$'
        suffix={
          <span
            className={cn(
              'mb-2 ml-2 flex h-max w-fit flex-row items-center gap-0.5 truncate rounded-full py-1 pl-1 pr-2 text-sm leading-4',
              Number(currentPrice?.percent_change_24h) >= 0 &&
                'bg-[#00DBBC1A] text-[#00DBBC]',
              Number(currentPrice?.percent_change_24h) < 0 &&
                'bg-[#EB53471A] text-[#EB5347]'
            )}
          >
            {Number(currentPrice?.percent_change_24h) >= 0 ? (
              <MdOutlineNorthEast size={24} className='p-1' />
            ) : (
              <MdCallReceived size={24} className='p-1' />
            )}
            {format(Number(currentPrice?.percent_change_24h), '0.00')}%
          </span>
        }
        mainClassName='!text-white !text-[64px] !leading-[78px] font-normal -mb-0.75'
        contentClassName='!text-white !text-[48px] !leading-[60px] items-end font-medium'
      />
    </div>
  );
}

import { Skeleton } from '@repo/ui/components';

/**
 * Shared skeleton component for the Bittensor chart section
 * Used by both Suspense fallback and dynamic import loading
 */
export const ChartSectionSkeleton = () => {
  return (
    <div className='!mx-2 -mt-10 flex flex-col gap-3 rounded-3xl px-2 md:mx-8 md:px-10 lg:gap-0'>
      {/* Header section with price view and analytic cards */}
      <div className='flex flex-col justify-between py-10 lg:flex-row lg:items-center'>
        {/* Price view skeleton */}
        <div className='md:w-70 flex w-52 flex-col gap-5'>
          <Skeleton className='h-6 w-full' />
          <Skeleton className='h-[75px] w-full' />
        </div>
        
        {/* Analytic cards skeleton - hidden on mobile, visible on larger screens */}
        <div className='relative hidden w-full grid-cols-2 grid-rows-3 gap-2 md:w-auto md:grid-cols-4 md:grid-rows-2 min-[1100px]:grid'>
          {Array.from({ length: 8 }).map((_, index) => (
            <Skeleton
              key={index}
              className='h-18 w-34.5 rounded-lg bg-[#272727]'
            />
          ))}
        </div>
      </div>
      
      {/* Chart skeleton */}
      <div className='relative my-6 w-full'>
        <Skeleton className='h-80 w-full rounded-lg' />
      </div>
      
      {/* Mobile analytic cards skeleton - visible only on mobile */}
      <div className='flex items-center justify-center px-3 md:px-10 min-[1100px]:hidden'>
        <div className='relative grid w-full grid-cols-2 gap-2 grid-rows-3'>
          {Array.from({ length: 6 }).map((_, index) => (
            <Skeleton
              key={index}
              className='h-18 w-full rounded-lg bg-[#272727]'
            />
          ))}
        </div>
      </div>
    </div>
  );
};

'use client';

import { type ReactNode, useEffect, useMemo, useState } from 'react';
import { format } from 'numerable';
import { CgExpand } from 'react-icons/cg';
import { Button, Text } from '@repo/ui/components';
import { cn, taoDivider, useWindowSize } from '@repo/ui/lib';
import { Bittensor } from '@/components/icons/bittensor';
import { useLatestBlock, usePrice } from '@/lib/hooks';
import { useMarketCap, useStats } from '@/lib/hooks/stats-hooks';

export function AnalyticCard({
  label,
  value,
  prefix,
  suffix,
  className,
  mainClassName,
  contentClassName,
  variant,
}: {
  label: string;
  value: string;
  prefix?: string | ReactNode;
  suffix?: string | ReactNode;
  className?: string;
  mainClassName?: string;
  contentClassName?: string;
  variant?: string;
}) {
  const [renderedValue, setRenderedValue] = useState<string>('');

  useEffect(() => {
    setRenderedValue(value);
  }, [value]);

  const [first, last] = useMemo(
    () => renderedValue.split('.'),
    [renderedValue]
  );

  if (variant === 'Total Supply') {
    return (
      <div className='flex flex-col gap-2 rounded-lg bg-[#272727] p-3'>
        <Text level='xs' className='font-medium opacity-60'>
          {label}
        </Text>
        <div className={cn('flex flex-row items-end', className)}>
          <p
            className={cn(
              'flex flex-row items-end text-xl font-normal leading-6',
              mainClassName
            )}
          >
            {prefix ? prefix : null}
            {/\d+/.exec(renderedValue)?.[0] ?? ''}
          </p>
          <p
            className={cn(
              'flex flex-row items-end text-base font-normal leading-5',
              contentClassName
            )}
          >
            {/[a-zA-Z]+/.exec(renderedValue)?.[0] ?? ''}
            {suffix ? suffix : null}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className='flex flex-col gap-2 rounded-lg bg-[#272727] p-3'>
      <Text level='xs' className='font-medium opacity-60'>
        {label}
      </Text>
      <div className={cn('flex flex-row items-end', className)}>
        <p
          className={cn(
            'flex flex-row items-end text-xl font-normal leading-6',
            mainClassName
          )}
        >
          {prefix ? prefix : null}
          {first}
          {last ? '.' : null}
        </p>
        <p
          className={cn(
            'flex flex-row items-end text-base font-normal leading-5',
            contentClassName
          )}
        >
          {last}
          {suffix ? suffix : null}
        </p>
      </div>
    </div>
  );
}

export function AnalyticCards({ className }: { className?: string }) {
  const { isMobile } = useWindowSize();
  const [gridCount, setGridCount] = useState<number>(3);
  const { data: metaData } = useStats();
  const { data: latestBlock } = useLatestBlock();
  const { data: priceData } = usePrice();

  const currentPriceData = useMemo(() => priceData?.data[0], [priceData?.data]);
  const statsData = useMemo(() => metaData?.data[0], [metaData]);

  const marketCap = useMarketCap(currentPriceData, statsData);

  const analyticList = [
    {
      label: 'Market Cap',
      value: `$${format(marketCap, '0.00 a')}`,
    },
    {
      label: '24hr Volume',
      value: `$${format(Number(currentPriceData?.volume_24h), '0.00 a')}`,
    },
    {
      prefix: <Bittensor className='-mb-1.25 -ml-2 -mr-1.5' />,
      label: 'Total Supply',
      value: format(Number(currentPriceData?.max_supply), '0 a'),
      variant: 'Total Supply',
    },
    {
      label: 'Circulating Supply',
      value: Math.floor(
        Number(statsData?.issued ?? '0') / taoDivider
      ).toLocaleString('en-US'),
      prefix: <Bittensor className='-mb-1.25 -ml-2 -mr-1.5' />,
    },
    {
      label: 'Finalised Blocks',
      value: latestBlock?.data[0].block_number.toLocaleString(),
    },
    {
      label: 'Signed extrinsics',
      value: (statsData?.extrinsics ?? 0).toLocaleString(),
    },
    {
      label: 'Total Accounts',
      value: (statsData?.accounts ?? 0).toLocaleString(),
    },
    {
      label: 'Transfers',
      value: (statsData?.transfers ?? 0).toLocaleString(),
    },
  ];

  return (
    <div
      className={cn(
        'relative grid w-full grid-cols-2 gap-2 md:w-auto md:grid-cols-4 md:grid-rows-2',
        `grid-rows-${gridCount}`,
        className
      )}
    >
      {analyticList
        .slice(0, isMobile ? gridCount * 2 : 10)
        .map(({ label, value, prefix, variant }) => (
          <AnalyticCard
            key={label}
            label={label}
            value={value ?? ''}
            prefix={prefix}
            variant={variant}
          />
        ))}
      {gridCount === 3 && isMobile ? (
        <div className='to-background absolute bottom-0 flex w-full justify-center bg-gradient-to-b from-transparent p-2 pt-6'>
          <Button
            className='bg-background gap-1'
            onClick={() => {
              setGridCount(5);
            }}
            variant='secondary'
          >
            Expand
            <CgExpand size={24} />
          </Button>
        </div>
      ) : null}
    </div>
  );
}

'use client';

import { use<PERSON><PERSON>back, useMemo } from 'react';
import type { Row, SortingState } from '@tanstack/react-table';
import { ChevronRight, Network } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { CgArrowTopRight } from 'react-icons/cg';
import type {
  ColumnSchema,
  SevenPriceItem,
} from '@repo/types/website-api-types';
import { Button, Link, Text, BubbleTable } from '@repo/ui/components';
import { appRoutes } from '@repo/ui/lib';
import {
  EmissionCell,
  EmissionHeader,
  LastDaysCell,
  LastDaysHeader,
  MarketCapCell,
  MarketCapHeader,
  NameCell,
  NameHeader,
  NetuidCell,
  NetuidHeader,
  PriceCell,
  PriceHeader,
} from './table-cell';
import { useDtaoSubnets } from '@/lib/hooks';

export interface TableData {
  netuid: number;
  symbol: string;
  subnet_name: string;
  subnet_description: string;
  price: number;
  market_cap: number;
  emission: string;
  price_change_one_week: number;
  last_7_days: SevenPriceItem[];
}

export default function SubnetTable() {
  const router = useRouter();

  const { data, isPending } = useDtaoSubnets();

  const columns = useMemo<ColumnSchema[]>(
    () => [
      {
        id: 'netuid',
        header: NetuidHeader,
        cell: NetuidCell,
      },
      {
        id: 'symbol',
        header: NameHeader,
        cell: NameCell,
        enableSorting: false,
      },
      {
        id: 'price',
        header: PriceHeader,
        cell: PriceCell,
      },
      {
        id: 'market_cap',
        header: MarketCapHeader,
        cell: MarketCapCell,
      },
      {
        id: 'emission',
        header: EmissionHeader,
        cell: EmissionCell,
        sortingFn: 'alphanumeric',
      },
      {
        id: 'last_7_days',
        header: LastDaysHeader,
        cell: LastDaysCell,
        enableSorting: false,
      },
    ],
    []
  );

  const tableData: TableData[] = useMemo(() => {
    return (
      data?.data
        .map(
          ({
            symbol,
            netuid,
            price,
            market_cap: marketCap,
            seven_day_prices: sevenDayPrices,
            subnet_name: subnetName,
            subnet_description: subnetDescription,
            price_change_1_week: priceChangeOneWeek,
            emission,
          }) => ({
            netuid,
            symbol,
            subnet_name: subnetName,
            subnet_description: subnetDescription,
            price: Number(price),
            market_cap: Number(marketCap),
            emission,
            price_change_one_week: Number(priceChangeOneWeek),
            last_7_days: sevenDayPrices,
          })
        )
        .sort((a, b) => Number(b.emission) - Number(a.emission))
        .slice(0, 8) ?? []
    );
  }, [data]);

  const defaultSort: SortingState = [
    {
      id: 'emission',
      desc: true,
    },
  ];

  const handleRowClick = useCallback(
    (event: React.MouseEvent, { original }: Row<TableData>) => {
      const url = appRoutes.subnets.chart(original.netuid as string);
      if (event.metaKey || event.ctrlKey) {
        window.open(url, '_blank');
      } else {
        router.push(url);
      }
    },
    [router]
  );

  return (
    <div className='flex w-full flex-col rounded-2xl p-2 md:border md:border-neutral-900 md:bg-neutral-900 md:p-6 md:py-10'>
      <Button variant='link' className='w-fit' asChild>
        <Link href={appRoutes.subnets.home}>
          <Text level='mdTitle' className='flex items-center gap-3 font-medium'>
            <Network /> Subnets <ChevronRight size={12} />
          </Text>
        </Link>
      </Button>
      <div className='pt-4' id='subnet-table'>
        <BubbleTable
          // stickyHeader
          // stickyHeaderClass="bg-darkgrey md:bg-neutral-900"
          initialSortBy={defaultSort}
          rowData={tableData}
          link
          columnSchemas={columns}
          isManual={false}
          isFetching={isPending}
          onRowClick={(event, rowInfo) => {
            handleRowClick(event, rowInfo);
          }}
        />
        <Button
          asChild
          variant='link'
          className='w-max gap-1 text-[#8A8989] decoration-[#8A8989]'
        >
          <Link href={appRoutes.subnets.home}>
            View All Subnets
            <CgArrowTopRight size={12} />
          </Link>
        </Button>
      </div>
    </div>
  );
}

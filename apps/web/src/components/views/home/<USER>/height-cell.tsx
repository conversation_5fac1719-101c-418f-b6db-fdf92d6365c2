import type { CellContext } from '@tanstack/react-table';
import { BiCubeAlt } from 'react-icons/bi';
import { Link, TableText } from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import type { TableData } from '@/components/views/home/<USER>';

export const heightCell = (
  context: CellContext<TableData, unknown>,
  page: number
) => {
  const value = context.row.original.block_height;
  return (
    <Link href={`/block/${value}/extrinsics`} className='flex gap-2'>
      <BiCubeAlt
        className={cn(
          context.row.index === 0 &&
            page === 1 &&
            'animate-spin [animation-duration:2s]'
        )}
        size={20}
      />
      <TableText className='hover:underline' info={value} />
    </Link>
  );
};

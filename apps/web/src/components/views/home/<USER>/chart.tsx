import type { CellContext } from '@tanstack/react-table';
import { DtaoPercentage } from '@/components/views/dtao/dtao-percentage';
import type { TableData } from '@/components/views/home/<USER>';

export const ChartCell = ({ row }: CellContext<TableData, unknown>) => (
  <DtaoPercentage
    root={row.original.weighted_root_stake}
    stake={row.original.global_alpha_stake_as_tao}
    leftContent='Root'
    rightContent='Alpha'
    contentClassName='text-sm'
  />
);

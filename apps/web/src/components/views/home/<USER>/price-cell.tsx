import type { CellContext } from '@tanstack/react-table';
import { DtaoWrapper } from '@/components/views/dtao/dtao-wrapper';
import type { TableData } from '@/components/views/home/<USER>';

export const PriceCell = ({ row }: CellContext<TableData, unknown>) => (
  <DtaoWrapper
    info={row.original.price}
    number
    isSubnetPrice
    className='flex w-20 justify-end'
  />
);

export const PriceHeader = () => (
  <p className='-mr-2 flex w-20 justify-end'>Price</p>
);

import type { CellContext } from '@tanstack/react-table';
import { SubnetsLastDaysChartDynamic } from '@/components/views/dtao/subnets-last-days-chart.dynamic';
import type { TableData } from '@/components/views/home/<USER>';

export const LastDaysCell = ({ row }: CellContext<TableData, unknown>) => (
  <div className='w-32'>
    <SubnetsLastDaysChartDynamic
      data={row.original.last_7_days}
      state={row.original.price_change_one_week >= 0}
      className='!h-[56px]'
    />
  </div>
);

export const LastDaysHeader = () => <p className='ml-2'>Last 7 days</p>;

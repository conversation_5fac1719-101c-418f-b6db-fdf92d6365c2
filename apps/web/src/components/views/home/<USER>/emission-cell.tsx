import type { CellContext } from '@tanstack/react-table';
import { TableText } from '@repo/ui/components';
import type { TableData } from '@/components/views/home/<USER>';

export const EmissionCell = ({ row }: CellContext<TableData, unknown>) => (
  <TableText
    amount={Number(row.getValue('emission')) / 10000000}
    className='flex w-14 justify-end'
    percentage
  />
);

export const EmissionHeader = () => (
  <p className='-mr-2 flex w-14 justify-end'>Emission</p>
);

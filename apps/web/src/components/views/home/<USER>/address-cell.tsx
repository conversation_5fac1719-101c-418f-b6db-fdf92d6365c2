import type { CellContext } from '@tanstack/react-table';
import { AddressFormatter } from '@repo/ui/components';
import type { TableData } from '@/components/views/home/<USER>';

export const AddressCell = ({ row }: CellContext<TableData, unknown>) => (
  <div className='flex w-36'>
    <AddressFormatter
      uid={row.original.id}
      noCopy
      noLink
      isValidator
      rootClassName='max-w-36'
      textClassName='text-sm flex-shrink-[1]'
    />
  </div>
);

export const AddressHeader = () => <span className='flex w-36'>Address</span>;

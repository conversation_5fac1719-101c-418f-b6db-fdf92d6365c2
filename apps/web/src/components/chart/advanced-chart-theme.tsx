import type { CustomThemeColors, CustomThemes } from "taostats-charting-library";

const customThemeBase:CustomThemeColors = {
	// Color that overrides blue
	"color1": [] as unknown as TradingView.ColorGradient,
	// Color that overrides grey
	"color2": [] as unknown as TradingView.ColorGradient,
	// Color that overrides red
	"color3": [] as unknown as TradingView.ColorGradient,
	// Color that overrides green
	"color4": [] as unknown as TradingView.ColorGradient,
	// Color that overrides orange
	"color5": [] as unknown as TradingView.ColorGradient,
	// Color that overrides purple
	"color6": [] as unknown as TradingView.ColorGradient,
	// Color that overrides yellow
	"color7": [] as unknown as TradingView.ColorGradient,
	//"white": undefined as unknown as string,
	//"black": undefined as unknown as string,
} as CustomThemeColors;

export const customThemeLight = {
	...customThemeBase
}

export const customThemeDark = {
	...customThemeBase,
	"color2": [
		"#F2F2F2",
		"#E5E5E5",
		"#D9D9D9",
		"#CCCCCC",
		"#BFBFBF",
		"#B2B2B2",
		"#A5A5A5",
		"#989898",
		"#8C8C8C",
		"#7F7F7F",
		"#727272",
		"#656565",
		"#585858",
		"#4B4B4B",
		"#3E3E3E",
		"#272727", // modified to match taostats lighter background
		"#252525",
		"#181818", // toastats card background used to generate gradients - https://coolors.co/gradient-palette/181818-ffffff?number=19
		"#121212", // match taostats page background
	] as TradingView.ColorGradient,
	// https://coolors.co/gradient-palette/EB5347-000000?number=11
	"color3": [
		"#FDEEED",
		"#FBDDDA",
		"#F9CBC8",
		"#F7BAB5",
		"#F5A9A3",
		"#F39891",
		"#F1877E",
		"#EF756C",
		"#ED6459",
		"#EB5347",
		"#D44B40",
		"#BC4239",
		"#A53A32",
		"#8D322B",
		"#762A24",
		"#5E211C",
		"#461915",
		"#2F110E",
		"#180807",
	] as TradingView.ColorGradient,
	//https://coolors.co/gradient-palette/BAEB47-000000?number=11
	"color4": [
		"#E6FBF8",
		"#CCF8F2",
		"#B3F4EB",
		"#99F1E4",
		"#80EDDE",
		"#66E9D7",
		"#4DE6D0",
		"#33E2C9",
		"#1ADFC3",
		"#00DBBC",
		"#00C5A9",
		"#00AF96",
		"#009984",
		"#008371",
		"#006E5E",
		"#00584B",
		"#004238",
		"#002C26",
		"#001613"
	] as TradingView.ColorGradient,
}

export const customTheme: CustomThemes = {
	light: customThemeLight,
	dark: customThemeDark
}

export default customTheme;
"use client";

import { useEffect, memo, useRef } from "react";
import {
  type IChartingLibraryWidget,
  PriceScaleMode,
  widget,
} from "taostats-charting-library";
import { UDFCompatibleDatafeedForSubnets } from "taostats-charting-library/datafeeds/udf";
import customTheme from "./advanced-chart-theme";

interface AdvancedChartProps {
  netuid: number;
  userId: string | undefined;
}

const AdvancedChart = ({ netuid, userId }: AdvancedChartProps) => {
  const containerRef = useRef<HTMLDivElement>(null);

  const chartStorageEnabled = false;

  useEffect(() => {
    let tvWidget: IChartingLibraryWidget | undefined;

    if (containerRef.current) {
      const datafeedUrl = "/api/dtao/udf";
      const chartSettingsUrl = chartStorageEnabled && userId ? `/api/chart-settings` : undefined;

      //  BEWARE: no trailing slash is expected in feed URL
      const datafeed = new UDFCompatibleDatafeedForSubnets(
        datafeedUrl,
        undefined,
        {
          maxResponseLength: 500,
          expectedOrder: "earliestFirst",
        }
      );

      datafeed.pinSubnet(netuid);

      tvWidget = new widget({
        // debug: true, // uncomment this line to see Library errors and warnings in the console
        fullscreen: false,
        symbol: `SUB-${netuid}`,
        interval: "60" as TradingView.ResolutionString,
        container: containerRef.current,
        autosize: true,
        datafeed,
        library_path: "/charting_library/",
        locale: "en",
        disabled_features: [
          "header_compare",
          "header_symbol_search",
          "symbol_search_hot_key",
          "use_localstorage_for_settings",
        ], // disable "use_localstorage_for_settings" for development - or remember to clear it!
        enabled_features: [],
        theme: "dark",
        custom_themes: { ...customTheme },
        studies_overrides: {
          "volume.volume.color.0": "#EB5347",
          "volume.volume.color.1": "#00DBBC",
        },
        overrides: {
          "paneProperties.backgroundType": "solid",
        },
        charts_storage_url: chartSettingsUrl,
        charts_storage_api_version: "1.1",
        client_id: `taostats.io/subnets/${netuid}`,
        user_id: userId,
      });
    }

    tvWidget?.onChartReady(() => {
      const priceScale = tvWidget
        ?.activeChart()
        .getPanes()[0]
        .getMainSourcePriceScale();
      priceScale?.setMode(PriceScaleMode.Log);
    });

    return () => {
      tvWidget?.remove();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div
      id="tv_chart_container"
      ref={containerRef}
      style={{ height: "100%", width: "100%" }}
    ></div>
  );
};

export default memo(AdvancedChart);

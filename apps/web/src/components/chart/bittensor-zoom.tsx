'use client';

import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { localPoint } from '@visx/event';
import { LinearGradient } from '@visx/gradient';
import { Group } from '@visx/group';
import { ParentSize } from '@visx/responsive';
import { scaleTime } from '@visx/scale';
import {
  AreaSeries,
  Axis,
  BarSeries,
  Tooltip,
  type TooltipData,
  XYChart,
  buildChartTheme,
} from '@visx/xychart';
import { Zoom } from '@visx/zoom';
import type { ProvidedZoom, TransformMatrix } from '@visx/zoom/lib/types';
import * as d3 from 'd3';
import { format } from 'date-fns';
import { useAtom } from 'jotai';
import { utc } from 'moment';
import Image from 'next/image';
import { BiMinus, BiPlus } from 'react-icons/bi';
import { Button, Text } from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { usePriceOhlc } from '@/lib/hooks';
import { useBittensorPrice } from '@/lib/hooks/use-bittensor-price';
import { imagePaths } from '@/lib/image-paths';
import { tradingViewAtom } from '@/store/atoms';
import useWindowSize from '@/store/use-window-size';

const accessors = {
  xAccessor: (d: DataPoint | ChartPoint) => d.x,
  yAccessor: (d: DataPoint | ChartPoint) => d.y,
};

type DataPoint = {
  x: string;
  y: number;
};

type ChartPoint = {
  x: Date;
  y: number;
};

export const Chart = memo(({ width }: { width: number }) => {
  const height = 300;
  const maxScale = 8;

  const { isMobile } = useWindowSize();

  const { data: priceData } = usePriceOhlc();
  const [tradingView, setTradingView] = useAtom(tradingViewAtom);

  const zoomRef = useRef<
    | (ProvidedZoom<SVGSVGElement> & {
        initialTransformMatrix: TransformMatrix;
        transformMatrix: TransformMatrix;
        isDragging: boolean;
      })
    | null
  >();

  const initialTransform = {
    scaleX: 1,
    scaleY: 1,
    translateX: 0,
    translateY: 0,
    skewX: 0,
    skewY: 0,
  };

  const chartData = useMemo(() => priceData ?? [], [priceData]);

  const data = useMemo<Date[]>(() => {
    return chartData.map((item) => new Date(item.timestamp));
  }, [chartData]);

  const xDomain = useMemo<[undefined, undefined] | [Date, Date]>(() => {
    return d3.extent(data, (d) => d);
  }, [data]);

  const dataMap = useMemo<
    Map<string, { price: number; volume: number }>
  >(() => {
    const map = new Map<string, { price: number; volume: number }>();

    let diff = 1;

    for (const value of chartData) {
      const price = Number(value.close);
      const volume = Number(value.volume_24h) / 1000000;
      if (price < volume && diff > (price - 50) / volume) {
        diff = (price - 50) / volume;
      }
    }

    for (const value of chartData) {
      const price = Number(value.close);
      const volume = (Number(value.volume_24h) / 1000000) * diff;

      map.set(new Date(value.timestamp).toISOString(), {
        price: Number(price.toFixed(2)),
        volume: Number(volume.toFixed(2)),
      });
    }

    return map;
  }, [chartData]);

  const [price, setPrice] = useState<number>(0);
  const { setPriceAtom } = useBittensorPrice();

  const customTheme = useMemo(
    () =>
      buildChartTheme({
        backgroundColor: '',
        colors: ['#00DBBC', '#EB5347'],
        gridColor: '#e8e8e8',
        gridColorDark: '#222831',
        tickLength: 1,
      }),
    []
  );

  const handleRenderTooltip = useCallback(
    ({ tooltipData }: { tooltipData?: TooltipData }) => {
      if (!tooltipData || !tooltipData.nearestDatum) {
        return null;
      }

      const { key } = tooltipData.nearestDatum;
      const datum1 = tooltipData.datumByKey['Line 1'].datum as DataPoint;
      const datum2 = tooltipData.datumByKey['Line 2'].datum as DataPoint;

      setPrice(datum1.y);

      return (
        <div
          key={key}
          className='min-w-60.75 rounded-lg border border-[#222222] bg-[#0E0E0E] bg-opacity-50 px-5 py-3 backdrop-blur-xl'
        >
          <div className='flex flex-col gap-4'>
            <div className='flex flex-row items-center gap-3'>
              <Image
                src={imagePaths.logo.ticket}
                height={30}
                width={30}
                alt=''
                className='h-5 w-5'
              />
              <Text level='lg' className='tracking-[-0.05em]'>
                Bittensor ($TAO)
              </Text>
            </div>
            <div className='flex flex-row justify-between gap-4'>
              <div className='flex flex-col gap-2'>
                <Text level='xs' className='text-neutral-400'>
                  Price
                </Text>
                <Text level='lg' className='tracking-[-0.05em] text-[#00DBBC]'>
                  ${datum1.y}
                </Text>
              </div>
              <div className='flex flex-col gap-2'>
                <Text level='xs' className='text-neutral-400'>
                  Volume
                </Text>
                <Text level='lg' className='tracking-[-0.05em] text-[#EB5347]'>
                  {datum2.y} million
                </Text>
              </div>
            </div>
          </div>
          <Text level='xs' className='mt-2 text-[#929292]/60'>
            {format(datum1.x, 'do MMMM yyyy')}
          </Text>
        </div>
      );
    },
    []
  );

  const getScaledData = useCallback(
    (transformMatrix: TransformMatrix) => {
      const scaleX = transformMatrix.scaleX;
      const xMovement = transformMatrix.translateX;
      const xScale = scaleTime({
        range: [0, width * scaleX],
        domain: xDomain[0] ? xDomain : undefined,
        nice: false,
        clamp: true,
      });

      const priceArray: ChartPoint[] = [];
      const volume: ChartPoint[] = [];

      for (const value of data) {
        const x = xScale.invert(xScale(value) / scaleX - xMovement);

        const dateString = new Date(x.setUTCHours(0, 0, 0, 0)).toISOString();

        const dataBox = dataMap.get(dateString);

        if (dataBox) {
          priceArray.push({ x, y: dataBox.price });
          volume.push({ x, y: dataBox.volume });
        }
      }

      return { reScaledData1: priceArray, reScaledData2: volume };
    },
    [data, width, dataMap, xDomain]
  );

  useEffect(() => {
    setPriceAtom({ price });
  }, [price, setPriceAtom]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: intentional
  useEffect(() => {
    zoomRef.current?.reset();
  }, [width, isMobile]);

  const getRenderedScale = useCallback(
    (transformMatrix: TransformMatrix) => {
      const overFlowByScale = width * Math.max(0, transformMatrix.scaleX - 1);
      const adjustedMovement =
        transformMatrix.translateX > 0
          ? transformMatrix.translateX
          : Math.min(0, transformMatrix.translateX + overFlowByScale);
      return (
        (width / (width - Math.abs(adjustedMovement))) * transformMatrix.scaleX
      );
    },
    [width]
  );

  return (
    <Zoom<SVGSVGElement>
      width={width}
      height={height}
      scaleXMin={1}
      scaleXMax={maxScale}
      scaleYMin={1}
      scaleYMax={1}
      initialTransformMatrix={initialTransform}
      constrain={(transformMatrix, prevTransformMatrix) => {
        const scaleX = getRenderedScale(transformMatrix);

        if (transformMatrix.scaleX < 1) {
          return {
            ...prevTransformMatrix,
            translateX: prevTransformMatrix.translateX * transformMatrix.scaleX,
          };
        }

        if (scaleX > maxScale || scaleX < 1) {
          return prevTransformMatrix;
        }

        return transformMatrix;
      }}
    >
      {(zoom) => {
        const { reScaledData1, reScaledData2 } = getScaledData(
          zoom.transformMatrix
        );

        if (!zoomRef.current) {
          zoomRef.current = zoom;
        }

        const transformMatrix = zoom.transformMatrix;

        const margin = { top: 0, right: 0, bottom: 60, left: 0 };

        return (
          <div className='relative'>
            {/* biome-ignore lint/a11y/noSvgWithoutTitle: <explanation> */}
            <svg
              width={width}
              height={height}
              style={{
                cursor: zoom.isDragging ? 'grabbing' : 'grab',
                touchAction: 'none',
              }}
              ref={zoom.containerRef}
            >
              <XYChart
                height={height}
                width={width}
                xScale={{ type: 'time', clamp: true }}
                yScale={{ type: 'linear' }}
                margin={margin}
                theme={customTheme}
              >
                <defs>
                  <LinearGradient
                    id='gradient1'
                    from='#00DBBC'
                    fromOpacity={0.2}
                    to='#000000'
                    toOpacity={0.1}
                    x1='100%'
                    y1='0%'
                    x2='100%'
                    y2='100%'
                  />
                  <LinearGradient
                    id='bargradient'
                    from='#EB5347'
                    fromOpacity={0.6}
                    to='#EB5347'
                    toOpacity={0.1}
                    x1='0%'
                    y1='0%'
                    x2='0%'
                    y2='100%'
                  />
                </defs>
                <Group>
                  <AreaSeries
                    dataKey='Line 1'
                    data={reScaledData1.slice().reverse()}
                    lineProps={{
                      stroke: '#00DBBC',
                      strokeWidth: 1,
                      opacity: 100,
                    }}
                    fill='url(#gradient1)'
                    {...accessors}
                  />
                  <BarSeries
                    dataKey='Line 2'
                    data={reScaledData2.slice().reverse()}
                    xAccessor={accessors.xAccessor}
                    yAccessor={accessors.yAccessor}
                    colorAccessor={() => 'url(#bargradient)'}
                  />
                </Group>
                <Group>
                  <Axis
                    orientation='bottom'
                    numTicks={isMobile ? 4 : 10}
                    tickFormat={(tick: Date) => utc(tick).format('MMM YY')}
                    tickLabelProps={() => ({
                      fill: '#909090',
                      fontSize: 9,
                      fontWeight: 300,
                      dy: 12,
                      fontFamily: 'var(--font-sans)',
                    })}
                  />
                </Group>
                <Group>
                  <Tooltip
                    verticalCrosshairStyle={{
                      stroke: '#404040',
                      strokeDasharray: '4 4',
                    }}
                    snapTooltipToDatumX
                    snapTooltipToDatumY
                    showVerticalCrosshair
                    showSeriesGlyphs
                    renderTooltip={handleRenderTooltip}
                  />
                </Group>
              </XYChart>
              <rect
                width={width}
                height={height}
                fill='transparent'
                onTouchStart={zoom.dragStart}
                onTouchMove={zoom.dragMove}
                onTouchEnd={zoom.dragEnd}
                onMouseDown={zoom.dragStart}
                onMouseMove={zoom.dragMove}
                onMouseUp={zoom.dragEnd}
                onMouseLeave={() => {
                  if (zoom.isDragging) zoom.dragEnd();
                }}
                onDoubleClick={(event) => {
                  const point = localPoint(event) || { x: 0, y: 0 };
                  zoom.scale({
                    scaleX: transformMatrix.scaleX * 1.1,
                    scaleY: 1.1,
                    point,
                  });
                }}
                style={{
                  pointerEvents: zoom.isDragging ? 'all' : 'none',
                }}
              />
            </svg>
            <div className='flex flex-row items-center justify-end gap-2'>
              <Button
                variant='secondary'
                onClick={() => {
                  setTradingView(!tradingView);
                }}
                className={cn(
                  'h-fit py-1.5 text-xs font-medium hover:opacity-80',
                  tradingView ? 'bg-ocean/10 text-ocean' : 'text-white'
                )}
              >
                Trading View
              </Button>
              <Button
                size='icon'
                className='border-[#494949]'
                variant='secondary'
                onClick={() => {
                  zoom.scale({
                    scaleX: 6 / 5,
                    scaleY: 1,
                  });
                }}
              >
                <BiPlus size={16} />
              </Button>
              <Button
                size='icon'
                className='border-[#494949]'
                variant='secondary'
                onClick={() => {
                  zoom.scale({
                    scaleX: 5 / 6,
                    scaleY: 1,
                  });
                }}
              >
                <BiMinus size={16} />
              </Button>
              <Button
                variant='secondary'
                className='h-fit py-1.5 text-xs font-medium hover:opacity-80'
                onClick={zoom.reset}
              >
                Reset
              </Button>
            </div>
          </div>
        );
      }}
    </Zoom>
  );
});

Chart.displayName = 'Chart';

export const BittensorZoom = () => {
  return <ParentSize>{({ width }) => <Chart width={width} />}</ParentSize>;
};

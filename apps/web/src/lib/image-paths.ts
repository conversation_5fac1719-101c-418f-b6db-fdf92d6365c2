export const imagePaths = {
	logo: {
		landing: "/images/logo/landing.png",
		landingStatus: "/images/logo/landing-status.png",
		taostats: "/images/logo/taostats.svg",
		subnetCard: "/images/background/card.png",
		opentensor: "/images/logo/validator.png",
		validatorIcon: "/images/logo/icon.svg",
		ticket: "/images/avatar/avatar6.png",
		github: "/images/logo/github.svg",
		discord: "/images/logo/discord.svg",
		icon3: "/images/logo/icon3.svg",
		icon4: "/images/logo/icon4.svg",
		indicator: "/images/background/indicator.png",
		indicator2: "/images/background/indicator2.png",
		circle: "/images/background/circle-bg.svg",
		sentiment: "/images/background/sentiment.svg",
	},
	avatars: [
		"/images/avatar/avatar1.png",
		"/images/avatar/avatar2.png",
		"/images/avatar/avatar3.png",
		"/images/avatar/avatar4.png",
		"/images/avatar/avatar5.png",
	],
	analytics: {
		exchange: "/images/analytics/exchange-chart.png",
		statHistory: "/images/analytics/stats-history-chart.png",
		dailyBlocks: "/images/analytics/daily-blocks.png",
		incentive: "/images/analytics/incentive.png",
		topDelegator: "/images/analytics/top-delegator.png",
		recentAccounts: "/images/analytics/recent-accounts.png",
		emissionRecycled: "/images/analytics/emission-recycled.png",
		topAccountBalance: "/images/analytics/top-account-balances.png",
		largestTransactions: "/images/analytics/largest-transaction.png",
		dailyPrice: "/images/analytics/daily-price.png",
		pricePercentage: "/images/analytics/price-percentage.png",
		lastTransaction: "/images/analytics/last-transaction.png",
		cumulativeSubnet: "/images/analytics/cumulative-subnets.png",
		minerWeights: "/images/analytics/miner-weights.png",
		successToFailureRatio: "/images/analytics/success-to-failure-ratio.png",
		extrinsicSuccessFailures:
			"/images/analytics/extrinsic-success-failures.png",
		dailySubnets: "/images/analytics/daily-subnets.png",
		dailyRegistrationRecycleView:
			"/images/analytics/daily-registration-recycle.png",
		hotkeyNetuid: "/images/analytics/hotkey-netuid.png",
		taoStaked: "/images/analytics/tao-staked.png",
		taoStakedRoot: "/images/analytics/tao-staked-root.png",
	},
	icons: {
		foundation: "/images/icons/foundation.jpg",
		foundry: "/images/icons/foundry.jpg",
		cortext: "/images/icons/cortext.png",
		northtensor: "/images/icons/northtensor.png",
		tatsu: "/images/icons/tatsu.png",
		kranken: "/images/icons/kraken.png",
		tao5: "/images/icons/tao5.png",
		holding: "/images/icons/holdings.jpg",
		taostats: "/images/icons/taostats.svg",
		tao: "/images/icons/tao.jpg",
	},
	buy: {
		coinbase: "/images/buy/coinbase.png",
		binance: "/images/buy/binance.png",
		crypto: "/images/buy/crypto.png",
		kraken: "/images/buy/kraken.png",
		kucoin: "/images/buy/kucoin.png",
		gate: "/images/buy/gate.png",
		bitget: "/images/buy/bitget.png",
		uniswap: "/images/buy/uniswap.png",
		bingx: "/images/buy/bingx.png",
		bybit: "/images/buy/bybit.png",
		tensor: "/images/buy/tensor.png",
		mexc: "/images/buy/mexc.png",
	},
};

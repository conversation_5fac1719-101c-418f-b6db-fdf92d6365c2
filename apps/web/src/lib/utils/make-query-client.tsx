import { QueryClient } from '@tanstack/react-query';
import { REACT_QUERY_CONFIG } from '@/lib/config';

export function makeQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // With SSR, we usually want to set some default staleTime
        // above 0 to avoid refetching immediately on the client
        staleTime: REACT_QUERY_CONFIG.DEFAULT_STALE_TIME,
        // Set default refetch interval
        refetchInterval: REACT_QUERY_CONFIG.DEFAULT_REFETCH_INTERVAL,
        // Set garbage collection time to match refetch interval to avoid high memory usage on server
        gcTime: REACT_QUERY_CONFIG.DEFAULT_GC_TIME,
        refetchOnMount: false,
        refetchOnWindowFocus: false,
        refetchOnReconnect: true,
        retry: (failureCount) => {
          return failureCount < 3;
        },
        throwOnError: (error, query) => {
          console.error('Error received in react query', { error, query });

          if (error.message && error.message.includes('data is undefined')) {
            fetch(new URL('/challenge', window.location.origin).href, {
              method: 'HEAD',
            })
              .then((response) => {
                if (response.status === 403) {
                  // Received a 403, so we need to redirect to the challenge endpoint
                  window.location.href = new URL(
                    `/challenge?redirectTo=${encodeURIComponent(
                      window.location.pathname
                    )}`,
                    window.location.origin
                  ).href;
                }
              })
              .catch((fetchError) => {
                console.error('Challenge endpoint request failed:', fetchError);
              });
          }

          return false;
        },
      },
    },
  });
}

import type { SortingState } from '@tanstack/react-table';
import { BigNumber } from 'bignumber.js';
import { generateFromString } from 'generate-avatar';
import type { SetStateAction } from 'jotai';
import <PERSON><PERSON> from 'js-cookie';
import type {
  DtaoSubnet,
  DtaoValidatorLatest,
  TableData,
  TotalCount,
  PriceItem
} from '@repo/types/website-api-types';
import { ExchangeAddressMapping } from './config/exchange-address-mapping';
import { validatorIconMapping } from './constants/data/validator-icon-mapping';
import { imagePaths } from './image-paths';

export function truncateString(str: string, max?: number) {
  if (str && str.length > 18) {
    const firstTen = str.substring(0, max ?? 10);
    // const lastSix = str.substring(str.length - 6, str.length);
    return max && max >= str.length ? firstTen : `${firstTen}...`;
  }
  return str;
}

export const round = (num: number | string, decimalPlaces: number): number => {
  return BigNumber(num)
    .decimalPlaces(decimalPlaces, BigNumber.ROUND_HALF_UP)
    .toNumber();
};

export const roundDown = (
  num: number | string,
  decimalPlaces: number
): number => {
  return BigNumber(num)
    .decimalPlaces(decimalPlaces, BigNumber.ROUND_DOWN)
    .toNumber();
};

export function timeDifference(diff: number) {
  const msPerMinute = 60 * 1000;
  const msPerHour = msPerMinute * 60;
  const msPerDay = msPerHour * 24;
  const msPerMonth = msPerDay * 30;
  const msPerYear = msPerDay * 365;

  const elapsed = diff;

  if (elapsed < msPerMinute) {
    return `${Math.round(elapsed / 1000)} seconds ago`;
  }
  if (elapsed < msPerHour) {
    return `${Math.round(elapsed / msPerMinute)} minutes ago`;
  }
  if (elapsed < msPerDay) {
    return `${Math.round(elapsed / msPerHour)} hours ago`;
  }
  if (elapsed < msPerMonth) {
    return `${Math.round(elapsed / msPerDay)} days ago`;
  }
  if (elapsed < msPerYear) {
    return `${Math.round(elapsed / msPerMonth)} months ago`;
  }
  return `${Math.round(elapsed / msPerYear)} years ago`;
}

export function obfuscateIp(ip: string) {
  const parts = ip.split('.');
  parts[2] = parts[2].replace(/\d/g, 'x');
  parts[3] = parts[3].replace(/\d/g, 'x');
  return parts.join('.');
}

export function constructURL(baseURL: string, params: any): string {
  const queryString = new URLSearchParams(params).toString();
  return queryString.length > 0 ? `${baseURL}?${queryString}` : baseURL;
}

export function blurIP(ipAddress: string): string {
  let count = 0;
  let result = '';

  if (!ipAddress) return '0.0.0.0';

  for (const char of ipAddress) {
    if (char === '.') {
      result += '.';
    } else if (count < 7) {
      result += char;
    } else {
      result += 'x';
    }
    count++;
  }

  return result;
}

export function getSorting(
  a: Record<string, string | number | boolean | PriceItem[] | undefined>,
  b: Record<string, string | number | boolean | PriceItem[] | undefined>,
  sorting: SortingState
): number {
  if (sorting.length === 0) return 0;

  const sort = sorting[0];

  if (sort.desc) {
    if (typeof a[sort.id] === 'number') {
      return Number(b[sort.id]) - Number(a[sort.id]);
    }
    if (typeof a[sort.id] === 'string') {
      return String(b[sort.id]).localeCompare(String(a[sort.id]));
    }
    return b[sort.id] ? -1 : 1;
  }

  if (!sort.desc) {
    if (typeof a[sort.id] === 'number') {
      return Number(a[sort.id]) - Number(b[sort.id]);
    }
    if (typeof a[sort.id] === 'string') {
      return String(a[sort.id]).localeCompare(String(b[sort.id]));
    }
    return a[sort.id] ? -1 : 1;
  }

  return 0;
}

export function getFilter(
  data: TableData | DtaoSubnet | DtaoValidatorLatest,
  search: string,
  searchFields?: string[]
) {
  let result = false;
  Object.entries(data).forEach(([key, value]) => {
    if (searchFields) {
      if (
        searchFields.includes(key) &&
        value?.toString().toLowerCase().includes(search.toLowerCase())
      ) {
        result = true;
      }
    } else if (value?.toString().toLowerCase().includes(search.toLowerCase())) {
      result = true;
    }
  });

  return result;
}

type TotalCountSchema = {
  total_count_filtered: TotalCount;
  total_count_unfiltered: TotalCount;
};

export function setTotalCount(
  setCount: (value: SetStateAction<number>) => void,
  data1?: TotalCountSchema,
  data2?: TotalCountSchema
) {
  if (
    data1?.total_count_filtered &&
    data1.total_count_filtered !== 'count_too_slow'
  ) {
    setCount(data1.total_count_filtered.count);
  } else if (
    data1?.total_count_unfiltered &&
    data1.total_count_unfiltered !== 'count_too_slow'
  ) {
    setCount(data1.total_count_unfiltered.count);
  }

  if (
    data2?.total_count_filtered &&
    data2.total_count_filtered !== 'count_too_slow'
  ) {
    setCount(data2.total_count_filtered.count);
  } else if (
    data2?.total_count_unfiltered &&
    data2.total_count_unfiltered !== 'count_too_slow'
  ) {
    setCount(data2.total_count_unfiltered.count);
  }
}

export function mapOrderToEnum<T extends Record<string, string>>(
  order: string,
  enumType: T
): T[keyof T] | undefined {
  // Transform the input order string into Enum value format (replacing ':' with '_').
  const formattedOrder = order.split(':').join('_');

  // Get the enum key that matches the formatted order by value.
  const matchingEnumKey = Object.keys(enumType).find(
    (key) => enumType[key] === formattedOrder
  );

  // If key found, return the equivalent Enum value, else `undefined`.
  if (matchingEnumKey) {
    return enumType[matchingEnumKey as keyof T];
  }

  return undefined;
}

export function createUrl(site: string, path: string) {
  return `${site.replace(/\/$/, '')}/${path.replace(/^\//, '')}`;
}

export function getIconUrl(address: string) {
  const exchangeImage = Object.entries(ExchangeAddressMapping).find(
    ([hotkey]) => hotkey === address
  )?.[1].icon;

  if (address === '5Fq5v71D4LX8Db1xsmRSy6udQThcZ8sFDqxQFwnUZ1BuqY5A') {
    return imagePaths.icons.northtensor;
  }

  if (address === '5CoZxgtfhcJKX2HmkwnsN18KbaT9aih9eF3b6qVPTgAUbifj') {
    return imagePaths.icons.holding;
  }

  if (address === '5CsvRJXuR955WojnGMdok1hbhffZyB4N5ocrv82f3p5A2zVp') {
    return imagePaths.icons.tao5;
  }

  if (address === '5D4gEn5S422dTGR5NJJKZ93FNV6hDmfwDPfxFNgcoVnUkZ4f') {
    return imagePaths.icons.tatsu;
  }

  if (address === '5E2b2DcMd5W8MBhzTCFt63t2ZEN8RsRgL7oDd7BFYL9aMQux') {
    return imagePaths.icons.kranken;
  }

  if (exchangeImage) {
    return `https://taostats-static.b-cdn.net/exchanges/${exchangeImage}.png`;
  }

  if (address === '5HEo565WAy4Dbq3Sv271SAi7syBSofyfhhwRNjFNSM2gP9M2') {
    return imagePaths.icons.foundry;
  }

  const validatorImage = Object.entries(validatorIconMapping).find(
    ([hotkey]) => hotkey === address
  )?.[1];
  if (validatorImage) {
    return `https://taostats-static.b-cdn.net/validators/${validatorImage}.png`;
  }

  if (address === '5D4oo3Z5VFUJtFWcK9wtfPqkGnzVubSFMnWnMKUVkDxsrWSj') {
    return imagePaths.icons.foundation;
  }

  if (address === '5DM7CPqPKtMSADhFKYsstsCS4Tm4Kd6PMXoh6DdqY4MtxmtX') {
    return imagePaths.icons.cortext;
  }

  return `data:image/svg+xml;utf8,${generateFromString(address)}`;
}

type AppEnv = 'dev' | 'beta' | 'prod';
export const getAppEnv = () => {
  const getAppEnvResult = process.env.NEXT_PUBLIC_APP_ENV;
  return getAppEnvResult as AppEnv;
};

export const isAppEnv = (env: AppEnv[]) => {
  const isAppEnvResult = env.includes(getAppEnv());
  return isAppEnvResult;
};

export const isDevOrBeta = () => {
  const isDevOrBetaResult = isAppEnv(['dev', 'beta']);
  return isDevOrBetaResult;
};

export const getCookie = (name: string) => {
  return Cookie.get(name);
};

export const setCookie = (name: string, value: string) => {
  return Cookie.set(name, value, { domain: 'taostats.io' });
};

export const walletFormat = (address: string) => {
  return `${address.slice(0, 6)}...${address.slice(
    address.length - 6,
    address.length
  )}`;
};

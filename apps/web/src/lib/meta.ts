import type { Metadata } from 'next';

export function constructMetadata({
  title = 'Taostats · Bittensor Network Block Explorer, Data Analytics, API and Node Support',
  description = 'Explore the official Bittensor blockchain explorer at taostats.io, your trusted source for metagraph analytics, TAO token data, and personalized dashboards. Access APIs, RPC services, and more.',
  image = '/thumbnail.png',
  icons = '/favicon.ico',
  noIndex = false,
}: {
  title?: string;
  description?: string;
  image?: string;
  icons?: string;
  noIndex?: boolean;
} = {}): Metadata {
  return {
    title,
    description,
    openGraph: {
      title,
      description,
      images: [
        {
          url: image,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [image],
    },
    icons,
    metadataBase: new URL('https://beta.taostats.io'),
    ...(noIndex && {
      robots: {
        index: false,
        follow: false,
      },
    }),
  };
}

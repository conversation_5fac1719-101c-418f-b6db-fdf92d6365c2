import type { QueryClient } from '@tanstack/react-query';
import type {
  DtaoValidatorLatestQueryParams,
  PoolQueryParams,
} from '@repo/types/website-api-types';
import { PoolOrder } from '@repo/types/website-api-types';
import { fetchBlocks } from '@/api-proxy/blocks';
import { fetchDtaoSubnet } from '@/api-proxy/dtao';
import { fetchLatestPrice } from '@/api-proxy/price';
import { fetchStats } from '@/api-proxy/stats';
import { fetchSubnetIdentity } from '@/api-proxy/subnets';
import { fetchAllIdentity, fetchDtaoValidator } from '@/api-proxy/validators';
import { queryKeys } from '@/lib/constants/query-keys';

export const prefetchLatestPrice = async (queryClient: QueryClient) => {
  return queryClient.prefetchQuery({
    queryKey: [queryKeys.price],
    queryFn: async () => {
      const response = await fetchLatestPrice({ asset: 'TAO' });
      return response;
    },
  });
};

export const prefetchLatestBlock = async (queryClient: QueryClient) => {
  return queryClient.prefetchQuery({
    queryKey: [queryKeys.latestBlock],
    queryFn: async () => {
      const response = await fetchBlocks({ limit: 1 });
      return response;
    },
  });
};

export const prefetchSubnetIdentities = async (queryClient: QueryClient) => {
  return queryClient.prefetchQuery({
    queryKey: [queryKeys.subnetIdentities],
    queryFn: async () => {
      const response = await fetchSubnetIdentity({});
      return response;
    },
  });
};

export const prefetchDtaoValidators = async (
  queryClient: QueryClient,
  params: DtaoValidatorLatestQueryParams
) => {
  return queryClient.prefetchQuery({
    queryKey: [queryKeys.validators, params],
    queryFn: async () => {
      const response = await fetchDtaoValidator();
      return response;
    },
  });
};

export const prefetchValidatorIdentities = async (queryClient: QueryClient) => {
  return queryClient.prefetchQuery({
    queryKey: [queryKeys.validatorIdentities],
    queryFn: async () => {
      const response = await fetchAllIdentity();
      return response;
    },
  });
};

export const prefetchDtaoSubnetPools = async (
  queryClient: QueryClient,
  params: PoolQueryParams = { order: PoolOrder.NetuidAsc }
) => {
  return queryClient.prefetchQuery({
    queryKey: [queryKeys.dtaoSubnetPools, params],
    queryFn: async () => {
      const response = await fetchDtaoSubnet({ ...params });
      return response;
    },
  });
};

export const prefetchStats = async (queryClient: QueryClient) => {
  return queryClient.prefetchQuery({
    queryKey: [queryKeys.stats],
    queryFn: async () => {
      const response = await fetchStats();
      return response;
    },
  });
};

// Prefetch all navigation-related data for TopNavbar
export const prefetchNavigationData = async (queryClient: QueryClient) => {
  await Promise.all([
    prefetchLatestPrice(queryClient),
    prefetchLatestBlock(queryClient),
    prefetchSubnetIdentities(queryClient),
    prefetchDtaoValidators(queryClient, {}),
    prefetchValidatorIdentities(queryClient),
    prefetchDtaoSubnetPools(queryClient),
  ]);
};

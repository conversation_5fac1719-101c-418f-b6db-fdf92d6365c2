export const ValidatorsCardList = [
	{
		icon: "O",
		title: "Opentensor Foundation",
		description:
			"Text information about the subnets will go here - just a brief explanation about the subnet.",
	},
	{
		icon: "α",
		title: "Taostats & Corcel",
		description:
			"Text information about the subnets will go here - just a brief explanation about the subnet.",
	},
	{
		icon: "β",
		title: "Foundry",
		description:
			"Text information about the subnets will go here - just a brief explanation about the subnet.",
	},
	{
		icon: "γ",
		title: "Bittensor Guru Podcast",
		description:
			"Text information about the subnets will go here - just a brief explanation about the subnet.",
	},
	{
		icon: "δ",
		title: "Tao-validator.com",
		description:
			"Text information about the subnets will go here - just a brief explanation about the subnet.",
	},
	{
		icon: "ζ",
		title: "FirstTensor",
		description:
			"Text information about the subnets will go here - just a brief explanation about the subnet.",
	},
	{
		icon: "η",
		title: "RoundTable21",
		description:
			"Text information about the subnets will go here - just a brief explanation about the subnet.",
	},
	{
		icon: "θ",
		title: "Rizzo",
		description:
			"Text information about the subnets will go here - just a brief explanation about the subnet.",
	},
];

export const weightCopiers = [
	"5GP7c3fFazW9GXK8Up3qgu2DJBk8inu4aK9TZy3RuoSWVCMi",
	"5E4hBXkG9uVc1y9zdNzgCiLHrPbFukChkYeN1LxFnZgg4ASL",
	"5CVS9d1NcQyWKUyadLevwGxg6LgBcF9Lik6NSnbe5q59jwhE",
	"5CsvRJXuR955WojnGMdok1hbhffZyB4N5ocrv82f3p5A2zVp",
	"5EWLgJHBVE64kJKb3GWzzh33QEPNcj6UNr4cqyr1kkDsaVrk",
	"5D4oo3Z5VFUJtFWcK9wtfPqkGnzVubSFMnWnMKUVkDxsrWSj",
	"5ELREhApbCahM7FyGLM1V9WDsnnjCRmMCJTmtQD51oAEqwVh",
];

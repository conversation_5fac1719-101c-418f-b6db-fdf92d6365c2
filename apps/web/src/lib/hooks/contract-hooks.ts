import { useQuery } from '@tanstack/react-query';
import {
  ContractOrder,
  type ContractParamsAtom,
} from '@repo/types/website-api-types';
import { apiClient, handleResponse } from '@/lib/api/api-client';

const contractApiClient = apiClient.contract;

export const useContract = (params: ContractParamsAtom = {}) => {
  return useQuery({
    queryKey: ['contract', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { page, limit, order } = queryParams as ContractParamsAtom;

      return handleResponse(
        await contractApiClient.contract.$get({
          query: {
            page,
            limit,
            order: order ? order : ContractOrder.TimestampDesc,
          },
        })
      );
    },
  });
};

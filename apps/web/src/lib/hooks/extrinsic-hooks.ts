import { useQuery } from '@tanstack/react-query';
import type {
  ExtrinsicParamsAtom,
  SudoQueryParamsAtom,
} from '@repo/types/website-api-types';
import { apiClient, handleResponse } from '@/lib/api/api-client';

const extrinsicApiClient = apiClient.extrinsic;

export const useExtrinsic = (params: ExtrinsicParamsAtom = {}) => {
  return useQuery({
    queryKey: ['extrinsic', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { page, limit, order, signerAddress, blockNumber, hash, network } =
        queryParams as ExtrinsicParamsAtom;

      return handleResponse(
        await extrinsicApiClient.extrinsic.$get({
          query: {
            page,
            limit,
            order,
            signer_address: signerAddress,
            block_number: blockNumber,
            hash,
            network,
          },
        })
      );
    },
  });
};

export const useSudo = (params: SudoQueryParamsAtom = { limit: 10 }) => {
  return useQuery({
    queryKey: ['sudo', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { page, limit, order } = queryParams as SudoQueryParamsAtom;

      return handleResponse(
        await extrinsicApiClient.sudo.$get({
          query: {
            full_name: 'Sudo.sudo',
            page,
            limit,
            order,
          },
        })
      );
    },
  });
};

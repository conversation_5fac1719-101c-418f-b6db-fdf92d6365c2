import { useQuery } from '@tanstack/react-query';
import type { EvmBlockParamsAtom } from '@repo/types/website-api-types';
import { apiClient, handleResponse } from '@/lib/api/api-client';

const evmApiClient = apiClient.evm;

export const useEvmBlocks = (params: EvmBlockParamsAtom = {}) => {
  return useQuery({
    queryKey: ['evmBlocks', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { page, limit, search, order } = queryParams as EvmBlockParamsAtom;
      let blockNumber;

      // Identify if the 'search' input is a hash or block number
      if (search && /^\d+$/.test(search)) {
        blockNumber = Number(search);
      }

      return handleResponse(
        await evmApiClient.evmBlocks.$get({
          query: {
            page,
            limit,
            block_number: blockNumber,
            order,
          },
        })
      );
    },

    retryDelay: 1000,
  });
};

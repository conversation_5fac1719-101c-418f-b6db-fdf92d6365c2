import { useQuery } from '@tanstack/react-query';
import type {
  DtaoValidatorLatestQueryParams,
  DtaoValidatorPerformanceLatestQueryParams,
  ValidatorHotkeyQueryParams,
  ValidatorLatestQueryParams,
  ValidatorMetricsLatestQueryParams,
  ValidatorPerformanceQueryParams,
} from '@repo/types/website-api-types';
import { apiClient, handleResponse } from '@/lib/api/api-client';
import { queryKeys } from '@/lib/constants/query-keys';

const validatorApiClient = apiClient.validator;

export const useValidators = (params: ValidatorLatestQueryParams = {}) => {
  return useQuery({
    queryKey: ['validators', params],
    queryFn: async () =>
      handleResponse(
        await validatorApiClient.validators.$get({ query: { ...params } })
      ),
  });
};

export const useFullValidators = (params: ValidatorLatestQueryParams = {}) => {
  return useQuery({
    queryKey: ['full-validators', params],
    queryFn: async () =>
      handleResponse(
        await validatorApiClient.fullValidators.$get({ query: { ...params } })
      ),
  });
};

export const useValidatorPerformanceHistory = (
  params: ValidatorPerformanceQueryParams = { hotkey: '', netuid: -1 }
) => {
  return useQuery({
    queryKey: ['validatorPerformanceHistory', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { hotkey, netuid } = queryParams as ValidatorPerformanceQueryParams;
      if (netuid < 0) return [];
      return handleResponse(
        await validatorApiClient.validatorPerformanceHistory.$get({
          query: { hotkey, netuid },
        })
      );
    },
    staleTime: 24 * 60 * 60 * 1000,
    refetchInterval: 24 * 60 * 60 * 1000,
  });
};

export const useChildPerformance = (
  params: ValidatorHotkeyQueryParams = { hotkey: '' }
) => {
  return useQuery({
    queryKey: ['childPerformance', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { hotkey } = queryParams as ValidatorHotkeyQueryParams;
      if (hotkey.length === 0) return [];

      return handleResponse(
        await validatorApiClient.childPerformance.$get({
          query: { hotkey },
        })
      );
    },
  });
};

export const useStakeChart = (
  params: ValidatorHotkeyQueryParams = { hotkey: '' }
) => {
  return useQuery({
    queryKey: ['validatorStakeChart', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { hotkey } = queryParams as ValidatorHotkeyQueryParams;
      if (hotkey.length === 0) return [];

      return handleResponse(
        await validatorApiClient.validatorStakeChart.$get({
          query: { hotkey },
        })
      );
    },
    staleTime: 24 * 60 * 60 * 1000,
    refetchInterval: 24 * 60 * 60 * 1000,
  });
};

export const useMetricsChart = (params: ValidatorMetricsLatestQueryParams) => {
  return useQuery({
    queryKey: ['metricsChart', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { netuid, hotkey, coldkey } =
        queryParams as ValidatorMetricsLatestQueryParams;

      if (!netuid && netuid !== 0) return [];

      return handleResponse(
        await validatorApiClient.metricsChart.$get({
          query: { netuid, hotkey, coldkey },
        })
      );
    },
    staleTime: 24 * 60 * 60 * 1000,
    refetchInterval: 24 * 60 * 60 * 1000,
  });
};

export const useDtaoValidatorPerformance = (
  params: DtaoValidatorPerformanceLatestQueryParams
) => {
  return useQuery({
    queryKey: ['dtaoValidatorPerformanceLatest', params],
    queryFn: async () =>
      handleResponse(
        await validatorApiClient.dtaoValidatorPerformanceLatest.$get({
          query: { ...params },
        })
      ),
    staleTime: 24 * 60 * 60 * 1000,
    refetchInterval: 24 * 60 * 60 * 1000,
  });
};

export const useDtaoValidators = (params: DtaoValidatorLatestQueryParams) => {
  return useQuery({
    queryKey: [queryKeys.validators, params],
    queryFn: async () =>
      handleResponse(
        await validatorApiClient.dtaoValidators.$get({
          query: { ...params },
        })
      ),
  });
};

export const useDtaoValidatorsForSubnet = () => {
  return useQuery({
    queryKey: ['dtaoValidatorsForSubnet'],
    queryFn: async () =>
      handleResponse(await validatorApiClient.dtaoValidatorsForSubnet.$get()),
    staleTime: 24 * 60 * 60 * 1000,
    refetchInterval: 24 * 60 * 60 * 1000,
  });
};

export const useValidatorIdentityData = () => {
  return useQuery({
    queryKey: [queryKeys.validatorIdentities],
    queryFn: async () =>
      handleResponse(await validatorApiClient.validatorIdentity.$get()),
    staleTime: 24 * 60 * 60 * 1000,
    refetchInterval: 24 * 60 * 60 * 1000,
  });
};

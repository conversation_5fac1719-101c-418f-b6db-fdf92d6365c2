import { useQuery } from '@tanstack/react-query';
import type {
  DelegateBalanceQueryParams,
  DelegateQueryParams,
  DtaoDelegateBalanceQueryParams,
  TopDelegatorTransactionQueryParams,
} from '@repo/types/website-api-types';
import { apiClient, handleResponse } from '@/lib/api/api-client';

const delegateApiClient = apiClient.delegate;

export const useDelegate = (params: DelegateQueryParams = { limit: 10 }) => {
  return useQuery({
    queryKey: ['delegate', params],
    queryFn: async () =>
      handleResponse(await delegateApiClient.delegate.$get()),
  });
};

export const useDelegateBalance = (params: DelegateBalanceQueryParams = {}) => {
  return useQuery({
    queryKey: ['delegateBalance', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { page, limit, hotkey, order, coldkey } =
        queryParams as DelegateBalanceQueryParams;

      if (!coldkey && !hotkey) return [];

      return handleResponse(
        await delegateApiClient.delegateBalance.$get({
          query: {
            page,
            limit,
            hotkey,
            coldkey,
            order,
          },
        })
      );
    },
  });
};

export const useTopDelegator = (
  params: TopDelegatorTransactionQueryParams = { validator: '' }
) => {
  return useQuery({
    queryKey: ['topDelegator', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { validator } = queryParams as TopDelegatorTransactionQueryParams;

      if (validator.length === 0) return [];

      return handleResponse(
        await delegateApiClient.topDelegator.$get({
          query: {
            validator,
          },
        })
      );
    },
    staleTime: 24 * 60 * 60 * 1000,
    refetchInterval: 24 * 60 * 60 * 1000,
  });
};

export const useDtaoDelegateBalance = (
  params: DtaoDelegateBalanceQueryParams = {}
) => {
  return useQuery({
    queryKey: ['dtaoDelegateBalance', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { page, limit, hotkey, order, coldkey, netuid } =
        queryParams as DtaoDelegateBalanceQueryParams;

      if (!coldkey && !hotkey) return [];

      return handleResponse(
        await delegateApiClient.dtaoDelegateBalance.$get({
          query: {
            page,
            limit,
            hotkey,
            coldkey,
            order,
            netuid,
          },
        })
      );
    },
  });
};

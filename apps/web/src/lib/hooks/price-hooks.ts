import { useQuery } from '@tanstack/react-query';
import { utc } from 'moment';
import type { DailyPriceChartParams } from '@repo/types/website-api-types';
import { apiClient, handleResponse } from '@/lib/api/api-client';
import { REACT_QUERY_CONFIG } from '@/lib/config';
import { queryKeys } from '@/lib/constants/query-keys';

const priceApiClient = apiClient.price;

export const usePrice = () => {
  return useQuery({
    queryKey: [queryKeys.price],
    queryFn: async () => handleResponse(await priceApiClient.price.$get()),
  });
};

export const usePriceHistory = () => {
  return useQuery({
    queryKey: [queryKeys.priceHistory],
    queryFn: async () =>
      handleResponse(await priceApiClient.priceHistory.$get()),
  });
};

export const usePriceOhlc = () => {
  return useQuery({
    queryKey: [queryKeys.priceOhlc],
    queryFn: async () => handleResponse(await priceApiClient.priceOhlc.$get()),
  });
};

export const useDailyPriceChart = (
  params: DailyPriceChartParams = {
    timestamp_start: utc().subtract(1, 'month').startOf('month').unix(),
    timestamp_end: utc().subtract(1, 'month').endOf('month').unix(),
  }
) => {
  return useQuery({
    queryKey: [queryKeys.dailyPriceChart, params],
    queryFn: async () => {
      return handleResponse(
        await priceApiClient.dailyPriceChart.$get({
          query: {
            timestampStart: params.timestamp_start,
            timestampEnd: params.timestamp_end,
          },
        })
      );
    },
    staleTime: REACT_QUERY_CONFIG.DAILY_STALE_TIME,
    refetchInterval: REACT_QUERY_CONFIG.DAILY_REFETCH_INTERVAL,
  });
};

export const usePriceHistoryChart = () => {
  return useQuery({
    queryKey: [queryKeys.priceHistoryChart],
    queryFn: async () =>
      handleResponse(await priceApiClient.priceHistoryChart.$get()),
    staleTime: 24 * 60 * 60 * 1000,
    refetchInterval: 24 * 60 * 60 * 1000,
  });
};

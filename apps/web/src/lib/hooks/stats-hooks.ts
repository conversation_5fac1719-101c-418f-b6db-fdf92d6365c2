import { useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import type {
  StatsHistoryQueryParams,
  Price,
  Stats,
} from '@repo/types/website-api-types';
import { taoDivider } from '@repo/ui/lib';
import { apiClient, handleResponse } from '@/lib/api/api-client';
import { queryKeys } from '@/lib/constants/query-keys';

const statsApiClient = apiClient.stats;

export const useStats = () => {
  return useQuery({
    queryKey: [queryKeys.stats],
    queryFn: async () => handleResponse(await statsApiClient.stats.$get()),
  });
};

export const useStatsHistory = () => {
  return useQuery({
    queryKey: ['statsHistory'],
    queryFn: async () =>
      handleResponse(await statsApiClient.statsHistory.$get()),
  });
};

export const useSubnetGrowth = () => {
  return useQuery({
    queryKey: ['subnetGrowth'],
    queryFn: async () =>
      handleResponse(await statsApiClient.subnetGrowth.$get()),
    staleTime: 12 * 1000,
    refetchInterval: 12 * 1000,
  });
};

export const useStatsYieldHistory = (params: StatsHistoryQueryParams = {}) => {
  return useQuery({
    queryKey: ['statsYieldHistory', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const {
        page,
        limit,
        timestamp_start: timestampStart,
      } = queryParams as StatsHistoryQueryParams;

      return handleResponse(
        await statsApiClient.statsYieldHistory.$get({
          query: { page, limit, timestamp_start: timestampStart },
        })
      );
    },
    staleTime: 12 * 1000,
    refetchInterval: 12 * 1000,
  });
};

const calculateMarketCap = (
  priceData: Price | undefined,
  statsData: Stats | undefined
) => {
  if (!priceData || !statsData) {
    return 0;
  }

  const marketCap =
    (Number(priceData.price) * Number(statsData.issued)) / taoDivider;
  return marketCap;
};

export const useMarketCap = (
  priceData: Price | undefined,
  statsData: Stats | undefined
) => {
  return useMemo(
    () => calculateMarketCap(priceData, statsData),
    [priceData, statsData]
  );
};

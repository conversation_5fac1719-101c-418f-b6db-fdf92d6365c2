import { useQuery } from '@tanstack/react-query';
import type {
  MetagraphHistoryQueryParams,
  MetagraphParamsAtom,
  RootMetagraphHistoryParams,
} from '@repo/types/website-api-types';
import { apiClient, handleResponse } from '@/lib/api/api-client';
import { REACT_QUERY_CONFIG } from '@/lib/config';

const metagraphApiClient = apiClient.metagraph;

export const useMetagraph = (params: MetagraphParamsAtom = {}) => {
  return useQuery({
    queryKey: ['metagraph', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { page, limit, subnetId, hotkey, coldkey, order, search } =
        queryParams as MetagraphParamsAtom;

      if (!subnetId && subnetId !== 0 && !hotkey && !coldkey) return [];

      return handleResponse(
        await metagraphApiClient.metagraph.$get({
          query: {
            netuid: subnetId,
            hotkey,
            coldkey,
            page,
            limit,
            search,
            order,
          },
        })
      );
    },
  });
};

export const useRootMetagraph = () => {
  return useQuery({
    queryKey: ['rootMetagraph'],
    queryFn: async () =>
      handleResponse(await metagraphApiClient.rootMetagraph.$get()),
    staleTime: REACT_QUERY_CONFIG.MINUTE_STALE_TIME,
    retryDelay: 1000,
  });
};

export const useRootMetagraphHistory = (
  params: RootMetagraphHistoryParams = {}
) => {
  return useQuery({
    queryKey: ['rootMetagraphHistory', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { order, limit, hotkey } =
        queryParams as RootMetagraphHistoryParams;

      return handleResponse(
        await metagraphApiClient.rootMetagraphHistory.$get({
          query: {
            order,
            limit,
            hotkey,
          },
        })
      );
    },

    retryDelay: 1000,
  });
};

export const useMetagraphHistory = (
  params: MetagraphHistoryQueryParams = {}
) => {
  return useQuery({
    queryKey: ['metagraphHistory', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const {
        netuid,
        timestamp_start: timestampStart,
        uid,
        order,
        limit,
      } = queryParams as MetagraphHistoryQueryParams;

      if (!netuid && netuid !== 0) return [];

      return handleResponse(
        await metagraphApiClient.metagraphHistory.$get({
          query: {
            netuid,
            uid,
            timestamp_start: timestampStart,
            limit,
            order,
          },
        })
      );
    },
    staleTime: 12 * 1000,
    refetchInterval: 12 * 1000,
    retryDelay: 1000,
  });
};

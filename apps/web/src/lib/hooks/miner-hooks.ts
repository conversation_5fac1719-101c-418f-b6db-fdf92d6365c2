import { useQuery } from '@tanstack/react-query';
import type {
  MinerPerformanceByNetuidParams,
  MinerWeightChartParams,
} from '@repo/types/website-api-types';
import { apiClient, handleResponse } from '@/lib/api/api-client';
import { REACT_QUERY_CONFIG } from '@/lib/config';

const minerApiClient = apiClient.miner;

export const useMinerWeightsChart = (
  params: MinerWeightChartParams = { netuid: -1, minerUid: -1 }
) => {
  return useQuery({
    queryKey: ['minerWeightsChart', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { netuid, minerUid } = queryParams as MinerWeightChartParams;

      if (netuid === -1 || minerUid === -1) return [];

      return handleResponse(
        await minerApiClient.minerWeightsChart.$get({
          query: { netuid, minerUid },
        })
      );
    },
    staleTime: REACT_QUERY_CONFIG.DAILY_STALE_TIME,
    refetchInterval: REACT_QUERY_CONFIG.DAILY_REFETCH_INTERVAL,
  });
};

export const useMinerPerformanceByNetuid = (
  params: MinerPerformanceByNetuidParams = { netuid: 1 }
) => {
  return useQuery({
    queryKey: ['minerPerformanceByNetuid', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { netuid } = queryParams as MinerPerformanceByNetuidParams;

      if (netuid === -1) return [];

      return handleResponse(
        await minerApiClient.minerPerformanceByNetuid.$get({
          query: { netuid },
        })
      );
    },
    staleTime: REACT_QUERY_CONFIG.DAILY_STALE_TIME,
    refetchInterval: REACT_QUERY_CONFIG.DAILY_REFETCH_INTERVAL,
  });
};

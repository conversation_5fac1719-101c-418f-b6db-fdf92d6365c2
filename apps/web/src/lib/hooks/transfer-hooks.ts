import { useQuery } from '@tanstack/react-query';
import type { TransferParamsAtom } from '@repo/types/website-api-types';
import { TransferOrder } from '@repo/types/website-api-types';
import { apiClient, handleResponse } from '@/lib/api/api-client';

const transfersApiClient = apiClient.transfer;

export const useTransfer = (params: TransferParamsAtom = {}) => {
  return useQuery({
    queryKey: ['transfers', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const {
        page,
        limit,
        address,
        amountMin,
        order,
        addressFrom,
        addressTo,
        network,
        timestampStart,
        timestampEnd,
      } = queryParams as TransferParamsAtom;
      return handleResponse(
        await transfersApiClient.transfers.$get({
          query: {
            page,
            limit,
            address,
            amount_min: amountMin,
            order: order ? order : TransferOrder.TimestampDesc,
            from: addressFrom,
            to: addressTo,
            network,
            timestamp_start: timestampStart,
            timestamp_end: timestampEnd,
          },
        })
      );
    },
  });
};

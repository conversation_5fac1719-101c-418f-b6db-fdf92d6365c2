import { useQuery } from '@tanstack/react-query';
import type {
  TransactionParamsAtom,
  WalletTransactionParamsAtom,
} from '@repo/types/website-api-types';
import { TransactionOrder } from '@repo/types/website-api-types';
import { apiClient, handleResponse } from '@/lib/api/api-client';

const transactionApiClient = apiClient.transaction;

export const useTransaction = (params: TransactionParamsAtom = {}) => {
  return useQuery({
    queryKey: ['transaction', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { page, limit, order } = queryParams as TransactionParamsAtom;

      return handleResponse(
        await transactionApiClient.transaction.$get({
          query: {
            page,
            limit,
            order: order ? order : TransactionOrder.TimestampDesc,
          },
        })
      );
    },
  });
};

export const useWalletTransaction = (
  params: WalletTransactionParamsAtom = {}
) => {
  return useQuery({
    queryKey: ['walletTransaction', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { address } = queryParams as WalletTransactionParamsAtom;
      if (address === '') return [];
      return handleResponse(
        await transactionApiClient.walletTransaction.$get({
          query: {
            address,
          },
        })
      );
    },
  });
};

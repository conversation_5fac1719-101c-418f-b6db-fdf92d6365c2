'use client';

import { useAtom } from 'jotai';
import { atomWithReset, useResetAtom } from 'jotai/utils';

const bittensorPriceAtom = atomWithReset<{
  price: number;
}>({
  price: 601.24,
});

export function useBittensorPrice() {
  const [priceAtom, setPriceAtom] = useAtom(bittensorPriceAtom);
  const resetPriceAtom = useResetAtom(bittensorPriceAtom);

  return {
    priceAtom,
    setPriceAtom,

    resetPriceAtom,
  };
}

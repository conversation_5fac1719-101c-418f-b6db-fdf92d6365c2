import { useQuery } from '@tanstack/react-query';
import type {
  AccountHistoryParamsAtom,
  AccountParamsAtom,
} from '@repo/types/website-api-types';
import { apiClient, handleResponse } from '@/lib/api/api-client';
import { REACT_QUERY_CONFIG } from '@/lib/config';

const accountApiClient = apiClient.account;

export const useAccount = (params: AccountParamsAtom = {}) => {
  return useQuery({
    queryKey: ['account', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const {
        page,
        limit,
        address,
        minBalance,
        order,
        network,
        timestampEnd,
        timestampStart,
      } = queryParams as AccountParamsAtom;

      return handleResponse(
        await accountApiClient.account.$get({
          query: {
            page,
            limit,
            address,
            balance_total_min: minBalance,
            created_on_network: network,
            created_on_timestamp_start: timestampStart,
            created_on_timestamp_end: timestampEnd,
            order,
          },
        })
      );
    },
  });
};

export const useAccountHistory = (
  params: AccountHistoryParamsAtom = { address: '', limit: 5000 }
) => {
  return useQuery({
    queryKey: ['accountHistory', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { limit = 5000, address } = queryParams as AccountParamsAtom;

      if (!address || address.length === 0) return [];

      return handleResponse(
        await accountApiClient.accountHistory.$get({
          query: {
            address,
            limit,
          },
        })
      );
    },
    staleTime: REACT_QUERY_CONFIG.DAILY_STALE_TIME,
    refetchInterval: REACT_QUERY_CONFIG.DAILY_REFETCH_INTERVAL,
  });
};

export const useRecentAccounts = () => {
  return useQuery({
    queryKey: ['recentAccounts'],
    queryFn: async () =>
      handleResponse(await accountApiClient.recentAccounts.$get()),
    staleTime: REACT_QUERY_CONFIG.DAILY_STALE_TIME,
    refetchInterval: REACT_QUERY_CONFIG.DAILY_REFETCH_INTERVAL,
  });
};

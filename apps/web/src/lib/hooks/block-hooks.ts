import { useQuery } from '@tanstack/react-query';
import type {
  BlockIntervalQueryParams,
  BlockParamsAtom,
} from '@repo/types/website-api-types';
import { BlockFrequency } from '@repo/types/website-api-types';
import { apiClient, handleResponse } from '@/lib/api/api-client';
import { REACT_QUERY_CONFIG } from '@/lib/config';
import { queryKeys } from '@/lib/constants/query-keys';

const blockApiClient = apiClient.blocks;

export const useBlock = (
  params: BlockParamsAtom = { refetchInterval: 12 * 1000 }
) => {
  return useQuery({
    queryKey: ['blocks', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { page, limit, search, order } = queryParams as BlockParamsAtom;
      let blockNumber;
      let hash;

      // Identify if the 'search' input is a hash or block number
      // If 'search' starts with '0x', it is considered as a hash
      if (search?.startsWith('0x')) {
        hash = search;
        // If 'search' contains only digits (and not null), it is taken as a block number
      } else if (search && /^\d+$/.test(search)) {
        blockNumber = Number(search);
      }

      return handleResponse(
        await blockApiClient.block.$get({
          query: {
            page,
            limit,
            block_number: blockNumber,
            hash,
            order,
          },
        })
      );
    },
    staleTime: REACT_QUERY_CONFIG.DAILY_STALE_TIME,
    refetchInterval: REACT_QUERY_CONFIG.DAILY_REFETCH_INTERVAL,
  });
};

export const useLatestBlock = () => {
  return useQuery({
    queryKey: [queryKeys.latestBlock],
    queryFn: async () =>
      handleResponse(
        await blockApiClient.block.$get({
          query: {
            limit: 1,
          },
        })
      ),
  });
};

export const useBlockInterval = (
  params: { frequency?: BlockFrequency } = {}
) => {
  return useQuery({
    queryKey: ['blockInterval', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { frequency = BlockFrequency.ByDay } =
        queryParams as BlockIntervalQueryParams;

      return handleResponse(
        await blockApiClient.blockInterval.$get({
          query: {
            frequency,
          },
        })
      );
    },
  });
};

import { useQuery } from '@tanstack/react-query';
import {
  EventOrder,
  type EventParamsAtom,
} from '@repo/types/website-api-types';
import { apiClient, handleResponse } from '@/lib/api/api-client';
import { REACT_QUERY_CONFIG } from '@/lib/config';

const eventApiClient = apiClient.event;

export const useRuntime = (params: EventParamsAtom = {}) => {
  return useQuery({
    queryKey: ['events', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { page, limit, order, extrinsicId, blockNumber, name, network } =
        queryParams as EventParamsAtom;

      return handleResponse(
        await eventApiClient.event.$get({
          query: {
            page,
            limit,
            name,
            order,
            extrinsic_id: extrinsicId,
            block_number: blockNumber,
            network,
          },
        })
      );
    },
  });
};

export const useSuccessToFailureRatio = () => {
  return useQuery({
    queryKey: ['successToFailureRatio'],
    queryFn: async () =>
      handleResponse(await eventApiClient.successToFailureRatio.$get()),
    staleTime: REACT_QUERY_CONFIG.DAILY_STALE_TIME,
    refetchInterval: REACT_QUERY_CONFIG.DAILY_REFETCH_INTERVAL,
  });
};

export const useWeeklyExtrinsic = (
  params: { type: string } = { type: 'Daily' }
) => {
  return useQuery({
    queryKey: ['weeklyExtrinsic', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { type } = queryParams as { type: string };

      return handleResponse(
        await eventApiClient.weeklyExtrinsic.$get({
          query: {
            type,
          },
        })
      );
    },
  });
};

export const useCumulativeSubnets = () => {
  return useQuery({
    queryKey: ['cumulativeSubnets'],
    queryFn: async () =>
      handleResponse(
        await eventApiClient.event.$get({
          query: {
            name: 'NetworkAdded',
            order: EventOrder.BlockNumberDesc,
            limit: 200,
          },
        })
      ),
    staleTime: 24 * 60 * 60 * 1000,
    refetchInterval: 24 * 60 * 60 * 1000,
  });
};

import { useQuery } from '@tanstack/react-query';
import type { RuntimeQueryParamsAtom } from '@repo/types/website-api-types';
import { apiClient, handleResponse } from '@/lib/api/api-client';

const runtimeApiClient = apiClient.runtime;

export const useRuntime = (params: RuntimeQueryParamsAtom = {}) => {
  return useQuery({
    queryKey: ['runtime', params],
    queryFn: async () => handleResponse(await runtimeApiClient.runtime.$get()),
  });
};

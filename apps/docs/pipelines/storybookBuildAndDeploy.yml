name: Azure Static Web Apps CI/CD

trigger:
  branches:
    include:
      - main

jobs:
- job: build_and_deploy_job
  displayName: Build and Deploy Job
  condition: or(eq(variables['Build.Reason'], 'Manual'),or(eq(variables['Build.Reason'], 'PullRequest'),eq(variables['Build.Reason'], 'IndividualCI')))
  pool:
    vmImage: ubuntu-latest
  variables:
  - group: Azure-Static-Web-Apps-kind-mud-0aaf83203-variable-group

  steps:
  - checkout: self
    submodules: true

  - task: NodeTool@0
    inputs:
      versionSpec: "18.x"
    displayName: "Install Node.js"

  - script: | 
      npm install -g pnpm
    displayName: "Install pnpm"

  - script: |
      pnpm install
    displayName: "pnpm install"

  - script: |
      pnpm build --filter docs
    displayName: "pnpm run build --filter docs"

  - task: AzureStaticWebApp@0
    inputs:
      skip_app_build: true
      app_build_command: "pnpm run build"
      azure_static_web_apps_api_token: $(AZURE_STATIC_WEB_APPS_API_TOKEN_KIND_MUD_0AAF83203)
###### Repository/Build Configurations - These values can be configured to match your app requirements. ######
# For more information regarding Static Web App workflow configurations, please visit: https://aka.ms/swaworkflowconfig
      app_location: "/apps/docs/storybook-static" # App source code path
      api_location: "" # Api source code path - optional
      ##output_location: "storybook-static" # Built app content directory - optional
###### End of Repository/Build Configurations ######


{"name": "docs", "private": true, "version": "0.1.0", "scripts": {"dev": "storybook dev -p 6006", "build": "storybook build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@repo/ui": "workspace:*"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@storybook/addon-essentials": "^7.6.7", "@storybook/addon-interactions": "^7.6.7", "@storybook/addon-links": "^7.6.7", "@storybook/blocks": "^7.6.7", "@storybook/cli": "^7.6.7", "@storybook/react": "^7.6.7", "@storybook/react-vite": "^7.6.7", "@storybook/testing-library": "^0.2.0", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "postcss": "^8.4.27", "prop-types": "^15.8.1", "storybook": "^7.6.7", "tailwindcss": "^3.3.3", "typescript": "^5.0.2", "vite": "^4.4.5"}}
{"eslint.workingDirectories": [{"mode": "auto"}], "editor.detectIndentation": false, "editor.tabSize": 2, "editor.formatOnType": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "never"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.formatOnSave": true, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.bracketPairColorization.enabled": true, "editor.guides.bracketPairs": true, "prettier.requireConfig": true, "typescript.updateImportsOnFileMove.enabled": "always", "javascript.updateImportsOnFileMove.enabled": "always", "window.commandCenter": true, "typescript.referencesCodeLens.enabled": true, "diffEditor.codeLens": true, "typescript.implementationsCodeLens.enabled": true, "typescript.referencesCodeLens.showOnAllFunctions": true, "javascript.referencesCodeLens.enabled": true, "javascript.referencesCodeLens.showOnAllFunctions": true, "editor.inlineSuggest.enabled": true, "eslint.codeActionsOnSave.rules": null, "eslint.enable": true, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"]}
# Taostats Frontend Monorepo

## Turborepo + NextJS + TailwindCSS + Shadcn + Storybook

## What's included?

This Turborepo includes the following packages/apps:

### Apps and Packages

- `dashboard`: Taostats Dashboard built with [Next.js](https://nextjs.org/)
- `docs`: a Storybook app with the `@repo/ui` package imported
  - Note - the stories for the components in storybook are co-located with the components in the `ui` package
- `@repo/types`: a shared types package
- `@repo/ui`: a React component library using [Tailwind CSS](https://tailwindcss.com/) with some components based on [shadcn-ui](https://github.com/shadcn/ui) included.
- `@repo/config-eslint`: package to hold any shared eslint configurations
- `@repo/config`: package to hold any other shared configurations

Each package/app is [TypeScript](https://www.typescriptlang.org/).

## Package manager

This repo is setup to use [pnpm](https://pnpm.io/)

## Getting started

To get up and running, from the project root, run the following:

```
pnpm i
pnpm run dev
```

That will install all dependencies and run all apps.

if you want to run a single app, you can run:
`pnpm run dev --filter={app-name}`

e.g.
`pnpm run dev --filter=dashboard`

## Docker

To get the dashboard running in docker, first build the docker image:

```
docker build -f apps/dashboard/Dockerfile -t dashboard .
```

Then run the docker container:

```
docker run -p 3000:3000 \
  -e AUTH_URL="http://localhost:3000" \
  -e AUTH_SECRET={AUTH_SECRET} \
  -e AUTH_GITHUB_ID={GITHUB_ID} \
  -e AUTH_GOOGLE_ID={GOOGLE_ID} \
  -e NEXT_PUBLIC_DASHBOARD_BASE_URL={NEXT_PUBLIC_DASHBOARD_BASE_URL} \
  -e DASHBOARD_API_URL={DASHBOARD_API_URL} \
  -e NEXT_PUBLIC_WEBSITE_BASE_URL={NEXT_PUBLIC_WEBSITE_BASE_URL} \
  -e WEBSITE_API_URL={WEBSITE_API_URL} \
  -e WEBSITE_API_TOKEN={WEBSITE_API_TOKEN} \
  dashboard
```

## Troubleshooting

- When adding types to @repo/types, if you have the ESLint extension running in VS Code or Cursor, it may not pick up the new types straight away at your call site. Usually restarting the ESLint extension should fix it: `CMD + Shift + P` -> `ESLint: Restart ESLint Server`. Similarly, if typescript is also struggling to see your type changes, you can try restarting the typescript server: `CMD + Shift + P` -> `TypeScript: Restart TS Server`.

## API

- The Dashboard API swagger can be found at `https://management-api.taostats.io/doc`
